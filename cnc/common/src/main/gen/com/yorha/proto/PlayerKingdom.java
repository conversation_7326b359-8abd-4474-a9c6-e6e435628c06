// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_kingdom.proto

package com.yorha.proto;

public final class PlayerKingdom {
  private PlayerKingdom() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_KingAppoint_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingAppoint_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 头衔（官职）id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 1;</code>
     * @return Whether the kingdomOfficeId field is set.
     */
    boolean hasKingdomOfficeId();
    /**
     * <pre>
     * 头衔（官职）id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 1;</code>
     * @return The kingdomOfficeId.
     */
    int getKingdomOfficeId();

    /**
     * <pre>
     * 给谁官职
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    boolean hasToPlayerId();
    /**
     * <pre>
     * 给谁官职
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    long getToPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingAppoint_C2S}
   */
  public static final class Player_KingAppoint_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingAppoint_C2S)
      Player_KingAppoint_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingAppoint_C2S.newBuilder() to construct.
    private Player_KingAppoint_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingAppoint_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingAppoint_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingAppoint_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomOfficeId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toPlayerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMOFFICEID_FIELD_NUMBER = 1;
    private int kingdomOfficeId_;
    /**
     * <pre>
     * 头衔（官职）id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 1;</code>
     * @return Whether the kingdomOfficeId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomOfficeId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 头衔（官职）id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 1;</code>
     * @return The kingdomOfficeId.
     */
    @java.lang.Override
    public int getKingdomOfficeId() {
      return kingdomOfficeId_;
    }

    public static final int TOPLAYERID_FIELD_NUMBER = 2;
    private long toPlayerId_;
    /**
     * <pre>
     * 给谁官职
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasToPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 给谁官职
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    @java.lang.Override
    public long getToPlayerId() {
      return toPlayerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomOfficeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toPlayerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomOfficeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toPlayerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S other = (com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S) obj;

      if (hasKingdomOfficeId() != other.hasKingdomOfficeId()) return false;
      if (hasKingdomOfficeId()) {
        if (getKingdomOfficeId()
            != other.getKingdomOfficeId()) return false;
      }
      if (hasToPlayerId() != other.hasToPlayerId()) return false;
      if (hasToPlayerId()) {
        if (getToPlayerId()
            != other.getToPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomOfficeId()) {
        hash = (37 * hash) + KINGDOMOFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomOfficeId();
      }
      if (hasToPlayerId()) {
        hash = (37 * hash) + TOPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingAppoint_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingAppoint_C2S)
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomOfficeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S result = new com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomOfficeId_ = kingdomOfficeId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toPlayerId_ = toPlayerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S.getDefaultInstance()) return this;
        if (other.hasKingdomOfficeId()) {
          setKingdomOfficeId(other.getKingdomOfficeId());
        }
        if (other.hasToPlayerId()) {
          setToPlayerId(other.getToPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomOfficeId_ ;
      /**
       * <pre>
       * 头衔（官职）id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 1;</code>
       * @return Whether the kingdomOfficeId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomOfficeId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 头衔（官职）id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 1;</code>
       * @return The kingdomOfficeId.
       */
      @java.lang.Override
      public int getKingdomOfficeId() {
        return kingdomOfficeId_;
      }
      /**
       * <pre>
       * 头衔（官职）id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 1;</code>
       * @param value The kingdomOfficeId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomOfficeId(int value) {
        bitField0_ |= 0x00000001;
        kingdomOfficeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 头衔（官职）id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomOfficeId_ = 0;
        onChanged();
        return this;
      }

      private long toPlayerId_ ;
      /**
       * <pre>
       * 给谁官职
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return Whether the toPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasToPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 给谁官职
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return The toPlayerId.
       */
      @java.lang.Override
      public long getToPlayerId() {
        return toPlayerId_;
      }
      /**
       * <pre>
       * 给谁官职
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @param value The toPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setToPlayerId(long value) {
        bitField0_ |= 0x00000002;
        toPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 给谁官职
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toPlayerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingAppoint_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingAppoint_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingAppoint_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingAppoint_C2S>() {
      @java.lang.Override
      public Player_KingAppoint_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingAppoint_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingAppoint_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingAppoint_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingAppoint_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingAppoint_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingAppoint_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingAppoint_S2C}
   */
  public static final class Player_KingAppoint_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingAppoint_S2C)
      Player_KingAppoint_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingAppoint_S2C.newBuilder() to construct.
    private Player_KingAppoint_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingAppoint_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingAppoint_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingAppoint_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C other = (com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingAppoint_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingAppoint_S2C)
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C result = new com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingAppoint_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingAppoint_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingAppoint_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingAppoint_S2C>() {
      @java.lang.Override
      public Player_KingAppoint_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingAppoint_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingAppoint_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingAppoint_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingAppoint_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingOpenBuff_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingOpenBuff_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 国王增益的id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return Whether the kingdomBuffId field is set.
     */
    boolean hasKingdomBuffId();
    /**
     * <pre>
     * 国王增益的id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return The kingdomBuffId.
     */
    int getKingdomBuffId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingOpenBuff_C2S}
   */
  public static final class Player_KingOpenBuff_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingOpenBuff_C2S)
      Player_KingOpenBuff_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingOpenBuff_C2S.newBuilder() to construct.
    private Player_KingOpenBuff_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingOpenBuff_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingOpenBuff_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingOpenBuff_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomBuffId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMBUFFID_FIELD_NUMBER = 1;
    private int kingdomBuffId_;
    /**
     * <pre>
     * 国王增益的id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return Whether the kingdomBuffId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomBuffId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 国王增益的id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return The kingdomBuffId.
     */
    @java.lang.Override
    public int getKingdomBuffId() {
      return kingdomBuffId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomBuffId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomBuffId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S other = (com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S) obj;

      if (hasKingdomBuffId() != other.hasKingdomBuffId()) return false;
      if (hasKingdomBuffId()) {
        if (getKingdomBuffId()
            != other.getKingdomBuffId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomBuffId()) {
        hash = (37 * hash) + KINGDOMBUFFID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomBuffId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingOpenBuff_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingOpenBuff_C2S)
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomBuffId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S result = new com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomBuffId_ = kingdomBuffId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S.getDefaultInstance()) return this;
        if (other.hasKingdomBuffId()) {
          setKingdomBuffId(other.getKingdomBuffId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomBuffId_ ;
      /**
       * <pre>
       * 国王增益的id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return Whether the kingdomBuffId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomBuffId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 国王增益的id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return The kingdomBuffId.
       */
      @java.lang.Override
      public int getKingdomBuffId() {
        return kingdomBuffId_;
      }
      /**
       * <pre>
       * 国王增益的id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @param value The kingdomBuffId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomBuffId(int value) {
        bitField0_ |= 0x00000001;
        kingdomBuffId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国王增益的id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomBuffId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomBuffId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingOpenBuff_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingOpenBuff_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingOpenBuff_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingOpenBuff_C2S>() {
      @java.lang.Override
      public Player_KingOpenBuff_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingOpenBuff_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingOpenBuff_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingOpenBuff_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingOpenBuff_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingOpenBuff_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingOpenBuff_S2C}
   */
  public static final class Player_KingOpenBuff_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingOpenBuff_S2C)
      Player_KingOpenBuff_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingOpenBuff_S2C.newBuilder() to construct.
    private Player_KingOpenBuff_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingOpenBuff_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingOpenBuff_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingOpenBuff_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C other = (com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingOpenBuff_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingOpenBuff_S2C)
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C result = new com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingOpenBuff_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingOpenBuff_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingOpenBuff_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingOpenBuff_S2C>() {
      @java.lang.Override
      public Player_KingOpenBuff_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingOpenBuff_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingOpenBuff_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingOpenBuff_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingOpenBuff_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingSendGift_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingSendGift_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return Whether the kingdomGiftId field is set.
     */
    boolean hasKingdomGiftId();
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return The kingdomGiftId.
     */
    int getKingdomGiftId();

    /**
     * <pre>
     * 礼物发给谁
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    boolean hasToPlayerId();
    /**
     * <pre>
     * 礼物发给谁
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    long getToPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingSendGift_C2S}
   */
  public static final class Player_KingSendGift_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingSendGift_C2S)
      Player_KingSendGift_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingSendGift_C2S.newBuilder() to construct.
    private Player_KingSendGift_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingSendGift_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingSendGift_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingSendGift_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomGiftId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toPlayerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMGIFTID_FIELD_NUMBER = 1;
    private int kingdomGiftId_;
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return Whether the kingdomGiftId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomGiftId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return The kingdomGiftId.
     */
    @java.lang.Override
    public int getKingdomGiftId() {
      return kingdomGiftId_;
    }

    public static final int TOPLAYERID_FIELD_NUMBER = 2;
    private long toPlayerId_;
    /**
     * <pre>
     * 礼物发给谁
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasToPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 礼物发给谁
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    @java.lang.Override
    public long getToPlayerId() {
      return toPlayerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomGiftId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toPlayerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomGiftId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toPlayerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S other = (com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S) obj;

      if (hasKingdomGiftId() != other.hasKingdomGiftId()) return false;
      if (hasKingdomGiftId()) {
        if (getKingdomGiftId()
            != other.getKingdomGiftId()) return false;
      }
      if (hasToPlayerId() != other.hasToPlayerId()) return false;
      if (hasToPlayerId()) {
        if (getToPlayerId()
            != other.getToPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomGiftId()) {
        hash = (37 * hash) + KINGDOMGIFTID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomGiftId();
      }
      if (hasToPlayerId()) {
        hash = (37 * hash) + TOPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingSendGift_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingSendGift_C2S)
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomGiftId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S result = new com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomGiftId_ = kingdomGiftId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toPlayerId_ = toPlayerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S.getDefaultInstance()) return this;
        if (other.hasKingdomGiftId()) {
          setKingdomGiftId(other.getKingdomGiftId());
        }
        if (other.hasToPlayerId()) {
          setToPlayerId(other.getToPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomGiftId_ ;
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return Whether the kingdomGiftId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomGiftId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return The kingdomGiftId.
       */
      @java.lang.Override
      public int getKingdomGiftId() {
        return kingdomGiftId_;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @param value The kingdomGiftId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomGiftId(int value) {
        bitField0_ |= 0x00000001;
        kingdomGiftId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomGiftId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomGiftId_ = 0;
        onChanged();
        return this;
      }

      private long toPlayerId_ ;
      /**
       * <pre>
       * 礼物发给谁
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return Whether the toPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasToPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 礼物发给谁
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return The toPlayerId.
       */
      @java.lang.Override
      public long getToPlayerId() {
        return toPlayerId_;
      }
      /**
       * <pre>
       * 礼物发给谁
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @param value The toPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setToPlayerId(long value) {
        bitField0_ |= 0x00000002;
        toPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 礼物发给谁
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toPlayerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingSendGift_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingSendGift_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingSendGift_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingSendGift_C2S>() {
      @java.lang.Override
      public Player_KingSendGift_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingSendGift_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingSendGift_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingSendGift_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingSendGift_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingSendGift_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingSendGift_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingSendGift_S2C}
   */
  public static final class Player_KingSendGift_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingSendGift_S2C)
      Player_KingSendGift_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingSendGift_S2C.newBuilder() to construct.
    private Player_KingSendGift_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingSendGift_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingSendGift_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingSendGift_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C other = (com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingSendGift_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingSendGift_S2C)
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C result = new com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingSendGift_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingSendGift_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingSendGift_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingSendGift_S2C>() {
      @java.lang.Override
      public Player_KingSendGift_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingSendGift_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingSendGift_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingSendGift_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingSendGift_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingUseSkill_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingUseSkill_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    boolean hasKingdomSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    int getKingdomSkillId();

    /**
     * <pre>
     * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    boolean hasToTargetId();
    /**
     * <pre>
     * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    long getToTargetId();

    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingUseSkill_C2S}
   */
  public static final class Player_KingUseSkill_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingUseSkill_C2S)
      Player_KingUseSkill_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingUseSkill_C2S.newBuilder() to construct.
    private Player_KingUseSkill_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingUseSkill_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingUseSkill_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingUseSkill_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomSkillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toTargetId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMSKILLID_FIELD_NUMBER = 1;
    private int kingdomSkillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    @java.lang.Override
    public int getKingdomSkillId() {
      return kingdomSkillId_;
    }

    public static final int TOTARGETID_FIELD_NUMBER = 2;
    private long toTargetId_;
    /**
     * <pre>
     * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    @java.lang.Override
    public boolean hasToTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    @java.lang.Override
    public long getToTargetId() {
      return toTargetId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 3;
    private int zoneId_;
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S other = (com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S) obj;

      if (hasKingdomSkillId() != other.hasKingdomSkillId()) return false;
      if (hasKingdomSkillId()) {
        if (getKingdomSkillId()
            != other.getKingdomSkillId()) return false;
      }
      if (hasToTargetId() != other.hasToTargetId()) return false;
      if (hasToTargetId()) {
        if (getToTargetId()
            != other.getToTargetId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomSkillId()) {
        hash = (37 * hash) + KINGDOMSKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomSkillId();
      }
      if (hasToTargetId()) {
        hash = (37 * hash) + TOTARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToTargetId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingUseSkill_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingUseSkill_C2S)
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.class, com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomSkillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toTargetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S result = new com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomSkillId_ = kingdomSkillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toTargetId_ = toTargetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S.getDefaultInstance()) return this;
        if (other.hasKingdomSkillId()) {
          setKingdomSkillId(other.getKingdomSkillId());
        }
        if (other.hasToTargetId()) {
          setToTargetId(other.getToTargetId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomSkillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return Whether the kingdomSkillId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return The kingdomSkillId.
       */
      @java.lang.Override
      public int getKingdomSkillId() {
        return kingdomSkillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @param value The kingdomSkillId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomSkillId(int value) {
        bitField0_ |= 0x00000001;
        kingdomSkillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomSkillId_ = 0;
        onChanged();
        return this;
      }

      private long toTargetId_ ;
      /**
       * <pre>
       * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return Whether the toTargetId field is set.
       */
      @java.lang.Override
      public boolean hasToTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return The toTargetId.
       */
      @java.lang.Override
      public long getToTargetId() {
        return toTargetId_;
      }
      /**
       * <pre>
       * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @param value The toTargetId to set.
       * @return This builder for chaining.
       */
      public Builder setToTargetId(long value) {
        bitField0_ |= 0x00000002;
        toTargetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 有目标的技能需要传，囚笼应该发玩家id，统御应该发部队id，赋税不用传
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toTargetId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000004;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingUseSkill_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingUseSkill_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingUseSkill_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingUseSkill_C2S>() {
      @java.lang.Override
      public Player_KingUseSkill_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingUseSkill_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingUseSkill_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingUseSkill_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_KingUseSkill_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_KingUseSkill_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_KingUseSkill_S2C}
   */
  public static final class Player_KingUseSkill_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_KingUseSkill_S2C)
      Player_KingUseSkill_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_KingUseSkill_S2C.newBuilder() to construct.
    private Player_KingUseSkill_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_KingUseSkill_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_KingUseSkill_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_KingUseSkill_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C other = (com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_KingUseSkill_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_KingUseSkill_S2C)
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.class, com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C result = new com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_KingUseSkill_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_KingUseSkill_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_KingUseSkill_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_KingUseSkill_S2C>() {
      @java.lang.Override
      public Player_KingUseSkill_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_KingUseSkill_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_KingUseSkill_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_KingUseSkill_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_KingUseSkill_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchHistoryKings_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchHistoryKings_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    int getPage();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchHistoryKings_C2S}
   */
  public static final class Player_FetchHistoryKings_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchHistoryKings_C2S)
      Player_FetchHistoryKings_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchHistoryKings_C2S.newBuilder() to construct.
    private Player_FetchHistoryKings_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchHistoryKings_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchHistoryKings_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchHistoryKings_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 1;
    private int page_;
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, page_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, page_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S other = (com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchHistoryKings_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchHistoryKings_C2S)
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S result = new com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchHistoryKings_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchHistoryKings_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchHistoryKings_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchHistoryKings_C2S>() {
      @java.lang.Override
      public Player_FetchHistoryKings_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchHistoryKings_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchHistoryKings_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchHistoryKings_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchHistoryKings_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchHistoryKings_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     * 总页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return Whether the totalPage field is set.
     */
    boolean hasTotalPage();
    /**
     * <pre>
     * 总页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return The totalPage.
     */
    int getTotalPage();

    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> 
        getKingInfosList();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index);
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    int getKingInfosCount();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
        getKingInfosOrBuilderList();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
        int index);

    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return Whether the curKingInfo field is set.
     */
    boolean hasCurKingInfo();
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return The curKingInfo.
     */
    com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo();
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchHistoryKings_S2C}
   */
  public static final class Player_FetchHistoryKings_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchHistoryKings_S2C)
      Player_FetchHistoryKings_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchHistoryKings_S2C.newBuilder() to construct.
    private Player_FetchHistoryKings_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchHistoryKings_S2C() {
      kingInfos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchHistoryKings_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchHistoryKings_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              totalPage_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                kingInfos_ = new java.util.ArrayList<com.yorha.proto.StructMsg.SimpleKingInfo>();
                mutable_bitField0_ |= 0x00000004;
              }
              kingInfos_.add(
                  input.readMessage(com.yorha.proto.StructMsg.SimpleKingInfo.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              com.yorha.proto.StructMsg.SimpleKingInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = curKingInfo_.toBuilder();
              }
              curKingInfo_ = input.readMessage(com.yorha.proto.StructMsg.SimpleKingInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(curKingInfo_);
                curKingInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          kingInfos_ = java.util.Collections.unmodifiableList(kingInfos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 1;
    private int page_;
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int TOTALPAGE_FIELD_NUMBER = 2;
    private int totalPage_;
    /**
     * <pre>
     * 总页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return Whether the totalPage field is set.
     */
    @java.lang.Override
    public boolean hasTotalPage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 总页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return The totalPage.
     */
    @java.lang.Override
    public int getTotalPage() {
      return totalPage_;
    }

    public static final int KINGINFOS_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> kingInfos_;
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> getKingInfosList() {
      return kingInfos_;
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
        getKingInfosOrBuilderList() {
      return kingInfos_;
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public int getKingInfosCount() {
      return kingInfos_.size();
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index) {
      return kingInfos_.get(index);
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
        int index) {
      return kingInfos_.get(index);
    }

    public static final int CURKINGINFO_FIELD_NUMBER = 4;
    private com.yorha.proto.StructMsg.SimpleKingInfo curKingInfo_;
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return Whether the curKingInfo field is set.
     */
    @java.lang.Override
    public boolean hasCurKingInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return The curKingInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo() {
      return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
    }
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder() {
      return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, totalPage_);
      }
      for (int i = 0; i < kingInfos_.size(); i++) {
        output.writeMessage(3, kingInfos_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(4, getCurKingInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, totalPage_);
      }
      for (int i = 0; i < kingInfos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, kingInfos_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCurKingInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C other = (com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasTotalPage() != other.hasTotalPage()) return false;
      if (hasTotalPage()) {
        if (getTotalPage()
            != other.getTotalPage()) return false;
      }
      if (!getKingInfosList()
          .equals(other.getKingInfosList())) return false;
      if (hasCurKingInfo() != other.hasCurKingInfo()) return false;
      if (hasCurKingInfo()) {
        if (!getCurKingInfo()
            .equals(other.getCurKingInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasTotalPage()) {
        hash = (37 * hash) + TOTALPAGE_FIELD_NUMBER;
        hash = (53 * hash) + getTotalPage();
      }
      if (getKingInfosCount() > 0) {
        hash = (37 * hash) + KINGINFOS_FIELD_NUMBER;
        hash = (53 * hash) + getKingInfosList().hashCode();
      }
      if (hasCurKingInfo()) {
        hash = (37 * hash) + CURKINGINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCurKingInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchHistoryKings_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchHistoryKings_S2C)
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getKingInfosFieldBuilder();
          getCurKingInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        totalPage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (kingInfosBuilder_ == null) {
          kingInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          kingInfosBuilder_.clear();
        }
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = null;
        } else {
          curKingInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C result = new com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.totalPage_ = totalPage_;
          to_bitField0_ |= 0x00000002;
        }
        if (kingInfosBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            kingInfos_ = java.util.Collections.unmodifiableList(kingInfos_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.kingInfos_ = kingInfos_;
        } else {
          result.kingInfos_ = kingInfosBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (curKingInfoBuilder_ == null) {
            result.curKingInfo_ = curKingInfo_;
          } else {
            result.curKingInfo_ = curKingInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasTotalPage()) {
          setTotalPage(other.getTotalPage());
        }
        if (kingInfosBuilder_ == null) {
          if (!other.kingInfos_.isEmpty()) {
            if (kingInfos_.isEmpty()) {
              kingInfos_ = other.kingInfos_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureKingInfosIsMutable();
              kingInfos_.addAll(other.kingInfos_);
            }
            onChanged();
          }
        } else {
          if (!other.kingInfos_.isEmpty()) {
            if (kingInfosBuilder_.isEmpty()) {
              kingInfosBuilder_.dispose();
              kingInfosBuilder_ = null;
              kingInfos_ = other.kingInfos_;
              bitField0_ = (bitField0_ & ~0x00000004);
              kingInfosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getKingInfosFieldBuilder() : null;
            } else {
              kingInfosBuilder_.addAllMessages(other.kingInfos_);
            }
          }
        }
        if (other.hasCurKingInfo()) {
          mergeCurKingInfo(other.getCurKingInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }

      private int totalPage_ ;
      /**
       * <pre>
       * 总页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return Whether the totalPage field is set.
       */
      @java.lang.Override
      public boolean hasTotalPage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 总页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return The totalPage.
       */
      @java.lang.Override
      public int getTotalPage() {
        return totalPage_;
      }
      /**
       * <pre>
       * 总页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @param value The totalPage to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPage(int value) {
        bitField0_ |= 0x00000002;
        totalPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        totalPage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> kingInfos_ =
        java.util.Collections.emptyList();
      private void ensureKingInfosIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          kingInfos_ = new java.util.ArrayList<com.yorha.proto.StructMsg.SimpleKingInfo>(kingInfos_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> kingInfosBuilder_;

      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> getKingInfosList() {
        if (kingInfosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(kingInfos_);
        } else {
          return kingInfosBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public int getKingInfosCount() {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.size();
        } else {
          return kingInfosBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index) {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.get(index);
        } else {
          return kingInfosBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder setKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.set(index, value);
          onChanged();
        } else {
          kingInfosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder setKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.set(index, builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.add(value);
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.add(index, value);
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.add(builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.add(index, builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addAllKingInfos(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.SimpleKingInfo> values) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, kingInfos_);
          onChanged();
        } else {
          kingInfosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder clearKingInfos() {
        if (kingInfosBuilder_ == null) {
          kingInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          kingInfosBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder removeKingInfos(int index) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.remove(index);
          onChanged();
        } else {
          kingInfosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder getKingInfosBuilder(
          int index) {
        return getKingInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
          int index) {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.get(index);  } else {
          return kingInfosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
           getKingInfosOrBuilderList() {
        if (kingInfosBuilder_ != null) {
          return kingInfosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(kingInfos_);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder addKingInfosBuilder() {
        return getKingInfosFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder addKingInfosBuilder(
          int index) {
        return getKingInfosFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo.Builder> 
           getKingInfosBuilderList() {
        return getKingInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
          getKingInfosFieldBuilder() {
        if (kingInfosBuilder_ == null) {
          kingInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder>(
                  kingInfos_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          kingInfos_ = null;
        }
        return kingInfosBuilder_;
      }

      private com.yorha.proto.StructMsg.SimpleKingInfo curKingInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> curKingInfoBuilder_;
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       * @return Whether the curKingInfo field is set.
       */
      public boolean hasCurKingInfo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       * @return The curKingInfo.
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo() {
        if (curKingInfoBuilder_ == null) {
          return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
        } else {
          return curKingInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder setCurKingInfo(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (curKingInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          curKingInfo_ = value;
          onChanged();
        } else {
          curKingInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder setCurKingInfo(
          com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = builderForValue.build();
          onChanged();
        } else {
          curKingInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder mergeCurKingInfo(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (curKingInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              curKingInfo_ != null &&
              curKingInfo_ != com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance()) {
            curKingInfo_ =
              com.yorha.proto.StructMsg.SimpleKingInfo.newBuilder(curKingInfo_).mergeFrom(value).buildPartial();
          } else {
            curKingInfo_ = value;
          }
          onChanged();
        } else {
          curKingInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder clearCurKingInfo() {
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = null;
          onChanged();
        } else {
          curKingInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder getCurKingInfoBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCurKingInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder() {
        if (curKingInfoBuilder_ != null) {
          return curKingInfoBuilder_.getMessageOrBuilder();
        } else {
          return curKingInfo_ == null ?
              com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
        }
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
          getCurKingInfoFieldBuilder() {
        if (curKingInfoBuilder_ == null) {
          curKingInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder>(
                  getCurKingInfo(),
                  getParentForChildren(),
                  isClean());
          curKingInfo_ = null;
        }
        return curKingInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchHistoryKings_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchHistoryKings_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchHistoryKings_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchHistoryKings_S2C>() {
      @java.lang.Override
      public Player_FetchHistoryKings_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchHistoryKings_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchHistoryKings_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchHistoryKings_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchHistoryKings_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchKingdomGiftInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchKingdomGiftInfo_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return Whether the isFetchingLeftNum field is set.
     */
    boolean hasIsFetchingLeftNum();
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return The isFetchingLeftNum.
     */
    boolean getIsFetchingLeftNum();

    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return Whether the giftId field is set.
     */
    boolean hasGiftId();
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return The giftId.
     */
    int getGiftId();

    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return Whether the checkPlayerId field is set.
     */
    boolean hasCheckPlayerId();
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return The checkPlayerId.
     */
    long getCheckPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchKingdomGiftInfo_C2S}
   */
  public static final class Player_FetchKingdomGiftInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchKingdomGiftInfo_C2S)
      Player_FetchKingdomGiftInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchKingdomGiftInfo_C2S.newBuilder() to construct.
    private Player_FetchKingdomGiftInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchKingdomGiftInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchKingdomGiftInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchKingdomGiftInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isFetchingLeftNum_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              giftId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              checkPlayerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ISFETCHINGLEFTNUM_FIELD_NUMBER = 1;
    private boolean isFetchingLeftNum_;
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return Whether the isFetchingLeftNum field is set.
     */
    @java.lang.Override
    public boolean hasIsFetchingLeftNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return The isFetchingLeftNum.
     */
    @java.lang.Override
    public boolean getIsFetchingLeftNum() {
      return isFetchingLeftNum_;
    }

    public static final int GIFTID_FIELD_NUMBER = 2;
    private int giftId_;
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return Whether the giftId field is set.
     */
    @java.lang.Override
    public boolean hasGiftId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return The giftId.
     */
    @java.lang.Override
    public int getGiftId() {
      return giftId_;
    }

    public static final int CHECKPLAYERID_FIELD_NUMBER = 3;
    private long checkPlayerId_;
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return Whether the checkPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasCheckPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return The checkPlayerId.
     */
    @java.lang.Override
    public long getCheckPlayerId() {
      return checkPlayerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isFetchingLeftNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, giftId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, checkPlayerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isFetchingLeftNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, giftId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, checkPlayerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S other = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S) obj;

      if (hasIsFetchingLeftNum() != other.hasIsFetchingLeftNum()) return false;
      if (hasIsFetchingLeftNum()) {
        if (getIsFetchingLeftNum()
            != other.getIsFetchingLeftNum()) return false;
      }
      if (hasGiftId() != other.hasGiftId()) return false;
      if (hasGiftId()) {
        if (getGiftId()
            != other.getGiftId()) return false;
      }
      if (hasCheckPlayerId() != other.hasCheckPlayerId()) return false;
      if (hasCheckPlayerId()) {
        if (getCheckPlayerId()
            != other.getCheckPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsFetchingLeftNum()) {
        hash = (37 * hash) + ISFETCHINGLEFTNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsFetchingLeftNum());
      }
      if (hasGiftId()) {
        hash = (37 * hash) + GIFTID_FIELD_NUMBER;
        hash = (53 * hash) + getGiftId();
      }
      if (hasCheckPlayerId()) {
        hash = (37 * hash) + CHECKPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCheckPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchKingdomGiftInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchKingdomGiftInfo_C2S)
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isFetchingLeftNum_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        giftId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        checkPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S result = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isFetchingLeftNum_ = isFetchingLeftNum_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.giftId_ = giftId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.checkPlayerId_ = checkPlayerId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.getDefaultInstance()) return this;
        if (other.hasIsFetchingLeftNum()) {
          setIsFetchingLeftNum(other.getIsFetchingLeftNum());
        }
        if (other.hasGiftId()) {
          setGiftId(other.getGiftId());
        }
        if (other.hasCheckPlayerId()) {
          setCheckPlayerId(other.getCheckPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isFetchingLeftNum_ ;
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return Whether the isFetchingLeftNum field is set.
       */
      @java.lang.Override
      public boolean hasIsFetchingLeftNum() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return The isFetchingLeftNum.
       */
      @java.lang.Override
      public boolean getIsFetchingLeftNum() {
        return isFetchingLeftNum_;
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @param value The isFetchingLeftNum to set.
       * @return This builder for chaining.
       */
      public Builder setIsFetchingLeftNum(boolean value) {
        bitField0_ |= 0x00000001;
        isFetchingLeftNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsFetchingLeftNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isFetchingLeftNum_ = false;
        onChanged();
        return this;
      }

      private int giftId_ ;
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return Whether the giftId field is set.
       */
      @java.lang.Override
      public boolean hasGiftId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return The giftId.
       */
      @java.lang.Override
      public int getGiftId() {
        return giftId_;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @param value The giftId to set.
       * @return This builder for chaining.
       */
      public Builder setGiftId(int value) {
        bitField0_ |= 0x00000002;
        giftId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGiftId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        giftId_ = 0;
        onChanged();
        return this;
      }

      private long checkPlayerId_ ;
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return Whether the checkPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasCheckPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return The checkPlayerId.
       */
      @java.lang.Override
      public long getCheckPlayerId() {
        return checkPlayerId_;
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @param value The checkPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setCheckPlayerId(long value) {
        bitField0_ |= 0x00000004;
        checkPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCheckPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        checkPlayerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchKingdomGiftInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchKingdomGiftInfo_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchKingdomGiftInfo_C2S>() {
      @java.lang.Override
      public Player_FetchKingdomGiftInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchKingdomGiftInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchKingdomGiftInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchKingdomGiftInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    int getLeftNumCount();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    boolean containsLeftNum(
        int key);
    /**
     * Use {@link #getLeftNumMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getLeftNum();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getLeftNumMap();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    int getLeftNumOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    int getLeftNumOrThrow(
        int key);

    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     * @return Whether the giftInfo field is set.
     */
    boolean hasGiftInfo();
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     * @return The giftInfo.
     */
    com.yorha.proto.ZonePB.KingdomGiftInfoPB getGiftInfo();
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     */
    com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder getGiftInfoOrBuilder();

    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return Whether the canGiveGift field is set.
     */
    boolean hasCanGiveGift();
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return The canGiveGift.
     */
    boolean getCanGiveGift();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchKingdomGiftInfo_S2C}
   */
  public static final class Player_FetchKingdomGiftInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchKingdomGiftInfo_S2C)
      Player_FetchKingdomGiftInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchKingdomGiftInfo_S2C.newBuilder() to construct.
    private Player_FetchKingdomGiftInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchKingdomGiftInfo_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchKingdomGiftInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchKingdomGiftInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                leftNum_ = com.google.protobuf.MapField.newMapField(
                    LeftNumDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              leftNum__ = input.readMessage(
                  LeftNumDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              leftNum_.getMutableMap().put(
                  leftNum__.getKey(), leftNum__.getValue());
              break;
            }
            case 18: {
              com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = giftInfo_.toBuilder();
              }
              giftInfo_ = input.readMessage(com.yorha.proto.ZonePB.KingdomGiftInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(giftInfo_);
                giftInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              canGiveGift_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetLeftNum();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int LEFTNUM_FIELD_NUMBER = 1;
    private static final class LeftNumDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> leftNum_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetLeftNum() {
      if (leftNum_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            LeftNumDefaultEntryHolder.defaultEntry);
      }
      return leftNum_;
    }

    public int getLeftNumCount() {
      return internalGetLeftNum().getMap().size();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    @java.lang.Override
    public boolean containsLeftNum(
        int key) {
      
      return internalGetLeftNum().getMap().containsKey(key);
    }
    /**
     * Use {@link #getLeftNumMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNum() {
      return getLeftNumMap();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNumMap() {
      return internalGetLeftNum().getMap();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public int getLeftNumOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetLeftNum().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public int getLeftNumOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetLeftNum().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int GIFTINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.ZonePB.KingdomGiftInfoPB giftInfo_;
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     * @return Whether the giftInfo field is set.
     */
    @java.lang.Override
    public boolean hasGiftInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     * @return The giftInfo.
     */
    @java.lang.Override
    public com.yorha.proto.ZonePB.KingdomGiftInfoPB getGiftInfo() {
      return giftInfo_ == null ? com.yorha.proto.ZonePB.KingdomGiftInfoPB.getDefaultInstance() : giftInfo_;
    }
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder getGiftInfoOrBuilder() {
      return giftInfo_ == null ? com.yorha.proto.ZonePB.KingdomGiftInfoPB.getDefaultInstance() : giftInfo_;
    }

    public static final int CANGIVEGIFT_FIELD_NUMBER = 3;
    private boolean canGiveGift_;
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return Whether the canGiveGift field is set.
     */
    @java.lang.Override
    public boolean hasCanGiveGift() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return The canGiveGift.
     */
    @java.lang.Override
    public boolean getCanGiveGift() {
      return canGiveGift_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetLeftNum(),
          LeftNumDefaultEntryHolder.defaultEntry,
          1);
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getGiftInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, canGiveGift_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetLeftNum().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        leftNum__ = LeftNumDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, leftNum__);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getGiftInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, canGiveGift_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C other = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C) obj;

      if (!internalGetLeftNum().equals(
          other.internalGetLeftNum())) return false;
      if (hasGiftInfo() != other.hasGiftInfo()) return false;
      if (hasGiftInfo()) {
        if (!getGiftInfo()
            .equals(other.getGiftInfo())) return false;
      }
      if (hasCanGiveGift() != other.hasCanGiveGift()) return false;
      if (hasCanGiveGift()) {
        if (getCanGiveGift()
            != other.getCanGiveGift()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetLeftNum().getMap().isEmpty()) {
        hash = (37 * hash) + LEFTNUM_FIELD_NUMBER;
        hash = (53 * hash) + internalGetLeftNum().hashCode();
      }
      if (hasGiftInfo()) {
        hash = (37 * hash) + GIFTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getGiftInfo().hashCode();
      }
      if (hasCanGiveGift()) {
        hash = (37 * hash) + CANGIVEGIFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCanGiveGift());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchKingdomGiftInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchKingdomGiftInfo_S2C)
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetLeftNum();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableLeftNum();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getGiftInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableLeftNum().clear();
        if (giftInfoBuilder_ == null) {
          giftInfo_ = null;
        } else {
          giftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        canGiveGift_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C result = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.leftNum_ = internalGetLeftNum();
        result.leftNum_.makeImmutable();
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (giftInfoBuilder_ == null) {
            result.giftInfo_ = giftInfo_;
          } else {
            result.giftInfo_ = giftInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.canGiveGift_ = canGiveGift_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.getDefaultInstance()) return this;
        internalGetMutableLeftNum().mergeFrom(
            other.internalGetLeftNum());
        if (other.hasGiftInfo()) {
          mergeGiftInfo(other.getGiftInfo());
        }
        if (other.hasCanGiveGift()) {
          setCanGiveGift(other.getCanGiveGift());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> leftNum_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetLeftNum() {
        if (leftNum_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              LeftNumDefaultEntryHolder.defaultEntry);
        }
        return leftNum_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableLeftNum() {
        onChanged();;
        if (leftNum_ == null) {
          leftNum_ = com.google.protobuf.MapField.newMapField(
              LeftNumDefaultEntryHolder.defaultEntry);
        }
        if (!leftNum_.isMutable()) {
          leftNum_ = leftNum_.copy();
        }
        return leftNum_;
      }

      public int getLeftNumCount() {
        return internalGetLeftNum().getMap().size();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      @java.lang.Override
      public boolean containsLeftNum(
          int key) {
        
        return internalGetLeftNum().getMap().containsKey(key);
      }
      /**
       * Use {@link #getLeftNumMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNum() {
        return getLeftNumMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNumMap() {
        return internalGetLeftNum().getMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public int getLeftNumOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetLeftNum().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public int getLeftNumOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetLeftNum().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearLeftNum() {
        internalGetMutableLeftNum().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      public Builder removeLeftNum(
          int key) {
        
        internalGetMutableLeftNum().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableLeftNum() {
        return internalGetMutableLeftNum().getMutableMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      public Builder putLeftNum(
          int key,
          int value) {
        
        
        internalGetMutableLeftNum().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      public Builder putAllLeftNum(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableLeftNum().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.yorha.proto.ZonePB.KingdomGiftInfoPB giftInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ZonePB.KingdomGiftInfoPB, com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder, com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder> giftInfoBuilder_;
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       * @return Whether the giftInfo field is set.
       */
      public boolean hasGiftInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       * @return The giftInfo.
       */
      public com.yorha.proto.ZonePB.KingdomGiftInfoPB getGiftInfo() {
        if (giftInfoBuilder_ == null) {
          return giftInfo_ == null ? com.yorha.proto.ZonePB.KingdomGiftInfoPB.getDefaultInstance() : giftInfo_;
        } else {
          return giftInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public Builder setGiftInfo(com.yorha.proto.ZonePB.KingdomGiftInfoPB value) {
        if (giftInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          giftInfo_ = value;
          onChanged();
        } else {
          giftInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public Builder setGiftInfo(
          com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder builderForValue) {
        if (giftInfoBuilder_ == null) {
          giftInfo_ = builderForValue.build();
          onChanged();
        } else {
          giftInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public Builder mergeGiftInfo(com.yorha.proto.ZonePB.KingdomGiftInfoPB value) {
        if (giftInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              giftInfo_ != null &&
              giftInfo_ != com.yorha.proto.ZonePB.KingdomGiftInfoPB.getDefaultInstance()) {
            giftInfo_ =
              com.yorha.proto.ZonePB.KingdomGiftInfoPB.newBuilder(giftInfo_).mergeFrom(value).buildPartial();
          } else {
            giftInfo_ = value;
          }
          onChanged();
        } else {
          giftInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public Builder clearGiftInfo() {
        if (giftInfoBuilder_ == null) {
          giftInfo_ = null;
          onChanged();
        } else {
          giftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder getGiftInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getGiftInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      public com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder getGiftInfoOrBuilder() {
        if (giftInfoBuilder_ != null) {
          return giftInfoBuilder_.getMessageOrBuilder();
        } else {
          return giftInfo_ == null ?
              com.yorha.proto.ZonePB.KingdomGiftInfoPB.getDefaultInstance() : giftInfo_;
        }
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfoPB giftInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.ZonePB.KingdomGiftInfoPB, com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder, com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder> 
          getGiftInfoFieldBuilder() {
        if (giftInfoBuilder_ == null) {
          giftInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.ZonePB.KingdomGiftInfoPB, com.yorha.proto.ZonePB.KingdomGiftInfoPB.Builder, com.yorha.proto.ZonePB.KingdomGiftInfoPBOrBuilder>(
                  getGiftInfo(),
                  getParentForChildren(),
                  isClean());
          giftInfo_ = null;
        }
        return giftInfoBuilder_;
      }

      private boolean canGiveGift_ ;
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return Whether the canGiveGift field is set.
       */
      @java.lang.Override
      public boolean hasCanGiveGift() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return The canGiveGift.
       */
      @java.lang.Override
      public boolean getCanGiveGift() {
        return canGiveGift_;
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @param value The canGiveGift to set.
       * @return This builder for chaining.
       */
      public Builder setCanGiveGift(boolean value) {
        bitField0_ |= 0x00000004;
        canGiveGift_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanGiveGift() {
        bitField0_ = (bitField0_ & ~0x00000004);
        canGiveGift_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchKingdomGiftInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchKingdomGiftInfo_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchKingdomGiftInfo_S2C>() {
      @java.lang.Override
      public Player_FetchKingdomGiftInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchKingdomGiftInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchKingdomGiftInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchKingdomGiftInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchKingdomOfficeInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S}
   */
  public static final class Player_FetchKingdomOfficeInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S)
      Player_FetchKingdomOfficeInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchKingdomOfficeInfo_C2S.newBuilder() to construct.
    private Player_FetchKingdomOfficeInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchKingdomOfficeInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchKingdomOfficeInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchKingdomOfficeInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S other = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S)
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S build() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S result = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchKingdomOfficeInfo_C2S)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchKingdomOfficeInfo_C2S>() {
      @java.lang.Override
      public Player_FetchKingdomOfficeInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchKingdomOfficeInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_FetchKingdomOfficeInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    int getOfficeInfoCount();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    boolean containsOfficeInfo(
        int key);
    /**
     * Use {@link #getOfficeInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    getOfficeInfo();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    getOfficeInfoMap();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
        int key,
        com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue);
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C}
   */
  public static final class Player_FetchKingdomOfficeInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C)
      Player_FetchKingdomOfficeInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_FetchKingdomOfficeInfo_S2C.newBuilder() to construct.
    private Player_FetchKingdomOfficeInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_FetchKingdomOfficeInfo_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_FetchKingdomOfficeInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_FetchKingdomOfficeInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                officeInfo_ = com.google.protobuf.MapField.newMapField(
                    OfficeInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
              officeInfo__ = input.readMessage(
                  OfficeInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              officeInfo_.getMutableMap().put(
                  officeInfo__.getKey(), officeInfo__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetOfficeInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.Builder.class);
    }

    public static final int OFFICEINFO_FIELD_NUMBER = 1;
    private static final class OfficeInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>newDefaultInstance(
                  com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.ZonePB.KingdomOfficeInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> officeInfo_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    internalGetOfficeInfo() {
      if (officeInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            OfficeInfoDefaultEntryHolder.defaultEntry);
      }
      return officeInfo_;
    }

    public int getOfficeInfoCount() {
      return internalGetOfficeInfo().getMap().size();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    @java.lang.Override
    public boolean containsOfficeInfo(
        int key) {
      
      return internalGetOfficeInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getOfficeInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfo() {
      return getOfficeInfoMap();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfoMap() {
      return internalGetOfficeInfo().getMap();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
        int key,
        com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
          internalGetOfficeInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
          internalGetOfficeInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetOfficeInfo(),
          OfficeInfoDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> entry
           : internalGetOfficeInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
        officeInfo__ = OfficeInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, officeInfo__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C other = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C) obj;

      if (!internalGetOfficeInfo().equals(
          other.internalGetOfficeInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetOfficeInfo().getMap().isEmpty()) {
        hash = (37 * hash) + OFFICEINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetOfficeInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C)
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetOfficeInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableOfficeInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.class, com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableOfficeInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerKingdom.internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C build() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C buildPartial() {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C result = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        result.officeInfo_ = internalGetOfficeInfo();
        result.officeInfo_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C other) {
        if (other == com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.getDefaultInstance()) return this;
        internalGetMutableOfficeInfo().mergeFrom(
            other.internalGetOfficeInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> officeInfo_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      internalGetOfficeInfo() {
        if (officeInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              OfficeInfoDefaultEntryHolder.defaultEntry);
        }
        return officeInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      internalGetMutableOfficeInfo() {
        onChanged();;
        if (officeInfo_ == null) {
          officeInfo_ = com.google.protobuf.MapField.newMapField(
              OfficeInfoDefaultEntryHolder.defaultEntry);
        }
        if (!officeInfo_.isMutable()) {
          officeInfo_ = officeInfo_.copy();
        }
        return officeInfo_;
      }

      public int getOfficeInfoCount() {
        return internalGetOfficeInfo().getMap().size();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      @java.lang.Override
      public boolean containsOfficeInfo(
          int key) {
        
        return internalGetOfficeInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getOfficeInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfo() {
        return getOfficeInfoMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfoMap() {
        return internalGetOfficeInfo().getMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
          int key,
          com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
            internalGetOfficeInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
            internalGetOfficeInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearOfficeInfo() {
        internalGetMutableOfficeInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      public Builder removeOfficeInfo(
          int key) {
        
        internalGetMutableOfficeInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      getMutableOfficeInfo() {
        return internalGetMutableOfficeInfo().getMutableMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      public Builder putOfficeInfo(
          int key,
          com.yorha.proto.ZonePB.KingdomOfficeInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableOfficeInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      public Builder putAllOfficeInfo(
          java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> values) {
        internalGetMutableOfficeInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_FetchKingdomOfficeInfo_S2C)
    private static final com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C();
    }

    public static com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_FetchKingdomOfficeInfo_S2C>() {
      @java.lang.Override
      public Player_FetchKingdomOfficeInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_FetchKingdomOfficeInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_FetchKingdomOfficeInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingAppoint_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingAppoint_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingSendGift_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingSendGift_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingUseSkill_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_KingUseSkill_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/cs/player_kingdom." +
      "proto\022\017com.yorha.proto\032\036cs_proto/gen/zon" +
      "e/zonePB.proto\032$ss_proto/gen/common/stru" +
      "ct_msg.proto\"E\n\026Player_KingAppoint_C2S\022\027" +
      "\n\017kingdomOfficeId\030\001 \001(\005\022\022\n\ntoPlayerId\030\002 " +
      "\001(\003\"\030\n\026Player_KingAppoint_S2C\"0\n\027Player_" +
      "KingOpenBuff_C2S\022\025\n\rkingdomBuffId\030\001 \001(\005\"" +
      "\031\n\027Player_KingOpenBuff_S2C\"D\n\027Player_Kin" +
      "gSendGift_C2S\022\025\n\rkingdomGiftId\030\001 \001(\005\022\022\n\n" +
      "toPlayerId\030\002 \001(\003\"\031\n\027Player_KingSendGift_" +
      "S2C\"U\n\027Player_KingUseSkill_C2S\022\026\n\016kingdo" +
      "mSkillId\030\001 \001(\005\022\022\n\ntoTargetId\030\002 \001(\003\022\016\n\006zo" +
      "neId\030\003 \001(\005\"\031\n\027Player_KingUseSkill_S2C\",\n" +
      "\034Player_FetchHistoryKings_C2S\022\014\n\004page\030\001 " +
      "\001(\005\"\251\001\n\034Player_FetchHistoryKings_S2C\022\014\n\004" +
      "page\030\001 \001(\005\022\021\n\ttotalPage\030\002 \001(\005\0222\n\tkingInf" +
      "os\030\003 \003(\0132\037.com.yorha.proto.SimpleKingInf" +
      "o\0224\n\013curKingInfo\030\004 \001(\0132\037.com.yorha.proto" +
      ".SimpleKingInfo\"c\n\037Player_FetchKingdomGi" +
      "ftInfo_C2S\022\031\n\021isFetchingLeftNum\030\001 \001(\010\022\016\n" +
      "\006giftId\030\002 \001(\005\022\025\n\rcheckPlayerId\030\003 \001(\003\"\354\001\n" +
      "\037Player_FetchKingdomGiftInfo_S2C\022N\n\007left" +
      "Num\030\001 \003(\0132=.com.yorha.proto.Player_Fetch" +
      "KingdomGiftInfo_S2C.LeftNumEntry\0224\n\010gift" +
      "Info\030\002 \001(\0132\".com.yorha.proto.KingdomGift" +
      "InfoPB\022\023\n\013canGiveGift\030\003 \001(\010\032.\n\014LeftNumEn" +
      "try\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\"#\n!P" +
      "layer_FetchKingdomOfficeInfo_C2S\"\324\001\n!Pla" +
      "yer_FetchKingdomOfficeInfo_S2C\022V\n\noffice" +
      "Info\030\001 \003(\0132B.com.yorha.proto.Player_Fetc" +
      "hKingdomOfficeInfo_S2C.OfficeInfoEntry\032W" +
      "\n\017OfficeInfoEntry\022\013\n\003key\030\001 \001(\005\0223\n\005value\030" +
      "\002 \001(\0132$.com.yorha.proto.KingdomOfficeInf" +
      "oPB:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.ZonePB.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_KingAppoint_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingAppoint_C2S_descriptor,
        new java.lang.String[] { "KingdomOfficeId", "ToPlayerId", });
    internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_KingAppoint_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingAppoint_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingOpenBuff_C2S_descriptor,
        new java.lang.String[] { "KingdomBuffId", });
    internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingOpenBuff_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_KingSendGift_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingSendGift_C2S_descriptor,
        new java.lang.String[] { "KingdomGiftId", "ToPlayerId", });
    internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_KingSendGift_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingSendGift_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_KingUseSkill_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingUseSkill_C2S_descriptor,
        new java.lang.String[] { "KingdomSkillId", "ToTargetId", "ZoneId", });
    internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_KingUseSkill_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_KingUseSkill_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchHistoryKings_C2S_descriptor,
        new java.lang.String[] { "Page", });
    internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchHistoryKings_S2C_descriptor,
        new java.lang.String[] { "Page", "TotalPage", "KingInfos", "CurKingInfo", });
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_C2S_descriptor,
        new java.lang.String[] { "IsFetchingLeftNum", "GiftId", "CheckPlayerId", });
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor,
        new java.lang.String[] { "LeftNum", "GiftInfo", "CanGiveGift", });
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_descriptor =
      internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomGiftInfo_S2C_LeftNumEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor,
        new java.lang.String[] { "OfficeInfo", });
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_descriptor =
      internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_FetchKingdomOfficeInfo_S2C_OfficeInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.ZonePB.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/dropObject/drop_objectPB.proto

package com.yorha.proto;

public final class DropObjectPB {
  private DropObjectPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DropObjectEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DropObjectEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return Whether the pickUpTimes field is set.
     */
    boolean hasPickUpTimes();
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return The pickUpTimes.
     */
    int getPickUpTimes();

    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return Whether the bornTime field is set.
     */
    boolean hasBornTime();
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return The bornTime.
     */
    long getBornTime();

    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     * @return Whether the pickUpArmy field is set.
     */
    boolean hasPickUpArmy();
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     * @return The pickUpArmy.
     */
    com.yorha.proto.BasicPB.Int64ListPB getPickUpArmy();
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     */
    com.yorha.proto.BasicPB.Int64ListPBOrBuilder getPickUpArmyOrBuilder();

    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     * @return Whether the itemReward field is set.
     */
    boolean hasItemReward();
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     * @return The itemReward.
     */
    com.yorha.proto.StructPB.ItemListPB getItemReward();
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     */
    com.yorha.proto.StructPB.ItemListPBOrBuilder getItemRewardOrBuilder();

    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     * @return Whether the groupLimit field is set.
     */
    boolean hasGroupLimit();
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     * @return The groupLimit.
     */
    com.yorha.proto.StructPB.DropGroupLimitPB getGroupLimit();
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     */
    com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder getGroupLimitOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DropObjectEntityPB}
   */
  public static final class DropObjectEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DropObjectEntityPB)
      DropObjectEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DropObjectEntityPB.newBuilder() to construct.
    private DropObjectEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DropObjectEntityPB() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DropObjectEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DropObjectEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              templateId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              pickUpTimes_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000008;
              bornTime_ = input.readInt64();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000010;
              ownerId_ = input.readInt64();
              break;
            }
            case 58: {
              com.yorha.proto.BasicPB.Int64ListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = pickUpArmy_.toBuilder();
              }
              pickUpArmy_ = input.readMessage(com.yorha.proto.BasicPB.Int64ListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(pickUpArmy_);
                pickUpArmy_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 66: {
              com.yorha.proto.StructPB.ItemListPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = itemReward_.toBuilder();
              }
              itemReward_ = input.readMessage(com.yorha.proto.StructPB.ItemListPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(itemReward_);
                itemReward_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 74: {
              com.yorha.proto.StructPB.DropGroupLimitPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = groupLimit_.toBuilder();
              }
              groupLimit_ = input.readMessage(com.yorha.proto.StructPB.DropGroupLimitPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(groupLimit_);
                groupLimit_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.DropObjectPB.internal_static_com_yorha_proto_DropObjectEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.DropObjectPB.internal_static_com_yorha_proto_DropObjectEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.DropObjectPB.DropObjectEntityPB.class, com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int TEMPLATEID_FIELD_NUMBER = 1;
    private int templateId_;
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 配置表id
     * </pre>
     *
     * <code>optional int32 templateId = 1;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int POINT_FIELD_NUMBER = 2;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int PICKUPTIMES_FIELD_NUMBER = 4;
    private int pickUpTimes_;
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return Whether the pickUpTimes field is set.
     */
    @java.lang.Override
    public boolean hasPickUpTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 已采集的次数
     * </pre>
     *
     * <code>optional int32 pickUpTimes = 4;</code>
     * @return The pickUpTimes.
     */
    @java.lang.Override
    public int getPickUpTimes() {
      return pickUpTimes_;
    }

    public static final int BORNTIME_FIELD_NUMBER = 5;
    private long bornTime_;
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return Whether the bornTime field is set.
     */
    @java.lang.Override
    public boolean hasBornTime() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 出生时间
     * </pre>
     *
     * <code>optional int64 bornTime = 5;</code>
     * @return The bornTime.
     */
    @java.lang.Override
    public long getBornTime() {
      return bornTime_;
    }

    public static final int OWNERID_FIELD_NUMBER = 6;
    private long ownerId_;
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 私有玩家id
     * </pre>
     *
     * <code>optional int64 ownerId = 6;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int PICKUPARMY_FIELD_NUMBER = 7;
    private com.yorha.proto.BasicPB.Int64ListPB pickUpArmy_;
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     * @return Whether the pickUpArmy field is set.
     */
    @java.lang.Override
    public boolean hasPickUpArmy() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     * @return The pickUpArmy.
     */
    @java.lang.Override
    public com.yorha.proto.BasicPB.Int64ListPB getPickUpArmy() {
      return pickUpArmy_ == null ? com.yorha.proto.BasicPB.Int64ListPB.getDefaultInstance() : pickUpArmy_;
    }
    /**
     * <pre>
     * 正在拾取的行军部队
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.BasicPB.Int64ListPBOrBuilder getPickUpArmyOrBuilder() {
      return pickUpArmy_ == null ? com.yorha.proto.BasicPB.Int64ListPB.getDefaultInstance() : pickUpArmy_;
    }

    public static final int ITEMREWARD_FIELD_NUMBER = 8;
    private com.yorha.proto.StructPB.ItemListPB itemReward_;
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     * @return Whether the itemReward field is set.
     */
    @java.lang.Override
    public boolean hasItemReward() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     * @return The itemReward.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPB getItemReward() {
      return itemReward_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemReward_;
    }
    /**
     * <pre>
     * 道具奖励
     * </pre>
     *
     * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemRewardOrBuilder() {
      return itemReward_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemReward_;
    }

    public static final int GROUPLIMIT_FIELD_NUMBER = 9;
    private com.yorha.proto.StructPB.DropGroupLimitPB groupLimit_;
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     * @return Whether the groupLimit field is set.
     */
    @java.lang.Override
    public boolean hasGroupLimit() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     * @return The groupLimit.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.DropGroupLimitPB getGroupLimit() {
      return groupLimit_ == null ? com.yorha.proto.StructPB.DropGroupLimitPB.getDefaultInstance() : groupLimit_;
    }
    /**
     * <pre>
     * 拾取限制
     * </pre>
     *
     * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder getGroupLimitOrBuilder() {
      return groupLimit_ == null ? com.yorha.proto.StructPB.DropGroupLimitPB.getDefaultInstance() : groupLimit_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, pickUpTimes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(5, bornTime_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(6, ownerId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(7, getPickUpArmy());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(8, getItemReward());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(9, getGroupLimit());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, templateId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getPoint());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, pickUpTimes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, bornTime_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(6, ownerId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getPickUpArmy());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getItemReward());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getGroupLimit());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.DropObjectPB.DropObjectEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.DropObjectPB.DropObjectEntityPB other = (com.yorha.proto.DropObjectPB.DropObjectEntityPB) obj;

      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasPickUpTimes() != other.hasPickUpTimes()) return false;
      if (hasPickUpTimes()) {
        if (getPickUpTimes()
            != other.getPickUpTimes()) return false;
      }
      if (hasBornTime() != other.hasBornTime()) return false;
      if (hasBornTime()) {
        if (getBornTime()
            != other.getBornTime()) return false;
      }
      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (hasPickUpArmy() != other.hasPickUpArmy()) return false;
      if (hasPickUpArmy()) {
        if (!getPickUpArmy()
            .equals(other.getPickUpArmy())) return false;
      }
      if (hasItemReward() != other.hasItemReward()) return false;
      if (hasItemReward()) {
        if (!getItemReward()
            .equals(other.getItemReward())) return false;
      }
      if (hasGroupLimit() != other.hasGroupLimit()) return false;
      if (hasGroupLimit()) {
        if (!getGroupLimit()
            .equals(other.getGroupLimit())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasPickUpTimes()) {
        hash = (37 * hash) + PICKUPTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpTimes();
      }
      if (hasBornTime()) {
        hash = (37 * hash) + BORNTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBornTime());
      }
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (hasPickUpArmy()) {
        hash = (37 * hash) + PICKUPARMY_FIELD_NUMBER;
        hash = (53 * hash) + getPickUpArmy().hashCode();
      }
      if (hasItemReward()) {
        hash = (37 * hash) + ITEMREWARD_FIELD_NUMBER;
        hash = (53 * hash) + getItemReward().hashCode();
      }
      if (hasGroupLimit()) {
        hash = (37 * hash) + GROUPLIMIT_FIELD_NUMBER;
        hash = (53 * hash) + getGroupLimit().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.DropObjectPB.DropObjectEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DropObjectEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DropObjectEntityPB)
        com.yorha.proto.DropObjectPB.DropObjectEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.DropObjectPB.internal_static_com_yorha_proto_DropObjectEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.DropObjectPB.internal_static_com_yorha_proto_DropObjectEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.DropObjectPB.DropObjectEntityPB.class, com.yorha.proto.DropObjectPB.DropObjectEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.DropObjectPB.DropObjectEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getPickUpArmyFieldBuilder();
          getItemRewardFieldBuilder();
          getGroupLimitFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        pickUpTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        bornTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = null;
        } else {
          pickUpArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (itemRewardBuilder_ == null) {
          itemReward_ = null;
        } else {
          itemRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        if (groupLimitBuilder_ == null) {
          groupLimit_ = null;
        } else {
          groupLimitBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.DropObjectPB.internal_static_com_yorha_proto_DropObjectEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.DropObjectPB.DropObjectEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.DropObjectPB.DropObjectEntityPB build() {
        com.yorha.proto.DropObjectPB.DropObjectEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.DropObjectPB.DropObjectEntityPB buildPartial() {
        com.yorha.proto.DropObjectPB.DropObjectEntityPB result = new com.yorha.proto.DropObjectPB.DropObjectEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.pickUpTimes_ = pickUpTimes_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.bornTime_ = bornTime_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (pickUpArmyBuilder_ == null) {
            result.pickUpArmy_ = pickUpArmy_;
          } else {
            result.pickUpArmy_ = pickUpArmyBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (itemRewardBuilder_ == null) {
            result.itemReward_ = itemReward_;
          } else {
            result.itemReward_ = itemRewardBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (groupLimitBuilder_ == null) {
            result.groupLimit_ = groupLimit_;
          } else {
            result.groupLimit_ = groupLimitBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.DropObjectPB.DropObjectEntityPB) {
          return mergeFrom((com.yorha.proto.DropObjectPB.DropObjectEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.DropObjectPB.DropObjectEntityPB other) {
        if (other == com.yorha.proto.DropObjectPB.DropObjectEntityPB.getDefaultInstance()) return this;
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasPickUpTimes()) {
          setPickUpTimes(other.getPickUpTimes());
        }
        if (other.hasBornTime()) {
          setBornTime(other.getBornTime());
        }
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (other.hasPickUpArmy()) {
          mergePickUpArmy(other.getPickUpArmy());
        }
        if (other.hasItemReward()) {
          mergeItemReward(other.getItemReward());
        }
        if (other.hasGroupLimit()) {
          mergeGroupLimit(other.getGroupLimit());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.DropObjectPB.DropObjectEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.DropObjectPB.DropObjectEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int templateId_ ;
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000001;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 配置表id
       * </pre>
       *
       * <code>optional int32 templateId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int pickUpTimes_ ;
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return Whether the pickUpTimes field is set.
       */
      @java.lang.Override
      public boolean hasPickUpTimes() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return The pickUpTimes.
       */
      @java.lang.Override
      public int getPickUpTimes() {
        return pickUpTimes_;
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @param value The pickUpTimes to set.
       * @return This builder for chaining.
       */
      public Builder setPickUpTimes(int value) {
        bitField0_ |= 0x00000004;
        pickUpTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已采集的次数
       * </pre>
       *
       * <code>optional int32 pickUpTimes = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPickUpTimes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        pickUpTimes_ = 0;
        onChanged();
        return this;
      }

      private long bornTime_ ;
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return Whether the bornTime field is set.
       */
      @java.lang.Override
      public boolean hasBornTime() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return The bornTime.
       */
      @java.lang.Override
      public long getBornTime() {
        return bornTime_;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @param value The bornTime to set.
       * @return This builder for chaining.
       */
      public Builder setBornTime(long value) {
        bitField0_ |= 0x00000008;
        bornTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 出生时间
       * </pre>
       *
       * <code>optional int64 bornTime = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearBornTime() {
        bitField0_ = (bitField0_ & ~0x00000008);
        bornTime_ = 0L;
        onChanged();
        return this;
      }

      private long ownerId_ ;
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000010;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 私有玩家id
       * </pre>
       *
       * <code>optional int64 ownerId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.BasicPB.Int64ListPB pickUpArmy_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.BasicPB.Int64ListPB, com.yorha.proto.BasicPB.Int64ListPB.Builder, com.yorha.proto.BasicPB.Int64ListPBOrBuilder> pickUpArmyBuilder_;
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       * @return Whether the pickUpArmy field is set.
       */
      public boolean hasPickUpArmy() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       * @return The pickUpArmy.
       */
      public com.yorha.proto.BasicPB.Int64ListPB getPickUpArmy() {
        if (pickUpArmyBuilder_ == null) {
          return pickUpArmy_ == null ? com.yorha.proto.BasicPB.Int64ListPB.getDefaultInstance() : pickUpArmy_;
        } else {
          return pickUpArmyBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public Builder setPickUpArmy(com.yorha.proto.BasicPB.Int64ListPB value) {
        if (pickUpArmyBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          pickUpArmy_ = value;
          onChanged();
        } else {
          pickUpArmyBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public Builder setPickUpArmy(
          com.yorha.proto.BasicPB.Int64ListPB.Builder builderForValue) {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = builderForValue.build();
          onChanged();
        } else {
          pickUpArmyBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public Builder mergePickUpArmy(com.yorha.proto.BasicPB.Int64ListPB value) {
        if (pickUpArmyBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              pickUpArmy_ != null &&
              pickUpArmy_ != com.yorha.proto.BasicPB.Int64ListPB.getDefaultInstance()) {
            pickUpArmy_ =
              com.yorha.proto.BasicPB.Int64ListPB.newBuilder(pickUpArmy_).mergeFrom(value).buildPartial();
          } else {
            pickUpArmy_ = value;
          }
          onChanged();
        } else {
          pickUpArmyBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public Builder clearPickUpArmy() {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmy_ = null;
          onChanged();
        } else {
          pickUpArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public com.yorha.proto.BasicPB.Int64ListPB.Builder getPickUpArmyBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getPickUpArmyFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      public com.yorha.proto.BasicPB.Int64ListPBOrBuilder getPickUpArmyOrBuilder() {
        if (pickUpArmyBuilder_ != null) {
          return pickUpArmyBuilder_.getMessageOrBuilder();
        } else {
          return pickUpArmy_ == null ?
              com.yorha.proto.BasicPB.Int64ListPB.getDefaultInstance() : pickUpArmy_;
        }
      }
      /**
       * <pre>
       * 正在拾取的行军部队
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ListPB pickUpArmy = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.BasicPB.Int64ListPB, com.yorha.proto.BasicPB.Int64ListPB.Builder, com.yorha.proto.BasicPB.Int64ListPBOrBuilder> 
          getPickUpArmyFieldBuilder() {
        if (pickUpArmyBuilder_ == null) {
          pickUpArmyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.BasicPB.Int64ListPB, com.yorha.proto.BasicPB.Int64ListPB.Builder, com.yorha.proto.BasicPB.Int64ListPBOrBuilder>(
                  getPickUpArmy(),
                  getParentForChildren(),
                  isClean());
          pickUpArmy_ = null;
        }
        return pickUpArmyBuilder_;
      }

      private com.yorha.proto.StructPB.ItemListPB itemReward_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> itemRewardBuilder_;
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       * @return Whether the itemReward field is set.
       */
      public boolean hasItemReward() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       * @return The itemReward.
       */
      public com.yorha.proto.StructPB.ItemListPB getItemReward() {
        if (itemRewardBuilder_ == null) {
          return itemReward_ == null ? com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemReward_;
        } else {
          return itemRewardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public Builder setItemReward(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemRewardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          itemReward_ = value;
          onChanged();
        } else {
          itemRewardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public Builder setItemReward(
          com.yorha.proto.StructPB.ItemListPB.Builder builderForValue) {
        if (itemRewardBuilder_ == null) {
          itemReward_ = builderForValue.build();
          onChanged();
        } else {
          itemRewardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public Builder mergeItemReward(com.yorha.proto.StructPB.ItemListPB value) {
        if (itemRewardBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              itemReward_ != null &&
              itemReward_ != com.yorha.proto.StructPB.ItemListPB.getDefaultInstance()) {
            itemReward_ =
              com.yorha.proto.StructPB.ItemListPB.newBuilder(itemReward_).mergeFrom(value).buildPartial();
          } else {
            itemReward_ = value;
          }
          onChanged();
        } else {
          itemRewardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public Builder clearItemReward() {
        if (itemRewardBuilder_ == null) {
          itemReward_ = null;
          onChanged();
        } else {
          itemRewardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public com.yorha.proto.StructPB.ItemListPB.Builder getItemRewardBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getItemRewardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      public com.yorha.proto.StructPB.ItemListPBOrBuilder getItemRewardOrBuilder() {
        if (itemRewardBuilder_ != null) {
          return itemRewardBuilder_.getMessageOrBuilder();
        } else {
          return itemReward_ == null ?
              com.yorha.proto.StructPB.ItemListPB.getDefaultInstance() : itemReward_;
        }
      }
      /**
       * <pre>
       * 道具奖励
       * </pre>
       *
       * <code>optional .com.yorha.proto.ItemListPB itemReward = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder> 
          getItemRewardFieldBuilder() {
        if (itemRewardBuilder_ == null) {
          itemRewardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.ItemListPB, com.yorha.proto.StructPB.ItemListPB.Builder, com.yorha.proto.StructPB.ItemListPBOrBuilder>(
                  getItemReward(),
                  getParentForChildren(),
                  isClean());
          itemReward_ = null;
        }
        return itemRewardBuilder_;
      }

      private com.yorha.proto.StructPB.DropGroupLimitPB groupLimit_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.DropGroupLimitPB, com.yorha.proto.StructPB.DropGroupLimitPB.Builder, com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder> groupLimitBuilder_;
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       * @return Whether the groupLimit field is set.
       */
      public boolean hasGroupLimit() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       * @return The groupLimit.
       */
      public com.yorha.proto.StructPB.DropGroupLimitPB getGroupLimit() {
        if (groupLimitBuilder_ == null) {
          return groupLimit_ == null ? com.yorha.proto.StructPB.DropGroupLimitPB.getDefaultInstance() : groupLimit_;
        } else {
          return groupLimitBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public Builder setGroupLimit(com.yorha.proto.StructPB.DropGroupLimitPB value) {
        if (groupLimitBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          groupLimit_ = value;
          onChanged();
        } else {
          groupLimitBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public Builder setGroupLimit(
          com.yorha.proto.StructPB.DropGroupLimitPB.Builder builderForValue) {
        if (groupLimitBuilder_ == null) {
          groupLimit_ = builderForValue.build();
          onChanged();
        } else {
          groupLimitBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public Builder mergeGroupLimit(com.yorha.proto.StructPB.DropGroupLimitPB value) {
        if (groupLimitBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              groupLimit_ != null &&
              groupLimit_ != com.yorha.proto.StructPB.DropGroupLimitPB.getDefaultInstance()) {
            groupLimit_ =
              com.yorha.proto.StructPB.DropGroupLimitPB.newBuilder(groupLimit_).mergeFrom(value).buildPartial();
          } else {
            groupLimit_ = value;
          }
          onChanged();
        } else {
          groupLimitBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public Builder clearGroupLimit() {
        if (groupLimitBuilder_ == null) {
          groupLimit_ = null;
          onChanged();
        } else {
          groupLimitBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public com.yorha.proto.StructPB.DropGroupLimitPB.Builder getGroupLimitBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getGroupLimitFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      public com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder getGroupLimitOrBuilder() {
        if (groupLimitBuilder_ != null) {
          return groupLimitBuilder_.getMessageOrBuilder();
        } else {
          return groupLimit_ == null ?
              com.yorha.proto.StructPB.DropGroupLimitPB.getDefaultInstance() : groupLimit_;
        }
      }
      /**
       * <pre>
       * 拾取限制
       * </pre>
       *
       * <code>optional .com.yorha.proto.DropGroupLimitPB groupLimit = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.DropGroupLimitPB, com.yorha.proto.StructPB.DropGroupLimitPB.Builder, com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder> 
          getGroupLimitFieldBuilder() {
        if (groupLimitBuilder_ == null) {
          groupLimitBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.DropGroupLimitPB, com.yorha.proto.StructPB.DropGroupLimitPB.Builder, com.yorha.proto.StructPB.DropGroupLimitPBOrBuilder>(
                  getGroupLimit(),
                  getParentForChildren(),
                  isClean());
          groupLimit_ = null;
        }
        return groupLimitBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DropObjectEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DropObjectEntityPB)
    private static final com.yorha.proto.DropObjectPB.DropObjectEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.DropObjectPB.DropObjectEntityPB();
    }

    public static com.yorha.proto.DropObjectPB.DropObjectEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DropObjectEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<DropObjectEntityPB>() {
      @java.lang.Override
      public DropObjectEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DropObjectEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DropObjectEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DropObjectEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.DropObjectPB.DropObjectEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DropObjectEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DropObjectEntityPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+cs_proto/gen/dropObject/drop_objectPB." +
      "proto\022\017com.yorha.proto\032\036cs_proto/gen/cnc" +
      "/basicPB.proto\032\"cs_proto/gen/common/stru" +
      "ctPB.proto\"\243\002\n\022DropObjectEntityPB\022\022\n\ntem" +
      "plateId\030\001 \001(\005\022\'\n\005point\030\002 \001(\0132\030.com.yorha" +
      ".proto.PointPB\022\023\n\013pickUpTimes\030\004 \001(\005\022\020\n\010b" +
      "ornTime\030\005 \001(\003\022\017\n\007ownerId\030\006 \001(\003\0220\n\npickUp" +
      "Army\030\007 \001(\0132\034.com.yorha.proto.Int64ListPB" +
      "\022/\n\nitemReward\030\010 \001(\0132\033.com.yorha.proto.I" +
      "temListPB\0225\n\ngroupLimit\030\t \001(\0132!.com.yorh" +
      "a.proto.DropGroupLimitPBB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.BasicPB.getDescriptor(),
          com.yorha.proto.StructPB.getDescriptor(),
        });
    internal_static_com_yorha_proto_DropObjectEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_DropObjectEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DropObjectEntityPB_descriptor,
        new java.lang.String[] { "TemplateId", "Point", "PickUpTimes", "BornTime", "OwnerId", "PickUpArmy", "ItemReward", "GroupLimit", });
    com.yorha.proto.BasicPB.getDescriptor();
    com.yorha.proto.StructPB.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

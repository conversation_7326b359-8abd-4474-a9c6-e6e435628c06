// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_tech.proto

package com.yorha.proto;

public final class SsClanTech {
  private SsClanTech() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ClanTechFetchInfoAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechFetchInfoAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechFetchInfoAsk}
   */
  public static final class ClanTechFetchInfoAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechFetchInfoAsk)
      ClanTechFetchInfoAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechFetchInfoAsk.newBuilder() to construct.
    private ClanTechFetchInfoAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechFetchInfoAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechFetchInfoAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechFetchInfoAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.class, com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk other = (com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechFetchInfoAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechFetchInfoAsk)
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.class, com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk build() {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk result = new com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechFetchInfoAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechFetchInfoAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechFetchInfoAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechFetchInfoAsk>() {
      @java.lang.Override
      public ClanTechFetchInfoAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechFetchInfoAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechFetchInfoAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechFetchInfoAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechFetchInfoAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechFetchInfoAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechFetchInfoAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     * @return Whether the techModel field is set.
     */
    boolean hasTechModel();
    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     * @return The techModel.
     */
    com.yorha.proto.Clan.ClanTechModel getTechModel();
    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     */
    com.yorha.proto.Clan.ClanTechModelOrBuilder getTechModelOrBuilder();

    /**
     * <code>optional int64 contributionDaily = 2;</code>
     * @return Whether the contributionDaily field is set.
     */
    boolean hasContributionDaily();
    /**
     * <code>optional int64 contributionDaily = 2;</code>
     * @return The contributionDaily.
     */
    long getContributionDaily();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechFetchInfoAns}
   */
  public static final class ClanTechFetchInfoAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechFetchInfoAns)
      ClanTechFetchInfoAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechFetchInfoAns.newBuilder() to construct.
    private ClanTechFetchInfoAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechFetchInfoAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechFetchInfoAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechFetchInfoAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Clan.ClanTechModel.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = techModel_.toBuilder();
              }
              techModel_ = input.readMessage(com.yorha.proto.Clan.ClanTechModel.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(techModel_);
                techModel_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              contributionDaily_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.class, com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.Builder.class);
    }

    private int bitField0_;
    public static final int TECHMODEL_FIELD_NUMBER = 1;
    private com.yorha.proto.Clan.ClanTechModel techModel_;
    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     * @return Whether the techModel field is set.
     */
    @java.lang.Override
    public boolean hasTechModel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     * @return The techModel.
     */
    @java.lang.Override
    public com.yorha.proto.Clan.ClanTechModel getTechModel() {
      return techModel_ == null ? com.yorha.proto.Clan.ClanTechModel.getDefaultInstance() : techModel_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Clan.ClanTechModelOrBuilder getTechModelOrBuilder() {
      return techModel_ == null ? com.yorha.proto.Clan.ClanTechModel.getDefaultInstance() : techModel_;
    }

    public static final int CONTRIBUTIONDAILY_FIELD_NUMBER = 2;
    private long contributionDaily_;
    /**
     * <code>optional int64 contributionDaily = 2;</code>
     * @return Whether the contributionDaily field is set.
     */
    @java.lang.Override
    public boolean hasContributionDaily() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 contributionDaily = 2;</code>
     * @return The contributionDaily.
     */
    @java.lang.Override
    public long getContributionDaily() {
      return contributionDaily_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTechModel());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, contributionDaily_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTechModel());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, contributionDaily_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechFetchInfoAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechFetchInfoAns other = (com.yorha.proto.SsClanTech.ClanTechFetchInfoAns) obj;

      if (hasTechModel() != other.hasTechModel()) return false;
      if (hasTechModel()) {
        if (!getTechModel()
            .equals(other.getTechModel())) return false;
      }
      if (hasContributionDaily() != other.hasContributionDaily()) return false;
      if (hasContributionDaily()) {
        if (getContributionDaily()
            != other.getContributionDaily()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechModel()) {
        hash = (37 * hash) + TECHMODEL_FIELD_NUMBER;
        hash = (53 * hash) + getTechModel().hashCode();
      }
      if (hasContributionDaily()) {
        hash = (37 * hash) + CONTRIBUTIONDAILY_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getContributionDaily());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechFetchInfoAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechFetchInfoAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechFetchInfoAns)
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.class, com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTechModelFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (techModelBuilder_ == null) {
          techModel_ = null;
        } else {
          techModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        contributionDaily_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAns build() {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechFetchInfoAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAns result = new com.yorha.proto.SsClanTech.ClanTechFetchInfoAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (techModelBuilder_ == null) {
            result.techModel_ = techModel_;
          } else {
            result.techModel_ = techModelBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.contributionDaily_ = contributionDaily_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechFetchInfoAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechFetchInfoAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechFetchInfoAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechFetchInfoAns.getDefaultInstance()) return this;
        if (other.hasTechModel()) {
          mergeTechModel(other.getTechModel());
        }
        if (other.hasContributionDaily()) {
          setContributionDaily(other.getContributionDaily());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechFetchInfoAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechFetchInfoAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Clan.ClanTechModel techModel_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Clan.ClanTechModel, com.yorha.proto.Clan.ClanTechModel.Builder, com.yorha.proto.Clan.ClanTechModelOrBuilder> techModelBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       * @return Whether the techModel field is set.
       */
      public boolean hasTechModel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       * @return The techModel.
       */
      public com.yorha.proto.Clan.ClanTechModel getTechModel() {
        if (techModelBuilder_ == null) {
          return techModel_ == null ? com.yorha.proto.Clan.ClanTechModel.getDefaultInstance() : techModel_;
        } else {
          return techModelBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public Builder setTechModel(com.yorha.proto.Clan.ClanTechModel value) {
        if (techModelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          techModel_ = value;
          onChanged();
        } else {
          techModelBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public Builder setTechModel(
          com.yorha.proto.Clan.ClanTechModel.Builder builderForValue) {
        if (techModelBuilder_ == null) {
          techModel_ = builderForValue.build();
          onChanged();
        } else {
          techModelBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public Builder mergeTechModel(com.yorha.proto.Clan.ClanTechModel value) {
        if (techModelBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              techModel_ != null &&
              techModel_ != com.yorha.proto.Clan.ClanTechModel.getDefaultInstance()) {
            techModel_ =
              com.yorha.proto.Clan.ClanTechModel.newBuilder(techModel_).mergeFrom(value).buildPartial();
          } else {
            techModel_ = value;
          }
          onChanged();
        } else {
          techModelBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public Builder clearTechModel() {
        if (techModelBuilder_ == null) {
          techModel_ = null;
          onChanged();
        } else {
          techModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public com.yorha.proto.Clan.ClanTechModel.Builder getTechModelBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTechModelFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      public com.yorha.proto.Clan.ClanTechModelOrBuilder getTechModelOrBuilder() {
        if (techModelBuilder_ != null) {
          return techModelBuilder_.getMessageOrBuilder();
        } else {
          return techModel_ == null ?
              com.yorha.proto.Clan.ClanTechModel.getDefaultInstance() : techModel_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechModel techModel = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Clan.ClanTechModel, com.yorha.proto.Clan.ClanTechModel.Builder, com.yorha.proto.Clan.ClanTechModelOrBuilder> 
          getTechModelFieldBuilder() {
        if (techModelBuilder_ == null) {
          techModelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Clan.ClanTechModel, com.yorha.proto.Clan.ClanTechModel.Builder, com.yorha.proto.Clan.ClanTechModelOrBuilder>(
                  getTechModel(),
                  getParentForChildren(),
                  isClean());
          techModel_ = null;
        }
        return techModelBuilder_;
      }

      private long contributionDaily_ ;
      /**
       * <code>optional int64 contributionDaily = 2;</code>
       * @return Whether the contributionDaily field is set.
       */
      @java.lang.Override
      public boolean hasContributionDaily() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 contributionDaily = 2;</code>
       * @return The contributionDaily.
       */
      @java.lang.Override
      public long getContributionDaily() {
        return contributionDaily_;
      }
      /**
       * <code>optional int64 contributionDaily = 2;</code>
       * @param value The contributionDaily to set.
       * @return This builder for chaining.
       */
      public Builder setContributionDaily(long value) {
        bitField0_ |= 0x00000002;
        contributionDaily_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 contributionDaily = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearContributionDaily() {
        bitField0_ = (bitField0_ & ~0x00000002);
        contributionDaily_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechFetchInfoAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechFetchInfoAns)
    private static final com.yorha.proto.SsClanTech.ClanTechFetchInfoAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechFetchInfoAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechFetchInfoAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechFetchInfoAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechFetchInfoAns>() {
      @java.lang.Override
      public ClanTechFetchInfoAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechFetchInfoAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechFetchInfoAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechFetchInfoAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechFetchInfoAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDonateCheckAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDonateCheckAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDonateCheckAsk}
   */
  public static final class ClanTechDonateCheckAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDonateCheckAsk)
      ClanTechDonateCheckAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDonateCheckAsk.newBuilder() to construct.
    private ClanTechDonateCheckAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDonateCheckAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDonateCheckAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDonateCheckAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.class, com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk other = (com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDonateCheckAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDonateCheckAsk)
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.class, com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk build() {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk result = new com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDonateCheckAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDonateCheckAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDonateCheckAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDonateCheckAsk>() {
      @java.lang.Override
      public ClanTechDonateCheckAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDonateCheckAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDonateCheckAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDonateCheckAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDonateCheckAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDonateCheckAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDonateCheckAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 当前的子科技id
     * </pre>
     *
     * <code>optional int32 clanTechSubId = 1;</code>
     * @return Whether the clanTechSubId field is set.
     */
    boolean hasClanTechSubId();
    /**
     * <pre>
     * 当前的子科技id
     * </pre>
     *
     * <code>optional int32 clanTechSubId = 1;</code>
     * @return The clanTechSubId.
     */
    int getClanTechSubId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDonateCheckAns}
   */
  public static final class ClanTechDonateCheckAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDonateCheckAns)
      ClanTechDonateCheckAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDonateCheckAns.newBuilder() to construct.
    private ClanTechDonateCheckAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDonateCheckAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDonateCheckAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDonateCheckAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechSubId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.class, com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHSUBID_FIELD_NUMBER = 1;
    private int clanTechSubId_;
    /**
     * <pre>
     * 当前的子科技id
     * </pre>
     *
     * <code>optional int32 clanTechSubId = 1;</code>
     * @return Whether the clanTechSubId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechSubId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 当前的子科技id
     * </pre>
     *
     * <code>optional int32 clanTechSubId = 1;</code>
     * @return The clanTechSubId.
     */
    @java.lang.Override
    public int getClanTechSubId() {
      return clanTechSubId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechSubId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechSubId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDonateCheckAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDonateCheckAns other = (com.yorha.proto.SsClanTech.ClanTechDonateCheckAns) obj;

      if (hasClanTechSubId() != other.hasClanTechSubId()) return false;
      if (hasClanTechSubId()) {
        if (getClanTechSubId()
            != other.getClanTechSubId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechSubId()) {
        hash = (37 * hash) + CLANTECHSUBID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechSubId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDonateCheckAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDonateCheckAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDonateCheckAns)
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.class, com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechSubId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAns build() {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateCheckAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAns result = new com.yorha.proto.SsClanTech.ClanTechDonateCheckAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechSubId_ = clanTechSubId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDonateCheckAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDonateCheckAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDonateCheckAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDonateCheckAns.getDefaultInstance()) return this;
        if (other.hasClanTechSubId()) {
          setClanTechSubId(other.getClanTechSubId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDonateCheckAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDonateCheckAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechSubId_ ;
      /**
       * <pre>
       * 当前的子科技id
       * </pre>
       *
       * <code>optional int32 clanTechSubId = 1;</code>
       * @return Whether the clanTechSubId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechSubId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 当前的子科技id
       * </pre>
       *
       * <code>optional int32 clanTechSubId = 1;</code>
       * @return The clanTechSubId.
       */
      @java.lang.Override
      public int getClanTechSubId() {
        return clanTechSubId_;
      }
      /**
       * <pre>
       * 当前的子科技id
       * </pre>
       *
       * <code>optional int32 clanTechSubId = 1;</code>
       * @param value The clanTechSubId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechSubId(int value) {
        bitField0_ |= 0x00000001;
        clanTechSubId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前的子科技id
       * </pre>
       *
       * <code>optional int32 clanTechSubId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechSubId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechSubId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDonateCheckAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDonateCheckAns)
    private static final com.yorha.proto.SsClanTech.ClanTechDonateCheckAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDonateCheckAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateCheckAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDonateCheckAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDonateCheckAns>() {
      @java.lang.Override
      public ClanTechDonateCheckAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDonateCheckAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDonateCheckAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDonateCheckAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDonateCheckAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDonateAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDonateAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();

    /**
     * <pre>
     * 倍率
     * </pre>
     *
     * <code>optional int32 magnification = 2;</code>
     * @return Whether the magnification field is set.
     */
    boolean hasMagnification();
    /**
     * <pre>
     * 倍率
     * </pre>
     *
     * <code>optional int32 magnification = 2;</code>
     * @return The magnification.
     */
    int getMagnification();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDonateAsk}
   */
  public static final class ClanTechDonateAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDonateAsk)
      ClanTechDonateAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDonateAsk.newBuilder() to construct.
    private ClanTechDonateAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDonateAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDonateAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDonateAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              magnification_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDonateAsk.class, com.yorha.proto.SsClanTech.ClanTechDonateAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    public static final int MAGNIFICATION_FIELD_NUMBER = 2;
    private int magnification_;
    /**
     * <pre>
     * 倍率
     * </pre>
     *
     * <code>optional int32 magnification = 2;</code>
     * @return Whether the magnification field is set.
     */
    @java.lang.Override
    public boolean hasMagnification() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 倍率
     * </pre>
     *
     * <code>optional int32 magnification = 2;</code>
     * @return The magnification.
     */
    @java.lang.Override
    public int getMagnification() {
      return magnification_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, magnification_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, magnification_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDonateAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDonateAsk other = (com.yorha.proto.SsClanTech.ClanTechDonateAsk) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (hasMagnification() != other.hasMagnification()) return false;
      if (hasMagnification()) {
        if (getMagnification()
            != other.getMagnification()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      if (hasMagnification()) {
        hash = (37 * hash) + MAGNIFICATION_FIELD_NUMBER;
        hash = (53 * hash) + getMagnification();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDonateAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDonateAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDonateAsk)
        com.yorha.proto.SsClanTech.ClanTechDonateAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDonateAsk.class, com.yorha.proto.SsClanTech.ClanTechDonateAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDonateAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        magnification_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDonateAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAsk build() {
        com.yorha.proto.SsClanTech.ClanTechDonateAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDonateAsk result = new com.yorha.proto.SsClanTech.ClanTechDonateAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.magnification_ = magnification_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDonateAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDonateAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDonateAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDonateAsk.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        if (other.hasMagnification()) {
          setMagnification(other.getMagnification());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDonateAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDonateAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }

      private int magnification_ ;
      /**
       * <pre>
       * 倍率
       * </pre>
       *
       * <code>optional int32 magnification = 2;</code>
       * @return Whether the magnification field is set.
       */
      @java.lang.Override
      public boolean hasMagnification() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 倍率
       * </pre>
       *
       * <code>optional int32 magnification = 2;</code>
       * @return The magnification.
       */
      @java.lang.Override
      public int getMagnification() {
        return magnification_;
      }
      /**
       * <pre>
       * 倍率
       * </pre>
       *
       * <code>optional int32 magnification = 2;</code>
       * @param value The magnification to set.
       * @return This builder for chaining.
       */
      public Builder setMagnification(int value) {
        bitField0_ |= 0x00000002;
        magnification_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 倍率
       * </pre>
       *
       * <code>optional int32 magnification = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMagnification() {
        bitField0_ = (bitField0_ & ~0x00000002);
        magnification_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDonateAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDonateAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechDonateAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDonateAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDonateAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDonateAsk>() {
      @java.lang.Override
      public ClanTechDonateAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDonateAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDonateAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDonateAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDonateAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDonateAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDonateAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 捐献后的科技点数
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return Whether the afterPoint field is set.
     */
    boolean hasAfterPoint();
    /**
     * <pre>
     * 捐献后的科技点数
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return The afterPoint.
     */
    int getAfterPoint();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDonateAns}
   */
  public static final class ClanTechDonateAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDonateAns)
      ClanTechDonateAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDonateAns.newBuilder() to construct.
    private ClanTechDonateAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDonateAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDonateAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDonateAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              afterPoint_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDonateAns.class, com.yorha.proto.SsClanTech.ClanTechDonateAns.Builder.class);
    }

    private int bitField0_;
    public static final int AFTERPOINT_FIELD_NUMBER = 1;
    private int afterPoint_;
    /**
     * <pre>
     * 捐献后的科技点数
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return Whether the afterPoint field is set.
     */
    @java.lang.Override
    public boolean hasAfterPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 捐献后的科技点数
     * </pre>
     *
     * <code>optional int32 afterPoint = 1;</code>
     * @return The afterPoint.
     */
    @java.lang.Override
    public int getAfterPoint() {
      return afterPoint_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, afterPoint_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, afterPoint_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDonateAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDonateAns other = (com.yorha.proto.SsClanTech.ClanTechDonateAns) obj;

      if (hasAfterPoint() != other.hasAfterPoint()) return false;
      if (hasAfterPoint()) {
        if (getAfterPoint()
            != other.getAfterPoint()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasAfterPoint()) {
        hash = (37 * hash) + AFTERPOINT_FIELD_NUMBER;
        hash = (53 * hash) + getAfterPoint();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDonateAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDonateAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDonateAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDonateAns)
        com.yorha.proto.SsClanTech.ClanTechDonateAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDonateAns.class, com.yorha.proto.SsClanTech.ClanTechDonateAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDonateAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        afterPoint_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDonateAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDonateAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAns build() {
        com.yorha.proto.SsClanTech.ClanTechDonateAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDonateAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDonateAns result = new com.yorha.proto.SsClanTech.ClanTechDonateAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.afterPoint_ = afterPoint_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDonateAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDonateAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDonateAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDonateAns.getDefaultInstance()) return this;
        if (other.hasAfterPoint()) {
          setAfterPoint(other.getAfterPoint());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDonateAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDonateAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int afterPoint_ ;
      /**
       * <pre>
       * 捐献后的科技点数
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return Whether the afterPoint field is set.
       */
      @java.lang.Override
      public boolean hasAfterPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 捐献后的科技点数
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return The afterPoint.
       */
      @java.lang.Override
      public int getAfterPoint() {
        return afterPoint_;
      }
      /**
       * <pre>
       * 捐献后的科技点数
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @param value The afterPoint to set.
       * @return This builder for chaining.
       */
      public Builder setAfterPoint(int value) {
        bitField0_ |= 0x00000001;
        afterPoint_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 捐献后的科技点数
       * </pre>
       *
       * <code>optional int32 afterPoint = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterPoint() {
        bitField0_ = (bitField0_ & ~0x00000001);
        afterPoint_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDonateAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDonateAns)
    private static final com.yorha.proto.SsClanTech.ClanTechDonateAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDonateAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDonateAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDonateAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDonateAns>() {
      @java.lang.Override
      public ClanTechDonateAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDonateAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDonateAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDonateAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDonateAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechResearchAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechResearchAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();

    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechResearchAsk}
   */
  public static final class ClanTechResearchAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechResearchAsk)
      ClanTechResearchAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechResearchAsk.newBuilder() to construct.
    private ClanTechResearchAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechResearchAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechResearchAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechResearchAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechResearchAsk.class, com.yorha.proto.SsClanTech.ClanTechResearchAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechResearchAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechResearchAsk other = (com.yorha.proto.SsClanTech.ClanTechResearchAsk) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechResearchAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechResearchAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechResearchAsk)
        com.yorha.proto.SsClanTech.ClanTechResearchAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechResearchAsk.class, com.yorha.proto.SsClanTech.ClanTechResearchAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechResearchAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechResearchAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAsk build() {
        com.yorha.proto.SsClanTech.ClanTechResearchAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechResearchAsk result = new com.yorha.proto.SsClanTech.ClanTechResearchAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechResearchAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechResearchAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechResearchAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechResearchAsk.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechResearchAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechResearchAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechResearchAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechResearchAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechResearchAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechResearchAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechResearchAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechResearchAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechResearchAsk>() {
      @java.lang.Override
      public ClanTechResearchAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechResearchAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechResearchAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechResearchAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechResearchAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechResearchAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechResearchAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechResearchAns}
   */
  public static final class ClanTechResearchAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechResearchAns)
      ClanTechResearchAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechResearchAns.newBuilder() to construct.
    private ClanTechResearchAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechResearchAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechResearchAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechResearchAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechResearchAns.class, com.yorha.proto.SsClanTech.ClanTechResearchAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechResearchAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechResearchAns other = (com.yorha.proto.SsClanTech.ClanTechResearchAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechResearchAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechResearchAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechResearchAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechResearchAns)
        com.yorha.proto.SsClanTech.ClanTechResearchAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechResearchAns.class, com.yorha.proto.SsClanTech.ClanTechResearchAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechResearchAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechResearchAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechResearchAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAns build() {
        com.yorha.proto.SsClanTech.ClanTechResearchAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechResearchAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechResearchAns result = new com.yorha.proto.SsClanTech.ClanTechResearchAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechResearchAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechResearchAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechResearchAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechResearchAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechResearchAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechResearchAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechResearchAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechResearchAns)
    private static final com.yorha.proto.SsClanTech.ClanTechResearchAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechResearchAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechResearchAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechResearchAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechResearchAns>() {
      @java.lang.Override
      public ClanTechResearchAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechResearchAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechResearchAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechResearchAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechResearchAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechRecommendAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechRecommendAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    boolean hasClanTechId();
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    int getClanTechId();

    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechRecommendAsk}
   */
  public static final class ClanTechRecommendAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechRecommendAsk)
      ClanTechRecommendAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechRecommendAsk.newBuilder() to construct.
    private ClanTechRecommendAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechRecommendAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechRecommendAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechRecommendAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanTechId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechRecommendAsk.class, com.yorha.proto.SsClanTech.ClanTechRecommendAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANTECHID_FIELD_NUMBER = 1;
    private int clanTechId_;
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return Whether the clanTechId field is set.
     */
    @java.lang.Override
    public boolean hasClanTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 clanTechId = 1;</code>
     * @return The clanTechId.
     */
    @java.lang.Override
    public int getClanTechId() {
      return clanTechId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 2;
    private long playerId_;
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 操作的玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 2;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, clanTechId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechRecommendAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechRecommendAsk other = (com.yorha.proto.SsClanTech.ClanTechRecommendAsk) obj;

      if (hasClanTechId() != other.hasClanTechId()) return false;
      if (hasClanTechId()) {
        if (getClanTechId()
            != other.getClanTechId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanTechId()) {
        hash = (37 * hash) + CLANTECHID_FIELD_NUMBER;
        hash = (53 * hash) + getClanTechId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechRecommendAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechRecommendAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechRecommendAsk)
        com.yorha.proto.SsClanTech.ClanTechRecommendAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechRecommendAsk.class, com.yorha.proto.SsClanTech.ClanTechRecommendAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechRecommendAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanTechId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechRecommendAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAsk build() {
        com.yorha.proto.SsClanTech.ClanTechRecommendAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechRecommendAsk result = new com.yorha.proto.SsClanTech.ClanTechRecommendAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanTechId_ = clanTechId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechRecommendAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechRecommendAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechRecommendAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechRecommendAsk.getDefaultInstance()) return this;
        if (other.hasClanTechId()) {
          setClanTechId(other.getClanTechId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechRecommendAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechRecommendAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int clanTechId_ ;
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return Whether the clanTechId field is set.
       */
      @java.lang.Override
      public boolean hasClanTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return The clanTechId.
       */
      @java.lang.Override
      public int getClanTechId() {
        return clanTechId_;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @param value The clanTechId to set.
       * @return This builder for chaining.
       */
      public Builder setClanTechId(int value) {
        bitField0_ |= 0x00000001;
        clanTechId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 clanTechId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanTechId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 操作的玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechRecommendAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechRecommendAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechRecommendAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechRecommendAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechRecommendAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechRecommendAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechRecommendAsk>() {
      @java.lang.Override
      public ClanTechRecommendAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechRecommendAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechRecommendAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechRecommendAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechRecommendAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechRecommendAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechRecommendAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechRecommendAns}
   */
  public static final class ClanTechRecommendAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechRecommendAns)
      ClanTechRecommendAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechRecommendAns.newBuilder() to construct.
    private ClanTechRecommendAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechRecommendAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechRecommendAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechRecommendAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechRecommendAns.class, com.yorha.proto.SsClanTech.ClanTechRecommendAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechRecommendAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechRecommendAns other = (com.yorha.proto.SsClanTech.ClanTechRecommendAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechRecommendAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechRecommendAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechRecommendAns)
        com.yorha.proto.SsClanTech.ClanTechRecommendAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechRecommendAns.class, com.yorha.proto.SsClanTech.ClanTechRecommendAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechRecommendAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechRecommendAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAns build() {
        com.yorha.proto.SsClanTech.ClanTechRecommendAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechRecommendAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechRecommendAns result = new com.yorha.proto.SsClanTech.ClanTechRecommendAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechRecommendAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechRecommendAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechRecommendAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechRecommendAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechRecommendAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechRecommendAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechRecommendAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechRecommendAns)
    private static final com.yorha.proto.SsClanTech.ClanTechRecommendAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechRecommendAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechRecommendAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechRecommendAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechRecommendAns>() {
      @java.lang.Override
      public ClanTechRecommendAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechRecommendAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechRecommendAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechRecommendAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechRecommendAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDetailAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDetailAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 techId = 1;</code>
     * @return Whether the techId field is set.
     */
    boolean hasTechId();
    /**
     * <code>optional int32 techId = 1;</code>
     * @return The techId.
     */
    int getTechId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDetailAsk}
   */
  public static final class ClanTechDetailAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDetailAsk)
      ClanTechDetailAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDetailAsk.newBuilder() to construct.
    private ClanTechDetailAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDetailAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDetailAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDetailAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              techId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDetailAsk.class, com.yorha.proto.SsClanTech.ClanTechDetailAsk.Builder.class);
    }

    private int bitField0_;
    public static final int TECHID_FIELD_NUMBER = 1;
    private int techId_;
    /**
     * <code>optional int32 techId = 1;</code>
     * @return Whether the techId field is set.
     */
    @java.lang.Override
    public boolean hasTechId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 techId = 1;</code>
     * @return The techId.
     */
    @java.lang.Override
    public int getTechId() {
      return techId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, techId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, techId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDetailAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDetailAsk other = (com.yorha.proto.SsClanTech.ClanTechDetailAsk) obj;

      if (hasTechId() != other.hasTechId()) return false;
      if (hasTechId()) {
        if (getTechId()
            != other.getTechId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechId()) {
        hash = (37 * hash) + TECHID_FIELD_NUMBER;
        hash = (53 * hash) + getTechId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDetailAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDetailAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDetailAsk)
        com.yorha.proto.SsClanTech.ClanTechDetailAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDetailAsk.class, com.yorha.proto.SsClanTech.ClanTechDetailAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDetailAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        techId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDetailAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAsk build() {
        com.yorha.proto.SsClanTech.ClanTechDetailAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAsk buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDetailAsk result = new com.yorha.proto.SsClanTech.ClanTechDetailAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.techId_ = techId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDetailAsk) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDetailAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDetailAsk other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDetailAsk.getDefaultInstance()) return this;
        if (other.hasTechId()) {
          setTechId(other.getTechId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDetailAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDetailAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int techId_ ;
      /**
       * <code>optional int32 techId = 1;</code>
       * @return Whether the techId field is set.
       */
      @java.lang.Override
      public boolean hasTechId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 techId = 1;</code>
       * @return The techId.
       */
      @java.lang.Override
      public int getTechId() {
        return techId_;
      }
      /**
       * <code>optional int32 techId = 1;</code>
       * @param value The techId to set.
       * @return This builder for chaining.
       */
      public Builder setTechId(int value) {
        bitField0_ |= 0x00000001;
        techId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 techId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearTechId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        techId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDetailAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDetailAsk)
    private static final com.yorha.proto.SsClanTech.ClanTechDetailAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDetailAsk();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDetailAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDetailAsk>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDetailAsk>() {
      @java.lang.Override
      public ClanTechDetailAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDetailAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDetailAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDetailAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDetailAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanTechDetailAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanTechDetailAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     * @return Whether the techInfo field is set.
     */
    boolean hasTechInfo();
    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     * @return The techInfo.
     */
    com.yorha.proto.Struct.ClanTechInfo getTechInfo();
    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     */
    com.yorha.proto.Struct.ClanTechInfoOrBuilder getTechInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanTechDetailAns}
   */
  public static final class ClanTechDetailAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanTechDetailAns)
      ClanTechDetailAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanTechDetailAns.newBuilder() to construct.
    private ClanTechDetailAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanTechDetailAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanTechDetailAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanTechDetailAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.ClanTechInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = techInfo_.toBuilder();
              }
              techInfo_ = input.readMessage(com.yorha.proto.Struct.ClanTechInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(techInfo_);
                techInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanTech.ClanTechDetailAns.class, com.yorha.proto.SsClanTech.ClanTechDetailAns.Builder.class);
    }

    private int bitField0_;
    public static final int TECHINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.ClanTechInfo techInfo_;
    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     * @return Whether the techInfo field is set.
     */
    @java.lang.Override
    public boolean hasTechInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     * @return The techInfo.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanTechInfo getTechInfo() {
      return techInfo_ == null ? com.yorha.proto.Struct.ClanTechInfo.getDefaultInstance() : techInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanTechInfoOrBuilder getTechInfoOrBuilder() {
      return techInfo_ == null ? com.yorha.proto.Struct.ClanTechInfo.getDefaultInstance() : techInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTechInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTechInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanTech.ClanTechDetailAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanTech.ClanTechDetailAns other = (com.yorha.proto.SsClanTech.ClanTechDetailAns) obj;

      if (hasTechInfo() != other.hasTechInfo()) return false;
      if (hasTechInfo()) {
        if (!getTechInfo()
            .equals(other.getTechInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTechInfo()) {
        hash = (37 * hash) + TECHINFO_FIELD_NUMBER;
        hash = (53 * hash) + getTechInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanTech.ClanTechDetailAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanTech.ClanTechDetailAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanTechDetailAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanTechDetailAns)
        com.yorha.proto.SsClanTech.ClanTechDetailAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanTech.ClanTechDetailAns.class, com.yorha.proto.SsClanTech.ClanTechDetailAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanTech.ClanTechDetailAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTechInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (techInfoBuilder_ == null) {
          techInfo_ = null;
        } else {
          techInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanTech.internal_static_com_yorha_proto_ClanTechDetailAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanTech.ClanTechDetailAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAns build() {
        com.yorha.proto.SsClanTech.ClanTechDetailAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanTech.ClanTechDetailAns buildPartial() {
        com.yorha.proto.SsClanTech.ClanTechDetailAns result = new com.yorha.proto.SsClanTech.ClanTechDetailAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (techInfoBuilder_ == null) {
            result.techInfo_ = techInfo_;
          } else {
            result.techInfo_ = techInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanTech.ClanTechDetailAns) {
          return mergeFrom((com.yorha.proto.SsClanTech.ClanTechDetailAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanTech.ClanTechDetailAns other) {
        if (other == com.yorha.proto.SsClanTech.ClanTechDetailAns.getDefaultInstance()) return this;
        if (other.hasTechInfo()) {
          mergeTechInfo(other.getTechInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanTech.ClanTechDetailAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanTech.ClanTechDetailAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.ClanTechInfo techInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanTechInfo, com.yorha.proto.Struct.ClanTechInfo.Builder, com.yorha.proto.Struct.ClanTechInfoOrBuilder> techInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       * @return Whether the techInfo field is set.
       */
      public boolean hasTechInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       * @return The techInfo.
       */
      public com.yorha.proto.Struct.ClanTechInfo getTechInfo() {
        if (techInfoBuilder_ == null) {
          return techInfo_ == null ? com.yorha.proto.Struct.ClanTechInfo.getDefaultInstance() : techInfo_;
        } else {
          return techInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public Builder setTechInfo(com.yorha.proto.Struct.ClanTechInfo value) {
        if (techInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          techInfo_ = value;
          onChanged();
        } else {
          techInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public Builder setTechInfo(
          com.yorha.proto.Struct.ClanTechInfo.Builder builderForValue) {
        if (techInfoBuilder_ == null) {
          techInfo_ = builderForValue.build();
          onChanged();
        } else {
          techInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public Builder mergeTechInfo(com.yorha.proto.Struct.ClanTechInfo value) {
        if (techInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              techInfo_ != null &&
              techInfo_ != com.yorha.proto.Struct.ClanTechInfo.getDefaultInstance()) {
            techInfo_ =
              com.yorha.proto.Struct.ClanTechInfo.newBuilder(techInfo_).mergeFrom(value).buildPartial();
          } else {
            techInfo_ = value;
          }
          onChanged();
        } else {
          techInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public Builder clearTechInfo() {
        if (techInfoBuilder_ == null) {
          techInfo_ = null;
          onChanged();
        } else {
          techInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public com.yorha.proto.Struct.ClanTechInfo.Builder getTechInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTechInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      public com.yorha.proto.Struct.ClanTechInfoOrBuilder getTechInfoOrBuilder() {
        if (techInfoBuilder_ != null) {
          return techInfoBuilder_.getMessageOrBuilder();
        } else {
          return techInfo_ == null ?
              com.yorha.proto.Struct.ClanTechInfo.getDefaultInstance() : techInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanTechInfo techInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanTechInfo, com.yorha.proto.Struct.ClanTechInfo.Builder, com.yorha.proto.Struct.ClanTechInfoOrBuilder> 
          getTechInfoFieldBuilder() {
        if (techInfoBuilder_ == null) {
          techInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.ClanTechInfo, com.yorha.proto.Struct.ClanTechInfo.Builder, com.yorha.proto.Struct.ClanTechInfoOrBuilder>(
                  getTechInfo(),
                  getParentForChildren(),
                  isClean());
          techInfo_ = null;
        }
        return techInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanTechDetailAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanTechDetailAns)
    private static final com.yorha.proto.SsClanTech.ClanTechDetailAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanTech.ClanTechDetailAns();
    }

    public static com.yorha.proto.SsClanTech.ClanTechDetailAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanTechDetailAns>
        PARSER = new com.google.protobuf.AbstractParser<ClanTechDetailAns>() {
      @java.lang.Override
      public ClanTechDetailAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanTechDetailAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanTechDetailAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanTechDetailAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanTech.ClanTechDetailAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechFetchInfoAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechFetchInfoAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDonateCheckAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDonateCheckAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDonateAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDonateAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDonateAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechResearchAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechResearchAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechResearchAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechRecommendAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechRecommendAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDetailAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanTechDetailAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanTechDetailAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/clan/ss_clan_tech.proto\022\017" +
      "com.yorha.proto\032\034ss_proto/gen/clan/clan." +
      "proto\032 ss_proto/gen/common/struct.proto\"" +
      "(\n\024ClanTechFetchInfoAsk\022\020\n\010playerId\030\001 \001(" +
      "\003\"d\n\024ClanTechFetchInfoAns\0221\n\ttechModel\030\001" +
      " \001(\0132\036.com.yorha.proto.ClanTechModel\022\031\n\021" +
      "contributionDaily\030\002 \001(\003\",\n\026ClanTechDonat" +
      "eCheckAsk\022\022\n\nclanTechId\030\001 \001(\005\"/\n\026ClanTec" +
      "hDonateCheckAns\022\025\n\rclanTechSubId\030\001 \001(\005\"P" +
      "\n\021ClanTechDonateAsk\022\022\n\nclanTechId\030\001 \001(\005\022" +
      "\025\n\rmagnification\030\002 \001(\005\022\020\n\010playerId\030\003 \001(\003" +
      "\"\'\n\021ClanTechDonateAns\022\022\n\nafterPoint\030\001 \001(" +
      "\005\";\n\023ClanTechResearchAsk\022\022\n\nclanTechId\030\001" +
      " \001(\005\022\020\n\010playerId\030\002 \001(\003\"\025\n\023ClanTechResear" +
      "chAns\"<\n\024ClanTechRecommendAsk\022\022\n\nclanTec" +
      "hId\030\001 \001(\005\022\020\n\010playerId\030\002 \001(\003\"\026\n\024ClanTechR" +
      "ecommendAns\"#\n\021ClanTechDetailAsk\022\016\n\006tech" +
      "Id\030\001 \001(\005\"D\n\021ClanTechDetailAns\022/\n\010techInf" +
      "o\030\001 \001(\0132\035.com.yorha.proto.ClanTechInfoB\002" +
      "H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Clan.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ClanTechFetchInfoAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechFetchInfoAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ClanTechFetchInfoAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechFetchInfoAns_descriptor,
        new java.lang.String[] { "TechModel", "ContributionDaily", });
    internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ClanTechDonateCheckAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDonateCheckAsk_descriptor,
        new java.lang.String[] { "ClanTechId", });
    internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_ClanTechDonateCheckAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDonateCheckAns_descriptor,
        new java.lang.String[] { "ClanTechSubId", });
    internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_ClanTechDonateAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDonateAsk_descriptor,
        new java.lang.String[] { "ClanTechId", "Magnification", "PlayerId", });
    internal_static_com_yorha_proto_ClanTechDonateAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_ClanTechDonateAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDonateAns_descriptor,
        new java.lang.String[] { "AfterPoint", });
    internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_ClanTechResearchAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechResearchAsk_descriptor,
        new java.lang.String[] { "ClanTechId", "PlayerId", });
    internal_static_com_yorha_proto_ClanTechResearchAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_ClanTechResearchAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechResearchAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_ClanTechRecommendAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechRecommendAsk_descriptor,
        new java.lang.String[] { "ClanTechId", "PlayerId", });
    internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_ClanTechRecommendAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechRecommendAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_ClanTechDetailAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDetailAsk_descriptor,
        new java.lang.String[] { "TechId", });
    internal_static_com_yorha_proto_ClanTechDetailAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_ClanTechDetailAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanTechDetailAns_descriptor,
        new java.lang.String[] { "TechInfo", });
    com.yorha.proto.Clan.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

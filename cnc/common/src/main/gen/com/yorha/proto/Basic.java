// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/cnc/basic.proto

package com.yorha.proto;

public final class Basic {
  private Basic() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Int32ListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32List)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int32 datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.Integer> getDatasList();
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated int32 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    int getDatas(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32List}
   */
  public static final class Int32List extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32List)
      Int32ListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32List.newBuilder() to construct.
    private Int32List(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32List() {
      datas_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32List();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32List(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                datas_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                datas_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32List_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32List_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.Int32List.class, com.yorha.proto.Basic.Int32List.Builder.class);
    }

    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList datas_;
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return A list containing the datas.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated int32 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public int getDatas(int index) {
      return datas_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        output.writeInt32(1, datas_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(datas_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.Int32List)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.Int32List other = (com.yorha.proto.Basic.Int32List) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.Int32List parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32List parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32List parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32List parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.Int32List prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32List}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32List)
        com.yorha.proto.Basic.Int32ListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32List_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32List_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.Int32List.class, com.yorha.proto.Basic.Int32List.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.Int32List.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32List_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32List getDefaultInstanceForType() {
        return com.yorha.proto.Basic.Int32List.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32List build() {
        com.yorha.proto.Basic.Int32List result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32List buildPartial() {
        com.yorha.proto.Basic.Int32List result = new com.yorha.proto.Basic.Int32List(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.Int32List) {
          return mergeFrom((com.yorha.proto.Basic.Int32List)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.Int32List other) {
        if (other == com.yorha.proto.Basic.Int32List.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.Int32List parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.Int32List) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList datas_ = emptyIntList();
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = mutableCopy(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return A list containing the datas.
       */
      public java.util.List<java.lang.Integer>
          getDatasList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(datas_) : datas_;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public int getDatas(int index) {
        return datas_.getInt(index);
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, int value) {
        ensureDatasIsMutable();
        datas_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(int value) {
        ensureDatasIsMutable();
        datas_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32List)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32List)
    private static final com.yorha.proto.Basic.Int32List DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.Int32List();
    }

    public static com.yorha.proto.Basic.Int32List getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32List>
        PARSER = new com.google.protobuf.AbstractParser<Int32List>() {
      @java.lang.Override
      public Int32List parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32List(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32List> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32List> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.Int32List getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64ListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64List)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.Long> getDatasList();
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated int64 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    long getDatas(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64List}
   */
  public static final class Int64List extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64List)
      Int64ListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64List.newBuilder() to construct.
    private Int64List(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64List() {
      datas_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64List();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64List(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                datas_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                datas_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64List_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64List_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.Int64List.class, com.yorha.proto.Basic.Int64List.Builder.class);
    }

    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList datas_;
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return A list containing the datas.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated int64 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public long getDatas(int index) {
      return datas_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        output.writeInt64(1, datas_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(datas_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.Int64List)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.Int64List other = (com.yorha.proto.Basic.Int64List) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.Int64List parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64List parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64List parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64List parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.Int64List prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64List}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64List)
        com.yorha.proto.Basic.Int64ListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64List_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64List_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.Int64List.class, com.yorha.proto.Basic.Int64List.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.Int64List.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64List_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64List getDefaultInstanceForType() {
        return com.yorha.proto.Basic.Int64List.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64List build() {
        com.yorha.proto.Basic.Int64List result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64List buildPartial() {
        com.yorha.proto.Basic.Int64List result = new com.yorha.proto.Basic.Int64List(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.Int64List) {
          return mergeFrom((com.yorha.proto.Basic.Int64List)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.Int64List other) {
        if (other == com.yorha.proto.Basic.Int64List.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.Int64List parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.Int64List) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList datas_ = emptyLongList();
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = mutableCopy(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return A list containing the datas.
       */
      public java.util.List<java.lang.Long>
          getDatasList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(datas_) : datas_;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public long getDatas(int index) {
        return datas_.getLong(index);
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, long value) {
        ensureDatasIsMutable();
        datas_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(long value) {
        ensureDatasIsMutable();
        datas_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64List)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64List)
    private static final com.yorha.proto.Basic.Int64List DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.Int64List();
    }

    public static com.yorha.proto.Basic.Int64List getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64List>
        PARSER = new com.google.protobuf.AbstractParser<Int64List>() {
      @java.lang.Override
      public Int64List parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64List(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64List> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64List> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.Int64List getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int32SetOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int32Set)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int32 datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.Integer> getDatasList();
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated int32 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    int getDatas(int index);

    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Integer> getDeleteKeysList();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    int getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int32Set}
   */
  public static final class Int32Set extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int32Set)
      Int32SetOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int32Set.newBuilder() to construct.
    private Int32Set(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int32Set() {
      datas_ = emptyIntList();
      deleteKeys_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int32Set();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int32Set(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                datas_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                datas_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addInt(input.readInt32());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newIntList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32Set_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32Set_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.Int32Set.class, com.yorha.proto.Basic.Int32Set.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList datas_;
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return A list containing the datas.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated int32 datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated int32 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public int getDatas(int index) {
      return datas_.getInt(index);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.IntList deleteKeys_;
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int32 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public int getDeleteKeys(int index) {
      return deleteKeys_.getInt(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        output.writeInt32(1, datas_.getInt(i));
      }
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt32(2, deleteKeys_.getInt(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(datas_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(deleteKeys_.getInt(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.Int32Set)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.Int32Set other = (com.yorha.proto.Basic.Int32Set) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.Int32Set parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32Set parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32Set parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int32Set parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.Int32Set prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int32Set}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int32Set)
        com.yorha.proto.Basic.Int32SetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32Set_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32Set_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.Int32Set.class, com.yorha.proto.Basic.Int32Set.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.Int32Set.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int32Set_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32Set getDefaultInstanceForType() {
        return com.yorha.proto.Basic.Int32Set.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32Set build() {
        com.yorha.proto.Basic.Int32Set result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int32Set buildPartial() {
        com.yorha.proto.Basic.Int32Set result = new com.yorha.proto.Basic.Int32Set(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.Int32Set) {
          return mergeFrom((com.yorha.proto.Basic.Int32Set)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.Int32Set other) {
        if (other == com.yorha.proto.Basic.Int32Set.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.Int32Set parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.Int32Set) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList datas_ = emptyIntList();
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = mutableCopy(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return A list containing the datas.
       */
      public java.util.List<java.lang.Integer>
          getDatasList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(datas_) : datas_;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public int getDatas(int index) {
        return datas_.getInt(index);
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, int value) {
        ensureDatasIsMutable();
        datas_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(int value) {
        ensureDatasIsMutable();
        datas_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.IntList deleteKeys_ = emptyIntList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Integer>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public int getDeleteKeys(int index) {
        return deleteKeys_.getInt(index);
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(int value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int32Set)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int32Set)
    private static final com.yorha.proto.Basic.Int32Set DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.Int32Set();
    }

    public static com.yorha.proto.Basic.Int32Set getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int32Set>
        PARSER = new com.google.protobuf.AbstractParser<Int32Set>() {
      @java.lang.Override
      public Int32Set parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int32Set(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int32Set> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int32Set> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.Int32Set getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StringListOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.StringList)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated string datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.String>
        getDatasList();
    /**
     * <code>repeated string datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    java.lang.String getDatas(int index);
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the value to return.
     * @return The bytes of the datas at the given index.
     */
    com.google.protobuf.ByteString
        getDatasBytes(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.StringList}
   */
  public static final class StringList extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.StringList)
      StringListOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StringList.newBuilder() to construct.
    private StringList(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StringList() {
      datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StringList();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private StringList(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.add(bs);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_ = datas_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringList_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringList_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.StringList.class, com.yorha.proto.Basic.StringList.Builder.class);
    }

    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.LazyStringList datas_;
    /**
     * <code>repeated string datas = 1;</code>
     * @return A list containing the datas.
     */
    public com.google.protobuf.ProtocolStringList
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public java.lang.String getDatas(int index) {
      return datas_.get(index);
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the value to return.
     * @return The bytes of the datas at the given index.
     */
    public com.google.protobuf.ByteString
        getDatasBytes(int index) {
      return datas_.getByteString(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, datas_.getRaw(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += computeStringSizeNoTag(datas_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.StringList)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.StringList other = (com.yorha.proto.Basic.StringList) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.StringList parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringList parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringList parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringList parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.StringList prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.StringList}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.StringList)
        com.yorha.proto.Basic.StringListOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringList_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringList_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.StringList.class, com.yorha.proto.Basic.StringList.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.StringList.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringList_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringList getDefaultInstanceForType() {
        return com.yorha.proto.Basic.StringList.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringList build() {
        com.yorha.proto.Basic.StringList result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringList buildPartial() {
        com.yorha.proto.Basic.StringList result = new com.yorha.proto.Basic.StringList(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_ = datas_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.StringList) {
          return mergeFrom((com.yorha.proto.Basic.StringList)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.StringList other) {
        if (other == com.yorha.proto.Basic.StringList.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.StringList parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.StringList) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.LazyStringList datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = new com.google.protobuf.LazyStringArrayList(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return A list containing the datas.
       */
      public com.google.protobuf.ProtocolStringList
          getDatasList() {
        return datas_.getUnmodifiableView();
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public java.lang.String getDatas(int index) {
        return datas_.get(index);
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index of the value to return.
       * @return The bytes of the datas at the given index.
       */
      public com.google.protobuf.ByteString
          getDatasBytes(int index) {
        return datas_.getByteString(index);
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<java.lang.String> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param value The bytes of the datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatasBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.add(value);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.StringList)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.StringList)
    private static final com.yorha.proto.Basic.StringList DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.StringList();
    }

    public static com.yorha.proto.Basic.StringList getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<StringList>
        PARSER = new com.google.protobuf.AbstractParser<StringList>() {
      @java.lang.Override
      public StringList parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new StringList(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<StringList> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StringList> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.StringList getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface StringSetOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.StringSet)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated string datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.String>
        getDatasList();
    /**
     * <code>repeated string datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    java.lang.String getDatas(int index);
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the value to return.
     * @return The bytes of the datas at the given index.
     */
    com.google.protobuf.ByteString
        getDatasBytes(int index);

    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.String>
        getDeleteKeysList();
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    java.lang.String getDeleteKeys(int index);
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @param index The index of the value to return.
     * @return The bytes of the deleteKeys at the given index.
     */
    com.google.protobuf.ByteString
        getDeleteKeysBytes(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.StringSet}
   */
  public static final class StringSet extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.StringSet)
      StringSetOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use StringSet.newBuilder() to construct.
    private StringSet(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private StringSet() {
      datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      deleteKeys_ = com.google.protobuf.LazyStringArrayList.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new StringSet();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private StringSet(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.add(bs);
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = new com.google.protobuf.LazyStringArrayList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.add(bs);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_ = datas_.getUnmodifiableView();
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = deleteKeys_.getUnmodifiableView();
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringSet_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringSet_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.StringSet.class, com.yorha.proto.Basic.StringSet.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.LazyStringList datas_;
    /**
     * <code>repeated string datas = 1;</code>
     * @return A list containing the datas.
     */
    public com.google.protobuf.ProtocolStringList
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public java.lang.String getDatas(int index) {
      return datas_.get(index);
    }
    /**
     * <code>repeated string datas = 1;</code>
     * @param index The index of the value to return.
     * @return The bytes of the datas at the given index.
     */
    public com.google.protobuf.ByteString
        getDatasBytes(int index) {
      return datas_.getByteString(index);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.LazyStringList deleteKeys_;
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    public com.google.protobuf.ProtocolStringList
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public java.lang.String getDeleteKeys(int index) {
      return deleteKeys_.get(index);
    }
    /**
     * <code>repeated string deleteKeys = 2;</code>
     * @param index The index of the value to return.
     * @return The bytes of the deleteKeys at the given index.
     */
    public com.google.protobuf.ByteString
        getDeleteKeysBytes(int index) {
      return deleteKeys_.getByteString(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, datas_.getRaw(i));
      }
      for (int i = 0; i < deleteKeys_.size(); i++) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, deleteKeys_.getRaw(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += computeStringSizeNoTag(datas_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += computeStringSizeNoTag(deleteKeys_.getRaw(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.StringSet)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.StringSet other = (com.yorha.proto.Basic.StringSet) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.StringSet parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringSet parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringSet parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.StringSet parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.StringSet prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.StringSet}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.StringSet)
        com.yorha.proto.Basic.StringSetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringSet_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringSet_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.StringSet.class, com.yorha.proto.Basic.StringSet.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.StringSet.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        deleteKeys_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_StringSet_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringSet getDefaultInstanceForType() {
        return com.yorha.proto.Basic.StringSet.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringSet build() {
        com.yorha.proto.Basic.StringSet result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.StringSet buildPartial() {
        com.yorha.proto.Basic.StringSet result = new com.yorha.proto.Basic.StringSet(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_ = datas_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = deleteKeys_.getUnmodifiableView();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.StringSet) {
          return mergeFrom((com.yorha.proto.Basic.StringSet)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.StringSet other) {
        if (other == com.yorha.proto.Basic.StringSet.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.StringSet parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.StringSet) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.LazyStringList datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = new com.google.protobuf.LazyStringArrayList(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return A list containing the datas.
       */
      public com.google.protobuf.ProtocolStringList
          getDatasList() {
        return datas_.getUnmodifiableView();
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public java.lang.String getDatas(int index) {
        return datas_.get(index);
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index of the value to return.
       * @return The bytes of the datas at the given index.
       */
      public com.google.protobuf.ByteString
          getDatasBytes(int index) {
        return datas_.getByteString(index);
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<java.lang.String> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string datas = 1;</code>
       * @param value The bytes of the datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatasBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDatasIsMutable();
        datas_.add(value);
        onChanged();
        return this;
      }

      private com.google.protobuf.LazyStringList deleteKeys_ = com.google.protobuf.LazyStringArrayList.EMPTY;
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = new com.google.protobuf.LazyStringArrayList(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public com.google.protobuf.ProtocolStringList
          getDeleteKeysList() {
        return deleteKeys_.getUnmodifiableView();
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public java.lang.String getDeleteKeys(int index) {
        return deleteKeys_.get(index);
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param index The index of the value to return.
       * @return The bytes of the deleteKeys at the given index.
       */
      public com.google.protobuf.ByteString
          getDeleteKeysBytes(int index) {
        return deleteKeys_.getByteString(index);
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDeleteKeysIsMutable();
        deleteKeys_.set(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDeleteKeysIsMutable();
        deleteKeys_.add(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<java.lang.String> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = com.google.protobuf.LazyStringArrayList.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }
      /**
       * <code>repeated string deleteKeys = 2;</code>
       * @param value The bytes of the deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeysBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  ensureDeleteKeysIsMutable();
        deleteKeys_.add(value);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.StringSet)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.StringSet)
    private static final com.yorha.proto.Basic.StringSet DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.StringSet();
    }

    public static com.yorha.proto.Basic.StringSet getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<StringSet>
        PARSER = new com.google.protobuf.AbstractParser<StringSet>() {
      @java.lang.Override
      public StringSet parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new StringSet(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<StringSet> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<StringSet> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.StringSet getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Int64SetOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Int64Set)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 datas = 1;</code>
     * @return A list containing the datas.
     */
    java.util.List<java.lang.Long> getDatasList();
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return The count of datas.
     */
    int getDatasCount();
    /**
     * <code>repeated int64 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    long getDatas(int index);

    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    java.util.List<java.lang.Long> getDeleteKeysList();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    int getDeleteKeysCount();
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    long getDeleteKeys(int index);

    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    boolean hasClearFlag();
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    boolean getClearFlag();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Int64Set}
   */
  public static final class Int64Set extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Int64Set)
      Int64SetOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Int64Set.newBuilder() to construct.
    private Int64Set(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Int64Set() {
      datas_ = emptyLongList();
      deleteKeys_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Int64Set();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Int64Set(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                datas_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              datas_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                datas_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                datas_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 16: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              deleteKeys_.addLong(input.readInt64());
              break;
            }
            case 18: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000002) != 0) && input.getBytesUntilLimit() > 0) {
                deleteKeys_ = newLongList();
                mutable_bitField0_ |= 0x00000002;
              }
              while (input.getBytesUntilLimit() > 0) {
                deleteKeys_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            case 24: {
              bitField0_ |= 0x00000001;
              clearFlag_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable(); // C
        }
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64Set_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64Set_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Basic.Int64Set.class, com.yorha.proto.Basic.Int64Set.Builder.class);
    }

    private int bitField0_;
    public static final int DATAS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList datas_;
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return A list containing the datas.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDatasList() {
      return datas_;
    }
    /**
     * <code>repeated int64 datas = 1;</code>
     * @return The count of datas.
     */
    public int getDatasCount() {
      return datas_.size();
    }
    /**
     * <code>repeated int64 datas = 1;</code>
     * @param index The index of the element to return.
     * @return The datas at the given index.
     */
    public long getDatas(int index) {
      return datas_.getLong(index);
    }

    public static final int DELETEKEYS_FIELD_NUMBER = 2;
    private com.google.protobuf.Internal.LongList deleteKeys_;
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return A list containing the deleteKeys.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getDeleteKeysList() {
      return deleteKeys_;
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @return The count of deleteKeys.
     */
    public int getDeleteKeysCount() {
      return deleteKeys_.size();
    }
    /**
     * <code>repeated int64 deleteKeys = 2;</code>
     * @param index The index of the element to return.
     * @return The deleteKeys at the given index.
     */
    public long getDeleteKeys(int index) {
      return deleteKeys_.getLong(index);
    }

    public static final int CLEARFLAG_FIELD_NUMBER = 3;
    private boolean clearFlag_;
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return Whether the clearFlag field is set.
     */
    @java.lang.Override
    public boolean hasClearFlag() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool clearFlag = 3;</code>
     * @return The clearFlag.
     */
    @java.lang.Override
    public boolean getClearFlag() {
      return clearFlag_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < datas_.size(); i++) {
        output.writeInt64(1, datas_.getLong(i));
      }
      for (int i = 0; i < deleteKeys_.size(); i++) {
        output.writeInt64(2, deleteKeys_.getLong(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(3, clearFlag_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < datas_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(datas_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDatasList().size();
      }
      {
        int dataSize = 0;
        for (int i = 0; i < deleteKeys_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(deleteKeys_.getLong(i));
        }
        size += dataSize;
        size += 1 * getDeleteKeysList().size();
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, clearFlag_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Basic.Int64Set)) {
        return super.equals(obj);
      }
      com.yorha.proto.Basic.Int64Set other = (com.yorha.proto.Basic.Int64Set) obj;

      if (!getDatasList()
          .equals(other.getDatasList())) return false;
      if (!getDeleteKeysList()
          .equals(other.getDeleteKeysList())) return false;
      if (hasClearFlag() != other.hasClearFlag()) return false;
      if (hasClearFlag()) {
        if (getClearFlag()
            != other.getClearFlag()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getDatasCount() > 0) {
        hash = (37 * hash) + DATAS_FIELD_NUMBER;
        hash = (53 * hash) + getDatasList().hashCode();
      }
      if (getDeleteKeysCount() > 0) {
        hash = (37 * hash) + DELETEKEYS_FIELD_NUMBER;
        hash = (53 * hash) + getDeleteKeysList().hashCode();
      }
      if (hasClearFlag()) {
        hash = (37 * hash) + CLEARFLAG_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getClearFlag());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Basic.Int64Set parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64Set parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64Set parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Basic.Int64Set parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Basic.Int64Set prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Int64Set}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Int64Set)
        com.yorha.proto.Basic.Int64SetOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64Set_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64Set_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Basic.Int64Set.class, com.yorha.proto.Basic.Int64Set.Builder.class);
      }

      // Construct using com.yorha.proto.Basic.Int64Set.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        datas_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        clearFlag_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Basic.internal_static_com_yorha_proto_Int64Set_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64Set getDefaultInstanceForType() {
        return com.yorha.proto.Basic.Int64Set.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64Set build() {
        com.yorha.proto.Basic.Int64Set result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Basic.Int64Set buildPartial() {
        com.yorha.proto.Basic.Int64Set result = new com.yorha.proto.Basic.Int64Set(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((bitField0_ & 0x00000001) != 0)) {
          datas_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.datas_ = datas_;
        if (((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000002);
        }
        result.deleteKeys_ = deleteKeys_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.clearFlag_ = clearFlag_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Basic.Int64Set) {
          return mergeFrom((com.yorha.proto.Basic.Int64Set)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Basic.Int64Set other) {
        if (other == com.yorha.proto.Basic.Int64Set.getDefaultInstance()) return this;
        if (!other.datas_.isEmpty()) {
          if (datas_.isEmpty()) {
            datas_ = other.datas_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureDatasIsMutable();
            datas_.addAll(other.datas_);
          }
          onChanged();
        }
        if (!other.deleteKeys_.isEmpty()) {
          if (deleteKeys_.isEmpty()) {
            deleteKeys_ = other.deleteKeys_;
            bitField0_ = (bitField0_ & ~0x00000002);
          } else {
            ensureDeleteKeysIsMutable();
            deleteKeys_.addAll(other.deleteKeys_);
          }
          onChanged();
        }
        if (other.hasClearFlag()) {
          setClearFlag(other.getClearFlag());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Basic.Int64Set parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Basic.Int64Set) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList datas_ = emptyLongList();
      private void ensureDatasIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          datas_ = mutableCopy(datas_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return A list containing the datas.
       */
      public java.util.List<java.lang.Long>
          getDatasList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(datas_) : datas_;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return The count of datas.
       */
      public int getDatasCount() {
        return datas_.size();
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param index The index of the element to return.
       * @return The datas at the given index.
       */
      public long getDatas(int index) {
        return datas_.getLong(index);
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param index The index to set the value at.
       * @param value The datas to set.
       * @return This builder for chaining.
       */
      public Builder setDatas(
          int index, long value) {
        ensureDatasIsMutable();
        datas_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param value The datas to add.
       * @return This builder for chaining.
       */
      public Builder addDatas(long value) {
        ensureDatasIsMutable();
        datas_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @param values The datas to add.
       * @return This builder for chaining.
       */
      public Builder addAllDatas(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDatasIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, datas_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 datas = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearDatas() {
        datas_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }

      private com.google.protobuf.Internal.LongList deleteKeys_ = emptyLongList();
      private void ensureDeleteKeysIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          deleteKeys_ = mutableCopy(deleteKeys_);
          bitField0_ |= 0x00000002;
         }
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return A list containing the deleteKeys.
       */
      public java.util.List<java.lang.Long>
          getDeleteKeysList() {
        return ((bitField0_ & 0x00000002) != 0) ?
                 java.util.Collections.unmodifiableList(deleteKeys_) : deleteKeys_;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return The count of deleteKeys.
       */
      public int getDeleteKeysCount() {
        return deleteKeys_.size();
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index of the element to return.
       * @return The deleteKeys at the given index.
       */
      public long getDeleteKeys(int index) {
        return deleteKeys_.getLong(index);
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param index The index to set the value at.
       * @param value The deleteKeys to set.
       * @return This builder for chaining.
       */
      public Builder setDeleteKeys(
          int index, long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param value The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addDeleteKeys(long value) {
        ensureDeleteKeysIsMutable();
        deleteKeys_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @param values The deleteKeys to add.
       * @return This builder for chaining.
       */
      public Builder addAllDeleteKeys(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureDeleteKeysIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, deleteKeys_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 deleteKeys = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDeleteKeys() {
        deleteKeys_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000002);
        onChanged();
        return this;
      }

      private boolean clearFlag_ ;
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return Whether the clearFlag field is set.
       */
      @java.lang.Override
      public boolean hasClearFlag() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return The clearFlag.
       */
      @java.lang.Override
      public boolean getClearFlag() {
        return clearFlag_;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @param value The clearFlag to set.
       * @return This builder for chaining.
       */
      public Builder setClearFlag(boolean value) {
        bitField0_ |= 0x00000004;
        clearFlag_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool clearFlag = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClearFlag() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clearFlag_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Int64Set)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Int64Set)
    private static final com.yorha.proto.Basic.Int64Set DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Basic.Int64Set();
    }

    public static com.yorha.proto.Basic.Int64Set getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Int64Set>
        PARSER = new com.google.protobuf.AbstractParser<Int64Set>() {
      @java.lang.Override
      public Int64Set parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Int64Set(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Int64Set> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Int64Set> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Basic.Int64Set getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32List_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32List_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64List_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64List_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int32Set_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int32Set_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_StringList_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_StringList_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_StringSet_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_StringSet_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Int64Set_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Int64Set_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\034ss_proto/gen/cnc/basic.proto\022\017com.yorh" +
      "a.proto\"\032\n\tInt32List\022\r\n\005datas\030\001 \003(\005\"\032\n\tI" +
      "nt64List\022\r\n\005datas\030\001 \003(\003\"@\n\010Int32Set\022\r\n\005d" +
      "atas\030\001 \003(\005\022\022\n\ndeleteKeys\030\002 \003(\005\022\021\n\tclearF" +
      "lag\030\003 \001(\010\"\033\n\nStringList\022\r\n\005datas\030\001 \003(\t\"A" +
      "\n\tStringSet\022\r\n\005datas\030\001 \003(\t\022\022\n\ndeleteKeys" +
      "\030\002 \003(\t\022\021\n\tclearFlag\030\003 \001(\010\"@\n\010Int64Set\022\r\n" +
      "\005datas\030\001 \003(\003\022\022\n\ndeleteKeys\030\002 \003(\003\022\021\n\tclea" +
      "rFlag\030\003 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_Int32List_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Int32List_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32List_descriptor,
        new java.lang.String[] { "Datas", });
    internal_static_com_yorha_proto_Int64List_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Int64List_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64List_descriptor,
        new java.lang.String[] { "Datas", });
    internal_static_com_yorha_proto_Int32Set_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Int32Set_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int32Set_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_StringList_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_StringList_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_StringList_descriptor,
        new java.lang.String[] { "Datas", });
    internal_static_com_yorha_proto_StringSet_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_StringSet_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_StringSet_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
    internal_static_com_yorha_proto_Int64Set_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Int64Set_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Int64Set_descriptor,
        new java.lang.String[] { "Datas", "DeleteKeys", "ClearFlag", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

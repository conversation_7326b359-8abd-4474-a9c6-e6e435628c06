// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/gate/ss_gate.proto

package com.yorha.proto;

public final class SsGate {
  private SsGate() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ShutdownCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ShutdownCmd)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.ShutdownCmd}
   */
  public static final class ShutdownCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ShutdownCmd)
      ShutdownCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ShutdownCmd.newBuilder() to construct.
    private ShutdownCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ShutdownCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ShutdownCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ShutdownCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_ShutdownCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_ShutdownCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGate.ShutdownCmd.class, com.yorha.proto.SsGate.ShutdownCmd.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGate.ShutdownCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGate.ShutdownCmd other = (com.yorha.proto.SsGate.ShutdownCmd) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.ShutdownCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGate.ShutdownCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ShutdownCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ShutdownCmd)
        com.yorha.proto.SsGate.ShutdownCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_ShutdownCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_ShutdownCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGate.ShutdownCmd.class, com.yorha.proto.SsGate.ShutdownCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGate.ShutdownCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_ShutdownCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.ShutdownCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGate.ShutdownCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.ShutdownCmd build() {
        com.yorha.proto.SsGate.ShutdownCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.ShutdownCmd buildPartial() {
        com.yorha.proto.SsGate.ShutdownCmd result = new com.yorha.proto.SsGate.ShutdownCmd(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGate.ShutdownCmd) {
          return mergeFrom((com.yorha.proto.SsGate.ShutdownCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGate.ShutdownCmd other) {
        if (other == com.yorha.proto.SsGate.ShutdownCmd.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGate.ShutdownCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGate.ShutdownCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ShutdownCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ShutdownCmd)
    private static final com.yorha.proto.SsGate.ShutdownCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGate.ShutdownCmd();
    }

    public static com.yorha.proto.SsGate.ShutdownCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ShutdownCmd>
        PARSER = new com.google.protobuf.AbstractParser<ShutdownCmd>() {
      @java.lang.Override
      public ShutdownCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ShutdownCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ShutdownCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ShutdownCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGate.ShutdownCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BroadcastAllCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BroadcastAllCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BroadcastAllCmd}
   */
  public static final class BroadcastAllCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BroadcastAllCmd)
      BroadcastAllCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BroadcastAllCmd.newBuilder() to construct.
    private BroadcastAllCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BroadcastAllCmd() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BroadcastAllCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BroadcastAllCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastAllCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastAllCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGate.BroadcastAllCmd.class, com.yorha.proto.SsGate.BroadcastAllCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGate.BroadcastAllCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGate.BroadcastAllCmd other = (com.yorha.proto.SsGate.BroadcastAllCmd) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastAllCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGate.BroadcastAllCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BroadcastAllCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BroadcastAllCmd)
        com.yorha.proto.SsGate.BroadcastAllCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastAllCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastAllCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGate.BroadcastAllCmd.class, com.yorha.proto.SsGate.BroadcastAllCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGate.BroadcastAllCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastAllCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastAllCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGate.BroadcastAllCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastAllCmd build() {
        com.yorha.proto.SsGate.BroadcastAllCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastAllCmd buildPartial() {
        com.yorha.proto.SsGate.BroadcastAllCmd result = new com.yorha.proto.SsGate.BroadcastAllCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGate.BroadcastAllCmd) {
          return mergeFrom((com.yorha.proto.SsGate.BroadcastAllCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGate.BroadcastAllCmd other) {
        if (other == com.yorha.proto.SsGate.BroadcastAllCmd.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGate.BroadcastAllCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGate.BroadcastAllCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BroadcastAllCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BroadcastAllCmd)
    private static final com.yorha.proto.SsGate.BroadcastAllCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGate.BroadcastAllCmd();
    }

    public static com.yorha.proto.SsGate.BroadcastAllCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BroadcastAllCmd>
        PARSER = new com.google.protobuf.AbstractParser<BroadcastAllCmd>() {
      @java.lang.Override
      public BroadcastAllCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BroadcastAllCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BroadcastAllCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BroadcastAllCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGate.BroadcastAllCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BroadcastOnlinePlayerSsCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BroadcastOnlinePlayerSsCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BroadcastOnlinePlayerSsCmd}
   */
  public static final class BroadcastOnlinePlayerSsCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BroadcastOnlinePlayerSsCmd)
      BroadcastOnlinePlayerSsCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BroadcastOnlinePlayerSsCmd.newBuilder() to construct.
    private BroadcastOnlinePlayerSsCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BroadcastOnlinePlayerSsCmd() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BroadcastOnlinePlayerSsCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BroadcastOnlinePlayerSsCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.class, com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd other = (com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BroadcastOnlinePlayerSsCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BroadcastOnlinePlayerSsCmd)
        com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.class, com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGate.internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd build() {
        com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd buildPartial() {
        com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd result = new com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd) {
          return mergeFrom((com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd other) {
        if (other == com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BroadcastOnlinePlayerSsCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BroadcastOnlinePlayerSsCmd)
    private static final com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd();
    }

    public static com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BroadcastOnlinePlayerSsCmd>
        PARSER = new com.google.protobuf.AbstractParser<BroadcastOnlinePlayerSsCmd>() {
      @java.lang.Override
      public BroadcastOnlinePlayerSsCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BroadcastOnlinePlayerSsCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BroadcastOnlinePlayerSsCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BroadcastOnlinePlayerSsCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGate.BroadcastOnlinePlayerSsCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ShutdownCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ShutdownCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BroadcastAllCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BroadcastAllCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037ss_proto/gen/gate/ss_gate.proto\022\017com.y" +
      "orha.proto\"\r\n\013ShutdownCmd\"4\n\017BroadcastAl" +
      "lCmd\022\017\n\007msgType\030\001 \001(\005\022\020\n\010msgBytes\030\002 \001(\014\"" +
      "?\n\032BroadcastOnlinePlayerSsCmd\022\017\n\007msgType" +
      "\030\001 \001(\005\022\020\n\010msgBytes\030\002 \001(\014B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_ShutdownCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ShutdownCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ShutdownCmd_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_BroadcastAllCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_BroadcastAllCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BroadcastAllCmd_descriptor,
        new java.lang.String[] { "MsgType", "MsgBytes", });
    internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BroadcastOnlinePlayerSsCmd_descriptor,
        new java.lang.String[] { "MsgType", "MsgBytes", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

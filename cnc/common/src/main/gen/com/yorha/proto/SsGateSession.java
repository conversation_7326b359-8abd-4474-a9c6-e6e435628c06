// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/gateSession/ss_gate_session.proto

package com.yorha.proto;

public final class SsGateSession {
  private SsGateSession() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SendMsgToSessionCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendMsgToSessionCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();

    /**
     * <code>optional int32 seqId = 3;</code>
     * @return Whether the seqId field is set.
     */
    boolean hasSeqId();
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return The seqId.
     */
    int getSeqId();

    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendMsgToSessionCmd}
   */
  public static final class SendMsgToSessionCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendMsgToSessionCmd)
      SendMsgToSessionCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendMsgToSessionCmd.newBuilder() to construct.
    private SendMsgToSessionCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendMsgToSessionCmd() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendMsgToSessionCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendMsgToSessionCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              seqId_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.SendMsgToSessionCmd.class, com.yorha.proto.SsGateSession.SendMsgToSessionCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    public static final int SEQID_FIELD_NUMBER = 3;
    private int seqId_;
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return Whether the seqId field is set.
     */
    @java.lang.Override
    public boolean hasSeqId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public int getSeqId() {
      return seqId_;
    }

    public static final int CODE_FIELD_NUMBER = 4;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, seqId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getCode());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, seqId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCode());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.SendMsgToSessionCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.SendMsgToSessionCmd other = (com.yorha.proto.SsGateSession.SendMsgToSessionCmd) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (hasSeqId() != other.hasSeqId()) return false;
      if (hasSeqId()) {
        if (getSeqId()
            != other.getSeqId()) return false;
      }
      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      if (hasSeqId()) {
        hash = (37 * hash) + SEQID_FIELD_NUMBER;
        hash = (53 * hash) + getSeqId();
      }
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.SendMsgToSessionCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendMsgToSessionCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendMsgToSessionCmd)
        com.yorha.proto.SsGateSession.SendMsgToSessionCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.SendMsgToSessionCmd.class, com.yorha.proto.SsGateSession.SendMsgToSessionCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.SendMsgToSessionCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        seqId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.SendMsgToSessionCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionCmd build() {
        com.yorha.proto.SsGateSession.SendMsgToSessionCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionCmd buildPartial() {
        com.yorha.proto.SsGateSession.SendMsgToSessionCmd result = new com.yorha.proto.SsGateSession.SendMsgToSessionCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seqId_ = seqId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.SendMsgToSessionCmd) {
          return mergeFrom((com.yorha.proto.SsGateSession.SendMsgToSessionCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.SendMsgToSessionCmd other) {
        if (other == com.yorha.proto.SsGateSession.SendMsgToSessionCmd.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        if (other.hasSeqId()) {
          setSeqId(other.getSeqId());
        }
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.SendMsgToSessionCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.SendMsgToSessionCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }

      private int seqId_ ;
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return Whether the seqId field is set.
       */
      @java.lang.Override
      public boolean hasSeqId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return The seqId.
       */
      @java.lang.Override
      public int getSeqId() {
        return seqId_;
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @param value The seqId to set.
       * @return This builder for chaining.
       */
      public Builder setSeqId(int value) {
        bitField0_ |= 0x00000004;
        seqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeqId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seqId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendMsgToSessionCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendMsgToSessionCmd)
    private static final com.yorha.proto.SsGateSession.SendMsgToSessionCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.SendMsgToSessionCmd();
    }

    public static com.yorha.proto.SsGateSession.SendMsgToSessionCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendMsgToSessionCmd>
        PARSER = new com.google.protobuf.AbstractParser<SendMsgToSessionCmd>() {
      @java.lang.Override
      public SendMsgToSessionCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendMsgToSessionCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendMsgToSessionCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendMsgToSessionCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.SendMsgToSessionCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PlayerBoundCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PlayerBoundCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();

    /**
     * <code>optional string openId = 2;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 2;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 2;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional string playerName = 4;</code>
     * @return Whether the playerName field is set.
     */
    boolean hasPlayerName();
    /**
     * <code>optional string playerName = 4;</code>
     * @return The playerName.
     */
    java.lang.String getPlayerName();
    /**
     * <code>optional string playerName = 4;</code>
     * @return The bytes for playerName.
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();

    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PlayerBoundCmd}
   */
  public static final class PlayerBoundCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PlayerBoundCmd)
      PlayerBoundCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PlayerBoundCmd.newBuilder() to construct.
    private PlayerBoundCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PlayerBoundCmd() {
      openId_ = "";
      playerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PlayerBoundCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PlayerBoundCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              openId_ = bs;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              playerName_ = bs;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_PlayerBoundCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_PlayerBoundCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.PlayerBoundCmd.class, com.yorha.proto.SsGateSession.PlayerBoundCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CODE_FIELD_NUMBER = 1;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    public static final int OPENID_FIELD_NUMBER = 2;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 2;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string openId = 2;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 2;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int PLAYERNAME_FIELD_NUMBER = 4;
    private volatile java.lang.Object playerName_;
    /**
     * <code>optional string playerName = 4;</code>
     * @return Whether the playerName field is set.
     */
    @java.lang.Override
    public boolean hasPlayerName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string playerName = 4;</code>
     * @return The playerName.
     */
    @java.lang.Override
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          playerName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string playerName = 4;</code>
     * @return The bytes for playerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ZONEID_FIELD_NUMBER = 5;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional int32 zoneId = 5;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCode());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, openId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, playerName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCode());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, openId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, playerName_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.PlayerBoundCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.PlayerBoundCmd other = (com.yorha.proto.SsGateSession.PlayerBoundCmd) obj;

      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasPlayerName() != other.hasPlayerName()) return false;
      if (hasPlayerName()) {
        if (!getPlayerName()
            .equals(other.getPlayerName())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasPlayerName()) {
        hash = (37 * hash) + PLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerName().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.PlayerBoundCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.PlayerBoundCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PlayerBoundCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PlayerBoundCmd)
        com.yorha.proto.SsGateSession.PlayerBoundCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_PlayerBoundCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_PlayerBoundCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.PlayerBoundCmd.class, com.yorha.proto.SsGateSession.PlayerBoundCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.PlayerBoundCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        playerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_PlayerBoundCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.PlayerBoundCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.PlayerBoundCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.PlayerBoundCmd build() {
        com.yorha.proto.SsGateSession.PlayerBoundCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.PlayerBoundCmd buildPartial() {
        com.yorha.proto.SsGateSession.PlayerBoundCmd result = new com.yorha.proto.SsGateSession.PlayerBoundCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.playerName_ = playerName_;
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.PlayerBoundCmd) {
          return mergeFrom((com.yorha.proto.SsGateSession.PlayerBoundCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.PlayerBoundCmd other) {
        if (other == com.yorha.proto.SsGateSession.PlayerBoundCmd.getDefaultInstance()) return this;
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000002;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasPlayerName()) {
          bitField0_ |= 0x00000008;
          playerName_ = other.playerName_;
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.PlayerBoundCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.PlayerBoundCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 2;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 2;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 2;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object playerName_ = "";
      /**
       * <code>optional string playerName = 4;</code>
       * @return Whether the playerName field is set.
       */
      public boolean hasPlayerName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string playerName = 4;</code>
       * @return The playerName.
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            playerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string playerName = 4;</code>
       * @return The bytes for playerName.
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string playerName = 4;</code>
       * @param value The playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string playerName = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string playerName = 4;</code>
       * @param value The bytes for playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        playerName_ = value;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000010;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PlayerBoundCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PlayerBoundCmd)
    private static final com.yorha.proto.SsGateSession.PlayerBoundCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.PlayerBoundCmd();
    }

    public static com.yorha.proto.SsGateSession.PlayerBoundCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PlayerBoundCmd>
        PARSER = new com.google.protobuf.AbstractParser<PlayerBoundCmd>() {
      @java.lang.Override
      public PlayerBoundCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PlayerBoundCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PlayerBoundCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PlayerBoundCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.PlayerBoundCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateClientInfoCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdateClientInfoCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     * @return Whether the newClientInfo field is set.
     */
    boolean hasNewClientInfo();
    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     * @return The newClientInfo.
     */
    com.yorha.proto.CommonMsg.ClientInfo getNewClientInfo();
    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     */
    com.yorha.proto.CommonMsg.ClientInfoOrBuilder getNewClientInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdateClientInfoCmd}
   */
  public static final class UpdateClientInfoCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdateClientInfoCmd)
      UpdateClientInfoCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateClientInfoCmd.newBuilder() to construct.
    private UpdateClientInfoCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateClientInfoCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateClientInfoCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateClientInfoCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ClientInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = newClientInfo_.toBuilder();
              }
              newClientInfo_ = input.readMessage(com.yorha.proto.CommonMsg.ClientInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newClientInfo_);
                newClientInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_UpdateClientInfoCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.UpdateClientInfoCmd.class, com.yorha.proto.SsGateSession.UpdateClientInfoCmd.Builder.class);
    }

    private int bitField0_;
    public static final int NEWCLIENTINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ClientInfo newClientInfo_;
    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     * @return Whether the newClientInfo field is set.
     */
    @java.lang.Override
    public boolean hasNewClientInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     * @return The newClientInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClientInfo getNewClientInfo() {
      return newClientInfo_ == null ? com.yorha.proto.CommonMsg.ClientInfo.getDefaultInstance() : newClientInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClientInfoOrBuilder getNewClientInfoOrBuilder() {
      return newClientInfo_ == null ? com.yorha.proto.CommonMsg.ClientInfo.getDefaultInstance() : newClientInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getNewClientInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getNewClientInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.UpdateClientInfoCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.UpdateClientInfoCmd other = (com.yorha.proto.SsGateSession.UpdateClientInfoCmd) obj;

      if (hasNewClientInfo() != other.hasNewClientInfo()) return false;
      if (hasNewClientInfo()) {
        if (!getNewClientInfo()
            .equals(other.getNewClientInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewClientInfo()) {
        hash = (37 * hash) + NEWCLIENTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getNewClientInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.UpdateClientInfoCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdateClientInfoCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdateClientInfoCmd)
        com.yorha.proto.SsGateSession.UpdateClientInfoCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_UpdateClientInfoCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.UpdateClientInfoCmd.class, com.yorha.proto.SsGateSession.UpdateClientInfoCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.UpdateClientInfoCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewClientInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (newClientInfoBuilder_ == null) {
          newClientInfo_ = null;
        } else {
          newClientInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.UpdateClientInfoCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.UpdateClientInfoCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.UpdateClientInfoCmd build() {
        com.yorha.proto.SsGateSession.UpdateClientInfoCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.UpdateClientInfoCmd buildPartial() {
        com.yorha.proto.SsGateSession.UpdateClientInfoCmd result = new com.yorha.proto.SsGateSession.UpdateClientInfoCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (newClientInfoBuilder_ == null) {
            result.newClientInfo_ = newClientInfo_;
          } else {
            result.newClientInfo_ = newClientInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.UpdateClientInfoCmd) {
          return mergeFrom((com.yorha.proto.SsGateSession.UpdateClientInfoCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.UpdateClientInfoCmd other) {
        if (other == com.yorha.proto.SsGateSession.UpdateClientInfoCmd.getDefaultInstance()) return this;
        if (other.hasNewClientInfo()) {
          mergeNewClientInfo(other.getNewClientInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.UpdateClientInfoCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.UpdateClientInfoCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ClientInfo newClientInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClientInfo, com.yorha.proto.CommonMsg.ClientInfo.Builder, com.yorha.proto.CommonMsg.ClientInfoOrBuilder> newClientInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       * @return Whether the newClientInfo field is set.
       */
      public boolean hasNewClientInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       * @return The newClientInfo.
       */
      public com.yorha.proto.CommonMsg.ClientInfo getNewClientInfo() {
        if (newClientInfoBuilder_ == null) {
          return newClientInfo_ == null ? com.yorha.proto.CommonMsg.ClientInfo.getDefaultInstance() : newClientInfo_;
        } else {
          return newClientInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public Builder setNewClientInfo(com.yorha.proto.CommonMsg.ClientInfo value) {
        if (newClientInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newClientInfo_ = value;
          onChanged();
        } else {
          newClientInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public Builder setNewClientInfo(
          com.yorha.proto.CommonMsg.ClientInfo.Builder builderForValue) {
        if (newClientInfoBuilder_ == null) {
          newClientInfo_ = builderForValue.build();
          onChanged();
        } else {
          newClientInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public Builder mergeNewClientInfo(com.yorha.proto.CommonMsg.ClientInfo value) {
        if (newClientInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              newClientInfo_ != null &&
              newClientInfo_ != com.yorha.proto.CommonMsg.ClientInfo.getDefaultInstance()) {
            newClientInfo_ =
              com.yorha.proto.CommonMsg.ClientInfo.newBuilder(newClientInfo_).mergeFrom(value).buildPartial();
          } else {
            newClientInfo_ = value;
          }
          onChanged();
        } else {
          newClientInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public Builder clearNewClientInfo() {
        if (newClientInfoBuilder_ == null) {
          newClientInfo_ = null;
          onChanged();
        } else {
          newClientInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClientInfo.Builder getNewClientInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewClientInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClientInfoOrBuilder getNewClientInfoOrBuilder() {
        if (newClientInfoBuilder_ != null) {
          return newClientInfoBuilder_.getMessageOrBuilder();
        } else {
          return newClientInfo_ == null ?
              com.yorha.proto.CommonMsg.ClientInfo.getDefaultInstance() : newClientInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClientInfo newClientInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClientInfo, com.yorha.proto.CommonMsg.ClientInfo.Builder, com.yorha.proto.CommonMsg.ClientInfoOrBuilder> 
          getNewClientInfoFieldBuilder() {
        if (newClientInfoBuilder_ == null) {
          newClientInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ClientInfo, com.yorha.proto.CommonMsg.ClientInfo.Builder, com.yorha.proto.CommonMsg.ClientInfoOrBuilder>(
                  getNewClientInfo(),
                  getParentForChildren(),
                  isClean());
          newClientInfo_ = null;
        }
        return newClientInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdateClientInfoCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdateClientInfoCmd)
    private static final com.yorha.proto.SsGateSession.UpdateClientInfoCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.UpdateClientInfoCmd();
    }

    public static com.yorha.proto.SsGateSession.UpdateClientInfoCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdateClientInfoCmd>
        PARSER = new com.google.protobuf.AbstractParser<UpdateClientInfoCmd>() {
      @java.lang.Override
      public UpdateClientInfoCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateClientInfoCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdateClientInfoCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateClientInfoCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.UpdateClientInfoCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendMsgToSessionWithLanguageCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendMsgToSessionWithLanguageCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    boolean hasMsgType();
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    int getMsgType();

    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    int getLanguageMapCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    boolean containsLanguageMap(
        int key);
    /**
     * Use {@link #getLanguageMapMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
    getLanguageMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
    getLanguageMapMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */

    com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrDefault(
        int key,
        com.yorha.proto.SsGateSession.LanguageMsgBytes defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */

    com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrThrow(
        int key);

    /**
     * <code>optional int32 seqId = 3;</code>
     * @return Whether the seqId field is set.
     */
    boolean hasSeqId();
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return The seqId.
     */
    int getSeqId();

    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return Whether the code field is set.
     */
    boolean hasCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return The code.
     */
    com.yorha.proto.Core.Code getCode();
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     */
    com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendMsgToSessionWithLanguageCmd}
   */
  public static final class SendMsgToSessionWithLanguageCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendMsgToSessionWithLanguageCmd)
      SendMsgToSessionWithLanguageCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendMsgToSessionWithLanguageCmd.newBuilder() to construct.
    private SendMsgToSessionWithLanguageCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendMsgToSessionWithLanguageCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendMsgToSessionWithLanguageCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendMsgToSessionWithLanguageCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              msgType_ = input.readInt32();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                languageMap_ = com.google.protobuf.MapField.newMapField(
                    LanguageMapDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000002;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
              languageMap__ = input.readMessage(
                  LanguageMapDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              languageMap_.getMutableMap().put(
                  languageMap__.getKey(), languageMap__.getValue());
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              seqId_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.Core.Code.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = code_.toBuilder();
              }
              code_ = input.readMessage(com.yorha.proto.Core.Code.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(code_);
                code_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 2:
          return internalGetLanguageMap();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.class, com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MSGTYPE_FIELD_NUMBER = 1;
    private int msgType_;
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return Whether the msgType field is set.
     */
    @java.lang.Override
    public boolean hasMsgType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 msgType = 1;</code>
     * @return The msgType.
     */
    @java.lang.Override
    public int getMsgType() {
      return msgType_;
    }

    public static final int LANGUAGEMAP_FIELD_NUMBER = 2;
    private static final class LanguageMapDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>newDefaultInstance(
                  com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.SsGateSession.LanguageMsgBytes.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> languageMap_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
    internalGetLanguageMap() {
      if (languageMap_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            LanguageMapDefaultEntryHolder.defaultEntry);
      }
      return languageMap_;
    }

    public int getLanguageMapCount() {
      return internalGetLanguageMap().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */

    @java.lang.Override
    public boolean containsLanguageMap(
        int key) {
      
      return internalGetLanguageMap().getMap().containsKey(key);
    }
    /**
     * Use {@link #getLanguageMapMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> getLanguageMap() {
      return getLanguageMapMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> getLanguageMapMap() {
      return internalGetLanguageMap().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrDefault(
        int key,
        com.yorha.proto.SsGateSession.LanguageMsgBytes defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> map =
          internalGetLanguageMap().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
     */
    @java.lang.Override

    public com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> map =
          internalGetLanguageMap().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int SEQID_FIELD_NUMBER = 3;
    private int seqId_;
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return Whether the seqId field is set.
     */
    @java.lang.Override
    public boolean hasSeqId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 seqId = 3;</code>
     * @return The seqId.
     */
    @java.lang.Override
    public int getSeqId() {
      return seqId_;
    }

    public static final int CODE_FIELD_NUMBER = 4;
    private com.yorha.proto.Core.Code code_;
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return Whether the code field is set.
     */
    @java.lang.Override
    public boolean hasCode() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     * @return The code.
     */
    @java.lang.Override
    public com.yorha.proto.Core.Code getCode() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }
    /**
     * <code>optional .com.yorha.proto.Code code = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
      return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, msgType_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetLanguageMap(),
          LanguageMapDefaultEntryHolder.defaultEntry,
          2);
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(4, getCode());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, msgType_);
      }
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> entry
           : internalGetLanguageMap().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
        languageMap__ = LanguageMapDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(2, languageMap__);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, seqId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCode());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd other = (com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd) obj;

      if (hasMsgType() != other.hasMsgType()) return false;
      if (hasMsgType()) {
        if (getMsgType()
            != other.getMsgType()) return false;
      }
      if (!internalGetLanguageMap().equals(
          other.internalGetLanguageMap())) return false;
      if (hasSeqId() != other.hasSeqId()) return false;
      if (hasSeqId()) {
        if (getSeqId()
            != other.getSeqId()) return false;
      }
      if (hasCode() != other.hasCode()) return false;
      if (hasCode()) {
        if (!getCode()
            .equals(other.getCode())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMsgType()) {
        hash = (37 * hash) + MSGTYPE_FIELD_NUMBER;
        hash = (53 * hash) + getMsgType();
      }
      if (!internalGetLanguageMap().getMap().isEmpty()) {
        hash = (37 * hash) + LANGUAGEMAP_FIELD_NUMBER;
        hash = (53 * hash) + internalGetLanguageMap().hashCode();
      }
      if (hasSeqId()) {
        hash = (37 * hash) + SEQID_FIELD_NUMBER;
        hash = (53 * hash) + getSeqId();
      }
      if (hasCode()) {
        hash = (37 * hash) + CODE_FIELD_NUMBER;
        hash = (53 * hash) + getCode().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendMsgToSessionWithLanguageCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendMsgToSessionWithLanguageCmd)
        com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetLanguageMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 2:
            return internalGetMutableLanguageMap();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.class, com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCodeFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        msgType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        internalGetMutableLanguageMap().clear();
        seqId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (codeBuilder_ == null) {
          code_ = null;
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd build() {
        com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd buildPartial() {
        com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd result = new com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.msgType_ = msgType_;
          to_bitField0_ |= 0x00000001;
        }
        result.languageMap_ = internalGetLanguageMap();
        result.languageMap_.makeImmutable();
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.seqId_ = seqId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (codeBuilder_ == null) {
            result.code_ = code_;
          } else {
            result.code_ = codeBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd) {
          return mergeFrom((com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd other) {
        if (other == com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd.getDefaultInstance()) return this;
        if (other.hasMsgType()) {
          setMsgType(other.getMsgType());
        }
        internalGetMutableLanguageMap().mergeFrom(
            other.internalGetLanguageMap());
        if (other.hasSeqId()) {
          setSeqId(other.getSeqId());
        }
        if (other.hasCode()) {
          mergeCode(other.getCode());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int msgType_ ;
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return Whether the msgType field is set.
       */
      @java.lang.Override
      public boolean hasMsgType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return The msgType.
       */
      @java.lang.Override
      public int getMsgType() {
        return msgType_;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @param value The msgType to set.
       * @return This builder for chaining.
       */
      public Builder setMsgType(int value) {
        bitField0_ |= 0x00000001;
        msgType_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 msgType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        msgType_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> languageMap_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
      internalGetLanguageMap() {
        if (languageMap_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              LanguageMapDefaultEntryHolder.defaultEntry);
        }
        return languageMap_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
      internalGetMutableLanguageMap() {
        onChanged();;
        if (languageMap_ == null) {
          languageMap_ = com.google.protobuf.MapField.newMapField(
              LanguageMapDefaultEntryHolder.defaultEntry);
        }
        if (!languageMap_.isMutable()) {
          languageMap_ = languageMap_.copy();
        }
        return languageMap_;
      }

      public int getLanguageMapCount() {
        return internalGetLanguageMap().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */

      @java.lang.Override
      public boolean containsLanguageMap(
          int key) {
        
        return internalGetLanguageMap().getMap().containsKey(key);
      }
      /**
       * Use {@link #getLanguageMapMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> getLanguageMap() {
        return getLanguageMapMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> getLanguageMapMap() {
        return internalGetLanguageMap().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrDefault(
          int key,
          com.yorha.proto.SsGateSession.LanguageMsgBytes defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> map =
            internalGetLanguageMap().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */
      @java.lang.Override

      public com.yorha.proto.SsGateSession.LanguageMsgBytes getLanguageMapOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> map =
            internalGetLanguageMap().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearLanguageMap() {
        internalGetMutableLanguageMap().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */

      public Builder removeLanguageMap(
          int key) {
        
        internalGetMutableLanguageMap().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes>
      getMutableLanguageMap() {
        return internalGetMutableLanguageMap().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */
      public Builder putLanguageMap(
          int key,
          com.yorha.proto.SsGateSession.LanguageMsgBytes value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableLanguageMap().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.LanguageMsgBytes&gt; languageMap = 2;</code>
       */

      public Builder putAllLanguageMap(
          java.util.Map<java.lang.Integer, com.yorha.proto.SsGateSession.LanguageMsgBytes> values) {
        internalGetMutableLanguageMap().getMutableMap()
            .putAll(values);
        return this;
      }

      private int seqId_ ;
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return Whether the seqId field is set.
       */
      @java.lang.Override
      public boolean hasSeqId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return The seqId.
       */
      @java.lang.Override
      public int getSeqId() {
        return seqId_;
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @param value The seqId to set.
       * @return This builder for chaining.
       */
      public Builder setSeqId(int value) {
        bitField0_ |= 0x00000004;
        seqId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 seqId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSeqId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        seqId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Core.Code code_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> codeBuilder_;
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       * @return Whether the code field is set.
       */
      public boolean hasCode() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       * @return The code.
       */
      public com.yorha.proto.Core.Code getCode() {
        if (codeBuilder_ == null) {
          return code_ == null ? com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        } else {
          return codeBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder setCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          code_ = value;
          onChanged();
        } else {
          codeBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder setCode(
          com.yorha.proto.Core.Code.Builder builderForValue) {
        if (codeBuilder_ == null) {
          code_ = builderForValue.build();
          onChanged();
        } else {
          codeBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder mergeCode(com.yorha.proto.Core.Code value) {
        if (codeBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              code_ != null &&
              code_ != com.yorha.proto.Core.Code.getDefaultInstance()) {
            code_ =
              com.yorha.proto.Core.Code.newBuilder(code_).mergeFrom(value).buildPartial();
          } else {
            code_ = value;
          }
          onChanged();
        } else {
          codeBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public Builder clearCode() {
        if (codeBuilder_ == null) {
          code_ = null;
          onChanged();
        } else {
          codeBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public com.yorha.proto.Core.Code.Builder getCodeBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCodeFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      public com.yorha.proto.Core.CodeOrBuilder getCodeOrBuilder() {
        if (codeBuilder_ != null) {
          return codeBuilder_.getMessageOrBuilder();
        } else {
          return code_ == null ?
              com.yorha.proto.Core.Code.getDefaultInstance() : code_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.Code code = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder> 
          getCodeFieldBuilder() {
        if (codeBuilder_ == null) {
          codeBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Core.Code, com.yorha.proto.Core.Code.Builder, com.yorha.proto.Core.CodeOrBuilder>(
                  getCode(),
                  getParentForChildren(),
                  isClean());
          code_ = null;
        }
        return codeBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendMsgToSessionWithLanguageCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendMsgToSessionWithLanguageCmd)
    private static final com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd();
    }

    public static com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendMsgToSessionWithLanguageCmd>
        PARSER = new com.google.protobuf.AbstractParser<SendMsgToSessionWithLanguageCmd>() {
      @java.lang.Override
      public SendMsgToSessionWithLanguageCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendMsgToSessionWithLanguageCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendMsgToSessionWithLanguageCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendMsgToSessionWithLanguageCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.SendMsgToSessionWithLanguageCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface LanguageMsgBytesOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.LanguageMsgBytes)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 languageId = 1;</code>
     * @return Whether the languageId field is set.
     */
    boolean hasLanguageId();
    /**
     * <code>optional int32 languageId = 1;</code>
     * @return The languageId.
     */
    int getLanguageId();

    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    boolean hasMsgBytes();
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    com.google.protobuf.ByteString getMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.LanguageMsgBytes}
   */
  public static final class LanguageMsgBytes extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.LanguageMsgBytes)
      LanguageMsgBytesOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use LanguageMsgBytes.newBuilder() to construct.
    private LanguageMsgBytes(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private LanguageMsgBytes() {
      msgBytes_ = com.google.protobuf.ByteString.EMPTY;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new LanguageMsgBytes();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private LanguageMsgBytes(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              languageId_ = input.readInt32();
              break;
            }
            case 18: {
              bitField0_ |= 0x00000002;
              msgBytes_ = input.readBytes();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_LanguageMsgBytes_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_LanguageMsgBytes_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.LanguageMsgBytes.class, com.yorha.proto.SsGateSession.LanguageMsgBytes.Builder.class);
    }

    private int bitField0_;
    public static final int LANGUAGEID_FIELD_NUMBER = 1;
    private int languageId_;
    /**
     * <code>optional int32 languageId = 1;</code>
     * @return Whether the languageId field is set.
     */
    @java.lang.Override
    public boolean hasLanguageId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 languageId = 1;</code>
     * @return The languageId.
     */
    @java.lang.Override
    public int getLanguageId() {
      return languageId_;
    }

    public static final int MSGBYTES_FIELD_NUMBER = 2;
    private com.google.protobuf.ByteString msgBytes_;
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return Whether the msgBytes field is set.
     */
    @java.lang.Override
    public boolean hasMsgBytes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional bytes msgBytes = 2;</code>
     * @return The msgBytes.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString getMsgBytes() {
      return msgBytes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, languageId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBytes(2, msgBytes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, languageId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBytesSize(2, msgBytes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.LanguageMsgBytes)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.LanguageMsgBytes other = (com.yorha.proto.SsGateSession.LanguageMsgBytes) obj;

      if (hasLanguageId() != other.hasLanguageId()) return false;
      if (hasLanguageId()) {
        if (getLanguageId()
            != other.getLanguageId()) return false;
      }
      if (hasMsgBytes() != other.hasMsgBytes()) return false;
      if (hasMsgBytes()) {
        if (!getMsgBytes()
            .equals(other.getMsgBytes())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasLanguageId()) {
        hash = (37 * hash) + LANGUAGEID_FIELD_NUMBER;
        hash = (53 * hash) + getLanguageId();
      }
      if (hasMsgBytes()) {
        hash = (37 * hash) + MSGBYTES_FIELD_NUMBER;
        hash = (53 * hash) + getMsgBytes().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.LanguageMsgBytes parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.LanguageMsgBytes prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.LanguageMsgBytes}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.LanguageMsgBytes)
        com.yorha.proto.SsGateSession.LanguageMsgBytesOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_LanguageMsgBytes_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_LanguageMsgBytes_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.LanguageMsgBytes.class, com.yorha.proto.SsGateSession.LanguageMsgBytes.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.LanguageMsgBytes.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        languageId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        msgBytes_ = com.google.protobuf.ByteString.EMPTY;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_LanguageMsgBytes_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.LanguageMsgBytes getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.LanguageMsgBytes.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.LanguageMsgBytes build() {
        com.yorha.proto.SsGateSession.LanguageMsgBytes result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.LanguageMsgBytes buildPartial() {
        com.yorha.proto.SsGateSession.LanguageMsgBytes result = new com.yorha.proto.SsGateSession.LanguageMsgBytes(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.languageId_ = languageId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.msgBytes_ = msgBytes_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.LanguageMsgBytes) {
          return mergeFrom((com.yorha.proto.SsGateSession.LanguageMsgBytes)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.LanguageMsgBytes other) {
        if (other == com.yorha.proto.SsGateSession.LanguageMsgBytes.getDefaultInstance()) return this;
        if (other.hasLanguageId()) {
          setLanguageId(other.getLanguageId());
        }
        if (other.hasMsgBytes()) {
          setMsgBytes(other.getMsgBytes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.LanguageMsgBytes parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.LanguageMsgBytes) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int languageId_ ;
      /**
       * <code>optional int32 languageId = 1;</code>
       * @return Whether the languageId field is set.
       */
      @java.lang.Override
      public boolean hasLanguageId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 languageId = 1;</code>
       * @return The languageId.
       */
      @java.lang.Override
      public int getLanguageId() {
        return languageId_;
      }
      /**
       * <code>optional int32 languageId = 1;</code>
       * @param value The languageId to set.
       * @return This builder for chaining.
       */
      public Builder setLanguageId(int value) {
        bitField0_ |= 0x00000001;
        languageId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 languageId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearLanguageId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        languageId_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.ByteString msgBytes_ = com.google.protobuf.ByteString.EMPTY;
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return Whether the msgBytes field is set.
       */
      @java.lang.Override
      public boolean hasMsgBytes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return The msgBytes.
       */
      @java.lang.Override
      public com.google.protobuf.ByteString getMsgBytes() {
        return msgBytes_;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @param value The msgBytes to set.
       * @return This builder for chaining.
       */
      public Builder setMsgBytes(com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        msgBytes_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bytes msgBytes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMsgBytes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        msgBytes_ = getDefaultInstance().getMsgBytes();
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.LanguageMsgBytes)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.LanguageMsgBytes)
    private static final com.yorha.proto.SsGateSession.LanguageMsgBytes DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.LanguageMsgBytes();
    }

    public static com.yorha.proto.SsGateSession.LanguageMsgBytes getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<LanguageMsgBytes>
        PARSER = new com.google.protobuf.AbstractParser<LanguageMsgBytes>() {
      @java.lang.Override
      public LanguageMsgBytes parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new LanguageMsgBytes(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<LanguageMsgBytes> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<LanguageMsgBytes> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.LanguageMsgBytes getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KickOffSessionCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KickOffSessionCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
     * @return Whether the reason field is set.
     */
    boolean hasReason();
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
     * @return The reason.
     */
    com.yorha.proto.CommonEnum.SessionCloseReason getReason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KickOffSessionCmd}
   */
  public static final class KickOffSessionCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KickOffSessionCmd)
      KickOffSessionCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KickOffSessionCmd.newBuilder() to construct.
    private KickOffSessionCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KickOffSessionCmd() {
      reason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KickOffSessionCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KickOffSessionCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.SessionCloseReason value = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                reason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_KickOffSessionCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_KickOffSessionCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsGateSession.KickOffSessionCmd.class, com.yorha.proto.SsGateSession.KickOffSessionCmd.Builder.class);
    }

    private int bitField0_;
    public static final int REASON_FIELD_NUMBER = 1;
    private int reason_;
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
     * @return Whether the reason field is set.
     */
    @java.lang.Override public boolean hasReason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
     * @return The reason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.SessionCloseReason getReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(reason_);
      return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, reason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, reason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsGateSession.KickOffSessionCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsGateSession.KickOffSessionCmd other = (com.yorha.proto.SsGateSession.KickOffSessionCmd) obj;

      if (hasReason() != other.hasReason()) return false;
      if (hasReason()) {
        if (reason_ != other.reason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasReason()) {
        hash = (37 * hash) + REASON_FIELD_NUMBER;
        hash = (53 * hash) + reason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsGateSession.KickOffSessionCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsGateSession.KickOffSessionCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KickOffSessionCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KickOffSessionCmd)
        com.yorha.proto.SsGateSession.KickOffSessionCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_KickOffSessionCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_KickOffSessionCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsGateSession.KickOffSessionCmd.class, com.yorha.proto.SsGateSession.KickOffSessionCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsGateSession.KickOffSessionCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        reason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsGateSession.internal_static_com_yorha_proto_KickOffSessionCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.KickOffSessionCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsGateSession.KickOffSessionCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.KickOffSessionCmd build() {
        com.yorha.proto.SsGateSession.KickOffSessionCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsGateSession.KickOffSessionCmd buildPartial() {
        com.yorha.proto.SsGateSession.KickOffSessionCmd result = new com.yorha.proto.SsGateSession.KickOffSessionCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.reason_ = reason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsGateSession.KickOffSessionCmd) {
          return mergeFrom((com.yorha.proto.SsGateSession.KickOffSessionCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsGateSession.KickOffSessionCmd other) {
        if (other == com.yorha.proto.SsGateSession.KickOffSessionCmd.getDefaultInstance()) return this;
        if (other.hasReason()) {
          setReason(other.getReason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsGateSession.KickOffSessionCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsGateSession.KickOffSessionCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int reason_ = 0;
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
       * @return Whether the reason field is set.
       */
      @java.lang.Override public boolean hasReason() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
       * @return The reason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.SessionCloseReason getReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.SessionCloseReason result = com.yorha.proto.CommonEnum.SessionCloseReason.valueOf(reason_);
        return result == null ? com.yorha.proto.CommonEnum.SessionCloseReason.SCR_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
       * @param value The reason to set.
       * @return This builder for chaining.
       */
      public Builder setReason(com.yorha.proto.CommonEnum.SessionCloseReason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        reason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.SessionCloseReason reason = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearReason() {
        bitField0_ = (bitField0_ & ~0x00000001);
        reason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KickOffSessionCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KickOffSessionCmd)
    private static final com.yorha.proto.SsGateSession.KickOffSessionCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsGateSession.KickOffSessionCmd();
    }

    public static com.yorha.proto.SsGateSession.KickOffSessionCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KickOffSessionCmd>
        PARSER = new com.google.protobuf.AbstractParser<KickOffSessionCmd>() {
      @java.lang.Override
      public KickOffSessionCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KickOffSessionCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KickOffSessionCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KickOffSessionCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsGateSession.KickOffSessionCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendMsgToSessionCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PlayerBoundCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PlayerBoundCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdateClientInfoCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_LanguageMsgBytes_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_LanguageMsgBytes_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KickOffSessionCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KickOffSessionCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/gateSession/ss_gate_sessi" +
      "on.proto\022\017com.yorha.proto\032%ss_proto/gen/" +
      "common/common_enum.proto\032$ss_proto/gen/c" +
      "ommon/common_msg.proto\032\034ss_proto/gen/cor" +
      "e/core.proto\"l\n\023SendMsgToSessionCmd\022\017\n\007m" +
      "sgType\030\001 \001(\005\022\020\n\010msgBytes\030\002 \001(\014\022\r\n\005seqId\030" +
      "\003 \001(\005\022#\n\004code\030\004 \001(\0132\025.com.yorha.proto.Co" +
      "de\"{\n\016PlayerBoundCmd\022#\n\004code\030\001 \001(\0132\025.com" +
      ".yorha.proto.Code\022\016\n\006openId\030\002 \001(\t\022\020\n\010pla" +
      "yerId\030\003 \001(\003\022\022\n\nplayerName\030\004 \001(\t\022\016\n\006zoneI" +
      "d\030\005 \001(\005\"I\n\023UpdateClientInfoCmd\0222\n\rnewCli" +
      "entInfo\030\001 \001(\0132\033.com.yorha.proto.ClientIn" +
      "fo\"\225\002\n\037SendMsgToSessionWithLanguageCmd\022\017" +
      "\n\007msgType\030\001 \001(\005\022V\n\013languageMap\030\002 \003(\0132A.c" +
      "om.yorha.proto.SendMsgToSessionWithLangu" +
      "ageCmd.LanguageMapEntry\022\r\n\005seqId\030\003 \001(\005\022#" +
      "\n\004code\030\004 \001(\0132\025.com.yorha.proto.Code\032U\n\020L" +
      "anguageMapEntry\022\013\n\003key\030\001 \001(\005\0220\n\005value\030\002 " +
      "\001(\0132!.com.yorha.proto.LanguageMsgBytes:\002" +
      "8\001\"8\n\020LanguageMsgBytes\022\022\n\nlanguageId\030\001 \001" +
      "(\005\022\020\n\010msgBytes\030\002 \001(\014\"H\n\021KickOffSessionCm" +
      "d\0223\n\006reason\030\001 \001(\0162#.com.yorha.proto.Sess" +
      "ionCloseReasonB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
          com.yorha.proto.Core.getDescriptor(),
        });
    internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SendMsgToSessionCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendMsgToSessionCmd_descriptor,
        new java.lang.String[] { "MsgType", "MsgBytes", "SeqId", "Code", });
    internal_static_com_yorha_proto_PlayerBoundCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_PlayerBoundCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PlayerBoundCmd_descriptor,
        new java.lang.String[] { "Code", "OpenId", "PlayerId", "PlayerName", "ZoneId", });
    internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_UpdateClientInfoCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdateClientInfoCmd_descriptor,
        new java.lang.String[] { "NewClientInfo", });
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor,
        new java.lang.String[] { "MsgType", "LanguageMap", "SeqId", "Code", });
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_descriptor =
      internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendMsgToSessionWithLanguageCmd_LanguageMapEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_LanguageMsgBytes_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_LanguageMsgBytes_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_LanguageMsgBytes_descriptor,
        new java.lang.String[] { "LanguageId", "MsgBytes", });
    internal_static_com_yorha_proto_KickOffSessionCmd_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_KickOffSessionCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KickOffSessionCmd_descriptor,
        new java.lang.String[] { "Reason", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
    com.yorha.proto.Core.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_vip_store.proto

package com.yorha.proto;

public final class PlayerVipStore {
  private PlayerVipStore() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_GetVipStoreInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetVipStoreInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetVipStoreInfo_C2S}
   */
  public static final class Player_GetVipStoreInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetVipStoreInfo_C2S)
      Player_GetVipStoreInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetVipStoreInfo_C2S.newBuilder() to construct.
    private Player_GetVipStoreInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetVipStoreInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetVipStoreInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetVipStoreInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.class, com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S other = (com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetVipStoreInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetVipStoreInfo_C2S)
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.class, com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S build() {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S result = new com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetVipStoreInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetVipStoreInfo_C2S)
    private static final com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S();
    }

    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetVipStoreInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetVipStoreInfo_C2S>() {
      @java.lang.Override
      public Player_GetVipStoreInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetVipStoreInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetVipStoreInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetVipStoreInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetVipStoreInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetVipStoreInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo> 
        getInfoList();
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo(int index);
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    int getInfoCount();
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> 
        getInfoOrBuilderList();
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder(
        int index);

    /**
     * <pre>
     * 下次商店更新时间ms
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    boolean hasNextRefreshTsMs();
    /**
     * <pre>
     * 下次商店更新时间ms
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return The nextRefreshTsMs.
     */
    long getNextRefreshTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetVipStoreInfo_S2C}
   */
  public static final class Player_GetVipStoreInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetVipStoreInfo_S2C)
      Player_GetVipStoreInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetVipStoreInfo_S2C.newBuilder() to construct.
    private Player_GetVipStoreInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetVipStoreInfo_S2C() {
      info_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetVipStoreInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetVipStoreInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = new java.util.ArrayList<com.yorha.proto.PlayerVipStore.VipStoreInfo>();
                mutable_bitField0_ |= 0x00000001;
              }
              info_.add(
                  input.readMessage(com.yorha.proto.PlayerVipStore.VipStoreInfo.PARSER, extensionRegistry));
              break;
            }
            case 16: {
              bitField0_ |= 0x00000001;
              nextRefreshTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          info_ = java.util.Collections.unmodifiableList(info_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.class, com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo> info_;
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo> getInfoList() {
      return info_;
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> 
        getInfoOrBuilderList() {
      return info_;
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public int getInfoCount() {
      return info_.size();
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo(int index) {
      return info_.get(index);
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder(
        int index) {
      return info_.get(index);
    }

    public static final int NEXTREFRESHTSMS_FIELD_NUMBER = 2;
    private long nextRefreshTsMs_;
    /**
     * <pre>
     * 下次商店更新时间ms
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return Whether the nextRefreshTsMs field is set.
     */
    @java.lang.Override
    public boolean hasNextRefreshTsMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 下次商店更新时间ms
     * </pre>
     *
     * <code>optional int64 nextRefreshTsMs = 2;</code>
     * @return The nextRefreshTsMs.
     */
    @java.lang.Override
    public long getNextRefreshTsMs() {
      return nextRefreshTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < info_.size(); i++) {
        output.writeMessage(1, info_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(2, nextRefreshTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < info_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, info_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, nextRefreshTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C other = (com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C) obj;

      if (!getInfoList()
          .equals(other.getInfoList())) return false;
      if (hasNextRefreshTsMs() != other.hasNextRefreshTsMs()) return false;
      if (hasNextRefreshTsMs()) {
        if (getNextRefreshTsMs()
            != other.getNextRefreshTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getInfoCount() > 0) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfoList().hashCode();
      }
      if (hasNextRefreshTsMs()) {
        hash = (37 * hash) + NEXTREFRESHTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNextRefreshTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetVipStoreInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetVipStoreInfo_S2C)
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.class, com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          infoBuilder_.clear();
        }
        nextRefreshTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C build() {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C result = new com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            info_ = java.util.Collections.unmodifiableList(info_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.info_ = info_;
        } else {
          result.info_ = infoBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.nextRefreshTsMs_ = nextRefreshTsMs_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C.getDefaultInstance()) return this;
        if (infoBuilder_ == null) {
          if (!other.info_.isEmpty()) {
            if (info_.isEmpty()) {
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureInfoIsMutable();
              info_.addAll(other.info_);
            }
            onChanged();
          }
        } else {
          if (!other.info_.isEmpty()) {
            if (infoBuilder_.isEmpty()) {
              infoBuilder_.dispose();
              infoBuilder_ = null;
              info_ = other.info_;
              bitField0_ = (bitField0_ & ~0x00000001);
              infoBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getInfoFieldBuilder() : null;
            } else {
              infoBuilder_.addAllMessages(other.info_);
            }
          }
        }
        if (other.hasNextRefreshTsMs()) {
          setNextRefreshTsMs(other.getNextRefreshTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo> info_ =
        java.util.Collections.emptyList();
      private void ensureInfoIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          info_ = new java.util.ArrayList<com.yorha.proto.PlayerVipStore.VipStoreInfo>(info_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> infoBuilder_;

      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo> getInfoList() {
        if (infoBuilder_ == null) {
          return java.util.Collections.unmodifiableList(info_);
        } else {
          return infoBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public int getInfoCount() {
        if (infoBuilder_ == null) {
          return info_.size();
        } else {
          return infoBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo(int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);
        } else {
          return infoBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerVipStore.VipStoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.set(index, value);
          onChanged();
        } else {
          infoBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder setInfo(
          int index, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.set(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder addInfo(com.yorha.proto.PlayerVipStore.VipStoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(value);
          onChanged();
        } else {
          infoBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerVipStore.VipStoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureInfoIsMutable();
          info_.add(index, value);
          onChanged();
        } else {
          infoBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder addInfo(
          com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder addInfo(
          int index, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.add(index, builderForValue.build());
          onChanged();
        } else {
          infoBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder addAllInfo(
          java.lang.Iterable<? extends com.yorha.proto.PlayerVipStore.VipStoreInfo> values) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, info_);
          onChanged();
        } else {
          infoBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder removeInfo(int index) {
        if (infoBuilder_ == null) {
          ensureInfoIsMutable();
          info_.remove(index);
          onChanged();
        } else {
          infoBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder getInfoBuilder(
          int index) {
        return getInfoFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder(
          int index) {
        if (infoBuilder_ == null) {
          return info_.get(index);  } else {
          return infoBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> 
           getInfoOrBuilderList() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(info_);
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder addInfoBuilder() {
        return getInfoFieldBuilder().addBuilder(
            com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder addInfoBuilder(
          int index) {
        return getInfoFieldBuilder().addBuilder(
            index, com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public java.util.List<com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder> 
           getInfoBuilderList() {
        return getInfoFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder>(
                  info_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }

      private long nextRefreshTsMs_ ;
      /**
       * <pre>
       * 下次商店更新时间ms
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return Whether the nextRefreshTsMs field is set.
       */
      @java.lang.Override
      public boolean hasNextRefreshTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 下次商店更新时间ms
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return The nextRefreshTsMs.
       */
      @java.lang.Override
      public long getNextRefreshTsMs() {
        return nextRefreshTsMs_;
      }
      /**
       * <pre>
       * 下次商店更新时间ms
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @param value The nextRefreshTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setNextRefreshTsMs(long value) {
        bitField0_ |= 0x00000002;
        nextRefreshTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次商店更新时间ms
       * </pre>
       *
       * <code>optional int64 nextRefreshTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRefreshTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        nextRefreshTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetVipStoreInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetVipStoreInfo_S2C)
    private static final com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C();
    }

    public static com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetVipStoreInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetVipStoreInfo_S2C>() {
      @java.lang.Override
      public Player_GetVipStoreInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetVipStoreInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetVipStoreInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetVipStoreInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_GetVipStoreInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface VipStoreInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.VipStoreInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 1;</code>
     * @return Whether the configGoodsId field is set.
     */
    boolean hasConfigGoodsId();
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 1;</code>
     * @return The configGoodsId.
     */
    long getConfigGoodsId();

    /**
     * <pre>
     * 已购买数量
     * </pre>
     *
     * <code>optional int32 boughtNum = 2;</code>
     * @return Whether the boughtNum field is set.
     */
    boolean hasBoughtNum();
    /**
     * <pre>
     * 已购买数量
     * </pre>
     *
     * <code>optional int32 boughtNum = 2;</code>
     * @return The boughtNum.
     */
    int getBoughtNum();
  }
  /**
   * <pre>
   * 仅下发客户端无法通过读表获取数据
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.VipStoreInfo}
   */
  public static final class VipStoreInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.VipStoreInfo)
      VipStoreInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use VipStoreInfo.newBuilder() to construct.
    private VipStoreInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private VipStoreInfo() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new VipStoreInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private VipStoreInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              configGoodsId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              boughtNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_VipStoreInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_VipStoreInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.VipStoreInfo.class, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGGOODSID_FIELD_NUMBER = 1;
    private long configGoodsId_;
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 1;</code>
     * @return Whether the configGoodsId field is set.
     */
    @java.lang.Override
    public boolean hasConfigGoodsId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 商品id
     * </pre>
     *
     * <code>optional int64 configGoodsId = 1;</code>
     * @return The configGoodsId.
     */
    @java.lang.Override
    public long getConfigGoodsId() {
      return configGoodsId_;
    }

    public static final int BOUGHTNUM_FIELD_NUMBER = 2;
    private int boughtNum_;
    /**
     * <pre>
     * 已购买数量
     * </pre>
     *
     * <code>optional int32 boughtNum = 2;</code>
     * @return Whether the boughtNum field is set.
     */
    @java.lang.Override
    public boolean hasBoughtNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 已购买数量
     * </pre>
     *
     * <code>optional int32 boughtNum = 2;</code>
     * @return The boughtNum.
     */
    @java.lang.Override
    public int getBoughtNum() {
      return boughtNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, configGoodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, boughtNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, configGoodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, boughtNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.VipStoreInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.VipStoreInfo other = (com.yorha.proto.PlayerVipStore.VipStoreInfo) obj;

      if (hasConfigGoodsId() != other.hasConfigGoodsId()) return false;
      if (hasConfigGoodsId()) {
        if (getConfigGoodsId()
            != other.getConfigGoodsId()) return false;
      }
      if (hasBoughtNum() != other.hasBoughtNum()) return false;
      if (hasBoughtNum()) {
        if (getBoughtNum()
            != other.getBoughtNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConfigGoodsId()) {
        hash = (37 * hash) + CONFIGGOODSID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getConfigGoodsId());
      }
      if (hasBoughtNum()) {
        hash = (37 * hash) + BOUGHTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getBoughtNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.VipStoreInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.VipStoreInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 仅下发客户端无法通过读表获取数据
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.VipStoreInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.VipStoreInfo)
        com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_VipStoreInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_VipStoreInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.VipStoreInfo.class, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.VipStoreInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configGoodsId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        boughtNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_VipStoreInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.VipStoreInfo getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.VipStoreInfo build() {
        com.yorha.proto.PlayerVipStore.VipStoreInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.VipStoreInfo buildPartial() {
        com.yorha.proto.PlayerVipStore.VipStoreInfo result = new com.yorha.proto.PlayerVipStore.VipStoreInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.configGoodsId_ = configGoodsId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.boughtNum_ = boughtNum_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.VipStoreInfo) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.VipStoreInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.VipStoreInfo other) {
        if (other == com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance()) return this;
        if (other.hasConfigGoodsId()) {
          setConfigGoodsId(other.getConfigGoodsId());
        }
        if (other.hasBoughtNum()) {
          setBoughtNum(other.getBoughtNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.VipStoreInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.VipStoreInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long configGoodsId_ ;
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 1;</code>
       * @return Whether the configGoodsId field is set.
       */
      @java.lang.Override
      public boolean hasConfigGoodsId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 1;</code>
       * @return The configGoodsId.
       */
      @java.lang.Override
      public long getConfigGoodsId() {
        return configGoodsId_;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 1;</code>
       * @param value The configGoodsId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigGoodsId(long value) {
        bitField0_ |= 0x00000001;
        configGoodsId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商品id
       * </pre>
       *
       * <code>optional int64 configGoodsId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        configGoodsId_ = 0L;
        onChanged();
        return this;
      }

      private int boughtNum_ ;
      /**
       * <pre>
       * 已购买数量
       * </pre>
       *
       * <code>optional int32 boughtNum = 2;</code>
       * @return Whether the boughtNum field is set.
       */
      @java.lang.Override
      public boolean hasBoughtNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 已购买数量
       * </pre>
       *
       * <code>optional int32 boughtNum = 2;</code>
       * @return The boughtNum.
       */
      @java.lang.Override
      public int getBoughtNum() {
        return boughtNum_;
      }
      /**
       * <pre>
       * 已购买数量
       * </pre>
       *
       * <code>optional int32 boughtNum = 2;</code>
       * @param value The boughtNum to set.
       * @return This builder for chaining.
       */
      public Builder setBoughtNum(int value) {
        bitField0_ |= 0x00000002;
        boughtNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已购买数量
       * </pre>
       *
       * <code>optional int32 boughtNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBoughtNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        boughtNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.VipStoreInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.VipStoreInfo)
    private static final com.yorha.proto.PlayerVipStore.VipStoreInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.VipStoreInfo();
    }

    public static com.yorha.proto.PlayerVipStore.VipStoreInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<VipStoreInfo>
        PARSER = new com.google.protobuf.AbstractParser<VipStoreInfo>() {
      @java.lang.Override
      public VipStoreInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new VipStoreInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<VipStoreInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<VipStoreInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.VipStoreInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyVipStoreItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyVipStoreItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 configGoodsId = 1;</code>
     * @return Whether the configGoodsId field is set.
     */
    boolean hasConfigGoodsId();
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 configGoodsId = 1;</code>
     * @return The configGoodsId.
     */
    int getConfigGoodsId();

    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 2;</code>
     * @return Whether the buyNum field is set.
     */
    boolean hasBuyNum();
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 2;</code>
     * @return The buyNum.
     */
    int getBuyNum();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyVipStoreItem_C2S}
   */
  public static final class Player_BuyVipStoreItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyVipStoreItem_C2S)
      Player_BuyVipStoreItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyVipStoreItem_C2S.newBuilder() to construct.
    private Player_BuyVipStoreItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyVipStoreItem_C2S() {
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyVipStoreItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyVipStoreItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              configGoodsId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              buyNum_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.class, com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int CONFIGGOODSID_FIELD_NUMBER = 1;
    private int configGoodsId_;
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 configGoodsId = 1;</code>
     * @return Whether the configGoodsId field is set.
     */
    @java.lang.Override
    public boolean hasConfigGoodsId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 configGoodsId = 1;</code>
     * @return The configGoodsId.
     */
    @java.lang.Override
    public int getConfigGoodsId() {
      return configGoodsId_;
    }

    public static final int BUYNUM_FIELD_NUMBER = 2;
    private int buyNum_;
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 2;</code>
     * @return Whether the buyNum field is set.
     */
    @java.lang.Override
    public boolean hasBuyNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 购买数目
     * </pre>
     *
     * <code>optional int32 buyNum = 2;</code>
     * @return The buyNum.
     */
    @java.lang.Override
    public int getBuyNum() {
      return buyNum_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 3;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, configGoodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, buyNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, configGoodsId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, buyNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S other = (com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S) obj;

      if (hasConfigGoodsId() != other.hasConfigGoodsId()) return false;
      if (hasConfigGoodsId()) {
        if (getConfigGoodsId()
            != other.getConfigGoodsId()) return false;
      }
      if (hasBuyNum() != other.hasBuyNum()) return false;
      if (hasBuyNum()) {
        if (getBuyNum()
            != other.getBuyNum()) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasConfigGoodsId()) {
        hash = (37 * hash) + CONFIGGOODSID_FIELD_NUMBER;
        hash = (53 * hash) + getConfigGoodsId();
      }
      if (hasBuyNum()) {
        hash = (37 * hash) + BUYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getBuyNum();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyVipStoreItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyVipStoreItem_C2S)
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.class, com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        configGoodsId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        buyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S build() {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S result = new com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.configGoodsId_ = configGoodsId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.buyNum_ = buyNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S.getDefaultInstance()) return this;
        if (other.hasConfigGoodsId()) {
          setConfigGoodsId(other.getConfigGoodsId());
        }
        if (other.hasBuyNum()) {
          setBuyNum(other.getBuyNum());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000004;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int configGoodsId_ ;
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 configGoodsId = 1;</code>
       * @return Whether the configGoodsId field is set.
       */
      @java.lang.Override
      public boolean hasConfigGoodsId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 configGoodsId = 1;</code>
       * @return The configGoodsId.
       */
      @java.lang.Override
      public int getConfigGoodsId() {
        return configGoodsId_;
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 configGoodsId = 1;</code>
       * @param value The configGoodsId to set.
       * @return This builder for chaining.
       */
      public Builder setConfigGoodsId(int value) {
        bitField0_ |= 0x00000001;
        configGoodsId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 configGoodsId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearConfigGoodsId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        configGoodsId_ = 0;
        onChanged();
        return this;
      }

      private int buyNum_ ;
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 2;</code>
       * @return Whether the buyNum field is set.
       */
      @java.lang.Override
      public boolean hasBuyNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 2;</code>
       * @return The buyNum.
       */
      @java.lang.Override
      public int getBuyNum() {
        return buyNum_;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 2;</code>
       * @param value The buyNum to set.
       * @return This builder for chaining.
       */
      public Builder setBuyNum(int value) {
        bitField0_ |= 0x00000002;
        buyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 购买数目
       * </pre>
       *
       * <code>optional int32 buyNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuyNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        buyNum_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyVipStoreItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyVipStoreItem_C2S)
    private static final com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S();
    }

    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyVipStoreItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyVipStoreItem_C2S>() {
      @java.lang.Override
      public Player_BuyVipStoreItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyVipStoreItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyVipStoreItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyVipStoreItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyVipStoreItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyVipStoreItem_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     * @return The info.
     */
    com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo();
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyVipStoreItem_S2C}
   */
  public static final class Player_BuyVipStoreItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyVipStoreItem_S2C)
      Player_BuyVipStoreItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyVipStoreItem_S2C.newBuilder() to construct.
    private Player_BuyVipStoreItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyVipStoreItem_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyVipStoreItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyVipStoreItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.PlayerVipStore.VipStoreInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.class, com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private com.yorha.proto.PlayerVipStore.VipStoreInfo info_;
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo() {
      return info_ == null ? com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance() : info_;
    }
    /**
     * <pre>
     * Vip商店已购买信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C other = (com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyVipStoreItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyVipStoreItem_S2C)
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.class, com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C build() {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C result = new com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.PlayerVipStore.VipStoreInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> infoBuilder_;
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       * @return The info.
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder setInfo(com.yorha.proto.PlayerVipStore.VipStoreInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder setInfo(
          com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder mergeInfo(com.yorha.proto.PlayerVipStore.VipStoreInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.PlayerVipStore.VipStoreInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      public com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.PlayerVipStore.VipStoreInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <pre>
       * Vip商店已购买信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipStoreInfo info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerVipStore.VipStoreInfo, com.yorha.proto.PlayerVipStore.VipStoreInfo.Builder, com.yorha.proto.PlayerVipStore.VipStoreInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyVipStoreItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyVipStoreItem_S2C)
    private static final com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C();
    }

    public static com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyVipStoreItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyVipStoreItem_S2C>() {
      @java.lang.Override
      public Player_BuyVipStoreItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyVipStoreItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyVipStoreItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyVipStoreItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_BuyVipStoreItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SelectVipHeroItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SelectVipHeroItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 要修改的Vip英雄道具来源
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
     * @return Whether the source field is set.
     */
    boolean hasSource();
    /**
     * <pre>
     * 要修改的Vip英雄道具来源
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
     * @return The source.
     */
    com.yorha.proto.CommonEnum.VipHeroItemSource getSource();

    /**
     * <pre>
     * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
     * </pre>
     *
     * <code>optional int32 qualityId = 2;</code>
     * @return Whether the qualityId field is set.
     */
    boolean hasQualityId();
    /**
     * <pre>
     * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
     * </pre>
     *
     * <code>optional int32 qualityId = 2;</code>
     * @return The qualityId.
     */
    int getQualityId();

    /**
     * <pre>
     * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
     * </pre>
     *
     * <code>optional int32 storeItemId = 3;</code>
     * @return Whether the storeItemId field is set.
     */
    boolean hasStoreItemId();
    /**
     * <pre>
     * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
     * </pre>
     *
     * <code>optional int32 storeItemId = 3;</code>
     * @return The storeItemId.
     */
    int getStoreItemId();

    /**
     * <pre>
     * 要选择的英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 4;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <pre>
     * 要选择的英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 4;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SelectVipHeroItem_C2S}
   */
  public static final class Player_SelectVipHeroItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SelectVipHeroItem_C2S)
      Player_SelectVipHeroItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SelectVipHeroItem_C2S.newBuilder() to construct.
    private Player_SelectVipHeroItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SelectVipHeroItem_C2S() {
      source_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SelectVipHeroItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SelectVipHeroItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.VipHeroItemSource value = com.yorha.proto.CommonEnum.VipHeroItemSource.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                source_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              qualityId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              storeItemId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.class, com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int SOURCE_FIELD_NUMBER = 1;
    private int source_;
    /**
     * <pre>
     * 要修改的Vip英雄道具来源
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
     * @return Whether the source field is set.
     */
    @java.lang.Override public boolean hasSource() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 要修改的Vip英雄道具来源
     * </pre>
     *
     * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
     * @return The source.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.VipHeroItemSource getSource() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.VipHeroItemSource result = com.yorha.proto.CommonEnum.VipHeroItemSource.valueOf(source_);
      return result == null ? com.yorha.proto.CommonEnum.VipHeroItemSource.VHIS_NONE : result;
    }

    public static final int QUALITYID_FIELD_NUMBER = 2;
    private int qualityId_;
    /**
     * <pre>
     * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
     * </pre>
     *
     * <code>optional int32 qualityId = 2;</code>
     * @return Whether the qualityId field is set.
     */
    @java.lang.Override
    public boolean hasQualityId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
     * </pre>
     *
     * <code>optional int32 qualityId = 2;</code>
     * @return The qualityId.
     */
    @java.lang.Override
    public int getQualityId() {
      return qualityId_;
    }

    public static final int STOREITEMID_FIELD_NUMBER = 3;
    private int storeItemId_;
    /**
     * <pre>
     * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
     * </pre>
     *
     * <code>optional int32 storeItemId = 3;</code>
     * @return Whether the storeItemId field is set.
     */
    @java.lang.Override
    public boolean hasStoreItemId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
     * </pre>
     *
     * <code>optional int32 storeItemId = 3;</code>
     * @return The storeItemId.
     */
    @java.lang.Override
    public int getStoreItemId() {
      return storeItemId_;
    }

    public static final int HEROID_FIELD_NUMBER = 4;
    private int heroId_;
    /**
     * <pre>
     * 要选择的英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 4;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 要选择的英雄id
     * </pre>
     *
     * <code>optional int32 heroId = 4;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, source_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, qualityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, storeItemId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, source_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, qualityId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, storeItemId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S other = (com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S) obj;

      if (hasSource() != other.hasSource()) return false;
      if (hasSource()) {
        if (source_ != other.source_) return false;
      }
      if (hasQualityId() != other.hasQualityId()) return false;
      if (hasQualityId()) {
        if (getQualityId()
            != other.getQualityId()) return false;
      }
      if (hasStoreItemId() != other.hasStoreItemId()) return false;
      if (hasStoreItemId()) {
        if (getStoreItemId()
            != other.getStoreItemId()) return false;
      }
      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSource()) {
        hash = (37 * hash) + SOURCE_FIELD_NUMBER;
        hash = (53 * hash) + source_;
      }
      if (hasQualityId()) {
        hash = (37 * hash) + QUALITYID_FIELD_NUMBER;
        hash = (53 * hash) + getQualityId();
      }
      if (hasStoreItemId()) {
        hash = (37 * hash) + STOREITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getStoreItemId();
      }
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SelectVipHeroItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SelectVipHeroItem_C2S)
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.class, com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        source_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        qualityId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        storeItemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S build() {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S result = new com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.source_ = source_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.qualityId_ = qualityId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.storeItemId_ = storeItemId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S.getDefaultInstance()) return this;
        if (other.hasSource()) {
          setSource(other.getSource());
        }
        if (other.hasQualityId()) {
          setQualityId(other.getQualityId());
        }
        if (other.hasStoreItemId()) {
          setStoreItemId(other.getStoreItemId());
        }
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int source_ = 0;
      /**
       * <pre>
       * 要修改的Vip英雄道具来源
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
       * @return Whether the source field is set.
       */
      @java.lang.Override public boolean hasSource() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 要修改的Vip英雄道具来源
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
       * @return The source.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.VipHeroItemSource getSource() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.VipHeroItemSource result = com.yorha.proto.CommonEnum.VipHeroItemSource.valueOf(source_);
        return result == null ? com.yorha.proto.CommonEnum.VipHeroItemSource.VHIS_NONE : result;
      }
      /**
       * <pre>
       * 要修改的Vip英雄道具来源
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
       * @param value The source to set.
       * @return This builder for chaining.
       */
      public Builder setSource(com.yorha.proto.CommonEnum.VipHeroItemSource value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        source_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 要修改的Vip英雄道具来源
       * </pre>
       *
       * <code>optional .com.yorha.proto.VipHeroItemSource source = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSource() {
        bitField0_ = (bitField0_ & ~0x00000001);
        source_ = 0;
        onChanged();
        return this;
      }

      private int qualityId_ ;
      /**
       * <pre>
       * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
       * </pre>
       *
       * <code>optional int32 qualityId = 2;</code>
       * @return Whether the qualityId field is set.
       */
      @java.lang.Override
      public boolean hasQualityId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
       * </pre>
       *
       * <code>optional int32 qualityId = 2;</code>
       * @return The qualityId.
       */
      @java.lang.Override
      public int getQualityId() {
        return qualityId_;
      }
      /**
       * <pre>
       * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
       * </pre>
       *
       * <code>optional int32 qualityId = 2;</code>
       * @param value The qualityId to set.
       * @return This builder for chaining.
       */
      public Builder setQualityId(int value) {
        bitField0_ |= 0x00000002;
        qualityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 仅当来源是Vip每日宝箱时需要设置，说明将要设置的品质等级
       * </pre>
       *
       * <code>optional int32 qualityId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearQualityId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        qualityId_ = 0;
        onChanged();
        return this;
      }

      private int storeItemId_ ;
      /**
       * <pre>
       * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
       * </pre>
       *
       * <code>optional int32 storeItemId = 3;</code>
       * @return Whether the storeItemId field is set.
       */
      @java.lang.Override
      public boolean hasStoreItemId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
       * </pre>
       *
       * <code>optional int32 storeItemId = 3;</code>
       * @return The storeItemId.
       */
      @java.lang.Override
      public int getStoreItemId() {
        return storeItemId_;
      }
      /**
       * <pre>
       * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
       * </pre>
       *
       * <code>optional int32 storeItemId = 3;</code>
       * @param value The storeItemId to set.
       * @return This builder for chaining.
       */
      public Builder setStoreItemId(int value) {
        bitField0_ |= 0x00000004;
        storeItemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 仅当来源是Vip商店时需要设置，说明将要设置的道具id
       * </pre>
       *
       * <code>optional int32 storeItemId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStoreItemId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        storeItemId_ = 0;
        onChanged();
        return this;
      }

      private int heroId_ ;
      /**
       * <pre>
       * 要选择的英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 4;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 要选择的英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 4;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <pre>
       * 要选择的英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 4;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000008;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 要选择的英雄id
       * </pre>
       *
       * <code>optional int32 heroId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SelectVipHeroItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SelectVipHeroItem_C2S)
    private static final com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S();
    }

    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SelectVipHeroItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SelectVipHeroItem_C2S>() {
      @java.lang.Override
      public Player_SelectVipHeroItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SelectVipHeroItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SelectVipHeroItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SelectVipHeroItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SelectVipHeroItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SelectVipHeroItem_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * <pre>
   * 返回为空，通过属性系统下发观察变化
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_SelectVipHeroItem_S2C}
   */
  public static final class Player_SelectVipHeroItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SelectVipHeroItem_S2C)
      Player_SelectVipHeroItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SelectVipHeroItem_S2C.newBuilder() to construct.
    private Player_SelectVipHeroItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SelectVipHeroItem_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SelectVipHeroItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SelectVipHeroItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.class, com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C other = (com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 返回为空，通过属性系统下发观察变化
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_SelectVipHeroItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SelectVipHeroItem_S2C)
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.class, com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerVipStore.internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C build() {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C buildPartial() {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C result = new com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C other) {
        if (other == com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SelectVipHeroItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SelectVipHeroItem_S2C)
    private static final com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C();
    }

    public static com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SelectVipHeroItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SelectVipHeroItem_S2C>() {
      @java.lang.Override
      public Player_SelectVipHeroItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SelectVipHeroItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SelectVipHeroItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SelectVipHeroItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerVipStore.Player_SelectVipHeroItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_VipStoreInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_VipStoreInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n-ss_proto/gen/player/cs/player_vip_stor" +
      "e.proto\022\017com.yorha.proto\032%ss_proto/gen/c" +
      "ommon/common_enum.proto\"\034\n\032Player_GetVip" +
      "StoreInfo_C2S\"b\n\032Player_GetVipStoreInfo_" +
      "S2C\022+\n\004info\030\001 \003(\0132\035.com.yorha.proto.VipS" +
      "toreInfo\022\027\n\017nextRefreshTsMs\030\002 \001(\003\"8\n\014Vip" +
      "StoreInfo\022\025\n\rconfigGoodsId\030\001 \001(\003\022\021\n\tboug" +
      "htNum\030\002 \001(\005\"V\n\032Player_BuyVipStoreItem_C2" +
      "S\022\025\n\rconfigGoodsId\030\001 \001(\005\022\016\n\006buyNum\030\002 \001(\005" +
      "\022\021\n\tsPassWord\030\003 \001(\t\"I\n\032Player_BuyVipStor" +
      "eItem_S2C\022+\n\004info\030\001 \001(\0132\035.com.yorha.prot" +
      "o.VipStoreInfo\"\212\001\n\034Player_SelectVipHeroI" +
      "tem_C2S\0222\n\006source\030\001 \001(\0162\".com.yorha.prot" +
      "o.VipHeroItemSource\022\021\n\tqualityId\030\002 \001(\005\022\023" +
      "\n\013storeItemId\030\003 \001(\005\022\016\n\006heroId\030\004 \001(\005\"\036\n\034P" +
      "layer_SelectVipHeroItem_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetVipStoreInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetVipStoreInfo_S2C_descriptor,
        new java.lang.String[] { "Info", "NextRefreshTsMs", });
    internal_static_com_yorha_proto_VipStoreInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_VipStoreInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_VipStoreInfo_descriptor,
        new java.lang.String[] { "ConfigGoodsId", "BoughtNum", });
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyVipStoreItem_C2S_descriptor,
        new java.lang.String[] { "ConfigGoodsId", "BuyNum", "SPassWord", });
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyVipStoreItem_S2C_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SelectVipHeroItem_C2S_descriptor,
        new java.lang.String[] { "Source", "QualityId", "StoreItemId", "HeroId", });
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SelectVipHeroItem_S2C_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

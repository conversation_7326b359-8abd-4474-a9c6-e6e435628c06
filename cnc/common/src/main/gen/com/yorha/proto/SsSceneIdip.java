// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_idip.proto

package com.yorha.proto;

public final class SsSceneIdip {
  private SsSceneIdip() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SoldierAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SoldierAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    boolean hasValue();
    /**
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    int getValue();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional string openId = 4;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 4;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 4;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SoldierAsk}
   */
  public static final class SoldierAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SoldierAsk)
      SoldierAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SoldierAsk.newBuilder() to construct.
    private SoldierAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SoldierAsk() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SoldierAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SoldierAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              value_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              openId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneIdip.SoldierAsk.class, com.yorha.proto.SsSceneIdip.SoldierAsk.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private int value_;
    /**
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    @java.lang.Override
    public boolean hasValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    @java.lang.Override
    public int getValue() {
      return value_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int OPENID_FIELD_NUMBER = 4;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 4;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string openId = 4;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 4;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, value_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, openId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, value_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, openId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneIdip.SoldierAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneIdip.SoldierAsk other = (com.yorha.proto.SsSceneIdip.SoldierAsk) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasValue() != other.hasValue()) return false;
      if (hasValue()) {
        if (getValue()
            != other.getValue()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasValue()) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneIdip.SoldierAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SoldierAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SoldierAsk)
        com.yorha.proto.SsSceneIdip.SoldierAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneIdip.SoldierAsk.class, com.yorha.proto.SsSceneIdip.SoldierAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneIdip.SoldierAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        value_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneIdip.SoldierAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAsk build() {
        com.yorha.proto.SsSceneIdip.SoldierAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAsk buildPartial() {
        com.yorha.proto.SsSceneIdip.SoldierAsk result = new com.yorha.proto.SsSceneIdip.SoldierAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.value_ = value_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.openId_ = openId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneIdip.SoldierAsk) {
          return mergeFrom((com.yorha.proto.SsSceneIdip.SoldierAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneIdip.SoldierAsk other) {
        if (other == com.yorha.proto.SsSceneIdip.SoldierAsk.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasValue()) {
          setValue(other.getValue());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000008;
          openId_ = other.openId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneIdip.SoldierAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneIdip.SoldierAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int value_ ;
      /**
       * <code>optional int32 value = 2;</code>
       * @return Whether the value field is set.
       */
      @java.lang.Override
      public boolean hasValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public int getValue() {
        return value_;
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(int value) {
        bitField0_ |= 0x00000002;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 4;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 4;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 4;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        openId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SoldierAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SoldierAsk)
    private static final com.yorha.proto.SsSceneIdip.SoldierAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneIdip.SoldierAsk();
    }

    public static com.yorha.proto.SsSceneIdip.SoldierAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SoldierAsk>
        PARSER = new com.google.protobuf.AbstractParser<SoldierAsk>() {
      @java.lang.Override
      public SoldierAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SoldierAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SoldierAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SoldierAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneIdip.SoldierAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SoldierAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SoldierAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    boolean hasBeforeValue();
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    int getBeforeValue();

    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    boolean hasAfterValue();
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    int getAfterValue();

    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    boolean hasExceptionId();
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    int getExceptionId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SoldierAns}
   */
  public static final class SoldierAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SoldierAns)
      SoldierAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SoldierAns.newBuilder() to construct.
    private SoldierAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SoldierAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SoldierAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SoldierAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeValue_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterValue_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              exceptionId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneIdip.SoldierAns.class, com.yorha.proto.SsSceneIdip.SoldierAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFOREVALUE_FIELD_NUMBER = 1;
    private int beforeValue_;
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    @java.lang.Override
    public boolean hasBeforeValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    @java.lang.Override
    public int getBeforeValue() {
      return beforeValue_;
    }

    public static final int AFTERVALUE_FIELD_NUMBER = 2;
    private int afterValue_;
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    @java.lang.Override
    public boolean hasAfterValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    @java.lang.Override
    public int getAfterValue() {
      return afterValue_;
    }

    public static final int EXCEPTIONID_FIELD_NUMBER = 3;
    private int exceptionId_;
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    @java.lang.Override
    public boolean hasExceptionId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    @java.lang.Override
    public int getExceptionId() {
      return exceptionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, exceptionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, exceptionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneIdip.SoldierAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneIdip.SoldierAns other = (com.yorha.proto.SsSceneIdip.SoldierAns) obj;

      if (hasBeforeValue() != other.hasBeforeValue()) return false;
      if (hasBeforeValue()) {
        if (getBeforeValue()
            != other.getBeforeValue()) return false;
      }
      if (hasAfterValue() != other.hasAfterValue()) return false;
      if (hasAfterValue()) {
        if (getAfterValue()
            != other.getAfterValue()) return false;
      }
      if (hasExceptionId() != other.hasExceptionId()) return false;
      if (hasExceptionId()) {
        if (getExceptionId()
            != other.getExceptionId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeValue()) {
        hash = (37 * hash) + BEFOREVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeValue();
      }
      if (hasAfterValue()) {
        hash = (37 * hash) + AFTERVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getAfterValue();
      }
      if (hasExceptionId()) {
        hash = (37 * hash) + EXCEPTIONID_FIELD_NUMBER;
        hash = (53 * hash) + getExceptionId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneIdip.SoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneIdip.SoldierAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SoldierAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SoldierAns)
        com.yorha.proto.SsSceneIdip.SoldierAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneIdip.SoldierAns.class, com.yorha.proto.SsSceneIdip.SoldierAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneIdip.SoldierAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        exceptionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneIdip.internal_static_com_yorha_proto_SoldierAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneIdip.SoldierAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAns build() {
        com.yorha.proto.SsSceneIdip.SoldierAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneIdip.SoldierAns buildPartial() {
        com.yorha.proto.SsSceneIdip.SoldierAns result = new com.yorha.proto.SsSceneIdip.SoldierAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeValue_ = beforeValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterValue_ = afterValue_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.exceptionId_ = exceptionId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneIdip.SoldierAns) {
          return mergeFrom((com.yorha.proto.SsSceneIdip.SoldierAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneIdip.SoldierAns other) {
        if (other == com.yorha.proto.SsSceneIdip.SoldierAns.getDefaultInstance()) return this;
        if (other.hasBeforeValue()) {
          setBeforeValue(other.getBeforeValue());
        }
        if (other.hasAfterValue()) {
          setAfterValue(other.getAfterValue());
        }
        if (other.hasExceptionId()) {
          setExceptionId(other.getExceptionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneIdip.SoldierAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneIdip.SoldierAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beforeValue_ ;
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return Whether the beforeValue field is set.
       */
      @java.lang.Override
      public boolean hasBeforeValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return The beforeValue.
       */
      @java.lang.Override
      public int getBeforeValue() {
        return beforeValue_;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @param value The beforeValue to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeValue(int value) {
        bitField0_ |= 0x00000001;
        beforeValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeValue_ = 0;
        onChanged();
        return this;
      }

      private int afterValue_ ;
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return Whether the afterValue field is set.
       */
      @java.lang.Override
      public boolean hasAfterValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return The afterValue.
       */
      @java.lang.Override
      public int getAfterValue() {
        return afterValue_;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @param value The afterValue to set.
       * @return This builder for chaining.
       */
      public Builder setAfterValue(int value) {
        bitField0_ |= 0x00000002;
        afterValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterValue_ = 0;
        onChanged();
        return this;
      }

      private int exceptionId_ ;
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return Whether the exceptionId field is set.
       */
      @java.lang.Override
      public boolean hasExceptionId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return The exceptionId.
       */
      @java.lang.Override
      public int getExceptionId() {
        return exceptionId_;
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @param value The exceptionId to set.
       * @return This builder for chaining.
       */
      public Builder setExceptionId(int value) {
        bitField0_ |= 0x00000004;
        exceptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExceptionId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        exceptionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SoldierAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SoldierAns)
    private static final com.yorha.proto.SsSceneIdip.SoldierAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneIdip.SoldierAns();
    }

    public static com.yorha.proto.SsSceneIdip.SoldierAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SoldierAns>
        PARSER = new com.google.protobuf.AbstractParser<SoldierAns>() {
      @java.lang.Override
      public SoldierAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SoldierAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SoldierAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SoldierAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneIdip.SoldierAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SoldierAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SoldierAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SoldierAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SoldierAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&ss_proto/gen/scene/ss_scene_idip.proto" +
      "\022\017com.yorha.proto\"P\n\nSoldierAsk\022\021\n\tsoldi" +
      "erId\030\001 \001(\005\022\r\n\005value\030\002 \001(\005\022\020\n\010playerId\030\003 " +
      "\001(\003\022\016\n\006openId\030\004 \001(\t\"J\n\nSoldierAns\022\023\n\013bef" +
      "oreValue\030\001 \001(\005\022\022\n\nafterValue\030\002 \001(\005\022\023\n\013ex" +
      "ceptionId\030\003 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_SoldierAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SoldierAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SoldierAsk_descriptor,
        new java.lang.String[] { "SoldierId", "Value", "PlayerId", "OpenId", });
    internal_static_com_yorha_proto_SoldierAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SoldierAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SoldierAns_descriptor,
        new java.lang.String[] { "BeforeValue", "AfterValue", "ExceptionId", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

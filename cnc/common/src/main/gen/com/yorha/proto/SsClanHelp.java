// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clan/ss_clan_help.proto

package com.yorha.proto;

public final class SsClanHelp {
  private SsClanHelp() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SyncPlayerQueueTaskCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncPlayerQueueTaskCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     * @return Whether the task field is set.
     */
    boolean hasTask();
    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     * @return The task.
     */
    com.yorha.proto.StructPlayer.QueueTask getTask();
    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     */
    com.yorha.proto.StructPlayer.QueueTaskOrBuilder getTaskOrBuilder();

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     * @return Whether the helpRecord field is set.
     */
    boolean hasHelpRecord();
    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     * @return The helpRecord.
     */
    com.yorha.proto.Struct.ClanRecord getHelpRecord();
    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     */
    com.yorha.proto.Struct.ClanRecordOrBuilder getHelpRecordOrBuilder();

    /**
     * <pre>
     * 目标等级
     * </pre>
     *
     * <code>optional int32 targetLevel = 6;</code>
     * @return Whether the targetLevel field is set.
     */
    boolean hasTargetLevel();
    /**
     * <pre>
     * 目标等级
     * </pre>
     *
     * <code>optional int32 targetLevel = 6;</code>
     * @return The targetLevel.
     */
    int getTargetLevel();

    /**
     * <pre>
     * 建筑id
     * </pre>
     *
     * <code>optional int64 buildId = 7;</code>
     * @return Whether the buildId field is set.
     */
    boolean hasBuildId();
    /**
     * <pre>
     * 建筑id
     * </pre>
     *
     * <code>optional int64 buildId = 7;</code>
     * @return The buildId.
     */
    long getBuildId();

    /**
     * <pre>
     * 最大帮助次数
     * </pre>
     *
     * <code>optional int32 maxHelpTimes = 8;</code>
     * @return Whether the maxHelpTimes field is set.
     */
    boolean hasMaxHelpTimes();
    /**
     * <pre>
     * 最大帮助次数
     * </pre>
     *
     * <code>optional int32 maxHelpTimes = 8;</code>
     * @return The maxHelpTimes.
     */
    int getMaxHelpTimes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncPlayerQueueTaskCmd}
   */
  public static final class SyncPlayerQueueTaskCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncPlayerQueueTaskCmd)
      SyncPlayerQueueTaskCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncPlayerQueueTaskCmd.newBuilder() to construct.
    private SyncPlayerQueueTaskCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncPlayerQueueTaskCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncPlayerQueueTaskCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncPlayerQueueTaskCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructPlayer.QueueTask.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = task_.toBuilder();
              }
              task_ = input.readMessage(com.yorha.proto.StructPlayer.QueueTask.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(task_);
                task_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000002;
              playerId_ = input.readInt64();
              break;
            }
            case 42: {
              com.yorha.proto.Struct.ClanRecord.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = helpRecord_.toBuilder();
              }
              helpRecord_ = input.readMessage(com.yorha.proto.Struct.ClanRecord.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(helpRecord_);
                helpRecord_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            case 48: {
              bitField0_ |= 0x00000008;
              targetLevel_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000010;
              buildId_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000020;
              maxHelpTimes_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.class, com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.Builder.class);
    }

    private int bitField0_;
    public static final int TASK_FIELD_NUMBER = 1;
    private com.yorha.proto.StructPlayer.QueueTask task_;
    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     * @return Whether the task field is set.
     */
    @java.lang.Override
    public boolean hasTask() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     * @return The task.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.QueueTask getTask() {
      return task_ == null ? com.yorha.proto.StructPlayer.QueueTask.getDefaultInstance() : task_;
    }
    /**
     * <pre>
     * task同步
     * </pre>
     *
     * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.QueueTaskOrBuilder getTaskOrBuilder() {
      return task_ == null ? com.yorha.proto.StructPlayer.QueueTask.getDefaultInstance() : task_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 4;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 4;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int HELPRECORD_FIELD_NUMBER = 5;
    private com.yorha.proto.Struct.ClanRecord helpRecord_;
    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     * @return Whether the helpRecord field is set.
     */
    @java.lang.Override
    public boolean hasHelpRecord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     * @return The helpRecord.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanRecord getHelpRecord() {
      return helpRecord_ == null ? com.yorha.proto.Struct.ClanRecord.getDefaultInstance() : helpRecord_;
    }
    /**
     * <pre>
     * 帮助记录
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ClanRecordOrBuilder getHelpRecordOrBuilder() {
      return helpRecord_ == null ? com.yorha.proto.Struct.ClanRecord.getDefaultInstance() : helpRecord_;
    }

    public static final int TARGETLEVEL_FIELD_NUMBER = 6;
    private int targetLevel_;
    /**
     * <pre>
     * 目标等级
     * </pre>
     *
     * <code>optional int32 targetLevel = 6;</code>
     * @return Whether the targetLevel field is set.
     */
    @java.lang.Override
    public boolean hasTargetLevel() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 目标等级
     * </pre>
     *
     * <code>optional int32 targetLevel = 6;</code>
     * @return The targetLevel.
     */
    @java.lang.Override
    public int getTargetLevel() {
      return targetLevel_;
    }

    public static final int BUILDID_FIELD_NUMBER = 7;
    private long buildId_;
    /**
     * <pre>
     * 建筑id
     * </pre>
     *
     * <code>optional int64 buildId = 7;</code>
     * @return Whether the buildId field is set.
     */
    @java.lang.Override
    public boolean hasBuildId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 建筑id
     * </pre>
     *
     * <code>optional int64 buildId = 7;</code>
     * @return The buildId.
     */
    @java.lang.Override
    public long getBuildId() {
      return buildId_;
    }

    public static final int MAXHELPTIMES_FIELD_NUMBER = 8;
    private int maxHelpTimes_;
    /**
     * <pre>
     * 最大帮助次数
     * </pre>
     *
     * <code>optional int32 maxHelpTimes = 8;</code>
     * @return Whether the maxHelpTimes field is set.
     */
    @java.lang.Override
    public boolean hasMaxHelpTimes() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 最大帮助次数
     * </pre>
     *
     * <code>optional int32 maxHelpTimes = 8;</code>
     * @return The maxHelpTimes.
     */
    @java.lang.Override
    public int getMaxHelpTimes() {
      return maxHelpTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(4, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(5, getHelpRecord());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(6, targetLevel_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(7, buildId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(8, maxHelpTimes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getTask());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, playerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getHelpRecord());
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, targetLevel_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, buildId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, maxHelpTimes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd other = (com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd) obj;

      if (hasTask() != other.hasTask()) return false;
      if (hasTask()) {
        if (!getTask()
            .equals(other.getTask())) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasHelpRecord() != other.hasHelpRecord()) return false;
      if (hasHelpRecord()) {
        if (!getHelpRecord()
            .equals(other.getHelpRecord())) return false;
      }
      if (hasTargetLevel() != other.hasTargetLevel()) return false;
      if (hasTargetLevel()) {
        if (getTargetLevel()
            != other.getTargetLevel()) return false;
      }
      if (hasBuildId() != other.hasBuildId()) return false;
      if (hasBuildId()) {
        if (getBuildId()
            != other.getBuildId()) return false;
      }
      if (hasMaxHelpTimes() != other.hasMaxHelpTimes()) return false;
      if (hasMaxHelpTimes()) {
        if (getMaxHelpTimes()
            != other.getMaxHelpTimes()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasTask()) {
        hash = (37 * hash) + TASK_FIELD_NUMBER;
        hash = (53 * hash) + getTask().hashCode();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasHelpRecord()) {
        hash = (37 * hash) + HELPRECORD_FIELD_NUMBER;
        hash = (53 * hash) + getHelpRecord().hashCode();
      }
      if (hasTargetLevel()) {
        hash = (37 * hash) + TARGETLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getTargetLevel();
      }
      if (hasBuildId()) {
        hash = (37 * hash) + BUILDID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBuildId());
      }
      if (hasMaxHelpTimes()) {
        hash = (37 * hash) + MAXHELPTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getMaxHelpTimes();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncPlayerQueueTaskCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncPlayerQueueTaskCmd)
        com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.class, com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTaskFieldBuilder();
          getHelpRecordFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (taskBuilder_ == null) {
          task_ = null;
        } else {
          taskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (helpRecordBuilder_ == null) {
          helpRecord_ = null;
        } else {
          helpRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        targetLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        buildId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        maxHelpTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd build() {
        com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd buildPartial() {
        com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd result = new com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (taskBuilder_ == null) {
            result.task_ = task_;
          } else {
            result.task_ = taskBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (helpRecordBuilder_ == null) {
            result.helpRecord_ = helpRecord_;
          } else {
            result.helpRecord_ = helpRecordBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.targetLevel_ = targetLevel_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.buildId_ = buildId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.maxHelpTimes_ = maxHelpTimes_;
          to_bitField0_ |= 0x00000020;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd) {
          return mergeFrom((com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd other) {
        if (other == com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd.getDefaultInstance()) return this;
        if (other.hasTask()) {
          mergeTask(other.getTask());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasHelpRecord()) {
          mergeHelpRecord(other.getHelpRecord());
        }
        if (other.hasTargetLevel()) {
          setTargetLevel(other.getTargetLevel());
        }
        if (other.hasBuildId()) {
          setBuildId(other.getBuildId());
        }
        if (other.hasMaxHelpTimes()) {
          setMaxHelpTimes(other.getMaxHelpTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructPlayer.QueueTask task_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.QueueTask, com.yorha.proto.StructPlayer.QueueTask.Builder, com.yorha.proto.StructPlayer.QueueTaskOrBuilder> taskBuilder_;
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       * @return Whether the task field is set.
       */
      public boolean hasTask() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       * @return The task.
       */
      public com.yorha.proto.StructPlayer.QueueTask getTask() {
        if (taskBuilder_ == null) {
          return task_ == null ? com.yorha.proto.StructPlayer.QueueTask.getDefaultInstance() : task_;
        } else {
          return taskBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public Builder setTask(com.yorha.proto.StructPlayer.QueueTask value) {
        if (taskBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          task_ = value;
          onChanged();
        } else {
          taskBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public Builder setTask(
          com.yorha.proto.StructPlayer.QueueTask.Builder builderForValue) {
        if (taskBuilder_ == null) {
          task_ = builderForValue.build();
          onChanged();
        } else {
          taskBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public Builder mergeTask(com.yorha.proto.StructPlayer.QueueTask value) {
        if (taskBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              task_ != null &&
              task_ != com.yorha.proto.StructPlayer.QueueTask.getDefaultInstance()) {
            task_ =
              com.yorha.proto.StructPlayer.QueueTask.newBuilder(task_).mergeFrom(value).buildPartial();
          } else {
            task_ = value;
          }
          onChanged();
        } else {
          taskBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public Builder clearTask() {
        if (taskBuilder_ == null) {
          task_ = null;
          onChanged();
        } else {
          taskBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public com.yorha.proto.StructPlayer.QueueTask.Builder getTaskBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getTaskFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      public com.yorha.proto.StructPlayer.QueueTaskOrBuilder getTaskOrBuilder() {
        if (taskBuilder_ != null) {
          return taskBuilder_.getMessageOrBuilder();
        } else {
          return task_ == null ?
              com.yorha.proto.StructPlayer.QueueTask.getDefaultInstance() : task_;
        }
      }
      /**
       * <pre>
       * task同步
       * </pre>
       *
       * <code>optional .com.yorha.proto.QueueTask task = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.QueueTask, com.yorha.proto.StructPlayer.QueueTask.Builder, com.yorha.proto.StructPlayer.QueueTaskOrBuilder> 
          getTaskFieldBuilder() {
        if (taskBuilder_ == null) {
          taskBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayer.QueueTask, com.yorha.proto.StructPlayer.QueueTask.Builder, com.yorha.proto.StructPlayer.QueueTaskOrBuilder>(
                  getTask(),
                  getParentForChildren(),
                  isClean());
          task_ = null;
        }
        return taskBuilder_;
      }

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000002;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.ClanRecord helpRecord_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanRecord, com.yorha.proto.Struct.ClanRecord.Builder, com.yorha.proto.Struct.ClanRecordOrBuilder> helpRecordBuilder_;
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       * @return Whether the helpRecord field is set.
       */
      public boolean hasHelpRecord() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       * @return The helpRecord.
       */
      public com.yorha.proto.Struct.ClanRecord getHelpRecord() {
        if (helpRecordBuilder_ == null) {
          return helpRecord_ == null ? com.yorha.proto.Struct.ClanRecord.getDefaultInstance() : helpRecord_;
        } else {
          return helpRecordBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public Builder setHelpRecord(com.yorha.proto.Struct.ClanRecord value) {
        if (helpRecordBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          helpRecord_ = value;
          onChanged();
        } else {
          helpRecordBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public Builder setHelpRecord(
          com.yorha.proto.Struct.ClanRecord.Builder builderForValue) {
        if (helpRecordBuilder_ == null) {
          helpRecord_ = builderForValue.build();
          onChanged();
        } else {
          helpRecordBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public Builder mergeHelpRecord(com.yorha.proto.Struct.ClanRecord value) {
        if (helpRecordBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              helpRecord_ != null &&
              helpRecord_ != com.yorha.proto.Struct.ClanRecord.getDefaultInstance()) {
            helpRecord_ =
              com.yorha.proto.Struct.ClanRecord.newBuilder(helpRecord_).mergeFrom(value).buildPartial();
          } else {
            helpRecord_ = value;
          }
          onChanged();
        } else {
          helpRecordBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public Builder clearHelpRecord() {
        if (helpRecordBuilder_ == null) {
          helpRecord_ = null;
          onChanged();
        } else {
          helpRecordBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public com.yorha.proto.Struct.ClanRecord.Builder getHelpRecordBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getHelpRecordFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      public com.yorha.proto.Struct.ClanRecordOrBuilder getHelpRecordOrBuilder() {
        if (helpRecordBuilder_ != null) {
          return helpRecordBuilder_.getMessageOrBuilder();
        } else {
          return helpRecord_ == null ?
              com.yorha.proto.Struct.ClanRecord.getDefaultInstance() : helpRecord_;
        }
      }
      /**
       * <pre>
       * 帮助记录
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanRecord helpRecord = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.ClanRecord, com.yorha.proto.Struct.ClanRecord.Builder, com.yorha.proto.Struct.ClanRecordOrBuilder> 
          getHelpRecordFieldBuilder() {
        if (helpRecordBuilder_ == null) {
          helpRecordBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.ClanRecord, com.yorha.proto.Struct.ClanRecord.Builder, com.yorha.proto.Struct.ClanRecordOrBuilder>(
                  getHelpRecord(),
                  getParentForChildren(),
                  isClean());
          helpRecord_ = null;
        }
        return helpRecordBuilder_;
      }

      private int targetLevel_ ;
      /**
       * <pre>
       * 目标等级
       * </pre>
       *
       * <code>optional int32 targetLevel = 6;</code>
       * @return Whether the targetLevel field is set.
       */
      @java.lang.Override
      public boolean hasTargetLevel() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 目标等级
       * </pre>
       *
       * <code>optional int32 targetLevel = 6;</code>
       * @return The targetLevel.
       */
      @java.lang.Override
      public int getTargetLevel() {
        return targetLevel_;
      }
      /**
       * <pre>
       * 目标等级
       * </pre>
       *
       * <code>optional int32 targetLevel = 6;</code>
       * @param value The targetLevel to set.
       * @return This builder for chaining.
       */
      public Builder setTargetLevel(int value) {
        bitField0_ |= 0x00000008;
        targetLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标等级
       * </pre>
       *
       * <code>optional int32 targetLevel = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearTargetLevel() {
        bitField0_ = (bitField0_ & ~0x00000008);
        targetLevel_ = 0;
        onChanged();
        return this;
      }

      private long buildId_ ;
      /**
       * <pre>
       * 建筑id
       * </pre>
       *
       * <code>optional int64 buildId = 7;</code>
       * @return Whether the buildId field is set.
       */
      @java.lang.Override
      public boolean hasBuildId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 建筑id
       * </pre>
       *
       * <code>optional int64 buildId = 7;</code>
       * @return The buildId.
       */
      @java.lang.Override
      public long getBuildId() {
        return buildId_;
      }
      /**
       * <pre>
       * 建筑id
       * </pre>
       *
       * <code>optional int64 buildId = 7;</code>
       * @param value The buildId to set.
       * @return This builder for chaining.
       */
      public Builder setBuildId(long value) {
        bitField0_ |= 0x00000010;
        buildId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑id
       * </pre>
       *
       * <code>optional int64 buildId = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearBuildId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        buildId_ = 0L;
        onChanged();
        return this;
      }

      private int maxHelpTimes_ ;
      /**
       * <pre>
       * 最大帮助次数
       * </pre>
       *
       * <code>optional int32 maxHelpTimes = 8;</code>
       * @return Whether the maxHelpTimes field is set.
       */
      @java.lang.Override
      public boolean hasMaxHelpTimes() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 最大帮助次数
       * </pre>
       *
       * <code>optional int32 maxHelpTimes = 8;</code>
       * @return The maxHelpTimes.
       */
      @java.lang.Override
      public int getMaxHelpTimes() {
        return maxHelpTimes_;
      }
      /**
       * <pre>
       * 最大帮助次数
       * </pre>
       *
       * <code>optional int32 maxHelpTimes = 8;</code>
       * @param value The maxHelpTimes to set.
       * @return This builder for chaining.
       */
      public Builder setMaxHelpTimes(int value) {
        bitField0_ |= 0x00000020;
        maxHelpTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最大帮助次数
       * </pre>
       *
       * <code>optional int32 maxHelpTimes = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxHelpTimes() {
        bitField0_ = (bitField0_ & ~0x00000020);
        maxHelpTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncPlayerQueueTaskCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncPlayerQueueTaskCmd)
    private static final com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd();
    }

    public static com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncPlayerQueueTaskCmd>
        PARSER = new com.google.protobuf.AbstractParser<SyncPlayerQueueTaskCmd>() {
      @java.lang.Override
      public SyncPlayerQueueTaskCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncPlayerQueueTaskCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncPlayerQueueTaskCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncPlayerQueueTaskCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.SyncPlayerQueueTaskCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanHelpsAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanHelpsAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanHelpsAsk}
   */
  public static final class FetchClanHelpsAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanHelpsAsk)
      FetchClanHelpsAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanHelpsAsk.newBuilder() to construct.
    private FetchClanHelpsAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanHelpsAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanHelpsAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanHelpsAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.class, com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.FetchClanHelpsAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.FetchClanHelpsAsk other = (com.yorha.proto.SsClanHelp.FetchClanHelpsAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.FetchClanHelpsAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanHelpsAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanHelpsAsk)
        com.yorha.proto.SsClanHelp.FetchClanHelpsAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.class, com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAsk build() {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAsk buildPartial() {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAsk result = new com.yorha.proto.SsClanHelp.FetchClanHelpsAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.FetchClanHelpsAsk) {
          return mergeFrom((com.yorha.proto.SsClanHelp.FetchClanHelpsAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.FetchClanHelpsAsk other) {
        if (other == com.yorha.proto.SsClanHelp.FetchClanHelpsAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.FetchClanHelpsAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanHelpsAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanHelpsAsk)
    private static final com.yorha.proto.SsClanHelp.FetchClanHelpsAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.FetchClanHelpsAsk();
    }

    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanHelpsAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanHelpsAsk>() {
      @java.lang.Override
      public FetchClanHelpsAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanHelpsAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanHelpsAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanHelpsAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.FetchClanHelpsAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchClanHelpsAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchClanHelpsAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    int getItemsCount();
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    boolean containsItems(
        long key);
    /**
     * Use {@link #getItemsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
    getItems();
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
    getItemsMap();
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */

    com.yorha.proto.Struct.ClanHelpItem getItemsOrDefault(
        long key,
        com.yorha.proto.Struct.ClanHelpItem defaultValue);
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */

    com.yorha.proto.Struct.ClanHelpItem getItemsOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchClanHelpsAns}
   */
  public static final class FetchClanHelpsAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchClanHelpsAns)
      FetchClanHelpsAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchClanHelpsAns.newBuilder() to construct.
    private FetchClanHelpsAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchClanHelpsAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchClanHelpsAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchClanHelpsAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                items_ = com.google.protobuf.MapField.newMapField(
                    ItemsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
              items__ = input.readMessage(
                  ItemsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              items_.getMutableMap().put(
                  items__.getKey(), items__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetItems();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.FetchClanHelpsAns.class, com.yorha.proto.SsClanHelp.FetchClanHelpsAns.Builder.class);
    }

    public static final int ITEMS_FIELD_NUMBER = 1;
    private static final class ItemsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>newDefaultInstance(
                  com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.Struct.ClanHelpItem.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> items_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
    internalGetItems() {
      if (items_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ItemsDefaultEntryHolder.defaultEntry);
      }
      return items_;
    }

    public int getItemsCount() {
      return internalGetItems().getMap().size();
    }
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */

    @java.lang.Override
    public boolean containsItems(
        long key) {
      
      return internalGetItems().getMap().containsKey(key);
    }
    /**
     * Use {@link #getItemsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> getItems() {
      return getItemsMap();
    }
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> getItemsMap() {
      return internalGetItems().getMap();
    }
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.ClanHelpItem getItemsOrDefault(
        long key,
        com.yorha.proto.Struct.ClanHelpItem defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> map =
          internalGetItems().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 帮助项列表
     * </pre>
     *
     * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.ClanHelpItem getItemsOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> map =
          internalGetItems().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetItems(),
          ItemsDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> entry
           : internalGetItems().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
        items__ = ItemsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, items__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.FetchClanHelpsAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.FetchClanHelpsAns other = (com.yorha.proto.SsClanHelp.FetchClanHelpsAns) obj;

      if (!internalGetItems().equals(
          other.internalGetItems())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetItems().getMap().isEmpty()) {
        hash = (37 * hash) + ITEMS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetItems().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.FetchClanHelpsAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchClanHelpsAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchClanHelpsAns)
        com.yorha.proto.SsClanHelp.FetchClanHelpsAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetItems();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableItems();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.FetchClanHelpsAns.class, com.yorha.proto.SsClanHelp.FetchClanHelpsAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.FetchClanHelpsAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableItems().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.FetchClanHelpsAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAns build() {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FetchClanHelpsAns buildPartial() {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAns result = new com.yorha.proto.SsClanHelp.FetchClanHelpsAns(this);
        int from_bitField0_ = bitField0_;
        result.items_ = internalGetItems();
        result.items_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.FetchClanHelpsAns) {
          return mergeFrom((com.yorha.proto.SsClanHelp.FetchClanHelpsAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.FetchClanHelpsAns other) {
        if (other == com.yorha.proto.SsClanHelp.FetchClanHelpsAns.getDefaultInstance()) return this;
        internalGetMutableItems().mergeFrom(
            other.internalGetItems());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.FetchClanHelpsAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.FetchClanHelpsAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> items_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
      internalGetItems() {
        if (items_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ItemsDefaultEntryHolder.defaultEntry);
        }
        return items_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
      internalGetMutableItems() {
        onChanged();;
        if (items_ == null) {
          items_ = com.google.protobuf.MapField.newMapField(
              ItemsDefaultEntryHolder.defaultEntry);
        }
        if (!items_.isMutable()) {
          items_ = items_.copy();
        }
        return items_;
      }

      public int getItemsCount() {
        return internalGetItems().getMap().size();
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */

      @java.lang.Override
      public boolean containsItems(
          long key) {
        
        return internalGetItems().getMap().containsKey(key);
      }
      /**
       * Use {@link #getItemsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> getItems() {
        return getItemsMap();
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> getItemsMap() {
        return internalGetItems().getMap();
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.ClanHelpItem getItemsOrDefault(
          long key,
          com.yorha.proto.Struct.ClanHelpItem defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> map =
            internalGetItems().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.ClanHelpItem getItemsOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> map =
            internalGetItems().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearItems() {
        internalGetMutableItems().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */

      public Builder removeItems(
          long key) {
        
        internalGetMutableItems().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem>
      getMutableItems() {
        return internalGetMutableItems().getMutableMap();
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */
      public Builder putItems(
          long key,
          com.yorha.proto.Struct.ClanHelpItem value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableItems().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 帮助项列表
       * </pre>
       *
       * <code>map&lt;int64, .com.yorha.proto.ClanHelpItem&gt; items = 1;</code>
       */

      public Builder putAllItems(
          java.util.Map<java.lang.Long, com.yorha.proto.Struct.ClanHelpItem> values) {
        internalGetMutableItems().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchClanHelpsAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchClanHelpsAns)
    private static final com.yorha.proto.SsClanHelp.FetchClanHelpsAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.FetchClanHelpsAns();
    }

    public static com.yorha.proto.SsClanHelp.FetchClanHelpsAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchClanHelpsAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchClanHelpsAns>() {
      @java.lang.Override
      public FetchClanHelpsAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchClanHelpsAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchClanHelpsAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchClanHelpsAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.FetchClanHelpsAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FinishAllClanHelpsAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FinishAllClanHelpsAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return Whether the playerName field is set.
     */
    boolean hasPlayerName();
    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return The playerName.
     */
    java.lang.String getPlayerName();
    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    com.google.protobuf.ByteString
        getPlayerNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FinishAllClanHelpsAsk}
   */
  public static final class FinishAllClanHelpsAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FinishAllClanHelpsAsk)
      FinishAllClanHelpsAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FinishAllClanHelpsAsk.newBuilder() to construct.
    private FinishAllClanHelpsAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FinishAllClanHelpsAsk() {
      playerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FinishAllClanHelpsAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FinishAllClanHelpsAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              playerName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.class, com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int PLAYERNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object playerName_;
    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return Whether the playerName field is set.
     */
    @java.lang.Override
    public boolean hasPlayerName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return The playerName.
     */
    @java.lang.Override
    public java.lang.String getPlayerName() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          playerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 玩家名称
     * </pre>
     *
     * <code>optional string playerName = 2;</code>
     * @return The bytes for playerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPlayerNameBytes() {
      java.lang.Object ref = playerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        playerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, playerName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, playerName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk other = (com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasPlayerName() != other.hasPlayerName()) return false;
      if (hasPlayerName()) {
        if (!getPlayerName()
            .equals(other.getPlayerName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasPlayerName()) {
        hash = (37 * hash) + PLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FinishAllClanHelpsAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FinishAllClanHelpsAsk)
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.class, com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        playerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk build() {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk buildPartial() {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk result = new com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.playerName_ = playerName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk) {
          return mergeFrom((com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk other) {
        if (other == com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasPlayerName()) {
          bitField0_ |= 0x00000002;
          playerName_ = other.playerName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家唯一id，用于区分是否是自己的帮助以及是否帮助过
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object playerName_ = "";
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @return Whether the playerName field is set.
       */
      public boolean hasPlayerName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @return The playerName.
       */
      public java.lang.String getPlayerName() {
        java.lang.Object ref = playerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            playerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @return The bytes for playerName.
       */
      public com.google.protobuf.ByteString
          getPlayerNameBytes() {
        java.lang.Object ref = playerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          playerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @param value The playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        playerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        playerName_ = getDefaultInstance().getPlayerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家名称
       * </pre>
       *
       * <code>optional string playerName = 2;</code>
       * @param value The bytes for playerName to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        playerName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FinishAllClanHelpsAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FinishAllClanHelpsAsk)
    private static final com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk();
    }

    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FinishAllClanHelpsAsk>
        PARSER = new com.google.protobuf.AbstractParser<FinishAllClanHelpsAsk>() {
      @java.lang.Override
      public FinishAllClanHelpsAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FinishAllClanHelpsAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FinishAllClanHelpsAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FinishAllClanHelpsAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FinishAllClanHelpsAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FinishAllClanHelpsAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 成功帮助到的帮助项条数
     * </pre>
     *
     * <code>optional int32 successHelpTimes = 1;</code>
     * @return Whether the successHelpTimes field is set.
     */
    boolean hasSuccessHelpTimes();
    /**
     * <pre>
     * 成功帮助到的帮助项条数
     * </pre>
     *
     * <code>optional int32 successHelpTimes = 1;</code>
     * @return The successHelpTimes.
     */
    int getSuccessHelpTimes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FinishAllClanHelpsAns}
   */
  public static final class FinishAllClanHelpsAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FinishAllClanHelpsAns)
      FinishAllClanHelpsAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FinishAllClanHelpsAns.newBuilder() to construct.
    private FinishAllClanHelpsAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FinishAllClanHelpsAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FinishAllClanHelpsAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FinishAllClanHelpsAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              successHelpTimes_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.class, com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.Builder.class);
    }

    private int bitField0_;
    public static final int SUCCESSHELPTIMES_FIELD_NUMBER = 1;
    private int successHelpTimes_;
    /**
     * <pre>
     * 成功帮助到的帮助项条数
     * </pre>
     *
     * <code>optional int32 successHelpTimes = 1;</code>
     * @return Whether the successHelpTimes field is set.
     */
    @java.lang.Override
    public boolean hasSuccessHelpTimes() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 成功帮助到的帮助项条数
     * </pre>
     *
     * <code>optional int32 successHelpTimes = 1;</code>
     * @return The successHelpTimes.
     */
    @java.lang.Override
    public int getSuccessHelpTimes() {
      return successHelpTimes_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, successHelpTimes_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, successHelpTimes_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns other = (com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns) obj;

      if (hasSuccessHelpTimes() != other.hasSuccessHelpTimes()) return false;
      if (hasSuccessHelpTimes()) {
        if (getSuccessHelpTimes()
            != other.getSuccessHelpTimes()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSuccessHelpTimes()) {
        hash = (37 * hash) + SUCCESSHELPTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getSuccessHelpTimes();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FinishAllClanHelpsAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FinishAllClanHelpsAns)
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.class, com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        successHelpTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns build() {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns buildPartial() {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns result = new com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.successHelpTimes_ = successHelpTimes_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns) {
          return mergeFrom((com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns other) {
        if (other == com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns.getDefaultInstance()) return this;
        if (other.hasSuccessHelpTimes()) {
          setSuccessHelpTimes(other.getSuccessHelpTimes());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int successHelpTimes_ ;
      /**
       * <pre>
       * 成功帮助到的帮助项条数
       * </pre>
       *
       * <code>optional int32 successHelpTimes = 1;</code>
       * @return Whether the successHelpTimes field is set.
       */
      @java.lang.Override
      public boolean hasSuccessHelpTimes() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 成功帮助到的帮助项条数
       * </pre>
       *
       * <code>optional int32 successHelpTimes = 1;</code>
       * @return The successHelpTimes.
       */
      @java.lang.Override
      public int getSuccessHelpTimes() {
        return successHelpTimes_;
      }
      /**
       * <pre>
       * 成功帮助到的帮助项条数
       * </pre>
       *
       * <code>optional int32 successHelpTimes = 1;</code>
       * @param value The successHelpTimes to set.
       * @return This builder for chaining.
       */
      public Builder setSuccessHelpTimes(int value) {
        bitField0_ |= 0x00000001;
        successHelpTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 成功帮助到的帮助项条数
       * </pre>
       *
       * <code>optional int32 successHelpTimes = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccessHelpTimes() {
        bitField0_ = (bitField0_ & ~0x00000001);
        successHelpTimes_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FinishAllClanHelpsAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FinishAllClanHelpsAns)
    private static final com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns();
    }

    public static com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FinishAllClanHelpsAns>
        PARSER = new com.google.protobuf.AbstractParser<FinishAllClanHelpsAns>() {
      @java.lang.Override
      public FinishAllClanHelpsAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FinishAllClanHelpsAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FinishAllClanHelpsAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FinishAllClanHelpsAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.FinishAllClanHelpsAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OnQueueTaskFinishedCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnQueueTaskFinishedCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    boolean hasQueueId();
    /**
     * <pre>
     * 队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    long getQueueId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnQueueTaskFinishedCmd}
   */
  public static final class OnQueueTaskFinishedCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnQueueTaskFinishedCmd)
      OnQueueTaskFinishedCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnQueueTaskFinishedCmd.newBuilder() to construct.
    private OnQueueTaskFinishedCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnQueueTaskFinishedCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnQueueTaskFinishedCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnQueueTaskFinishedCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              queueId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.class, com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.Builder.class);
    }

    private int bitField0_;
    public static final int QUEUEID_FIELD_NUMBER = 1;
    private long queueId_;
    /**
     * <pre>
     * 队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return Whether the queueId field is set.
     */
    @java.lang.Override
    public boolean hasQueueId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 队列id
     * </pre>
     *
     * <code>optional int64 queueId = 1;</code>
     * @return The queueId.
     */
    @java.lang.Override
    public long getQueueId() {
      return queueId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, queueId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, queueId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd other = (com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd) obj;

      if (hasQueueId() != other.hasQueueId()) return false;
      if (hasQueueId()) {
        if (getQueueId()
            != other.getQueueId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasQueueId()) {
        hash = (37 * hash) + QUEUEID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getQueueId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnQueueTaskFinishedCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnQueueTaskFinishedCmd)
        com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.class, com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        queueId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd build() {
        com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd buildPartial() {
        com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd result = new com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.queueId_ = queueId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd) {
          return mergeFrom((com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd other) {
        if (other == com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd.getDefaultInstance()) return this;
        if (other.hasQueueId()) {
          setQueueId(other.getQueueId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long queueId_ ;
      /**
       * <pre>
       * 队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return Whether the queueId field is set.
       */
      @java.lang.Override
      public boolean hasQueueId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return The queueId.
       */
      @java.lang.Override
      public long getQueueId() {
        return queueId_;
      }
      /**
       * <pre>
       * 队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @param value The queueId to set.
       * @return This builder for chaining.
       */
      public Builder setQueueId(long value) {
        bitField0_ |= 0x00000001;
        queueId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 队列id
       * </pre>
       *
       * <code>optional int64 queueId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearQueueId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        queueId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnQueueTaskFinishedCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnQueueTaskFinishedCmd)
    private static final com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd();
    }

    public static com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnQueueTaskFinishedCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnQueueTaskFinishedCmd>() {
      @java.lang.Override
      public OnQueueTaskFinishedCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnQueueTaskFinishedCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnQueueTaskFinishedCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnQueueTaskFinishedCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.OnQueueTaskFinishedCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface NeedClanHelpItemIdsNtfCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.NeedClanHelpItemIdsNtfCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    long getPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.NeedClanHelpItemIdsNtfCmd}
   */
  public static final class NeedClanHelpItemIdsNtfCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.NeedClanHelpItemIdsNtfCmd)
      NeedClanHelpItemIdsNtfCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NeedClanHelpItemIdsNtfCmd.newBuilder() to construct.
    private NeedClanHelpItemIdsNtfCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NeedClanHelpItemIdsNtfCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NeedClanHelpItemIdsNtfCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NeedClanHelpItemIdsNtfCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              playerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.class, com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.Builder.class);
    }

    private int bitField0_;
    public static final int PLAYERID_FIELD_NUMBER = 1;
    private long playerId_;
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家id
     * </pre>
     *
     * <code>optional int64 playerId = 1;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, playerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, playerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd other = (com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd) obj;

      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.NeedClanHelpItemIdsNtfCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.NeedClanHelpItemIdsNtfCmd)
        com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.class, com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanHelp.internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd build() {
        com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd buildPartial() {
        com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd result = new com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd) {
          return mergeFrom((com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd other) {
        if (other == com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd.getDefaultInstance()) return this;
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long playerId_ ;
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000001;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家id
       * </pre>
       *
       * <code>optional int64 playerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        playerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.NeedClanHelpItemIdsNtfCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.NeedClanHelpItemIdsNtfCmd)
    private static final com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd();
    }

    public static com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<NeedClanHelpItemIdsNtfCmd>
        PARSER = new com.google.protobuf.AbstractParser<NeedClanHelpItemIdsNtfCmd>() {
      @java.lang.Override
      public NeedClanHelpItemIdsNtfCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NeedClanHelpItemIdsNtfCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NeedClanHelpItemIdsNtfCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NeedClanHelpItemIdsNtfCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanHelp.NeedClanHelpItemIdsNtfCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanHelpsAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanHelpsAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FinishAllClanHelpsAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FinishAllClanHelpsAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n$ss_proto/gen/clan/ss_clan_help.proto\022\017" +
      "com.yorha.proto\032 ss_proto/gen/common/str" +
      "uct.proto\032\'ss_proto/gen/common/struct_pl" +
      "ayer.proto\"\301\001\n\026SyncPlayerQueueTaskCmd\022(\n" +
      "\004task\030\001 \001(\0132\032.com.yorha.proto.QueueTask\022" +
      "\020\n\010playerId\030\004 \001(\003\022/\n\nhelpRecord\030\005 \001(\0132\033." +
      "com.yorha.proto.ClanRecord\022\023\n\013targetLeve" +
      "l\030\006 \001(\005\022\017\n\007buildId\030\007 \001(\003\022\024\n\014maxHelpTimes" +
      "\030\010 \001(\005\"%\n\021FetchClanHelpsAsk\022\020\n\010playerId\030" +
      "\001 \001(\003\"\236\001\n\021FetchClanHelpsAns\022<\n\005items\030\001 \003" +
      "(\0132-.com.yorha.proto.FetchClanHelpsAns.I" +
      "temsEntry\032K\n\nItemsEntry\022\013\n\003key\030\001 \001(\003\022,\n\005" +
      "value\030\002 \001(\0132\035.com.yorha.proto.ClanHelpIt" +
      "em:\0028\001\"=\n\025FinishAllClanHelpsAsk\022\020\n\010playe" +
      "rId\030\001 \001(\003\022\022\n\nplayerName\030\002 \001(\t\"1\n\025FinishA" +
      "llClanHelpsAns\022\030\n\020successHelpTimes\030\001 \001(\005" +
      "\")\n\026OnQueueTaskFinishedCmd\022\017\n\007queueId\030\001 " +
      "\001(\003\"-\n\031NeedClanHelpItemIdsNtfCmd\022\020\n\010play" +
      "erId\030\001 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructPlayer.getDescriptor(),
        });
    internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncPlayerQueueTaskCmd_descriptor,
        new java.lang.String[] { "Task", "PlayerId", "HelpRecord", "TargetLevel", "BuildId", "MaxHelpTimes", });
    internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_FetchClanHelpsAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanHelpsAsk_descriptor,
        new java.lang.String[] { "PlayerId", });
    internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_FetchClanHelpsAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor,
        new java.lang.String[] { "Items", });
    internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_descriptor =
      internal_static_com_yorha_proto_FetchClanHelpsAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchClanHelpsAns_ItemsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_FinishAllClanHelpsAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FinishAllClanHelpsAsk_descriptor,
        new java.lang.String[] { "PlayerId", "PlayerName", });
    internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_FinishAllClanHelpsAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FinishAllClanHelpsAns_descriptor,
        new java.lang.String[] { "SuccessHelpTimes", });
    internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnQueueTaskFinishedCmd_descriptor,
        new java.lang.String[] { "QueueId", });
    internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_NeedClanHelpItemIdsNtfCmd_descriptor,
        new java.lang.String[] { "PlayerId", });
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructPlayer.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

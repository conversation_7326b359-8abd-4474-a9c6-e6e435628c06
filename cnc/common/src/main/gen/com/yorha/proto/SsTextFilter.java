// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/textFilter/ss_text_filter.proto

package com.yorha.proto;

public final class SsTextFilter {
  private SsTextFilter() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface CheckTextAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckTextAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
     * @return Whether the sceneId field is set.
     */
    boolean hasSceneId();
    /**
     * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
     * @return The sceneId.
     */
    com.yorha.proto.CommonEnum.UgcSceneId getSceneId();

    /**
     * <code>optional string text = 2;</code>
     * @return Whether the text field is set.
     */
    boolean hasText();
    /**
     * <code>optional string text = 2;</code>
     * @return The text.
     */
    java.lang.String getText();
    /**
     * <code>optional string text = 2;</code>
     * @return The bytes for text.
     */
    com.google.protobuf.ByteString
        getTextBytes();

    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return Whether the userInfo field is set.
     */
    boolean hasUserInfo();
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return The userInfo.
     */
    com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo();
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     */
    com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckTextAsk}
   */
  public static final class CheckTextAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckTextAsk)
      CheckTextAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckTextAsk.newBuilder() to construct.
    private CheckTextAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckTextAsk() {
      sceneId_ = 0;
      text_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckTextAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckTextAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.UgcSceneId value = com.yorha.proto.CommonEnum.UgcSceneId.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                sceneId_ = rawValue;
              }
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              text_ = bs;
              break;
            }
            case 26: {
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = userInfo_.toBuilder();
              }
              userInfo_ = input.readMessage(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(userInfo_);
                userInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTextFilter.CheckTextAsk.class, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder.class);
    }

    private int bitField0_;
    public static final int SCENEID_FIELD_NUMBER = 1;
    private int sceneId_;
    /**
     * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
     * @return Whether the sceneId field is set.
     */
    @java.lang.Override public boolean hasSceneId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
     * @return The sceneId.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.UgcSceneId getSceneId() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.UgcSceneId result = com.yorha.proto.CommonEnum.UgcSceneId.valueOf(sceneId_);
      return result == null ? com.yorha.proto.CommonEnum.UgcSceneId.USI_NONE : result;
    }

    public static final int TEXT_FIELD_NUMBER = 2;
    private volatile java.lang.Object text_;
    /**
     * <code>optional string text = 2;</code>
     * @return Whether the text field is set.
     */
    @java.lang.Override
    public boolean hasText() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string text = 2;</code>
     * @return The text.
     */
    @java.lang.Override
    public java.lang.String getText() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          text_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string text = 2;</code>
     * @return The bytes for text.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTextBytes() {
      java.lang.Object ref = text_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        text_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int USERINFO_FIELD_NUMBER = 3;
    private com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo userInfo_;
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return Whether the userInfo field is set.
     */
    @java.lang.Override
    public boolean hasUserInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return The userInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo() {
      return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
    }
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder() {
      return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, sceneId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, text_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getUserInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, sceneId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, text_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getUserInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTextFilter.CheckTextAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTextFilter.CheckTextAsk other = (com.yorha.proto.SsTextFilter.CheckTextAsk) obj;

      if (hasSceneId() != other.hasSceneId()) return false;
      if (hasSceneId()) {
        if (sceneId_ != other.sceneId_) return false;
      }
      if (hasText() != other.hasText()) return false;
      if (hasText()) {
        if (!getText()
            .equals(other.getText())) return false;
      }
      if (hasUserInfo() != other.hasUserInfo()) return false;
      if (hasUserInfo()) {
        if (!getUserInfo()
            .equals(other.getUserInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSceneId()) {
        hash = (37 * hash) + SCENEID_FIELD_NUMBER;
        hash = (53 * hash) + sceneId_;
      }
      if (hasText()) {
        hash = (37 * hash) + TEXT_FIELD_NUMBER;
        hash = (53 * hash) + getText().hashCode();
      }
      if (hasUserInfo()) {
        hash = (37 * hash) + USERINFO_FIELD_NUMBER;
        hash = (53 * hash) + getUserInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTextFilter.CheckTextAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckTextAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckTextAsk)
        com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTextFilter.CheckTextAsk.class, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsTextFilter.CheckTextAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getUserInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        sceneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        text_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
        } else {
          userInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsTextFilter.CheckTextAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAsk build() {
        com.yorha.proto.SsTextFilter.CheckTextAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAsk buildPartial() {
        com.yorha.proto.SsTextFilter.CheckTextAsk result = new com.yorha.proto.SsTextFilter.CheckTextAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.sceneId_ = sceneId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.text_ = text_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (userInfoBuilder_ == null) {
            result.userInfo_ = userInfo_;
          } else {
            result.userInfo_ = userInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTextFilter.CheckTextAsk) {
          return mergeFrom((com.yorha.proto.SsTextFilter.CheckTextAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTextFilter.CheckTextAsk other) {
        if (other == com.yorha.proto.SsTextFilter.CheckTextAsk.getDefaultInstance()) return this;
        if (other.hasSceneId()) {
          setSceneId(other.getSceneId());
        }
        if (other.hasText()) {
          bitField0_ |= 0x00000002;
          text_ = other.text_;
          onChanged();
        }
        if (other.hasUserInfo()) {
          mergeUserInfo(other.getUserInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTextFilter.CheckTextAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTextFilter.CheckTextAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int sceneId_ = 0;
      /**
       * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
       * @return Whether the sceneId field is set.
       */
      @java.lang.Override public boolean hasSceneId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
       * @return The sceneId.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.UgcSceneId getSceneId() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.UgcSceneId result = com.yorha.proto.CommonEnum.UgcSceneId.valueOf(sceneId_);
        return result == null ? com.yorha.proto.CommonEnum.UgcSceneId.USI_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
       * @param value The sceneId to set.
       * @return This builder for chaining.
       */
      public Builder setSceneId(com.yorha.proto.CommonEnum.UgcSceneId value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        sceneId_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.UgcSceneId sceneId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSceneId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        sceneId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object text_ = "";
      /**
       * <code>optional string text = 2;</code>
       * @return Whether the text field is set.
       */
      public boolean hasText() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string text = 2;</code>
       * @return The text.
       */
      public java.lang.String getText() {
        java.lang.Object ref = text_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            text_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string text = 2;</code>
       * @return The bytes for text.
       */
      public com.google.protobuf.ByteString
          getTextBytes() {
        java.lang.Object ref = text_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          text_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string text = 2;</code>
       * @param value The text to set.
       * @return This builder for chaining.
       */
      public Builder setText(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        text_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string text = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearText() {
        bitField0_ = (bitField0_ & ~0x00000002);
        text_ = getDefaultInstance().getText();
        onChanged();
        return this;
      }
      /**
       * <code>optional string text = 2;</code>
       * @param value The bytes for text to set.
       * @return This builder for chaining.
       */
      public Builder setTextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        text_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo userInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder> userInfoBuilder_;
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       * @return Whether the userInfo field is set.
       */
      public boolean hasUserInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       * @return The userInfo.
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo() {
        if (userInfoBuilder_ == null) {
          return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
        } else {
          return userInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder setUserInfo(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          userInfo_ = value;
          onChanged();
        } else {
          userInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder setUserInfo(
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder builderForValue) {
        if (userInfoBuilder_ == null) {
          userInfo_ = builderForValue.build();
          onChanged();
        } else {
          userInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder mergeUserInfo(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              userInfo_ != null &&
              userInfo_ != com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance()) {
            userInfo_ =
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.newBuilder(userInfo_).mergeFrom(value).buildPartial();
          } else {
            userInfo_ = value;
          }
          onChanged();
        } else {
          userInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder clearUserInfo() {
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
          onChanged();
        } else {
          userInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder getUserInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getUserInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder() {
        if (userInfoBuilder_ != null) {
          return userInfoBuilder_.getMessageOrBuilder();
        } else {
          return userInfo_ == null ?
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
        }
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder> 
          getUserInfoFieldBuilder() {
        if (userInfoBuilder_ == null) {
          userInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder>(
                  getUserInfo(),
                  getParentForChildren(),
                  isClean());
          userInfo_ = null;
        }
        return userInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckTextAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckTextAsk)
    private static final com.yorha.proto.SsTextFilter.CheckTextAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTextFilter.CheckTextAsk();
    }

    public static com.yorha.proto.SsTextFilter.CheckTextAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckTextAsk>
        PARSER = new com.google.protobuf.AbstractParser<CheckTextAsk>() {
      @java.lang.Override
      public CheckTextAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckTextAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckTextAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckTextAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface CheckTextAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.CheckTextAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool isLegal = 1;</code>
     * @return Whether the isLegal field is set.
     */
    boolean hasIsLegal();
    /**
     * <code>optional bool isLegal = 1;</code>
     * @return The isLegal.
     */
    boolean getIsLegal();

    /**
     * <code>optional string filteredText = 2;</code>
     * @return Whether the filteredText field is set.
     */
    boolean hasFilteredText();
    /**
     * <code>optional string filteredText = 2;</code>
     * @return The filteredText.
     */
    java.lang.String getFilteredText();
    /**
     * <code>optional string filteredText = 2;</code>
     * @return The bytes for filteredText.
     */
    com.google.protobuf.ByteString
        getFilteredTextBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.CheckTextAns}
   */
  public static final class CheckTextAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.CheckTextAns)
      CheckTextAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use CheckTextAns.newBuilder() to construct.
    private CheckTextAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private CheckTextAns() {
      filteredText_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new CheckTextAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private CheckTextAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isLegal_ = input.readBool();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              filteredText_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTextFilter.CheckTextAns.class, com.yorha.proto.SsTextFilter.CheckTextAns.Builder.class);
    }

    private int bitField0_;
    public static final int ISLEGAL_FIELD_NUMBER = 1;
    private boolean isLegal_;
    /**
     * <code>optional bool isLegal = 1;</code>
     * @return Whether the isLegal field is set.
     */
    @java.lang.Override
    public boolean hasIsLegal() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool isLegal = 1;</code>
     * @return The isLegal.
     */
    @java.lang.Override
    public boolean getIsLegal() {
      return isLegal_;
    }

    public static final int FILTEREDTEXT_FIELD_NUMBER = 2;
    private volatile java.lang.Object filteredText_;
    /**
     * <code>optional string filteredText = 2;</code>
     * @return Whether the filteredText field is set.
     */
    @java.lang.Override
    public boolean hasFilteredText() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string filteredText = 2;</code>
     * @return The filteredText.
     */
    @java.lang.Override
    public java.lang.String getFilteredText() {
      java.lang.Object ref = filteredText_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          filteredText_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string filteredText = 2;</code>
     * @return The bytes for filteredText.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getFilteredTextBytes() {
      java.lang.Object ref = filteredText_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        filteredText_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isLegal_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, filteredText_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isLegal_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, filteredText_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTextFilter.CheckTextAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTextFilter.CheckTextAns other = (com.yorha.proto.SsTextFilter.CheckTextAns) obj;

      if (hasIsLegal() != other.hasIsLegal()) return false;
      if (hasIsLegal()) {
        if (getIsLegal()
            != other.getIsLegal()) return false;
      }
      if (hasFilteredText() != other.hasFilteredText()) return false;
      if (hasFilteredText()) {
        if (!getFilteredText()
            .equals(other.getFilteredText())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsLegal()) {
        hash = (37 * hash) + ISLEGAL_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsLegal());
      }
      if (hasFilteredText()) {
        hash = (37 * hash) + FILTEREDTEXT_FIELD_NUMBER;
        hash = (53 * hash) + getFilteredText().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.CheckTextAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTextFilter.CheckTextAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.CheckTextAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.CheckTextAns)
        com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTextFilter.CheckTextAns.class, com.yorha.proto.SsTextFilter.CheckTextAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsTextFilter.CheckTextAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isLegal_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        filteredText_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_CheckTextAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAns getDefaultInstanceForType() {
        return com.yorha.proto.SsTextFilter.CheckTextAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAns build() {
        com.yorha.proto.SsTextFilter.CheckTextAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.CheckTextAns buildPartial() {
        com.yorha.proto.SsTextFilter.CheckTextAns result = new com.yorha.proto.SsTextFilter.CheckTextAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isLegal_ = isLegal_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.filteredText_ = filteredText_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTextFilter.CheckTextAns) {
          return mergeFrom((com.yorha.proto.SsTextFilter.CheckTextAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTextFilter.CheckTextAns other) {
        if (other == com.yorha.proto.SsTextFilter.CheckTextAns.getDefaultInstance()) return this;
        if (other.hasIsLegal()) {
          setIsLegal(other.getIsLegal());
        }
        if (other.hasFilteredText()) {
          bitField0_ |= 0x00000002;
          filteredText_ = other.filteredText_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTextFilter.CheckTextAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTextFilter.CheckTextAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isLegal_ ;
      /**
       * <code>optional bool isLegal = 1;</code>
       * @return Whether the isLegal field is set.
       */
      @java.lang.Override
      public boolean hasIsLegal() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool isLegal = 1;</code>
       * @return The isLegal.
       */
      @java.lang.Override
      public boolean getIsLegal() {
        return isLegal_;
      }
      /**
       * <code>optional bool isLegal = 1;</code>
       * @param value The isLegal to set.
       * @return This builder for chaining.
       */
      public Builder setIsLegal(boolean value) {
        bitField0_ |= 0x00000001;
        isLegal_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool isLegal = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsLegal() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isLegal_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object filteredText_ = "";
      /**
       * <code>optional string filteredText = 2;</code>
       * @return Whether the filteredText field is set.
       */
      public boolean hasFilteredText() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string filteredText = 2;</code>
       * @return The filteredText.
       */
      public java.lang.String getFilteredText() {
        java.lang.Object ref = filteredText_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            filteredText_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string filteredText = 2;</code>
       * @return The bytes for filteredText.
       */
      public com.google.protobuf.ByteString
          getFilteredTextBytes() {
        java.lang.Object ref = filteredText_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          filteredText_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string filteredText = 2;</code>
       * @param value The filteredText to set.
       * @return This builder for chaining.
       */
      public Builder setFilteredText(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        filteredText_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string filteredText = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearFilteredText() {
        bitField0_ = (bitField0_ & ~0x00000002);
        filteredText_ = getDefaultInstance().getFilteredText();
        onChanged();
        return this;
      }
      /**
       * <code>optional string filteredText = 2;</code>
       * @param value The bytes for filteredText to set.
       * @return This builder for chaining.
       */
      public Builder setFilteredTextBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        filteredText_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.CheckTextAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.CheckTextAns)
    private static final com.yorha.proto.SsTextFilter.CheckTextAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTextFilter.CheckTextAns();
    }

    public static com.yorha.proto.SsTextFilter.CheckTextAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<CheckTextAns>
        PARSER = new com.google.protobuf.AbstractParser<CheckTextAns>() {
      @java.lang.Override
      public CheckTextAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new CheckTextAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<CheckTextAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<CheckTextAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchCheckTextAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchCheckTextAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk> 
        getTextsList();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    com.yorha.proto.SsTextFilter.CheckTextAsk getTexts(int index);
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    int getTextsCount();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder> 
        getTextsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder getTextsOrBuilder(
        int index);

    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return Whether the userInfo field is set.
     */
    boolean hasUserInfo();
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return The userInfo.
     */
    com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo();
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     */
    com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchCheckTextAsk}
   */
  public static final class BatchCheckTextAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchCheckTextAsk)
      BatchCheckTextAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchCheckTextAsk.newBuilder() to construct.
    private BatchCheckTextAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchCheckTextAsk() {
      texts_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchCheckTextAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchCheckTextAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                texts_ = new java.util.ArrayList<com.yorha.proto.SsTextFilter.CheckTextAsk>();
                mutable_bitField0_ |= 0x00000001;
              }
              texts_.add(
                  input.readMessage(com.yorha.proto.SsTextFilter.CheckTextAsk.PARSER, extensionRegistry));
              break;
            }
            case 26: {
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = userInfo_.toBuilder();
              }
              userInfo_ = input.readMessage(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(userInfo_);
                userInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          texts_ = java.util.Collections.unmodifiableList(texts_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTextFilter.BatchCheckTextAsk.class, com.yorha.proto.SsTextFilter.BatchCheckTextAsk.Builder.class);
    }

    private int bitField0_;
    public static final int TEXTS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk> texts_;
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk> getTextsList() {
      return texts_;
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder> 
        getTextsOrBuilderList() {
      return texts_;
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    @java.lang.Override
    public int getTextsCount() {
      return texts_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAsk getTexts(int index) {
      return texts_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder getTextsOrBuilder(
        int index) {
      return texts_.get(index);
    }

    public static final int USERINFO_FIELD_NUMBER = 3;
    private com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo userInfo_;
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return Whether the userInfo field is set.
     */
    @java.lang.Override
    public boolean hasUserInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     * @return The userInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo() {
      return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
    }
    /**
     * <pre>
     * tsssdk模式下才会填充
     * </pre>
     *
     * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder() {
      return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < texts_.size(); i++) {
        output.writeMessage(1, texts_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(3, getUserInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < texts_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, texts_.get(i));
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getUserInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTextFilter.BatchCheckTextAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTextFilter.BatchCheckTextAsk other = (com.yorha.proto.SsTextFilter.BatchCheckTextAsk) obj;

      if (!getTextsList()
          .equals(other.getTextsList())) return false;
      if (hasUserInfo() != other.hasUserInfo()) return false;
      if (hasUserInfo()) {
        if (!getUserInfo()
            .equals(other.getUserInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getTextsCount() > 0) {
        hash = (37 * hash) + TEXTS_FIELD_NUMBER;
        hash = (53 * hash) + getTextsList().hashCode();
      }
      if (hasUserInfo()) {
        hash = (37 * hash) + USERINFO_FIELD_NUMBER;
        hash = (53 * hash) + getUserInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTextFilter.BatchCheckTextAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchCheckTextAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchCheckTextAsk)
        com.yorha.proto.SsTextFilter.BatchCheckTextAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTextFilter.BatchCheckTextAsk.class, com.yorha.proto.SsTextFilter.BatchCheckTextAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsTextFilter.BatchCheckTextAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getTextsFieldBuilder();
          getUserInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (textsBuilder_ == null) {
          texts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          textsBuilder_.clear();
        }
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
        } else {
          userInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsTextFilter.BatchCheckTextAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAsk build() {
        com.yorha.proto.SsTextFilter.BatchCheckTextAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAsk buildPartial() {
        com.yorha.proto.SsTextFilter.BatchCheckTextAsk result = new com.yorha.proto.SsTextFilter.BatchCheckTextAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (textsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            texts_ = java.util.Collections.unmodifiableList(texts_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.texts_ = texts_;
        } else {
          result.texts_ = textsBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (userInfoBuilder_ == null) {
            result.userInfo_ = userInfo_;
          } else {
            result.userInfo_ = userInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTextFilter.BatchCheckTextAsk) {
          return mergeFrom((com.yorha.proto.SsTextFilter.BatchCheckTextAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTextFilter.BatchCheckTextAsk other) {
        if (other == com.yorha.proto.SsTextFilter.BatchCheckTextAsk.getDefaultInstance()) return this;
        if (textsBuilder_ == null) {
          if (!other.texts_.isEmpty()) {
            if (texts_.isEmpty()) {
              texts_ = other.texts_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureTextsIsMutable();
              texts_.addAll(other.texts_);
            }
            onChanged();
          }
        } else {
          if (!other.texts_.isEmpty()) {
            if (textsBuilder_.isEmpty()) {
              textsBuilder_.dispose();
              textsBuilder_ = null;
              texts_ = other.texts_;
              bitField0_ = (bitField0_ & ~0x00000001);
              textsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getTextsFieldBuilder() : null;
            } else {
              textsBuilder_.addAllMessages(other.texts_);
            }
          }
        }
        if (other.hasUserInfo()) {
          mergeUserInfo(other.getUserInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTextFilter.BatchCheckTextAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTextFilter.BatchCheckTextAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk> texts_ =
        java.util.Collections.emptyList();
      private void ensureTextsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          texts_ = new java.util.ArrayList<com.yorha.proto.SsTextFilter.CheckTextAsk>(texts_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsTextFilter.CheckTextAsk, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder, com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder> textsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk> getTextsList() {
        if (textsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(texts_);
        } else {
          return textsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public int getTextsCount() {
        if (textsBuilder_ == null) {
          return texts_.size();
        } else {
          return textsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAsk getTexts(int index) {
        if (textsBuilder_ == null) {
          return texts_.get(index);
        } else {
          return textsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder setTexts(
          int index, com.yorha.proto.SsTextFilter.CheckTextAsk value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.set(index, value);
          onChanged();
        } else {
          textsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder setTexts(
          int index, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.set(index, builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder addTexts(com.yorha.proto.SsTextFilter.CheckTextAsk value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.add(value);
          onChanged();
        } else {
          textsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder addTexts(
          int index, com.yorha.proto.SsTextFilter.CheckTextAsk value) {
        if (textsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureTextsIsMutable();
          texts_.add(index, value);
          onChanged();
        } else {
          textsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder addTexts(
          com.yorha.proto.SsTextFilter.CheckTextAsk.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.add(builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder addTexts(
          int index, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder builderForValue) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.add(index, builderForValue.build());
          onChanged();
        } else {
          textsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder addAllTexts(
          java.lang.Iterable<? extends com.yorha.proto.SsTextFilter.CheckTextAsk> values) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, texts_);
          onChanged();
        } else {
          textsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder clearTexts() {
        if (textsBuilder_ == null) {
          texts_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          textsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public Builder removeTexts(int index) {
        if (textsBuilder_ == null) {
          ensureTextsIsMutable();
          texts_.remove(index);
          onChanged();
        } else {
          textsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAsk.Builder getTextsBuilder(
          int index) {
        return getTextsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder getTextsOrBuilder(
          int index) {
        if (textsBuilder_ == null) {
          return texts_.get(index);  } else {
          return textsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder> 
           getTextsOrBuilderList() {
        if (textsBuilder_ != null) {
          return textsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(texts_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAsk.Builder addTextsBuilder() {
        return getTextsFieldBuilder().addBuilder(
            com.yorha.proto.SsTextFilter.CheckTextAsk.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAsk.Builder addTextsBuilder(
          int index) {
        return getTextsFieldBuilder().addBuilder(
            index, com.yorha.proto.SsTextFilter.CheckTextAsk.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAsk texts = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAsk.Builder> 
           getTextsBuilderList() {
        return getTextsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsTextFilter.CheckTextAsk, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder, com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder> 
          getTextsFieldBuilder() {
        if (textsBuilder_ == null) {
          textsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.SsTextFilter.CheckTextAsk, com.yorha.proto.SsTextFilter.CheckTextAsk.Builder, com.yorha.proto.SsTextFilter.CheckTextAskOrBuilder>(
                  texts_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          texts_ = null;
        }
        return textsBuilder_;
      }

      private com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo userInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder> userInfoBuilder_;
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       * @return Whether the userInfo field is set.
       */
      public boolean hasUserInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       * @return The userInfo.
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo getUserInfo() {
        if (userInfoBuilder_ == null) {
          return userInfo_ == null ? com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
        } else {
          return userInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder setUserInfo(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          userInfo_ = value;
          onChanged();
        } else {
          userInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder setUserInfo(
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder builderForValue) {
        if (userInfoBuilder_ == null) {
          userInfo_ = builderForValue.build();
          onChanged();
        } else {
          userInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder mergeUserInfo(com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo value) {
        if (userInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              userInfo_ != null &&
              userInfo_ != com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance()) {
            userInfo_ =
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.newBuilder(userInfo_).mergeFrom(value).buildPartial();
          } else {
            userInfo_ = value;
          }
          onChanged();
        } else {
          userInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public Builder clearUserInfo() {
        if (userInfoBuilder_ == null) {
          userInfo_ = null;
          onChanged();
        } else {
          userInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder getUserInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getUserInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      public com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder getUserInfoOrBuilder() {
        if (userInfoBuilder_ != null) {
          return userInfoBuilder_.getMessageOrBuilder();
        } else {
          return userInfo_ == null ?
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.getDefaultInstance() : userInfo_;
        }
      }
      /**
       * <pre>
       * tsssdk模式下才会填充
       * </pre>
       *
       * <code>optional .com.yorha.proto.TsssdkJudgeUserInfo userInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder> 
          getUserInfoFieldBuilder() {
        if (userInfoBuilder_ == null) {
          userInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfo.Builder, com.yorha.proto.CommonMsg.TsssdkJudgeUserInfoOrBuilder>(
                  getUserInfo(),
                  getParentForChildren(),
                  isClean());
          userInfo_ = null;
        }
        return userInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchCheckTextAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchCheckTextAsk)
    private static final com.yorha.proto.SsTextFilter.BatchCheckTextAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTextFilter.BatchCheckTextAsk();
    }

    public static com.yorha.proto.SsTextFilter.BatchCheckTextAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchCheckTextAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchCheckTextAsk>() {
      @java.lang.Override
      public BatchCheckTextAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchCheckTextAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchCheckTextAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchCheckTextAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTextFilter.BatchCheckTextAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchCheckTextAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchCheckTextAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns> 
        getResultsList();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    com.yorha.proto.SsTextFilter.CheckTextAns getResults(int index);
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    int getResultsCount();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder> 
        getResultsOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder getResultsOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchCheckTextAns}
   */
  public static final class BatchCheckTextAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchCheckTextAns)
      BatchCheckTextAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchCheckTextAns.newBuilder() to construct.
    private BatchCheckTextAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchCheckTextAns() {
      results_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchCheckTextAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchCheckTextAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                results_ = new java.util.ArrayList<com.yorha.proto.SsTextFilter.CheckTextAns>();
                mutable_bitField0_ |= 0x00000001;
              }
              results_.add(
                  input.readMessage(com.yorha.proto.SsTextFilter.CheckTextAns.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          results_ = java.util.Collections.unmodifiableList(results_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsTextFilter.BatchCheckTextAns.class, com.yorha.proto.SsTextFilter.BatchCheckTextAns.Builder.class);
    }

    public static final int RESULTS_FIELD_NUMBER = 1;
    private java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns> results_;
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns> getResultsList() {
      return results_;
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder> 
        getResultsOrBuilderList() {
      return results_;
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    @java.lang.Override
    public int getResultsCount() {
      return results_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAns getResults(int index) {
      return results_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder getResultsOrBuilder(
        int index) {
      return results_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < results_.size(); i++) {
        output.writeMessage(1, results_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (int i = 0; i < results_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, results_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsTextFilter.BatchCheckTextAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsTextFilter.BatchCheckTextAns other = (com.yorha.proto.SsTextFilter.BatchCheckTextAns) obj;

      if (!getResultsList()
          .equals(other.getResultsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getResultsCount() > 0) {
        hash = (37 * hash) + RESULTS_FIELD_NUMBER;
        hash = (53 * hash) + getResultsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsTextFilter.BatchCheckTextAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchCheckTextAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchCheckTextAns)
        com.yorha.proto.SsTextFilter.BatchCheckTextAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsTextFilter.BatchCheckTextAns.class, com.yorha.proto.SsTextFilter.BatchCheckTextAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsTextFilter.BatchCheckTextAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getResultsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (resultsBuilder_ == null) {
          results_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
        } else {
          resultsBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsTextFilter.internal_static_com_yorha_proto_BatchCheckTextAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAns getDefaultInstanceForType() {
        return com.yorha.proto.SsTextFilter.BatchCheckTextAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAns build() {
        com.yorha.proto.SsTextFilter.BatchCheckTextAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsTextFilter.BatchCheckTextAns buildPartial() {
        com.yorha.proto.SsTextFilter.BatchCheckTextAns result = new com.yorha.proto.SsTextFilter.BatchCheckTextAns(this);
        int from_bitField0_ = bitField0_;
        if (resultsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0)) {
            results_ = java.util.Collections.unmodifiableList(results_);
            bitField0_ = (bitField0_ & ~0x00000001);
          }
          result.results_ = results_;
        } else {
          result.results_ = resultsBuilder_.build();
        }
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsTextFilter.BatchCheckTextAns) {
          return mergeFrom((com.yorha.proto.SsTextFilter.BatchCheckTextAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsTextFilter.BatchCheckTextAns other) {
        if (other == com.yorha.proto.SsTextFilter.BatchCheckTextAns.getDefaultInstance()) return this;
        if (resultsBuilder_ == null) {
          if (!other.results_.isEmpty()) {
            if (results_.isEmpty()) {
              results_ = other.results_;
              bitField0_ = (bitField0_ & ~0x00000001);
            } else {
              ensureResultsIsMutable();
              results_.addAll(other.results_);
            }
            onChanged();
          }
        } else {
          if (!other.results_.isEmpty()) {
            if (resultsBuilder_.isEmpty()) {
              resultsBuilder_.dispose();
              resultsBuilder_ = null;
              results_ = other.results_;
              bitField0_ = (bitField0_ & ~0x00000001);
              resultsBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getResultsFieldBuilder() : null;
            } else {
              resultsBuilder_.addAllMessages(other.results_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsTextFilter.BatchCheckTextAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsTextFilter.BatchCheckTextAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns> results_ =
        java.util.Collections.emptyList();
      private void ensureResultsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          results_ = new java.util.ArrayList<com.yorha.proto.SsTextFilter.CheckTextAns>(results_);
          bitField0_ |= 0x00000001;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsTextFilter.CheckTextAns, com.yorha.proto.SsTextFilter.CheckTextAns.Builder, com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder> resultsBuilder_;

      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns> getResultsList() {
        if (resultsBuilder_ == null) {
          return java.util.Collections.unmodifiableList(results_);
        } else {
          return resultsBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public int getResultsCount() {
        if (resultsBuilder_ == null) {
          return results_.size();
        } else {
          return resultsBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAns getResults(int index) {
        if (resultsBuilder_ == null) {
          return results_.get(index);
        } else {
          return resultsBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder setResults(
          int index, com.yorha.proto.SsTextFilter.CheckTextAns value) {
        if (resultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultsIsMutable();
          results_.set(index, value);
          onChanged();
        } else {
          resultsBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder setResults(
          int index, com.yorha.proto.SsTextFilter.CheckTextAns.Builder builderForValue) {
        if (resultsBuilder_ == null) {
          ensureResultsIsMutable();
          results_.set(index, builderForValue.build());
          onChanged();
        } else {
          resultsBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder addResults(com.yorha.proto.SsTextFilter.CheckTextAns value) {
        if (resultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultsIsMutable();
          results_.add(value);
          onChanged();
        } else {
          resultsBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder addResults(
          int index, com.yorha.proto.SsTextFilter.CheckTextAns value) {
        if (resultsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureResultsIsMutable();
          results_.add(index, value);
          onChanged();
        } else {
          resultsBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder addResults(
          com.yorha.proto.SsTextFilter.CheckTextAns.Builder builderForValue) {
        if (resultsBuilder_ == null) {
          ensureResultsIsMutable();
          results_.add(builderForValue.build());
          onChanged();
        } else {
          resultsBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder addResults(
          int index, com.yorha.proto.SsTextFilter.CheckTextAns.Builder builderForValue) {
        if (resultsBuilder_ == null) {
          ensureResultsIsMutable();
          results_.add(index, builderForValue.build());
          onChanged();
        } else {
          resultsBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder addAllResults(
          java.lang.Iterable<? extends com.yorha.proto.SsTextFilter.CheckTextAns> values) {
        if (resultsBuilder_ == null) {
          ensureResultsIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, results_);
          onChanged();
        } else {
          resultsBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder clearResults() {
        if (resultsBuilder_ == null) {
          results_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000001);
          onChanged();
        } else {
          resultsBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public Builder removeResults(int index) {
        if (resultsBuilder_ == null) {
          ensureResultsIsMutable();
          results_.remove(index);
          onChanged();
        } else {
          resultsBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAns.Builder getResultsBuilder(
          int index) {
        return getResultsFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder getResultsOrBuilder(
          int index) {
        if (resultsBuilder_ == null) {
          return results_.get(index);  } else {
          return resultsBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public java.util.List<? extends com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder> 
           getResultsOrBuilderList() {
        if (resultsBuilder_ != null) {
          return resultsBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(results_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAns.Builder addResultsBuilder() {
        return getResultsFieldBuilder().addBuilder(
            com.yorha.proto.SsTextFilter.CheckTextAns.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public com.yorha.proto.SsTextFilter.CheckTextAns.Builder addResultsBuilder(
          int index) {
        return getResultsFieldBuilder().addBuilder(
            index, com.yorha.proto.SsTextFilter.CheckTextAns.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.CheckTextAns results = 1;</code>
       */
      public java.util.List<com.yorha.proto.SsTextFilter.CheckTextAns.Builder> 
           getResultsBuilderList() {
        return getResultsFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsTextFilter.CheckTextAns, com.yorha.proto.SsTextFilter.CheckTextAns.Builder, com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder> 
          getResultsFieldBuilder() {
        if (resultsBuilder_ == null) {
          resultsBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.SsTextFilter.CheckTextAns, com.yorha.proto.SsTextFilter.CheckTextAns.Builder, com.yorha.proto.SsTextFilter.CheckTextAnsOrBuilder>(
                  results_,
                  ((bitField0_ & 0x00000001) != 0),
                  getParentForChildren(),
                  isClean());
          results_ = null;
        }
        return resultsBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchCheckTextAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchCheckTextAns)
    private static final com.yorha.proto.SsTextFilter.BatchCheckTextAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsTextFilter.BatchCheckTextAns();
    }

    public static com.yorha.proto.SsTextFilter.BatchCheckTextAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchCheckTextAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchCheckTextAns>() {
      @java.lang.Override
      public BatchCheckTextAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchCheckTextAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchCheckTextAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchCheckTextAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsTextFilter.BatchCheckTextAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckTextAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckTextAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_CheckTextAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_CheckTextAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchCheckTextAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchCheckTextAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchCheckTextAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,ss_proto/gen/textFilter/ss_text_filter" +
      ".proto\022\017com.yorha.proto\032%ss_proto/gen/co" +
      "mmon/common_enum.proto\032$ss_proto/gen/com" +
      "mon/common_msg.proto\"\202\001\n\014CheckTextAsk\022,\n" +
      "\007sceneId\030\001 \001(\0162\033.com.yorha.proto.UgcScen" +
      "eId\022\014\n\004text\030\002 \001(\t\0226\n\010userInfo\030\003 \001(\0132$.co" +
      "m.yorha.proto.TsssdkJudgeUserInfo\"5\n\014Che" +
      "ckTextAns\022\017\n\007isLegal\030\001 \001(\010\022\024\n\014filteredTe" +
      "xt\030\002 \001(\t\"y\n\021BatchCheckTextAsk\022,\n\005texts\030\001" +
      " \003(\0132\035.com.yorha.proto.CheckTextAsk\0226\n\010u" +
      "serInfo\030\003 \001(\0132$.com.yorha.proto.TsssdkJu" +
      "dgeUserInfo\"C\n\021BatchCheckTextAns\022.\n\007resu" +
      "lts\030\001 \003(\0132\035.com.yorha.proto.CheckTextAns" +
      "B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_CheckTextAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_CheckTextAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckTextAsk_descriptor,
        new java.lang.String[] { "SceneId", "Text", "UserInfo", });
    internal_static_com_yorha_proto_CheckTextAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_CheckTextAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_CheckTextAns_descriptor,
        new java.lang.String[] { "IsLegal", "FilteredText", });
    internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_BatchCheckTextAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchCheckTextAsk_descriptor,
        new java.lang.String[] { "Texts", "UserInfo", });
    internal_static_com_yorha_proto_BatchCheckTextAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_BatchCheckTextAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchCheckTextAns_descriptor,
        new java.lang.String[] { "Results", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

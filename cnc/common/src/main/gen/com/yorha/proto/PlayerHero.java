// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_hero.proto

package com.yorha.proto;

public final class PlayerHero {
  private PlayerHero() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_UnLockHero_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_UnLockHero_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * 英雄解锁
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_UnLockHero_C2S}
   */
  public static final class Player_UnLockHero_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_UnLockHero_C2S)
      Player_UnLockHero_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_UnLockHero_C2S.newBuilder() to construct.
    private Player_UnLockHero_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_UnLockHero_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_UnLockHero_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_UnLockHero_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.class, com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_UnLockHero_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_UnLockHero_C2S other = (com.yorha.proto.PlayerHero.Player_UnLockHero_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_UnLockHero_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄解锁
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_UnLockHero_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_UnLockHero_C2S)
        com.yorha.proto.PlayerHero.Player_UnLockHero_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.class, com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_C2S build() {
        com.yorha.proto.PlayerHero.Player_UnLockHero_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_UnLockHero_C2S result = new com.yorha.proto.PlayerHero.Player_UnLockHero_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_UnLockHero_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_UnLockHero_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_UnLockHero_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_UnLockHero_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_UnLockHero_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_UnLockHero_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_UnLockHero_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_UnLockHero_C2S)
    private static final com.yorha.proto.PlayerHero.Player_UnLockHero_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_UnLockHero_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_UnLockHero_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_UnLockHero_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_UnLockHero_C2S>() {
      @java.lang.Override
      public Player_UnLockHero_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_UnLockHero_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_UnLockHero_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_UnLockHero_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_UnLockHero_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_UnLockHero_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_UnLockHero_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_UnLockHero_S2C}
   */
  public static final class Player_UnLockHero_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_UnLockHero_S2C)
      Player_UnLockHero_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_UnLockHero_S2C.newBuilder() to construct.
    private Player_UnLockHero_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_UnLockHero_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_UnLockHero_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_UnLockHero_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.class, com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_UnLockHero_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_UnLockHero_S2C other = (com.yorha.proto.PlayerHero.Player_UnLockHero_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_UnLockHero_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_UnLockHero_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_UnLockHero_S2C)
        com.yorha.proto.PlayerHero.Player_UnLockHero_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.class, com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_S2C build() {
        com.yorha.proto.PlayerHero.Player_UnLockHero_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_UnLockHero_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_UnLockHero_S2C result = new com.yorha.proto.PlayerHero.Player_UnLockHero_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_UnLockHero_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_UnLockHero_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_UnLockHero_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_UnLockHero_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_UnLockHero_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_UnLockHero_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_UnLockHero_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_UnLockHero_S2C)
    private static final com.yorha.proto.PlayerHero.Player_UnLockHero_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_UnLockHero_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_UnLockHero_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_UnLockHero_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_UnLockHero_S2C>() {
      @java.lang.Override
      public Player_UnLockHero_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_UnLockHero_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_UnLockHero_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_UnLockHero_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_UnLockHero_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroLevelUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroLevelUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * 英雄升级
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroLevelUp_C2S}
   */
  public static final class Player_HeroLevelUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroLevelUp_C2S)
      Player_HeroLevelUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroLevelUp_C2S.newBuilder() to construct.
    private Player_HeroLevelUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroLevelUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroLevelUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroLevelUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S other = (com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄升级
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroLevelUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroLevelUp_C2S)
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S result = new com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroLevelUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroLevelUp_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroLevelUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroLevelUp_C2S>() {
      @java.lang.Override
      public Player_HeroLevelUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroLevelUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroLevelUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroLevelUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroLevelUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroLevelUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroLevelUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroLevelUp_S2C}
   */
  public static final class Player_HeroLevelUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroLevelUp_S2C)
      Player_HeroLevelUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroLevelUp_S2C.newBuilder() to construct.
    private Player_HeroLevelUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroLevelUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroLevelUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroLevelUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C other = (com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroLevelUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroLevelUp_S2C)
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C result = new com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroLevelUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroLevelUp_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroLevelUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroLevelUp_S2C>() {
      @java.lang.Override
      public Player_HeroLevelUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroLevelUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroLevelUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroLevelUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroLevelUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroStageUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroStageUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <pre>
     * 是否使用通用碎片
     * </pre>
     *
     * <code>optional bool common = 2;</code>
     * @return Whether the common field is set.
     */
    boolean hasCommon();
    /**
     * <pre>
     * 是否使用通用碎片
     * </pre>
     *
     * <code>optional bool common = 2;</code>
     * @return The common.
     */
    boolean getCommon();
  }
  /**
   * <pre>
   * 英雄升星
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroStageUp_C2S}
   */
  public static final class Player_HeroStageUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroStageUp_C2S)
      Player_HeroStageUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroStageUp_C2S.newBuilder() to construct.
    private Player_HeroStageUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroStageUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroStageUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroStageUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              common_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int COMMON_FIELD_NUMBER = 2;
    private boolean common_;
    /**
     * <pre>
     * 是否使用通用碎片
     * </pre>
     *
     * <code>optional bool common = 2;</code>
     * @return Whether the common field is set.
     */
    @java.lang.Override
    public boolean hasCommon() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否使用通用碎片
     * </pre>
     *
     * <code>optional bool common = 2;</code>
     * @return The common.
     */
    @java.lang.Override
    public boolean getCommon() {
      return common_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(2, common_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(2, common_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S other = (com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasCommon() != other.hasCommon()) return false;
      if (hasCommon()) {
        if (getCommon()
            != other.getCommon()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasCommon()) {
        hash = (37 * hash) + COMMON_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCommon());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄升星
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroStageUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroStageUp_C2S)
        com.yorha.proto.PlayerHero.Player_HeroStageUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        common_ = false;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S result = new com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.common_ = common_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasCommon()) {
          setCommon(other.getCommon());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private boolean common_ ;
      /**
       * <pre>
       * 是否使用通用碎片
       * </pre>
       *
       * <code>optional bool common = 2;</code>
       * @return Whether the common field is set.
       */
      @java.lang.Override
      public boolean hasCommon() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 是否使用通用碎片
       * </pre>
       *
       * <code>optional bool common = 2;</code>
       * @return The common.
       */
      @java.lang.Override
      public boolean getCommon() {
        return common_;
      }
      /**
       * <pre>
       * 是否使用通用碎片
       * </pre>
       *
       * <code>optional bool common = 2;</code>
       * @param value The common to set.
       * @return This builder for chaining.
       */
      public Builder setCommon(boolean value) {
        bitField0_ |= 0x00000002;
        common_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否使用通用碎片
       * </pre>
       *
       * <code>optional bool common = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCommon() {
        bitField0_ = (bitField0_ & ~0x00000002);
        common_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroStageUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroStageUp_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroStageUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroStageUp_C2S>() {
      @java.lang.Override
      public Player_HeroStageUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroStageUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroStageUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroStageUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroStageUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroStageUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroStageUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroStageUp_S2C}
   */
  public static final class Player_HeroStageUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroStageUp_S2C)
      Player_HeroStageUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroStageUp_S2C.newBuilder() to construct.
    private Player_HeroStageUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroStageUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroStageUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroStageUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C other = (com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroStageUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroStageUp_S2C)
        com.yorha.proto.PlayerHero.Player_HeroStageUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C result = new com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroStageUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroStageUp_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroStageUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroStageUp_S2C>() {
      @java.lang.Override
      public Player_HeroStageUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroStageUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroStageUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroStageUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroStageUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroSkillLevelUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroSkillLevelUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <pre>
     * 技能组id
     * </pre>
     *
     * <code>optional int32 skillGroupId = 2;</code>
     * @return Whether the skillGroupId field is set.
     */
    boolean hasSkillGroupId();
    /**
     * <pre>
     * 技能组id
     * </pre>
     *
     * <code>optional int32 skillGroupId = 2;</code>
     * @return The skillGroupId.
     */
    int getSkillGroupId();
  }
  /**
   * <pre>
   * 英雄升技能
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroSkillLevelUp_C2S}
   */
  public static final class Player_HeroSkillLevelUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroSkillLevelUp_C2S)
      Player_HeroSkillLevelUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroSkillLevelUp_C2S.newBuilder() to construct.
    private Player_HeroSkillLevelUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroSkillLevelUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroSkillLevelUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroSkillLevelUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              skillGroupId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int SKILLGROUPID_FIELD_NUMBER = 2;
    private int skillGroupId_;
    /**
     * <pre>
     * 技能组id
     * </pre>
     *
     * <code>optional int32 skillGroupId = 2;</code>
     * @return Whether the skillGroupId field is set.
     */
    @java.lang.Override
    public boolean hasSkillGroupId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 技能组id
     * </pre>
     *
     * <code>optional int32 skillGroupId = 2;</code>
     * @return The skillGroupId.
     */
    @java.lang.Override
    public int getSkillGroupId() {
      return skillGroupId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, skillGroupId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, skillGroupId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S other = (com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasSkillGroupId() != other.hasSkillGroupId()) return false;
      if (hasSkillGroupId()) {
        if (getSkillGroupId()
            != other.getSkillGroupId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasSkillGroupId()) {
        hash = (37 * hash) + SKILLGROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getSkillGroupId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄升技能
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroSkillLevelUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroSkillLevelUp_C2S)
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        skillGroupId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S result = new com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.skillGroupId_ = skillGroupId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasSkillGroupId()) {
          setSkillGroupId(other.getSkillGroupId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int skillGroupId_ ;
      /**
       * <pre>
       * 技能组id
       * </pre>
       *
       * <code>optional int32 skillGroupId = 2;</code>
       * @return Whether the skillGroupId field is set.
       */
      @java.lang.Override
      public boolean hasSkillGroupId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 技能组id
       * </pre>
       *
       * <code>optional int32 skillGroupId = 2;</code>
       * @return The skillGroupId.
       */
      @java.lang.Override
      public int getSkillGroupId() {
        return skillGroupId_;
      }
      /**
       * <pre>
       * 技能组id
       * </pre>
       *
       * <code>optional int32 skillGroupId = 2;</code>
       * @param value The skillGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setSkillGroupId(int value) {
        bitField0_ |= 0x00000002;
        skillGroupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能组id
       * </pre>
       *
       * <code>optional int32 skillGroupId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSkillGroupId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        skillGroupId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroSkillLevelUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroSkillLevelUp_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroSkillLevelUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroSkillLevelUp_C2S>() {
      @java.lang.Override
      public Player_HeroSkillLevelUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroSkillLevelUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroSkillLevelUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroSkillLevelUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroSkillLevelUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroSkillLevelUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroSkillLevelUp_S2C}
   */
  public static final class Player_HeroSkillLevelUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroSkillLevelUp_S2C)
      Player_HeroSkillLevelUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroSkillLevelUp_S2C.newBuilder() to construct.
    private Player_HeroSkillLevelUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroSkillLevelUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroSkillLevelUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroSkillLevelUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C other = (com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroSkillLevelUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroSkillLevelUp_S2C)
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C result = new com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroSkillLevelUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroSkillLevelUp_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroSkillLevelUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroSkillLevelUp_S2C>() {
      @java.lang.Override
      public Player_HeroSkillLevelUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroSkillLevelUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroSkillLevelUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroSkillLevelUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroSkillLevelUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeSkillSlotLimit_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeSkillSlotLimit_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 slotLimit = 2;</code>
     * @return Whether the slotLimit field is set.
     */
    boolean hasSlotLimit();
    /**
     * <code>optional int32 slotLimit = 2;</code>
     * @return The slotLimit.
     */
    int getSlotLimit();
  }
  /**
   * <pre>
   * 英雄调整可升级技能槽
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_ChangeSkillSlotLimit_C2S}
   */
  public static final class Player_ChangeSkillSlotLimit_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeSkillSlotLimit_C2S)
      Player_ChangeSkillSlotLimit_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeSkillSlotLimit_C2S.newBuilder() to construct.
    private Player_ChangeSkillSlotLimit_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeSkillSlotLimit_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeSkillSlotLimit_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeSkillSlotLimit_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              slotLimit_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.class, com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int SLOTLIMIT_FIELD_NUMBER = 2;
    private int slotLimit_;
    /**
     * <code>optional int32 slotLimit = 2;</code>
     * @return Whether the slotLimit field is set.
     */
    @java.lang.Override
    public boolean hasSlotLimit() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 slotLimit = 2;</code>
     * @return The slotLimit.
     */
    @java.lang.Override
    public int getSlotLimit() {
      return slotLimit_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, slotLimit_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, slotLimit_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S other = (com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasSlotLimit() != other.hasSlotLimit()) return false;
      if (hasSlotLimit()) {
        if (getSlotLimit()
            != other.getSlotLimit()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasSlotLimit()) {
        hash = (37 * hash) + SLOTLIMIT_FIELD_NUMBER;
        hash = (53 * hash) + getSlotLimit();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄调整可升级技能槽
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_ChangeSkillSlotLimit_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeSkillSlotLimit_C2S)
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.class, com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        slotLimit_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S build() {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S result = new com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.slotLimit_ = slotLimit_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasSlotLimit()) {
          setSlotLimit(other.getSlotLimit());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int slotLimit_ ;
      /**
       * <code>optional int32 slotLimit = 2;</code>
       * @return Whether the slotLimit field is set.
       */
      @java.lang.Override
      public boolean hasSlotLimit() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 slotLimit = 2;</code>
       * @return The slotLimit.
       */
      @java.lang.Override
      public int getSlotLimit() {
        return slotLimit_;
      }
      /**
       * <code>optional int32 slotLimit = 2;</code>
       * @param value The slotLimit to set.
       * @return This builder for chaining.
       */
      public Builder setSlotLimit(int value) {
        bitField0_ |= 0x00000002;
        slotLimit_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 slotLimit = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSlotLimit() {
        bitField0_ = (bitField0_ & ~0x00000002);
        slotLimit_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeSkillSlotLimit_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeSkillSlotLimit_C2S)
    private static final com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeSkillSlotLimit_C2S>() {
      @java.lang.Override
      public Player_ChangeSkillSlotLimit_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeSkillSlotLimit_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ChangeSkillSlotLimit_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ChangeSkillSlotLimit_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ChangeSkillSlotLimit_S2C}
   */
  public static final class Player_ChangeSkillSlotLimit_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ChangeSkillSlotLimit_S2C)
      Player_ChangeSkillSlotLimit_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ChangeSkillSlotLimit_S2C.newBuilder() to construct.
    private Player_ChangeSkillSlotLimit_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ChangeSkillSlotLimit_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ChangeSkillSlotLimit_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ChangeSkillSlotLimit_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.class, com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C other = (com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ChangeSkillSlotLimit_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ChangeSkillSlotLimit_S2C)
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.class, com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C build() {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C result = new com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ChangeSkillSlotLimit_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ChangeSkillSlotLimit_S2C)
    private static final com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ChangeSkillSlotLimit_S2C>() {
      @java.lang.Override
      public Player_ChangeSkillSlotLimit_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ChangeSkillSlotLimit_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ChangeSkillSlotLimit_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ChangeSkillSlotLimit_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ResetSkill_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ResetSkill_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * 英雄重置技能
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_ResetSkill_C2S}
   */
  public static final class Player_ResetSkill_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ResetSkill_C2S)
      Player_ResetSkill_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ResetSkill_C2S.newBuilder() to construct.
    private Player_ResetSkill_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ResetSkill_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ResetSkill_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ResetSkill_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.class, com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ResetSkill_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ResetSkill_C2S other = (com.yorha.proto.PlayerHero.Player_ResetSkill_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ResetSkill_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄重置技能
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_ResetSkill_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ResetSkill_C2S)
        com.yorha.proto.PlayerHero.Player_ResetSkill_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.class, com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_C2S build() {
        com.yorha.proto.PlayerHero.Player_ResetSkill_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_ResetSkill_C2S result = new com.yorha.proto.PlayerHero.Player_ResetSkill_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ResetSkill_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ResetSkill_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ResetSkill_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_ResetSkill_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ResetSkill_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ResetSkill_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ResetSkill_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ResetSkill_C2S)
    private static final com.yorha.proto.PlayerHero.Player_ResetSkill_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ResetSkill_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_ResetSkill_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ResetSkill_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ResetSkill_C2S>() {
      @java.lang.Override
      public Player_ResetSkill_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ResetSkill_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ResetSkill_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ResetSkill_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ResetSkill_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ResetSkill_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ResetSkill_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ResetSkill_S2C}
   */
  public static final class Player_ResetSkill_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ResetSkill_S2C)
      Player_ResetSkill_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ResetSkill_S2C.newBuilder() to construct.
    private Player_ResetSkill_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ResetSkill_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ResetSkill_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ResetSkill_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.class, com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ResetSkill_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ResetSkill_S2C other = (com.yorha.proto.PlayerHero.Player_ResetSkill_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ResetSkill_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ResetSkill_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ResetSkill_S2C)
        com.yorha.proto.PlayerHero.Player_ResetSkill_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.class, com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_S2C build() {
        com.yorha.proto.PlayerHero.Player_ResetSkill_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetSkill_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_ResetSkill_S2C result = new com.yorha.proto.PlayerHero.Player_ResetSkill_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ResetSkill_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ResetSkill_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ResetSkill_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_ResetSkill_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ResetSkill_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ResetSkill_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ResetSkill_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ResetSkill_S2C)
    private static final com.yorha.proto.PlayerHero.Player_ResetSkill_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ResetSkill_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_ResetSkill_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ResetSkill_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ResetSkill_S2C>() {
      @java.lang.Override
      public Player_ResetSkill_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ResetSkill_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ResetSkill_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ResetSkill_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ResetSkill_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroTalentLevelUp_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroTalentLevelUp_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    boolean hasPageSlot();
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    int getPageSlot();

    /**
     * <code>optional int32 talentGroupId = 3;</code>
     * @return Whether the talentGroupId field is set.
     */
    boolean hasTalentGroupId();
    /**
     * <code>optional int32 talentGroupId = 3;</code>
     * @return The talentGroupId.
     */
    int getTalentGroupId();
  }
  /**
   * <pre>
   * 英雄天赋升级
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroTalentLevelUp_C2S}
   */
  public static final class Player_HeroTalentLevelUp_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroTalentLevelUp_C2S)
      Player_HeroTalentLevelUp_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroTalentLevelUp_C2S.newBuilder() to construct.
    private Player_HeroTalentLevelUp_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroTalentLevelUp_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroTalentLevelUp_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroTalentLevelUp_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              pageSlot_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              talentGroupId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int PAGESLOT_FIELD_NUMBER = 2;
    private int pageSlot_;
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    @java.lang.Override
    public boolean hasPageSlot() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    @java.lang.Override
    public int getPageSlot() {
      return pageSlot_;
    }

    public static final int TALENTGROUPID_FIELD_NUMBER = 3;
    private int talentGroupId_;
    /**
     * <code>optional int32 talentGroupId = 3;</code>
     * @return Whether the talentGroupId field is set.
     */
    @java.lang.Override
    public boolean hasTalentGroupId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 talentGroupId = 3;</code>
     * @return The talentGroupId.
     */
    @java.lang.Override
    public int getTalentGroupId() {
      return talentGroupId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, pageSlot_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, talentGroupId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, pageSlot_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, talentGroupId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S other = (com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasPageSlot() != other.hasPageSlot()) return false;
      if (hasPageSlot()) {
        if (getPageSlot()
            != other.getPageSlot()) return false;
      }
      if (hasTalentGroupId() != other.hasTalentGroupId()) return false;
      if (hasTalentGroupId()) {
        if (getTalentGroupId()
            != other.getTalentGroupId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasPageSlot()) {
        hash = (37 * hash) + PAGESLOT_FIELD_NUMBER;
        hash = (53 * hash) + getPageSlot();
      }
      if (hasTalentGroupId()) {
        hash = (37 * hash) + TALENTGROUPID_FIELD_NUMBER;
        hash = (53 * hash) + getTalentGroupId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄天赋升级
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroTalentLevelUp_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroTalentLevelUp_C2S)
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.class, com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        pageSlot_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        talentGroupId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S result = new com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.pageSlot_ = pageSlot_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.talentGroupId_ = talentGroupId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasPageSlot()) {
          setPageSlot(other.getPageSlot());
        }
        if (other.hasTalentGroupId()) {
          setTalentGroupId(other.getTalentGroupId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int pageSlot_ ;
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return Whether the pageSlot field is set.
       */
      @java.lang.Override
      public boolean hasPageSlot() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return The pageSlot.
       */
      @java.lang.Override
      public int getPageSlot() {
        return pageSlot_;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @param value The pageSlot to set.
       * @return This builder for chaining.
       */
      public Builder setPageSlot(int value) {
        bitField0_ |= 0x00000002;
        pageSlot_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSlot() {
        bitField0_ = (bitField0_ & ~0x00000002);
        pageSlot_ = 0;
        onChanged();
        return this;
      }

      private int talentGroupId_ ;
      /**
       * <code>optional int32 talentGroupId = 3;</code>
       * @return Whether the talentGroupId field is set.
       */
      @java.lang.Override
      public boolean hasTalentGroupId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 talentGroupId = 3;</code>
       * @return The talentGroupId.
       */
      @java.lang.Override
      public int getTalentGroupId() {
        return talentGroupId_;
      }
      /**
       * <code>optional int32 talentGroupId = 3;</code>
       * @param value The talentGroupId to set.
       * @return This builder for chaining.
       */
      public Builder setTalentGroupId(int value) {
        bitField0_ |= 0x00000004;
        talentGroupId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 talentGroupId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTalentGroupId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        talentGroupId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroTalentLevelUp_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroTalentLevelUp_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroTalentLevelUp_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroTalentLevelUp_C2S>() {
      @java.lang.Override
      public Player_HeroTalentLevelUp_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroTalentLevelUp_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroTalentLevelUp_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroTalentLevelUp_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroTalentLevelUp_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroTalentLevelUp_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroTalentLevelUp_S2C}
   */
  public static final class Player_HeroTalentLevelUp_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroTalentLevelUp_S2C)
      Player_HeroTalentLevelUp_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroTalentLevelUp_S2C.newBuilder() to construct.
    private Player_HeroTalentLevelUp_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroTalentLevelUp_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroTalentLevelUp_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroTalentLevelUp_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C other = (com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroTalentLevelUp_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroTalentLevelUp_S2C)
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.class, com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C result = new com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroTalentLevelUp_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroTalentLevelUp_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroTalentLevelUp_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroTalentLevelUp_S2C>() {
      @java.lang.Override
      public Player_HeroTalentLevelUp_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroTalentLevelUp_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroTalentLevelUp_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroTalentLevelUp_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroTalentLevelUp_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SwitchTalentPage_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SwitchTalentPage_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    boolean hasPageSlot();
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    int getPageSlot();
  }
  /**
   * <pre>
   * 英雄切换天赋页
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_SwitchTalentPage_C2S}
   */
  public static final class Player_SwitchTalentPage_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SwitchTalentPage_C2S)
      Player_SwitchTalentPage_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SwitchTalentPage_C2S.newBuilder() to construct.
    private Player_SwitchTalentPage_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SwitchTalentPage_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SwitchTalentPage_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SwitchTalentPage_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              pageSlot_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.class, com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int PAGESLOT_FIELD_NUMBER = 2;
    private int pageSlot_;
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    @java.lang.Override
    public boolean hasPageSlot() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    @java.lang.Override
    public int getPageSlot() {
      return pageSlot_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, pageSlot_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, pageSlot_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S other = (com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasPageSlot() != other.hasPageSlot()) return false;
      if (hasPageSlot()) {
        if (getPageSlot()
            != other.getPageSlot()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasPageSlot()) {
        hash = (37 * hash) + PAGESLOT_FIELD_NUMBER;
        hash = (53 * hash) + getPageSlot();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄切换天赋页
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_SwitchTalentPage_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SwitchTalentPage_C2S)
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.class, com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        pageSlot_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S build() {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S result = new com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.pageSlot_ = pageSlot_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasPageSlot()) {
          setPageSlot(other.getPageSlot());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int pageSlot_ ;
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return Whether the pageSlot field is set.
       */
      @java.lang.Override
      public boolean hasPageSlot() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return The pageSlot.
       */
      @java.lang.Override
      public int getPageSlot() {
        return pageSlot_;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @param value The pageSlot to set.
       * @return This builder for chaining.
       */
      public Builder setPageSlot(int value) {
        bitField0_ |= 0x00000002;
        pageSlot_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSlot() {
        bitField0_ = (bitField0_ & ~0x00000002);
        pageSlot_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SwitchTalentPage_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SwitchTalentPage_C2S)
    private static final com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SwitchTalentPage_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_SwitchTalentPage_C2S>() {
      @java.lang.Override
      public Player_SwitchTalentPage_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SwitchTalentPage_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SwitchTalentPage_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SwitchTalentPage_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_SwitchTalentPage_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_SwitchTalentPage_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_SwitchTalentPage_S2C}
   */
  public static final class Player_SwitchTalentPage_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_SwitchTalentPage_S2C)
      Player_SwitchTalentPage_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_SwitchTalentPage_S2C.newBuilder() to construct.
    private Player_SwitchTalentPage_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_SwitchTalentPage_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_SwitchTalentPage_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_SwitchTalentPage_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.class, com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C other = (com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_SwitchTalentPage_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_SwitchTalentPage_S2C)
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.class, com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C build() {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C result = new com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_SwitchTalentPage_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_SwitchTalentPage_S2C)
    private static final com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_SwitchTalentPage_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_SwitchTalentPage_S2C>() {
      @java.lang.Override
      public Player_SwitchTalentPage_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_SwitchTalentPage_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_SwitchTalentPage_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_SwitchTalentPage_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_SwitchTalentPage_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ResetNameTalentPage_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ResetNameTalentPage_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    boolean hasPageSlot();
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    int getPageSlot();

    /**
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * <pre>
   * 英雄变更天赋页名称
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_ResetNameTalentPage_C2S}
   */
  public static final class Player_ResetNameTalentPage_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ResetNameTalentPage_C2S)
      Player_ResetNameTalentPage_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ResetNameTalentPage_C2S.newBuilder() to construct.
    private Player_ResetNameTalentPage_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ResetNameTalentPage_C2S() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ResetNameTalentPage_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ResetNameTalentPage_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              pageSlot_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.class, com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int PAGESLOT_FIELD_NUMBER = 2;
    private int pageSlot_;
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    @java.lang.Override
    public boolean hasPageSlot() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    @java.lang.Override
    public int getPageSlot() {
      return pageSlot_;
    }

    public static final int NAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, pageSlot_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, pageSlot_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S other = (com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasPageSlot() != other.hasPageSlot()) return false;
      if (hasPageSlot()) {
        if (getPageSlot()
            != other.getPageSlot()) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasPageSlot()) {
        hash = (37 * hash) + PAGESLOT_FIELD_NUMBER;
        hash = (53 * hash) + getPageSlot();
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄变更天赋页名称
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_ResetNameTalentPage_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ResetNameTalentPage_C2S)
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.class, com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        pageSlot_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S build() {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S result = new com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.pageSlot_ = pageSlot_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasPageSlot()) {
          setPageSlot(other.getPageSlot());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000004;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int pageSlot_ ;
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return Whether the pageSlot field is set.
       */
      @java.lang.Override
      public boolean hasPageSlot() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return The pageSlot.
       */
      @java.lang.Override
      public int getPageSlot() {
        return pageSlot_;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @param value The pageSlot to set.
       * @return This builder for chaining.
       */
      public Builder setPageSlot(int value) {
        bitField0_ |= 0x00000002;
        pageSlot_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSlot() {
        bitField0_ = (bitField0_ & ~0x00000002);
        pageSlot_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 3;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string name = 3;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 3;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 3;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 3;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ResetNameTalentPage_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ResetNameTalentPage_C2S)
    private static final com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ResetNameTalentPage_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_ResetNameTalentPage_C2S>() {
      @java.lang.Override
      public Player_ResetNameTalentPage_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ResetNameTalentPage_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ResetNameTalentPage_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ResetNameTalentPage_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_ResetNameTalentPage_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_ResetNameTalentPage_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_ResetNameTalentPage_S2C}
   */
  public static final class Player_ResetNameTalentPage_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_ResetNameTalentPage_S2C)
      Player_ResetNameTalentPage_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_ResetNameTalentPage_S2C.newBuilder() to construct.
    private Player_ResetNameTalentPage_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_ResetNameTalentPage_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_ResetNameTalentPage_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_ResetNameTalentPage_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.class, com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C other = (com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_ResetNameTalentPage_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_ResetNameTalentPage_S2C)
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.class, com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C build() {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C result = new com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_ResetNameTalentPage_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_ResetNameTalentPage_S2C)
    private static final com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_ResetNameTalentPage_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_ResetNameTalentPage_S2C>() {
      @java.lang.Override
      public Player_ResetNameTalentPage_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_ResetNameTalentPage_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_ResetNameTalentPage_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_ResetNameTalentPage_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_ResetNameTalentPage_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroResetTalent_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroResetTalent_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    boolean hasPageSlot();
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    int getPageSlot();
  }
  /**
   * <pre>
   * 英雄重置天赋页
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroResetTalent_C2S}
   */
  public static final class Player_HeroResetTalent_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroResetTalent_C2S)
      Player_HeroResetTalent_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroResetTalent_C2S.newBuilder() to construct.
    private Player_HeroResetTalent_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroResetTalent_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroResetTalent_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroResetTalent_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              pageSlot_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.class, com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int PAGESLOT_FIELD_NUMBER = 2;
    private int pageSlot_;
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return Whether the pageSlot field is set.
     */
    @java.lang.Override
    public boolean hasPageSlot() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 pageSlot = 2;</code>
     * @return The pageSlot.
     */
    @java.lang.Override
    public int getPageSlot() {
      return pageSlot_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, pageSlot_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, pageSlot_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S other = (com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasPageSlot() != other.hasPageSlot()) return false;
      if (hasPageSlot()) {
        if (getPageSlot()
            != other.getPageSlot()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasPageSlot()) {
        hash = (37 * hash) + PAGESLOT_FIELD_NUMBER;
        hash = (53 * hash) + getPageSlot();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄重置天赋页
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroResetTalent_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroResetTalent_C2S)
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.class, com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        pageSlot_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S result = new com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.pageSlot_ = pageSlot_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasPageSlot()) {
          setPageSlot(other.getPageSlot());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int pageSlot_ ;
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return Whether the pageSlot field is set.
       */
      @java.lang.Override
      public boolean hasPageSlot() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return The pageSlot.
       */
      @java.lang.Override
      public int getPageSlot() {
        return pageSlot_;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @param value The pageSlot to set.
       * @return This builder for chaining.
       */
      public Builder setPageSlot(int value) {
        bitField0_ |= 0x00000002;
        pageSlot_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 pageSlot = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPageSlot() {
        bitField0_ = (bitField0_ & ~0x00000002);
        pageSlot_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroResetTalent_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroResetTalent_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroResetTalent_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroResetTalent_C2S>() {
      @java.lang.Override
      public Player_HeroResetTalent_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroResetTalent_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroResetTalent_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroResetTalent_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroResetTalent_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroResetTalent_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroResetTalent_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroResetTalent_S2C}
   */
  public static final class Player_HeroResetTalent_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroResetTalent_S2C)
      Player_HeroResetTalent_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroResetTalent_S2C.newBuilder() to construct.
    private Player_HeroResetTalent_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroResetTalent_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroResetTalent_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroResetTalent_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.class, com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C other = (com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroResetTalent_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroResetTalent_S2C)
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.class, com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C result = new com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroResetTalent_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroResetTalent_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroResetTalent_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroResetTalent_S2C>() {
      @java.lang.Override
      public Player_HeroResetTalent_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroResetTalent_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroResetTalent_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroResetTalent_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroResetTalent_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroItemExchange_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroItemExchange_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();

    /**
     * <code>optional int32 exChangeHeroChipNum = 2;</code>
     * @return Whether the exChangeHeroChipNum field is set.
     */
    boolean hasExChangeHeroChipNum();
    /**
     * <code>optional int32 exChangeHeroChipNum = 2;</code>
     * @return The exChangeHeroChipNum.
     */
    int getExChangeHeroChipNum();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * <pre>
   * 英雄道具兑换
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroItemExchange_C2S}
   */
  public static final class Player_HeroItemExchange_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroItemExchange_C2S)
      Player_HeroItemExchange_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroItemExchange_C2S.newBuilder() to construct.
    private Player_HeroItemExchange_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroItemExchange_C2S() {
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroItemExchange_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroItemExchange_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              exChangeHeroChipNum_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.class, com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    public static final int EXCHANGEHEROCHIPNUM_FIELD_NUMBER = 2;
    private int exChangeHeroChipNum_;
    /**
     * <code>optional int32 exChangeHeroChipNum = 2;</code>
     * @return Whether the exChangeHeroChipNum field is set.
     */
    @java.lang.Override
    public boolean hasExChangeHeroChipNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 exChangeHeroChipNum = 2;</code>
     * @return The exChangeHeroChipNum.
     */
    @java.lang.Override
    public int getExChangeHeroChipNum() {
      return exChangeHeroChipNum_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 3;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 3;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, exChangeHeroChipNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, exChangeHeroChipNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S other = (com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (hasExChangeHeroChipNum() != other.hasExChangeHeroChipNum()) return false;
      if (hasExChangeHeroChipNum()) {
        if (getExChangeHeroChipNum()
            != other.getExChangeHeroChipNum()) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      if (hasExChangeHeroChipNum()) {
        hash = (37 * hash) + EXCHANGEHEROCHIPNUM_FIELD_NUMBER;
        hash = (53 * hash) + getExChangeHeroChipNum();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄道具兑换
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroItemExchange_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroItemExchange_C2S)
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.class, com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        exChangeHeroChipNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S result = new com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.exChangeHeroChipNum_ = exChangeHeroChipNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        if (other.hasExChangeHeroChipNum()) {
          setExChangeHeroChipNum(other.getExChangeHeroChipNum());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000004;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }

      private int exChangeHeroChipNum_ ;
      /**
       * <code>optional int32 exChangeHeroChipNum = 2;</code>
       * @return Whether the exChangeHeroChipNum field is set.
       */
      @java.lang.Override
      public boolean hasExChangeHeroChipNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 exChangeHeroChipNum = 2;</code>
       * @return The exChangeHeroChipNum.
       */
      @java.lang.Override
      public int getExChangeHeroChipNum() {
        return exChangeHeroChipNum_;
      }
      /**
       * <code>optional int32 exChangeHeroChipNum = 2;</code>
       * @param value The exChangeHeroChipNum to set.
       * @return This builder for chaining.
       */
      public Builder setExChangeHeroChipNum(int value) {
        bitField0_ |= 0x00000002;
        exChangeHeroChipNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 exChangeHeroChipNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExChangeHeroChipNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        exChangeHeroChipNum_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000004);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 3;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroItemExchange_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroItemExchange_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroItemExchange_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroItemExchange_C2S>() {
      @java.lang.Override
      public Player_HeroItemExchange_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroItemExchange_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroItemExchange_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroItemExchange_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroItemExchange_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroItemExchange_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroItemExchange_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroItemExchange_S2C}
   */
  public static final class Player_HeroItemExchange_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroItemExchange_S2C)
      Player_HeroItemExchange_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroItemExchange_S2C.newBuilder() to construct.
    private Player_HeroItemExchange_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroItemExchange_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroItemExchange_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroItemExchange_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.class, com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C other = (com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroItemExchange_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroItemExchange_S2C)
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.class, com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C result = new com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroItemExchange_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroItemExchange_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroItemExchange_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroItemExchange_S2C>() {
      @java.lang.Override
      public Player_HeroItemExchange_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroItemExchange_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroItemExchange_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroItemExchange_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroItemExchange_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroInterensiveSkill_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroInterensiveSkill_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    boolean hasHeroId();
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    int getHeroId();
  }
  /**
   * <pre>
   * 英雄觉醒天赋技能
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.Player_HeroInterensiveSkill_C2S}
   */
  public static final class Player_HeroInterensiveSkill_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroInterensiveSkill_C2S)
      Player_HeroInterensiveSkill_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroInterensiveSkill_C2S.newBuilder() to construct.
    private Player_HeroInterensiveSkill_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroInterensiveSkill_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroInterensiveSkill_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroInterensiveSkill_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              heroId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.class, com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int HEROID_FIELD_NUMBER = 1;
    private int heroId_;
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return Whether the heroId field is set.
     */
    @java.lang.Override
    public boolean hasHeroId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 heroId = 1;</code>
     * @return The heroId.
     */
    @java.lang.Override
    public int getHeroId() {
      return heroId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, heroId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, heroId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S other = (com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S) obj;

      if (hasHeroId() != other.hasHeroId()) return false;
      if (hasHeroId()) {
        if (getHeroId()
            != other.getHeroId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasHeroId()) {
        hash = (37 * hash) + HEROID_FIELD_NUMBER;
        hash = (53 * hash) + getHeroId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 英雄觉醒天赋技能
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.Player_HeroInterensiveSkill_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroInterensiveSkill_C2S)
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.class, com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        heroId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S build() {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S result = new com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.heroId_ = heroId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S.getDefaultInstance()) return this;
        if (other.hasHeroId()) {
          setHeroId(other.getHeroId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int heroId_ ;
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return Whether the heroId field is set.
       */
      @java.lang.Override
      public boolean hasHeroId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return The heroId.
       */
      @java.lang.Override
      public int getHeroId() {
        return heroId_;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @param value The heroId to set.
       * @return This builder for chaining.
       */
      public Builder setHeroId(int value) {
        bitField0_ |= 0x00000001;
        heroId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 heroId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearHeroId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        heroId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroInterensiveSkill_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroInterensiveSkill_C2S)
    private static final com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroInterensiveSkill_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroInterensiveSkill_C2S>() {
      @java.lang.Override
      public Player_HeroInterensiveSkill_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroInterensiveSkill_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroInterensiveSkill_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroInterensiveSkill_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_HeroInterensiveSkill_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_HeroInterensiveSkill_S2C)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_HeroInterensiveSkill_S2C}
   */
  public static final class Player_HeroInterensiveSkill_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_HeroInterensiveSkill_S2C)
      Player_HeroInterensiveSkill_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_HeroInterensiveSkill_S2C.newBuilder() to construct.
    private Player_HeroInterensiveSkill_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_HeroInterensiveSkill_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_HeroInterensiveSkill_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_HeroInterensiveSkill_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.class, com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C other = (com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_HeroInterensiveSkill_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_HeroInterensiveSkill_S2C)
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.class, com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerHero.internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C build() {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C buildPartial() {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C result = new com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C) {
          return mergeFrom((com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C other) {
        if (other == com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_HeroInterensiveSkill_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_HeroInterensiveSkill_S2C)
    private static final com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C();
    }

    public static com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_HeroInterensiveSkill_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_HeroInterensiveSkill_S2C>() {
      @java.lang.Override
      public Player_HeroInterensiveSkill_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_HeroInterensiveSkill_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_HeroInterensiveSkill_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_HeroInterensiveSkill_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerHero.Player_HeroInterensiveSkill_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_UnLockHero_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_UnLockHero_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroStageUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroStageUp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ResetSkill_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ResetSkill_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n(ss_proto/gen/player/cs/player_hero.pro" +
      "to\022\017com.yorha.proto\"\'\n\025Player_UnLockHero" +
      "_C2S\022\016\n\006heroId\030\001 \001(\005\"\027\n\025Player_UnLockHer" +
      "o_S2C\"(\n\026Player_HeroLevelUp_C2S\022\016\n\006heroI" +
      "d\030\001 \001(\005\"\030\n\026Player_HeroLevelUp_S2C\"8\n\026Pla" +
      "yer_HeroStageUp_C2S\022\016\n\006heroId\030\001 \001(\005\022\016\n\006c" +
      "ommon\030\002 \001(\010\"\030\n\026Player_HeroStageUp_S2C\"C\n" +
      "\033Player_HeroSkillLevelUp_C2S\022\016\n\006heroId\030\001" +
      " \001(\005\022\024\n\014skillGroupId\030\002 \001(\005\"\035\n\033Player_Her" +
      "oSkillLevelUp_S2C\"D\n\037Player_ChangeSkillS" +
      "lotLimit_C2S\022\016\n\006heroId\030\001 \001(\005\022\021\n\tslotLimi" +
      "t\030\002 \001(\005\"!\n\037Player_ChangeSkillSlotLimit_S" +
      "2C\"\'\n\025Player_ResetSkill_C2S\022\016\n\006heroId\030\001 " +
      "\001(\005\"\027\n\025Player_ResetSkill_S2C\"W\n\034Player_H" +
      "eroTalentLevelUp_C2S\022\016\n\006heroId\030\001 \001(\005\022\020\n\010" +
      "pageSlot\030\002 \001(\005\022\025\n\rtalentGroupId\030\003 \001(\005\"\036\n" +
      "\034Player_HeroTalentLevelUp_S2C\"?\n\033Player_" +
      "SwitchTalentPage_C2S\022\016\n\006heroId\030\001 \001(\005\022\020\n\010" +
      "pageSlot\030\002 \001(\005\"\035\n\033Player_SwitchTalentPag" +
      "e_S2C\"P\n\036Player_ResetNameTalentPage_C2S\022" +
      "\016\n\006heroId\030\001 \001(\005\022\020\n\010pageSlot\030\002 \001(\005\022\014\n\004nam" +
      "e\030\003 \001(\t\" \n\036Player_ResetNameTalentPage_S2" +
      "C\">\n\032Player_HeroResetTalent_C2S\022\016\n\006heroI" +
      "d\030\001 \001(\005\022\020\n\010pageSlot\030\002 \001(\005\"\034\n\032Player_Hero" +
      "ResetTalent_S2C\"]\n\033Player_HeroItemExchan" +
      "ge_C2S\022\016\n\006heroId\030\001 \001(\005\022\033\n\023exChangeHeroCh" +
      "ipNum\030\002 \001(\005\022\021\n\tsPassWord\030\003 \001(\t\"\035\n\033Player" +
      "_HeroItemExchange_S2C\"1\n\037Player_HeroInte" +
      "rensiveSkill_C2S\022\016\n\006heroId\030\001 \001(\005\"!\n\037Play" +
      "er_HeroInterensiveSkill_S2CB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_UnLockHero_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_UnLockHero_C2S_descriptor,
        new java.lang.String[] { "HeroId", });
    internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_UnLockHero_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_UnLockHero_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroLevelUp_C2S_descriptor,
        new java.lang.String[] { "HeroId", });
    internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroLevelUp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_HeroStageUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroStageUp_C2S_descriptor,
        new java.lang.String[] { "HeroId", "Common", });
    internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_HeroStageUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroStageUp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroSkillLevelUp_C2S_descriptor,
        new java.lang.String[] { "HeroId", "SkillGroupId", });
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroSkillLevelUp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_C2S_descriptor,
        new java.lang.String[] { "HeroId", "SlotLimit", });
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ChangeSkillSlotLimit_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_Player_ResetSkill_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ResetSkill_C2S_descriptor,
        new java.lang.String[] { "HeroId", });
    internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_Player_ResetSkill_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ResetSkill_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroTalentLevelUp_C2S_descriptor,
        new java.lang.String[] { "HeroId", "PageSlot", "TalentGroupId", });
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroTalentLevelUp_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SwitchTalentPage_C2S_descriptor,
        new java.lang.String[] { "HeroId", "PageSlot", });
    internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_SwitchTalentPage_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor =
      getDescriptor().getMessageTypes().get(16);
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ResetNameTalentPage_C2S_descriptor,
        new java.lang.String[] { "HeroId", "PageSlot", "Name", });
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor =
      getDescriptor().getMessageTypes().get(17);
    internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_ResetNameTalentPage_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor =
      getDescriptor().getMessageTypes().get(18);
    internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroResetTalent_C2S_descriptor,
        new java.lang.String[] { "HeroId", "PageSlot", });
    internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor =
      getDescriptor().getMessageTypes().get(19);
    internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroResetTalent_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor =
      getDescriptor().getMessageTypes().get(20);
    internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroItemExchange_C2S_descriptor,
        new java.lang.String[] { "HeroId", "ExChangeHeroChipNum", "SPassWord", });
    internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor =
      getDescriptor().getMessageTypes().get(21);
    internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroItemExchange_S2C_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor =
      getDescriptor().getMessageTypes().get(22);
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroInterensiveSkill_C2S_descriptor,
        new java.lang.String[] { "HeroId", });
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor =
      getDescriptor().getMessageTypes().get(23);
    internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_HeroInterensiveSkill_S2C_descriptor,
        new java.lang.String[] { });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

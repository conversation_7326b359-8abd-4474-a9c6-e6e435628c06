// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/name/ss_name.proto

package com.yorha.proto;

public final class SsName {
  private SsName() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface NamePairOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.NamePair)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return Whether the nameType field is set.
     */
    boolean hasNameType();
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return The nameType.
     */
    com.yorha.proto.CommonEnum.NameType getNameType();

    /**
     * <code>optional string name = 2;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.NamePair}
   */
  public static final class NamePair extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.NamePair)
      NamePairOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use NamePair.newBuilder() to construct.
    private NamePair(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private NamePair() {
      nameType_ = 0;
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new NamePair();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private NamePair(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.NameType value = com.yorha.proto.CommonEnum.NameType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                nameType_ = rawValue;
              }
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_NamePair_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_NamePair_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.NamePair.class, com.yorha.proto.SsName.NamePair.Builder.class);
    }

    private int bitField0_;
    public static final int NAMETYPE_FIELD_NUMBER = 1;
    private int nameType_;
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return Whether the nameType field is set.
     */
    @java.lang.Override public boolean hasNameType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return The nameType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.NameType getNameType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
      return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 2;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, nameType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, nameType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.NamePair)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.NamePair other = (com.yorha.proto.SsName.NamePair) obj;

      if (hasNameType() != other.hasNameType()) return false;
      if (hasNameType()) {
        if (nameType_ != other.nameType_) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNameType()) {
        hash = (37 * hash) + NAMETYPE_FIELD_NUMBER;
        hash = (53 * hash) + nameType_;
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.NamePair parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.NamePair parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.NamePair parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.NamePair parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.NamePair prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.NamePair}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.NamePair)
        com.yorha.proto.SsName.NamePairOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_NamePair_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_NamePair_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.NamePair.class, com.yorha.proto.SsName.NamePair.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.NamePair.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nameType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_NamePair_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.NamePair getDefaultInstanceForType() {
        return com.yorha.proto.SsName.NamePair.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.NamePair build() {
        com.yorha.proto.SsName.NamePair result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.NamePair buildPartial() {
        com.yorha.proto.SsName.NamePair result = new com.yorha.proto.SsName.NamePair(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nameType_ = nameType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.NamePair) {
          return mergeFrom((com.yorha.proto.SsName.NamePair)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.NamePair other) {
        if (other == com.yorha.proto.SsName.NamePair.getDefaultInstance()) return this;
        if (other.hasNameType()) {
          setNameType(other.getNameType());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000002;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.NamePair parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.NamePair) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int nameType_ = 0;
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return Whether the nameType field is set.
       */
      @java.lang.Override public boolean hasNameType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return The nameType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.NameType getNameType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
        return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @param value The nameType to set.
       * @return This builder for chaining.
       */
      public Builder setNameType(com.yorha.proto.CommonEnum.NameType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        nameType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nameType_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 2;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.NamePair)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.NamePair)
    private static final com.yorha.proto.SsName.NamePair DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.NamePair();
    }

    public static com.yorha.proto.SsName.NamePair getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<NamePair>
        PARSER = new com.google.protobuf.AbstractParser<NamePair>() {
      @java.lang.Override
      public NamePair parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new NamePair(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<NamePair> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<NamePair> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.NamePair getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OccupyBatchNameCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OccupyBatchNameCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    long getOwnerId();

    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    java.util.List<com.yorha.proto.SsName.NamePair> 
        getNamePairList();
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    com.yorha.proto.SsName.NamePair getNamePair(int index);
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    int getNamePairCount();
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    java.util.List<? extends com.yorha.proto.SsName.NamePairOrBuilder> 
        getNamePairOrBuilderList();
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    com.yorha.proto.SsName.NamePairOrBuilder getNamePairOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.OccupyBatchNameCmd}
   */
  public static final class OccupyBatchNameCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OccupyBatchNameCmd)
      OccupyBatchNameCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OccupyBatchNameCmd.newBuilder() to construct.
    private OccupyBatchNameCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OccupyBatchNameCmd() {
      namePair_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OccupyBatchNameCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OccupyBatchNameCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ownerId_ = input.readInt64();
              break;
            }
            case 18: {
              if (!((mutable_bitField0_ & 0x00000002) != 0)) {
                namePair_ = new java.util.ArrayList<com.yorha.proto.SsName.NamePair>();
                mutable_bitField0_ |= 0x00000002;
              }
              namePair_.add(
                  input.readMessage(com.yorha.proto.SsName.NamePair.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000002) != 0)) {
          namePair_ = java.util.Collections.unmodifiableList(namePair_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_OccupyBatchNameCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.OccupyBatchNameCmd.class, com.yorha.proto.SsName.OccupyBatchNameCmd.Builder.class);
    }

    private int bitField0_;
    public static final int OWNERID_FIELD_NUMBER = 1;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    public static final int NAMEPAIR_FIELD_NUMBER = 2;
    private java.util.List<com.yorha.proto.SsName.NamePair> namePair_;
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.SsName.NamePair> getNamePairList() {
      return namePair_;
    }
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.SsName.NamePairOrBuilder> 
        getNamePairOrBuilderList() {
      return namePair_;
    }
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    @java.lang.Override
    public int getNamePairCount() {
      return namePair_.size();
    }
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsName.NamePair getNamePair(int index) {
      return namePair_.get(index);
    }
    /**
     * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.SsName.NamePairOrBuilder getNamePairOrBuilder(
        int index) {
      return namePair_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, ownerId_);
      }
      for (int i = 0; i < namePair_.size(); i++) {
        output.writeMessage(2, namePair_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, ownerId_);
      }
      for (int i = 0; i < namePair_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, namePair_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.OccupyBatchNameCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.OccupyBatchNameCmd other = (com.yorha.proto.SsName.OccupyBatchNameCmd) obj;

      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (!getNamePairList()
          .equals(other.getNamePairList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      if (getNamePairCount() > 0) {
        hash = (37 * hash) + NAMEPAIR_FIELD_NUMBER;
        hash = (53 * hash) + getNamePairList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.OccupyBatchNameCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.OccupyBatchNameCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OccupyBatchNameCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OccupyBatchNameCmd)
        com.yorha.proto.SsName.OccupyBatchNameCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_OccupyBatchNameCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.OccupyBatchNameCmd.class, com.yorha.proto.SsName.OccupyBatchNameCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.OccupyBatchNameCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNamePairFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (namePairBuilder_ == null) {
          namePair_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
        } else {
          namePairBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.OccupyBatchNameCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsName.OccupyBatchNameCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.OccupyBatchNameCmd build() {
        com.yorha.proto.SsName.OccupyBatchNameCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.OccupyBatchNameCmd buildPartial() {
        com.yorha.proto.SsName.OccupyBatchNameCmd result = new com.yorha.proto.SsName.OccupyBatchNameCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000001;
        }
        if (namePairBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0)) {
            namePair_ = java.util.Collections.unmodifiableList(namePair_);
            bitField0_ = (bitField0_ & ~0x00000002);
          }
          result.namePair_ = namePair_;
        } else {
          result.namePair_ = namePairBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.OccupyBatchNameCmd) {
          return mergeFrom((com.yorha.proto.SsName.OccupyBatchNameCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.OccupyBatchNameCmd other) {
        if (other == com.yorha.proto.SsName.OccupyBatchNameCmd.getDefaultInstance()) return this;
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        if (namePairBuilder_ == null) {
          if (!other.namePair_.isEmpty()) {
            if (namePair_.isEmpty()) {
              namePair_ = other.namePair_;
              bitField0_ = (bitField0_ & ~0x00000002);
            } else {
              ensureNamePairIsMutable();
              namePair_.addAll(other.namePair_);
            }
            onChanged();
          }
        } else {
          if (!other.namePair_.isEmpty()) {
            if (namePairBuilder_.isEmpty()) {
              namePairBuilder_.dispose();
              namePairBuilder_ = null;
              namePair_ = other.namePair_;
              bitField0_ = (bitField0_ & ~0x00000002);
              namePairBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getNamePairFieldBuilder() : null;
            } else {
              namePairBuilder_.addAllMessages(other.namePair_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.OccupyBatchNameCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.OccupyBatchNameCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000001;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.SsName.NamePair> namePair_ =
        java.util.Collections.emptyList();
      private void ensureNamePairIsMutable() {
        if (!((bitField0_ & 0x00000002) != 0)) {
          namePair_ = new java.util.ArrayList<com.yorha.proto.SsName.NamePair>(namePair_);
          bitField0_ |= 0x00000002;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsName.NamePair, com.yorha.proto.SsName.NamePair.Builder, com.yorha.proto.SsName.NamePairOrBuilder> namePairBuilder_;

      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public java.util.List<com.yorha.proto.SsName.NamePair> getNamePairList() {
        if (namePairBuilder_ == null) {
          return java.util.Collections.unmodifiableList(namePair_);
        } else {
          return namePairBuilder_.getMessageList();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public int getNamePairCount() {
        if (namePairBuilder_ == null) {
          return namePair_.size();
        } else {
          return namePairBuilder_.getCount();
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public com.yorha.proto.SsName.NamePair getNamePair(int index) {
        if (namePairBuilder_ == null) {
          return namePair_.get(index);
        } else {
          return namePairBuilder_.getMessage(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder setNamePair(
          int index, com.yorha.proto.SsName.NamePair value) {
        if (namePairBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNamePairIsMutable();
          namePair_.set(index, value);
          onChanged();
        } else {
          namePairBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder setNamePair(
          int index, com.yorha.proto.SsName.NamePair.Builder builderForValue) {
        if (namePairBuilder_ == null) {
          ensureNamePairIsMutable();
          namePair_.set(index, builderForValue.build());
          onChanged();
        } else {
          namePairBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder addNamePair(com.yorha.proto.SsName.NamePair value) {
        if (namePairBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNamePairIsMutable();
          namePair_.add(value);
          onChanged();
        } else {
          namePairBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder addNamePair(
          int index, com.yorha.proto.SsName.NamePair value) {
        if (namePairBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureNamePairIsMutable();
          namePair_.add(index, value);
          onChanged();
        } else {
          namePairBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder addNamePair(
          com.yorha.proto.SsName.NamePair.Builder builderForValue) {
        if (namePairBuilder_ == null) {
          ensureNamePairIsMutable();
          namePair_.add(builderForValue.build());
          onChanged();
        } else {
          namePairBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder addNamePair(
          int index, com.yorha.proto.SsName.NamePair.Builder builderForValue) {
        if (namePairBuilder_ == null) {
          ensureNamePairIsMutable();
          namePair_.add(index, builderForValue.build());
          onChanged();
        } else {
          namePairBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder addAllNamePair(
          java.lang.Iterable<? extends com.yorha.proto.SsName.NamePair> values) {
        if (namePairBuilder_ == null) {
          ensureNamePairIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, namePair_);
          onChanged();
        } else {
          namePairBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder clearNamePair() {
        if (namePairBuilder_ == null) {
          namePair_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000002);
          onChanged();
        } else {
          namePairBuilder_.clear();
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public Builder removeNamePair(int index) {
        if (namePairBuilder_ == null) {
          ensureNamePairIsMutable();
          namePair_.remove(index);
          onChanged();
        } else {
          namePairBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public com.yorha.proto.SsName.NamePair.Builder getNamePairBuilder(
          int index) {
        return getNamePairFieldBuilder().getBuilder(index);
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public com.yorha.proto.SsName.NamePairOrBuilder getNamePairOrBuilder(
          int index) {
        if (namePairBuilder_ == null) {
          return namePair_.get(index);  } else {
          return namePairBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public java.util.List<? extends com.yorha.proto.SsName.NamePairOrBuilder> 
           getNamePairOrBuilderList() {
        if (namePairBuilder_ != null) {
          return namePairBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(namePair_);
        }
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public com.yorha.proto.SsName.NamePair.Builder addNamePairBuilder() {
        return getNamePairFieldBuilder().addBuilder(
            com.yorha.proto.SsName.NamePair.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public com.yorha.proto.SsName.NamePair.Builder addNamePairBuilder(
          int index) {
        return getNamePairFieldBuilder().addBuilder(
            index, com.yorha.proto.SsName.NamePair.getDefaultInstance());
      }
      /**
       * <code>repeated .com.yorha.proto.NamePair namePair = 2;</code>
       */
      public java.util.List<com.yorha.proto.SsName.NamePair.Builder> 
           getNamePairBuilderList() {
        return getNamePairFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.SsName.NamePair, com.yorha.proto.SsName.NamePair.Builder, com.yorha.proto.SsName.NamePairOrBuilder> 
          getNamePairFieldBuilder() {
        if (namePairBuilder_ == null) {
          namePairBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.SsName.NamePair, com.yorha.proto.SsName.NamePair.Builder, com.yorha.proto.SsName.NamePairOrBuilder>(
                  namePair_,
                  ((bitField0_ & 0x00000002) != 0),
                  getParentForChildren(),
                  isClean());
          namePair_ = null;
        }
        return namePairBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OccupyBatchNameCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OccupyBatchNameCmd)
    private static final com.yorha.proto.SsName.OccupyBatchNameCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.OccupyBatchNameCmd();
    }

    public static com.yorha.proto.SsName.OccupyBatchNameCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OccupyBatchNameCmd>
        PARSER = new com.google.protobuf.AbstractParser<OccupyBatchNameCmd>() {
      @java.lang.Override
      public OccupyBatchNameCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OccupyBatchNameCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OccupyBatchNameCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OccupyBatchNameCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.OccupyBatchNameCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ReleaseNameCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ReleaseNameCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return Whether the nameType field is set.
     */
    boolean hasNameType();
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return The nameType.
     */
    com.yorha.proto.CommonEnum.NameType getNameType();

    /**
     * <code>optional int64 ownerId = 2;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 2;</code>
     * @return The ownerId.
     */
    long getOwnerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ReleaseNameCmd}
   */
  public static final class ReleaseNameCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ReleaseNameCmd)
      ReleaseNameCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ReleaseNameCmd.newBuilder() to construct.
    private ReleaseNameCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ReleaseNameCmd() {
      nameType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ReleaseNameCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ReleaseNameCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.NameType value = com.yorha.proto.CommonEnum.NameType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                nameType_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              ownerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_ReleaseNameCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_ReleaseNameCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.ReleaseNameCmd.class, com.yorha.proto.SsName.ReleaseNameCmd.Builder.class);
    }

    private int bitField0_;
    public static final int NAMETYPE_FIELD_NUMBER = 1;
    private int nameType_;
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return Whether the nameType field is set.
     */
    @java.lang.Override public boolean hasNameType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
     * @return The nameType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.NameType getNameType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
      return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
    }

    public static final int OWNERID_FIELD_NUMBER = 2;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 2;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 ownerId = 2;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, nameType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, ownerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, nameType_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, ownerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.ReleaseNameCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.ReleaseNameCmd other = (com.yorha.proto.SsName.ReleaseNameCmd) obj;

      if (hasNameType() != other.hasNameType()) return false;
      if (hasNameType()) {
        if (nameType_ != other.nameType_) return false;
      }
      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNameType()) {
        hash = (37 * hash) + NAMETYPE_FIELD_NUMBER;
        hash = (53 * hash) + nameType_;
      }
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.ReleaseNameCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.ReleaseNameCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ReleaseNameCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ReleaseNameCmd)
        com.yorha.proto.SsName.ReleaseNameCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_ReleaseNameCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_ReleaseNameCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.ReleaseNameCmd.class, com.yorha.proto.SsName.ReleaseNameCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.ReleaseNameCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nameType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_ReleaseNameCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.ReleaseNameCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsName.ReleaseNameCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.ReleaseNameCmd build() {
        com.yorha.proto.SsName.ReleaseNameCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.ReleaseNameCmd buildPartial() {
        com.yorha.proto.SsName.ReleaseNameCmd result = new com.yorha.proto.SsName.ReleaseNameCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.nameType_ = nameType_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.ReleaseNameCmd) {
          return mergeFrom((com.yorha.proto.SsName.ReleaseNameCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.ReleaseNameCmd other) {
        if (other == com.yorha.proto.SsName.ReleaseNameCmd.getDefaultInstance()) return this;
        if (other.hasNameType()) {
          setNameType(other.getNameType());
        }
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.ReleaseNameCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.ReleaseNameCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int nameType_ = 0;
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return Whether the nameType field is set.
       */
      @java.lang.Override public boolean hasNameType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return The nameType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.NameType getNameType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
        return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @param value The nameType to set.
       * @return This builder for chaining.
       */
      public Builder setNameType(com.yorha.proto.CommonEnum.NameType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        nameType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nameType_ = 0;
        onChanged();
        return this;
      }

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 2;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 ownerId = 2;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 2;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000002;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        ownerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ReleaseNameCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ReleaseNameCmd)
    private static final com.yorha.proto.SsName.ReleaseNameCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.ReleaseNameCmd();
    }

    public static com.yorha.proto.SsName.ReleaseNameCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ReleaseNameCmd>
        PARSER = new com.google.protobuf.AbstractParser<ReleaseNameCmd>() {
      @java.lang.Override
      public ReleaseNameCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ReleaseNameCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ReleaseNameCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ReleaseNameCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.ReleaseNameCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchClanNameAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchClanNameAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     * 个数
     * </pre>
     *
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    boolean hasNum();
    /**
     * <pre>
     * 个数
     * </pre>
     *
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    int getNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchClanNameAsk}
   */
  public static final class SearchClanNameAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchClanNameAsk)
      SearchClanNameAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchClanNameAsk.newBuilder() to construct.
    private SearchClanNameAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchClanNameAsk() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchClanNameAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchClanNameAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              name_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              num_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchClanNameAsk.class, com.yorha.proto.SsName.SearchClanNameAsk.Builder.class);
    }

    private int bitField0_;
    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 匹配名称
     * </pre>
     *
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NUM_FIELD_NUMBER = 2;
    private int num_;
    /**
     * <pre>
     * 个数
     * </pre>
     *
     * <code>optional int32 num = 2;</code>
     * @return Whether the num field is set.
     */
    @java.lang.Override
    public boolean hasNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 个数
     * </pre>
     *
     * <code>optional int32 num = 2;</code>
     * @return The num.
     */
    @java.lang.Override
    public int getNum() {
      return num_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, num_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, num_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchClanNameAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchClanNameAsk other = (com.yorha.proto.SsName.SearchClanNameAsk) obj;

      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (hasNum() != other.hasNum()) return false;
      if (hasNum()) {
        if (getNum()
            != other.getNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      if (hasNum()) {
        hash = (37 * hash) + NUM_FIELD_NUMBER;
        hash = (53 * hash) + getNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchClanNameAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchClanNameAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchClanNameAsk)
        com.yorha.proto.SsName.SearchClanNameAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchClanNameAsk.class, com.yorha.proto.SsName.SearchClanNameAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchClanNameAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        num_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchClanNameAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAsk build() {
        com.yorha.proto.SsName.SearchClanNameAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAsk buildPartial() {
        com.yorha.proto.SsName.SearchClanNameAsk result = new com.yorha.proto.SsName.SearchClanNameAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.num_ = num_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchClanNameAsk) {
          return mergeFrom((com.yorha.proto.SsName.SearchClanNameAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchClanNameAsk other) {
        if (other == com.yorha.proto.SsName.SearchClanNameAsk.getDefaultInstance()) return this;
        if (other.hasName()) {
          bitField0_ |= 0x00000001;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasNum()) {
          setNum(other.getNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchClanNameAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchClanNameAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object name_ = "";
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 匹配名称
       * </pre>
       *
       * <code>optional string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }

      private int num_ ;
      /**
       * <pre>
       * 个数
       * </pre>
       *
       * <code>optional int32 num = 2;</code>
       * @return Whether the num field is set.
       */
      @java.lang.Override
      public boolean hasNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 个数
       * </pre>
       *
       * <code>optional int32 num = 2;</code>
       * @return The num.
       */
      @java.lang.Override
      public int getNum() {
        return num_;
      }
      /**
       * <pre>
       * 个数
       * </pre>
       *
       * <code>optional int32 num = 2;</code>
       * @param value The num to set.
       * @return This builder for chaining.
       */
      public Builder setNum(int value) {
        bitField0_ |= 0x00000002;
        num_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 个数
       * </pre>
       *
       * <code>optional int32 num = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        num_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchClanNameAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchClanNameAsk)
    private static final com.yorha.proto.SsName.SearchClanNameAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchClanNameAsk();
    }

    public static com.yorha.proto.SsName.SearchClanNameAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchClanNameAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchClanNameAsk>() {
      @java.lang.Override
      public SearchClanNameAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchClanNameAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchClanNameAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchClanNameAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchClanNameAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchClanNameAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchClanNameAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 clanList = 1;</code>
     * @return A list containing the clanList.
     */
    java.util.List<java.lang.Long> getClanListList();
    /**
     * <code>repeated int64 clanList = 1;</code>
     * @return The count of clanList.
     */
    int getClanListCount();
    /**
     * <code>repeated int64 clanList = 1;</code>
     * @param index The index of the element to return.
     * @return The clanList at the given index.
     */
    long getClanList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchClanNameAns}
   */
  public static final class SearchClanNameAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchClanNameAns)
      SearchClanNameAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchClanNameAns.newBuilder() to construct.
    private SearchClanNameAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchClanNameAns() {
      clanList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchClanNameAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchClanNameAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                clanList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              clanList_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                clanList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                clanList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          clanList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchClanNameAns.class, com.yorha.proto.SsName.SearchClanNameAns.Builder.class);
    }

    public static final int CLANLIST_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList clanList_;
    /**
     * <code>repeated int64 clanList = 1;</code>
     * @return A list containing the clanList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getClanListList() {
      return clanList_;
    }
    /**
     * <code>repeated int64 clanList = 1;</code>
     * @return The count of clanList.
     */
    public int getClanListCount() {
      return clanList_.size();
    }
    /**
     * <code>repeated int64 clanList = 1;</code>
     * @param index The index of the element to return.
     * @return The clanList at the given index.
     */
    public long getClanList(int index) {
      return clanList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < clanList_.size(); i++) {
        output.writeInt64(1, clanList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < clanList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(clanList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getClanListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchClanNameAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchClanNameAns other = (com.yorha.proto.SsName.SearchClanNameAns) obj;

      if (!getClanListList()
          .equals(other.getClanListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getClanListCount() > 0) {
        hash = (37 * hash) + CLANLIST_FIELD_NUMBER;
        hash = (53 * hash) + getClanListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchClanNameAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchClanNameAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchClanNameAns)
        com.yorha.proto.SsName.SearchClanNameAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchClanNameAns.class, com.yorha.proto.SsName.SearchClanNameAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchClanNameAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchClanNameAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAns getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchClanNameAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAns build() {
        com.yorha.proto.SsName.SearchClanNameAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchClanNameAns buildPartial() {
        com.yorha.proto.SsName.SearchClanNameAns result = new com.yorha.proto.SsName.SearchClanNameAns(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          clanList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.clanList_ = clanList_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchClanNameAns) {
          return mergeFrom((com.yorha.proto.SsName.SearchClanNameAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchClanNameAns other) {
        if (other == com.yorha.proto.SsName.SearchClanNameAns.getDefaultInstance()) return this;
        if (!other.clanList_.isEmpty()) {
          if (clanList_.isEmpty()) {
            clanList_ = other.clanList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureClanListIsMutable();
            clanList_.addAll(other.clanList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchClanNameAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchClanNameAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList clanList_ = emptyLongList();
      private void ensureClanListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          clanList_ = mutableCopy(clanList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @return A list containing the clanList.
       */
      public java.util.List<java.lang.Long>
          getClanListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(clanList_) : clanList_;
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @return The count of clanList.
       */
      public int getClanListCount() {
        return clanList_.size();
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @param index The index of the element to return.
       * @return The clanList at the given index.
       */
      public long getClanList(int index) {
        return clanList_.getLong(index);
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @param index The index to set the value at.
       * @param value The clanList to set.
       * @return This builder for chaining.
       */
      public Builder setClanList(
          int index, long value) {
        ensureClanListIsMutable();
        clanList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @param value The clanList to add.
       * @return This builder for chaining.
       */
      public Builder addClanList(long value) {
        ensureClanListIsMutable();
        clanList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @param values The clanList to add.
       * @return This builder for chaining.
       */
      public Builder addAllClanList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureClanListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clanList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clanList = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanList() {
        clanList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchClanNameAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchClanNameAns)
    private static final com.yorha.proto.SsName.SearchClanNameAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchClanNameAns();
    }

    public static com.yorha.proto.SsName.SearchClanNameAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchClanNameAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchClanNameAns>() {
      @java.lang.Override
      public SearchClanNameAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchClanNameAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchClanNameAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchClanNameAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchClanNameAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchPlayerNameAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPlayerNameAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>optional int32 numLimit = 2;</code>
     * @return Whether the numLimit field is set.
     */
    boolean hasNumLimit();
    /**
     * <code>optional int32 numLimit = 2;</code>
     * @return The numLimit.
     */
    int getNumLimit();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPlayerNameAsk}
   */
  public static final class SearchPlayerNameAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPlayerNameAsk)
      SearchPlayerNameAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPlayerNameAsk.newBuilder() to construct.
    private SearchPlayerNameAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPlayerNameAsk() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPlayerNameAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPlayerNameAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              name_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              numLimit_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchPlayerNameAsk.class, com.yorha.proto.SsName.SearchPlayerNameAsk.Builder.class);
    }

    private int bitField0_;
    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NUMLIMIT_FIELD_NUMBER = 2;
    private int numLimit_;
    /**
     * <code>optional int32 numLimit = 2;</code>
     * @return Whether the numLimit field is set.
     */
    @java.lang.Override
    public boolean hasNumLimit() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 numLimit = 2;</code>
     * @return The numLimit.
     */
    @java.lang.Override
    public int getNumLimit() {
      return numLimit_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, numLimit_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, numLimit_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchPlayerNameAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchPlayerNameAsk other = (com.yorha.proto.SsName.SearchPlayerNameAsk) obj;

      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (hasNumLimit() != other.hasNumLimit()) return false;
      if (hasNumLimit()) {
        if (getNumLimit()
            != other.getNumLimit()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      if (hasNumLimit()) {
        hash = (37 * hash) + NUMLIMIT_FIELD_NUMBER;
        hash = (53 * hash) + getNumLimit();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchPlayerNameAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPlayerNameAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPlayerNameAsk)
        com.yorha.proto.SsName.SearchPlayerNameAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchPlayerNameAsk.class, com.yorha.proto.SsName.SearchPlayerNameAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchPlayerNameAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        numLimit_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchPlayerNameAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAsk build() {
        com.yorha.proto.SsName.SearchPlayerNameAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAsk buildPartial() {
        com.yorha.proto.SsName.SearchPlayerNameAsk result = new com.yorha.proto.SsName.SearchPlayerNameAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.numLimit_ = numLimit_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchPlayerNameAsk) {
          return mergeFrom((com.yorha.proto.SsName.SearchPlayerNameAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchPlayerNameAsk other) {
        if (other == com.yorha.proto.SsName.SearchPlayerNameAsk.getDefaultInstance()) return this;
        if (other.hasName()) {
          bitField0_ |= 0x00000001;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasNumLimit()) {
          setNumLimit(other.getNumLimit());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchPlayerNameAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchPlayerNameAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 1;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }

      private int numLimit_ ;
      /**
       * <code>optional int32 numLimit = 2;</code>
       * @return Whether the numLimit field is set.
       */
      @java.lang.Override
      public boolean hasNumLimit() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 numLimit = 2;</code>
       * @return The numLimit.
       */
      @java.lang.Override
      public int getNumLimit() {
        return numLimit_;
      }
      /**
       * <code>optional int32 numLimit = 2;</code>
       * @param value The numLimit to set.
       * @return This builder for chaining.
       */
      public Builder setNumLimit(int value) {
        bitField0_ |= 0x00000002;
        numLimit_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 numLimit = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNumLimit() {
        bitField0_ = (bitField0_ & ~0x00000002);
        numLimit_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPlayerNameAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPlayerNameAsk)
    private static final com.yorha.proto.SsName.SearchPlayerNameAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchPlayerNameAsk();
    }

    public static com.yorha.proto.SsName.SearchPlayerNameAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPlayerNameAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchPlayerNameAsk>() {
      @java.lang.Override
      public SearchPlayerNameAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPlayerNameAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPlayerNameAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPlayerNameAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchPlayerNameAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchPlayerNameAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchPlayerNameAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 playerList = 1;</code>
     * @return A list containing the playerList.
     */
    java.util.List<java.lang.Long> getPlayerListList();
    /**
     * <code>repeated int64 playerList = 1;</code>
     * @return The count of playerList.
     */
    int getPlayerListCount();
    /**
     * <code>repeated int64 playerList = 1;</code>
     * @param index The index of the element to return.
     * @return The playerList at the given index.
     */
    long getPlayerList(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchPlayerNameAns}
   */
  public static final class SearchPlayerNameAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchPlayerNameAns)
      SearchPlayerNameAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchPlayerNameAns.newBuilder() to construct.
    private SearchPlayerNameAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchPlayerNameAns() {
      playerList_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchPlayerNameAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchPlayerNameAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                playerList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              playerList_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                playerList_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                playerList_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          playerList_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchPlayerNameAns.class, com.yorha.proto.SsName.SearchPlayerNameAns.Builder.class);
    }

    public static final int PLAYERLIST_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList playerList_;
    /**
     * <code>repeated int64 playerList = 1;</code>
     * @return A list containing the playerList.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getPlayerListList() {
      return playerList_;
    }
    /**
     * <code>repeated int64 playerList = 1;</code>
     * @return The count of playerList.
     */
    public int getPlayerListCount() {
      return playerList_.size();
    }
    /**
     * <code>repeated int64 playerList = 1;</code>
     * @param index The index of the element to return.
     * @return The playerList at the given index.
     */
    public long getPlayerList(int index) {
      return playerList_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < playerList_.size(); i++) {
        output.writeInt64(1, playerList_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < playerList_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(playerList_.getLong(i));
        }
        size += dataSize;
        size += 1 * getPlayerListList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchPlayerNameAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchPlayerNameAns other = (com.yorha.proto.SsName.SearchPlayerNameAns) obj;

      if (!getPlayerListList()
          .equals(other.getPlayerListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getPlayerListCount() > 0) {
        hash = (37 * hash) + PLAYERLIST_FIELD_NUMBER;
        hash = (53 * hash) + getPlayerListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchPlayerNameAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchPlayerNameAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchPlayerNameAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchPlayerNameAns)
        com.yorha.proto.SsName.SearchPlayerNameAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchPlayerNameAns.class, com.yorha.proto.SsName.SearchPlayerNameAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchPlayerNameAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        playerList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAns getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchPlayerNameAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAns build() {
        com.yorha.proto.SsName.SearchPlayerNameAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchPlayerNameAns buildPartial() {
        com.yorha.proto.SsName.SearchPlayerNameAns result = new com.yorha.proto.SsName.SearchPlayerNameAns(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          playerList_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.playerList_ = playerList_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchPlayerNameAns) {
          return mergeFrom((com.yorha.proto.SsName.SearchPlayerNameAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchPlayerNameAns other) {
        if (other == com.yorha.proto.SsName.SearchPlayerNameAns.getDefaultInstance()) return this;
        if (!other.playerList_.isEmpty()) {
          if (playerList_.isEmpty()) {
            playerList_ = other.playerList_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensurePlayerListIsMutable();
            playerList_.addAll(other.playerList_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchPlayerNameAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchPlayerNameAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList playerList_ = emptyLongList();
      private void ensurePlayerListIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          playerList_ = mutableCopy(playerList_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @return A list containing the playerList.
       */
      public java.util.List<java.lang.Long>
          getPlayerListList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(playerList_) : playerList_;
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @return The count of playerList.
       */
      public int getPlayerListCount() {
        return playerList_.size();
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @param index The index of the element to return.
       * @return The playerList at the given index.
       */
      public long getPlayerList(int index) {
        return playerList_.getLong(index);
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @param index The index to set the value at.
       * @param value The playerList to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerList(
          int index, long value) {
        ensurePlayerListIsMutable();
        playerList_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @param value The playerList to add.
       * @return This builder for chaining.
       */
      public Builder addPlayerList(long value) {
        ensurePlayerListIsMutable();
        playerList_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @param values The playerList to add.
       * @return This builder for chaining.
       */
      public Builder addAllPlayerList(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensurePlayerListIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, playerList_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 playerList = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerList() {
        playerList_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchPlayerNameAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchPlayerNameAns)
    private static final com.yorha.proto.SsName.SearchPlayerNameAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchPlayerNameAns();
    }

    public static com.yorha.proto.SsName.SearchPlayerNameAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchPlayerNameAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchPlayerNameAns>() {
      @java.lang.Override
      public SearchPlayerNameAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchPlayerNameAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchPlayerNameAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchPlayerNameAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchPlayerNameAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchNameMatchedAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchNameMatchedAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
     * @return Whether the nameType field is set.
     */
    boolean hasNameType();
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
     * @return The nameType.
     */
    com.yorha.proto.CommonEnum.NameType getNameType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchNameMatchedAsk}
   */
  public static final class SearchNameMatchedAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchNameMatchedAsk)
      SearchNameMatchedAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchNameMatchedAsk.newBuilder() to construct.
    private SearchNameMatchedAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchNameMatchedAsk() {
      name_ = "";
      nameType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchNameMatchedAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchNameMatchedAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              name_ = bs;
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.NameType value = com.yorha.proto.CommonEnum.NameType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                nameType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchNameMatchedAsk.class, com.yorha.proto.SsName.SearchNameMatchedAsk.Builder.class);
    }

    private int bitField0_;
    public static final int NAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 1;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 1;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int NAMETYPE_FIELD_NUMBER = 2;
    private int nameType_;
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
     * @return Whether the nameType field is set.
     */
    @java.lang.Override public boolean hasNameType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
     * @return The nameType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.NameType getNameType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
      return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, nameType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, name_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, nameType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchNameMatchedAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchNameMatchedAsk other = (com.yorha.proto.SsName.SearchNameMatchedAsk) obj;

      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (hasNameType() != other.hasNameType()) return false;
      if (hasNameType()) {
        if (nameType_ != other.nameType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      if (hasNameType()) {
        hash = (37 * hash) + NAMETYPE_FIELD_NUMBER;
        hash = (53 * hash) + nameType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchNameMatchedAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchNameMatchedAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchNameMatchedAsk)
        com.yorha.proto.SsName.SearchNameMatchedAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchNameMatchedAsk.class, com.yorha.proto.SsName.SearchNameMatchedAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchNameMatchedAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        nameType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchNameMatchedAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAsk build() {
        com.yorha.proto.SsName.SearchNameMatchedAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAsk buildPartial() {
        com.yorha.proto.SsName.SearchNameMatchedAsk result = new com.yorha.proto.SsName.SearchNameMatchedAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.nameType_ = nameType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchNameMatchedAsk) {
          return mergeFrom((com.yorha.proto.SsName.SearchNameMatchedAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchNameMatchedAsk other) {
        if (other == com.yorha.proto.SsName.SearchNameMatchedAsk.getDefaultInstance()) return this;
        if (other.hasName()) {
          bitField0_ |= 0x00000001;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasNameType()) {
          setNameType(other.getNameType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchNameMatchedAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchNameMatchedAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 1;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 1;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        name_ = value;
        onChanged();
        return this;
      }

      private int nameType_ = 0;
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
       * @return Whether the nameType field is set.
       */
      @java.lang.Override public boolean hasNameType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
       * @return The nameType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.NameType getNameType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.NameType result = com.yorha.proto.CommonEnum.NameType.valueOf(nameType_);
        return result == null ? com.yorha.proto.CommonEnum.NameType.NT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
       * @param value The nameType to set.
       * @return This builder for chaining.
       */
      public Builder setNameType(com.yorha.proto.CommonEnum.NameType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        nameType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NameType nameType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearNameType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        nameType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchNameMatchedAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchNameMatchedAsk)
    private static final com.yorha.proto.SsName.SearchNameMatchedAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchNameMatchedAsk();
    }

    public static com.yorha.proto.SsName.SearchNameMatchedAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchNameMatchedAsk>
        PARSER = new com.google.protobuf.AbstractParser<SearchNameMatchedAsk>() {
      @java.lang.Override
      public SearchNameMatchedAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchNameMatchedAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchNameMatchedAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchNameMatchedAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchNameMatchedAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SearchNameMatchedAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SearchNameMatchedAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    boolean hasOwnerId();
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    long getOwnerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SearchNameMatchedAns}
   */
  public static final class SearchNameMatchedAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SearchNameMatchedAns)
      SearchNameMatchedAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SearchNameMatchedAns.newBuilder() to construct.
    private SearchNameMatchedAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SearchNameMatchedAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SearchNameMatchedAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SearchNameMatchedAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ownerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsName.SearchNameMatchedAns.class, com.yorha.proto.SsName.SearchNameMatchedAns.Builder.class);
    }

    private int bitField0_;
    public static final int OWNERID_FIELD_NUMBER = 1;
    private long ownerId_;
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return Whether the ownerId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 ownerId = 1;</code>
     * @return The ownerId.
     */
    @java.lang.Override
    public long getOwnerId() {
      return ownerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, ownerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, ownerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsName.SearchNameMatchedAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsName.SearchNameMatchedAns other = (com.yorha.proto.SsName.SearchNameMatchedAns) obj;

      if (hasOwnerId() != other.hasOwnerId()) return false;
      if (hasOwnerId()) {
        if (getOwnerId()
            != other.getOwnerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOwnerId()) {
        hash = (37 * hash) + OWNERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsName.SearchNameMatchedAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsName.SearchNameMatchedAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SearchNameMatchedAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SearchNameMatchedAns)
        com.yorha.proto.SsName.SearchNameMatchedAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsName.SearchNameMatchedAns.class, com.yorha.proto.SsName.SearchNameMatchedAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsName.SearchNameMatchedAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ownerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsName.internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAns getDefaultInstanceForType() {
        return com.yorha.proto.SsName.SearchNameMatchedAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAns build() {
        com.yorha.proto.SsName.SearchNameMatchedAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsName.SearchNameMatchedAns buildPartial() {
        com.yorha.proto.SsName.SearchNameMatchedAns result = new com.yorha.proto.SsName.SearchNameMatchedAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ownerId_ = ownerId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsName.SearchNameMatchedAns) {
          return mergeFrom((com.yorha.proto.SsName.SearchNameMatchedAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsName.SearchNameMatchedAns other) {
        if (other == com.yorha.proto.SsName.SearchNameMatchedAns.getDefaultInstance()) return this;
        if (other.hasOwnerId()) {
          setOwnerId(other.getOwnerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsName.SearchNameMatchedAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsName.SearchNameMatchedAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long ownerId_ ;
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return Whether the ownerId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return The ownerId.
       */
      @java.lang.Override
      public long getOwnerId() {
        return ownerId_;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @param value The ownerId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerId(long value) {
        bitField0_ |= 0x00000001;
        ownerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 ownerId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ownerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SearchNameMatchedAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SearchNameMatchedAns)
    private static final com.yorha.proto.SsName.SearchNameMatchedAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsName.SearchNameMatchedAns();
    }

    public static com.yorha.proto.SsName.SearchNameMatchedAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SearchNameMatchedAns>
        PARSER = new com.google.protobuf.AbstractParser<SearchNameMatchedAns>() {
      @java.lang.Override
      public SearchNameMatchedAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SearchNameMatchedAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SearchNameMatchedAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SearchNameMatchedAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsName.SearchNameMatchedAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_NamePair_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_NamePair_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OccupyBatchNameCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ReleaseNameCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ReleaseNameCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchClanNameAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchClanNameAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchClanNameAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchClanNameAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPlayerNameAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchPlayerNameAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchNameMatchedAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SearchNameMatchedAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\037ss_proto/gen/name/ss_name.proto\022\017com.y" +
      "orha.proto\032%ss_proto/gen/common/common_e" +
      "num.proto\"E\n\010NamePair\022+\n\010nameType\030\001 \001(\0162" +
      "\031.com.yorha.proto.NameType\022\014\n\004name\030\002 \001(\t" +
      "\"R\n\022OccupyBatchNameCmd\022\017\n\007ownerId\030\001 \001(\003\022" +
      "+\n\010namePair\030\002 \003(\0132\031.com.yorha.proto.Name" +
      "Pair\"N\n\016ReleaseNameCmd\022+\n\010nameType\030\001 \001(\016" +
      "2\031.com.yorha.proto.NameType\022\017\n\007ownerId\030\002" +
      " \001(\003\".\n\021SearchClanNameAsk\022\014\n\004name\030\001 \001(\t\022" +
      "\013\n\003num\030\002 \001(\005\"%\n\021SearchClanNameAns\022\020\n\010cla" +
      "nList\030\001 \003(\003\"5\n\023SearchPlayerNameAsk\022\014\n\004na" +
      "me\030\001 \001(\t\022\020\n\010numLimit\030\002 \001(\005\")\n\023SearchPlay" +
      "erNameAns\022\022\n\nplayerList\030\001 \003(\003\"Q\n\024SearchN" +
      "ameMatchedAsk\022\014\n\004name\030\001 \001(\t\022+\n\010nameType\030" +
      "\002 \001(\0162\031.com.yorha.proto.NameType\"\'\n\024Sear" +
      "chNameMatchedAns\022\017\n\007ownerId\030\001 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_NamePair_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_NamePair_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_NamePair_descriptor,
        new java.lang.String[] { "NameType", "Name", });
    internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_OccupyBatchNameCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OccupyBatchNameCmd_descriptor,
        new java.lang.String[] { "OwnerId", "NamePair", });
    internal_static_com_yorha_proto_ReleaseNameCmd_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ReleaseNameCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ReleaseNameCmd_descriptor,
        new java.lang.String[] { "NameType", "OwnerId", });
    internal_static_com_yorha_proto_SearchClanNameAsk_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_SearchClanNameAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchClanNameAsk_descriptor,
        new java.lang.String[] { "Name", "Num", });
    internal_static_com_yorha_proto_SearchClanNameAns_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_SearchClanNameAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchClanNameAns_descriptor,
        new java.lang.String[] { "ClanList", });
    internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_SearchPlayerNameAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPlayerNameAsk_descriptor,
        new java.lang.String[] { "Name", "NumLimit", });
    internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_SearchPlayerNameAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchPlayerNameAns_descriptor,
        new java.lang.String[] { "PlayerList", });
    internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_SearchNameMatchedAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchNameMatchedAsk_descriptor,
        new java.lang.String[] { "Name", "NameType", });
    internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_SearchNameMatchedAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SearchNameMatchedAns_descriptor,
        new java.lang.String[] { "OwnerId", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

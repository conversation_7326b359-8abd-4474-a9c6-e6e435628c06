// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_discount_store.proto

package com.yorha.proto;

public final class PlayerDiscountStore {
  private PlayerDiscountStore() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_GetDiscountStoreInfo_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetDiscountStoreInfo_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetDiscountStoreInfo_C2S}
   */
  public static final class Player_GetDiscountStoreInfo_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetDiscountStoreInfo_C2S)
      Player_GetDiscountStoreInfo_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetDiscountStoreInfo_C2S.newBuilder() to construct.
    private Player_GetDiscountStoreInfo_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetDiscountStoreInfo_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetDiscountStoreInfo_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetDiscountStoreInfo_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S other = (com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetDiscountStoreInfo_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetDiscountStoreInfo_C2S)
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S build() {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S result = new com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetDiscountStoreInfo_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetDiscountStoreInfo_C2S)
    private static final com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetDiscountStoreInfo_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetDiscountStoreInfo_C2S>() {
      @java.lang.Override
      public Player_GetDiscountStoreInfo_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetDiscountStoreInfo_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetDiscountStoreInfo_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetDiscountStoreInfo_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetDiscountStoreInfo_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetDiscountStoreInfo_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    boolean hasDto();
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return The dto.
     */
    com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto();
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     */
    com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetDiscountStoreInfo_S2C}
   */
  public static final class Player_GetDiscountStoreInfo_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetDiscountStoreInfo_S2C)
      Player_GetDiscountStoreInfo_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetDiscountStoreInfo_S2C.newBuilder() to construct.
    private Player_GetDiscountStoreInfo_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetDiscountStoreInfo_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetDiscountStoreInfo_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetDiscountStoreInfo_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = dto_.toBuilder();
              }
              dto_ = input.readMessage(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(dto_);
                dto_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int DTO_FIELD_NUMBER = 1;
    private com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO dto_;
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    @java.lang.Override
    public boolean hasDto() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return The dto.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto() {
      return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
    }
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder() {
      return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getDto());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDto());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C other = (com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C) obj;

      if (hasDto() != other.hasDto()) return false;
      if (hasDto()) {
        if (!getDto()
            .equals(other.getDto())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDto()) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDto().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetDiscountStoreInfo_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetDiscountStoreInfo_S2C)
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dtoBuilder_ == null) {
          dto_ = null;
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C build() {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C result = new com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (dtoBuilder_ == null) {
            result.dto_ = dto_;
          } else {
            result.dto_ = dtoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.getDefaultInstance()) return this;
        if (other.hasDto()) {
          mergeDto(other.getDto());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO dto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder> dtoBuilder_;
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       * @return Whether the dto field is set.
       */
      public boolean hasDto() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       * @return The dto.
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto() {
        if (dtoBuilder_ == null) {
          return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
        } else {
          return dtoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder setDto(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dto_ = value;
          onChanged();
        } else {
          dtoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder setDto(
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          dto_ = builderForValue.build();
          onChanged();
        } else {
          dtoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder mergeDto(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO value) {
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              dto_ != null &&
              dto_ != com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance()) {
            dto_ =
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.newBuilder(dto_).mergeFrom(value).buildPartial();
          } else {
            dto_ = value;
          }
          onChanged();
        } else {
          dtoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = null;
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder getDtoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDtoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilder();
        } else {
          return dto_ == null ?
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder>(
                  getDto(),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetDiscountStoreInfo_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetDiscountStoreInfo_S2C)
    private static final com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetDiscountStoreInfo_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetDiscountStoreInfo_S2C>() {
      @java.lang.Override
      public Player_GetDiscountStoreInfo_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetDiscountStoreInfo_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetDiscountStoreInfo_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetDiscountStoreInfo_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface DiscountStoreDTOOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DiscountStoreDTO)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 下次刷新时间
     * </pre>
     *
     * <code>optional int64 nextRefreshTime = 1;</code>
     * @return Whether the nextRefreshTime field is set.
     */
    boolean hasNextRefreshTime();
    /**
     * <pre>
     * 下次刷新时间
     * </pre>
     *
     * <code>optional int64 nextRefreshTime = 1;</code>
     * @return The nextRefreshTime.
     */
    long getNextRefreshTime();

    /**
     * <pre>
     * 还剩刷新次数
     * </pre>
     *
     * <code>optional int32 refreshTimes = 2;</code>
     * @return Whether the refreshTimes field is set.
     */
    boolean hasRefreshTimes();
    /**
     * <pre>
     * 还剩刷新次数
     * </pre>
     *
     * <code>optional int32 refreshTimes = 2;</code>
     * @return The refreshTimes.
     */
    int getRefreshTimes();

    /**
     * <pre>
     * 最大刷新次数
     * </pre>
     *
     * <code>optional int32 maxRefreshTimes = 3;</code>
     * @return Whether the maxRefreshTimes field is set.
     */
    boolean hasMaxRefreshTimes();
    /**
     * <pre>
     * 最大刷新次数
     * </pre>
     *
     * <code>optional int32 maxRefreshTimes = 3;</code>
     * @return The maxRefreshTimes.
     */
    int getMaxRefreshTimes();

    /**
     * <pre>
     * 剩余免费次数
     * </pre>
     *
     * <code>optional int32 freeTimes = 4;</code>
     * @return Whether the freeTimes field is set.
     */
    boolean hasFreeTimes();
    /**
     * <pre>
     * 剩余免费次数
     * </pre>
     *
     * <code>optional int32 freeTimes = 4;</code>
     * @return The freeTimes.
     */
    int getFreeTimes();

    /**
     * <pre>
     * 最大免费次数
     * </pre>
     *
     * <code>optional int32 maxFreeTimes = 5;</code>
     * @return Whether the maxFreeTimes field is set.
     */
    boolean hasMaxFreeTimes();
    /**
     * <pre>
     * 最大免费次数
     * </pre>
     *
     * <code>optional int32 maxFreeTimes = 5;</code>
     * @return The maxFreeTimes.
     */
    int getMaxFreeTimes();

    /**
     * <pre>
     * 刷新所需价格
     * </pre>
     *
     * <code>optional int32 refreshPrice = 6;</code>
     * @return Whether the refreshPrice field is set.
     */
    boolean hasRefreshPrice();
    /**
     * <pre>
     * 刷新所需价格
     * </pre>
     *
     * <code>optional int32 refreshPrice = 6;</code>
     * @return The refreshPrice.
     */
    int getRefreshPrice();

    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.StoreItemDTO> 
        getListList();
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    com.yorha.proto.StructMsg.StoreItemDTO getList(int index);
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    int getListCount();
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> 
        getListOrBuilderList();
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getListOrBuilder(
        int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.DiscountStoreDTO}
   */
  public static final class DiscountStoreDTO extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DiscountStoreDTO)
      DiscountStoreDTOOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DiscountStoreDTO.newBuilder() to construct.
    private DiscountStoreDTO(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DiscountStoreDTO() {
      list_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DiscountStoreDTO();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DiscountStoreDTO(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              nextRefreshTime_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              refreshTimes_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxRefreshTimes_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              freeTimes_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              maxFreeTimes_ = input.readInt32();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              refreshPrice_ = input.readInt32();
              break;
            }
            case 58: {
              if (!((mutable_bitField0_ & 0x00000040) != 0)) {
                list_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreItemDTO>();
                mutable_bitField0_ |= 0x00000040;
              }
              list_.add(
                  input.readMessage(com.yorha.proto.StructMsg.StoreItemDTO.PARSER, extensionRegistry));
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000040) != 0)) {
          list_ = java.util.Collections.unmodifiableList(list_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_DiscountStoreDTO_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_DiscountStoreDTO_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.class, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder.class);
    }

    private int bitField0_;
    public static final int NEXTREFRESHTIME_FIELD_NUMBER = 1;
    private long nextRefreshTime_;
    /**
     * <pre>
     * 下次刷新时间
     * </pre>
     *
     * <code>optional int64 nextRefreshTime = 1;</code>
     * @return Whether the nextRefreshTime field is set.
     */
    @java.lang.Override
    public boolean hasNextRefreshTime() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 下次刷新时间
     * </pre>
     *
     * <code>optional int64 nextRefreshTime = 1;</code>
     * @return The nextRefreshTime.
     */
    @java.lang.Override
    public long getNextRefreshTime() {
      return nextRefreshTime_;
    }

    public static final int REFRESHTIMES_FIELD_NUMBER = 2;
    private int refreshTimes_;
    /**
     * <pre>
     * 还剩刷新次数
     * </pre>
     *
     * <code>optional int32 refreshTimes = 2;</code>
     * @return Whether the refreshTimes field is set.
     */
    @java.lang.Override
    public boolean hasRefreshTimes() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 还剩刷新次数
     * </pre>
     *
     * <code>optional int32 refreshTimes = 2;</code>
     * @return The refreshTimes.
     */
    @java.lang.Override
    public int getRefreshTimes() {
      return refreshTimes_;
    }

    public static final int MAXREFRESHTIMES_FIELD_NUMBER = 3;
    private int maxRefreshTimes_;
    /**
     * <pre>
     * 最大刷新次数
     * </pre>
     *
     * <code>optional int32 maxRefreshTimes = 3;</code>
     * @return Whether the maxRefreshTimes field is set.
     */
    @java.lang.Override
    public boolean hasMaxRefreshTimes() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 最大刷新次数
     * </pre>
     *
     * <code>optional int32 maxRefreshTimes = 3;</code>
     * @return The maxRefreshTimes.
     */
    @java.lang.Override
    public int getMaxRefreshTimes() {
      return maxRefreshTimes_;
    }

    public static final int FREETIMES_FIELD_NUMBER = 4;
    private int freeTimes_;
    /**
     * <pre>
     * 剩余免费次数
     * </pre>
     *
     * <code>optional int32 freeTimes = 4;</code>
     * @return Whether the freeTimes field is set.
     */
    @java.lang.Override
    public boolean hasFreeTimes() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 剩余免费次数
     * </pre>
     *
     * <code>optional int32 freeTimes = 4;</code>
     * @return The freeTimes.
     */
    @java.lang.Override
    public int getFreeTimes() {
      return freeTimes_;
    }

    public static final int MAXFREETIMES_FIELD_NUMBER = 5;
    private int maxFreeTimes_;
    /**
     * <pre>
     * 最大免费次数
     * </pre>
     *
     * <code>optional int32 maxFreeTimes = 5;</code>
     * @return Whether the maxFreeTimes field is set.
     */
    @java.lang.Override
    public boolean hasMaxFreeTimes() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 最大免费次数
     * </pre>
     *
     * <code>optional int32 maxFreeTimes = 5;</code>
     * @return The maxFreeTimes.
     */
    @java.lang.Override
    public int getMaxFreeTimes() {
      return maxFreeTimes_;
    }

    public static final int REFRESHPRICE_FIELD_NUMBER = 6;
    private int refreshPrice_;
    /**
     * <pre>
     * 刷新所需价格
     * </pre>
     *
     * <code>optional int32 refreshPrice = 6;</code>
     * @return Whether the refreshPrice field is set.
     */
    @java.lang.Override
    public boolean hasRefreshPrice() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 刷新所需价格
     * </pre>
     *
     * <code>optional int32 refreshPrice = 6;</code>
     * @return The refreshPrice.
     */
    @java.lang.Override
    public int getRefreshPrice() {
      return refreshPrice_;
    }

    public static final int LIST_FIELD_NUMBER = 7;
    private java.util.List<com.yorha.proto.StructMsg.StoreItemDTO> list_;
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.StoreItemDTO> getListList() {
      return list_;
    }
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> 
        getListOrBuilderList() {
      return list_;
    }
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    @java.lang.Override
    public int getListCount() {
      return list_.size();
    }
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreItemDTO getList(int index) {
      return list_.get(index);
    }
    /**
     * <pre>
     * 商店商品信息
     * </pre>
     *
     * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getListOrBuilder(
        int index) {
      return list_.get(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, nextRefreshTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, refreshTimes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, maxRefreshTimes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, freeTimes_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, maxFreeTimes_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, refreshPrice_);
      }
      for (int i = 0; i < list_.size(); i++) {
        output.writeMessage(7, list_.get(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, nextRefreshTime_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, refreshTimes_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, maxRefreshTimes_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, freeTimes_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, maxFreeTimes_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, refreshPrice_);
      }
      for (int i = 0; i < list_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, list_.get(i));
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO other = (com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO) obj;

      if (hasNextRefreshTime() != other.hasNextRefreshTime()) return false;
      if (hasNextRefreshTime()) {
        if (getNextRefreshTime()
            != other.getNextRefreshTime()) return false;
      }
      if (hasRefreshTimes() != other.hasRefreshTimes()) return false;
      if (hasRefreshTimes()) {
        if (getRefreshTimes()
            != other.getRefreshTimes()) return false;
      }
      if (hasMaxRefreshTimes() != other.hasMaxRefreshTimes()) return false;
      if (hasMaxRefreshTimes()) {
        if (getMaxRefreshTimes()
            != other.getMaxRefreshTimes()) return false;
      }
      if (hasFreeTimes() != other.hasFreeTimes()) return false;
      if (hasFreeTimes()) {
        if (getFreeTimes()
            != other.getFreeTimes()) return false;
      }
      if (hasMaxFreeTimes() != other.hasMaxFreeTimes()) return false;
      if (hasMaxFreeTimes()) {
        if (getMaxFreeTimes()
            != other.getMaxFreeTimes()) return false;
      }
      if (hasRefreshPrice() != other.hasRefreshPrice()) return false;
      if (hasRefreshPrice()) {
        if (getRefreshPrice()
            != other.getRefreshPrice()) return false;
      }
      if (!getListList()
          .equals(other.getListList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNextRefreshTime()) {
        hash = (37 * hash) + NEXTREFRESHTIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getNextRefreshTime());
      }
      if (hasRefreshTimes()) {
        hash = (37 * hash) + REFRESHTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getRefreshTimes();
      }
      if (hasMaxRefreshTimes()) {
        hash = (37 * hash) + MAXREFRESHTIMES_FIELD_NUMBER;
        hash = (53 * hash) + getMaxRefreshTimes();
      }
      if (hasFreeTimes()) {
        hash = (37 * hash) + FREETIMES_FIELD_NUMBER;
        hash = (53 * hash) + getFreeTimes();
      }
      if (hasMaxFreeTimes()) {
        hash = (37 * hash) + MAXFREETIMES_FIELD_NUMBER;
        hash = (53 * hash) + getMaxFreeTimes();
      }
      if (hasRefreshPrice()) {
        hash = (37 * hash) + REFRESHPRICE_FIELD_NUMBER;
        hash = (53 * hash) + getRefreshPrice();
      }
      if (getListCount() > 0) {
        hash = (37 * hash) + LIST_FIELD_NUMBER;
        hash = (53 * hash) + getListList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DiscountStoreDTO}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DiscountStoreDTO)
        com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_DiscountStoreDTO_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_DiscountStoreDTO_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.class, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        nextRefreshTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        refreshTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxRefreshTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        freeTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        maxFreeTimes_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        refreshPrice_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
        } else {
          listBuilder_.clear();
        }
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_DiscountStoreDTO_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO build() {
        com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO buildPartial() {
        com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO result = new com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.nextRefreshTime_ = nextRefreshTime_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.refreshTimes_ = refreshTimes_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxRefreshTimes_ = maxRefreshTimes_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.freeTimes_ = freeTimes_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.maxFreeTimes_ = maxFreeTimes_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.refreshPrice_ = refreshPrice_;
          to_bitField0_ |= 0x00000020;
        }
        if (listBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0)) {
            list_ = java.util.Collections.unmodifiableList(list_);
            bitField0_ = (bitField0_ & ~0x00000040);
          }
          result.list_ = list_;
        } else {
          result.list_ = listBuilder_.build();
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO other) {
        if (other == com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance()) return this;
        if (other.hasNextRefreshTime()) {
          setNextRefreshTime(other.getNextRefreshTime());
        }
        if (other.hasRefreshTimes()) {
          setRefreshTimes(other.getRefreshTimes());
        }
        if (other.hasMaxRefreshTimes()) {
          setMaxRefreshTimes(other.getMaxRefreshTimes());
        }
        if (other.hasFreeTimes()) {
          setFreeTimes(other.getFreeTimes());
        }
        if (other.hasMaxFreeTimes()) {
          setMaxFreeTimes(other.getMaxFreeTimes());
        }
        if (other.hasRefreshPrice()) {
          setRefreshPrice(other.getRefreshPrice());
        }
        if (listBuilder_ == null) {
          if (!other.list_.isEmpty()) {
            if (list_.isEmpty()) {
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000040);
            } else {
              ensureListIsMutable();
              list_.addAll(other.list_);
            }
            onChanged();
          }
        } else {
          if (!other.list_.isEmpty()) {
            if (listBuilder_.isEmpty()) {
              listBuilder_.dispose();
              listBuilder_ = null;
              list_ = other.list_;
              bitField0_ = (bitField0_ & ~0x00000040);
              listBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getListFieldBuilder() : null;
            } else {
              listBuilder_.addAllMessages(other.list_);
            }
          }
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long nextRefreshTime_ ;
      /**
       * <pre>
       * 下次刷新时间
       * </pre>
       *
       * <code>optional int64 nextRefreshTime = 1;</code>
       * @return Whether the nextRefreshTime field is set.
       */
      @java.lang.Override
      public boolean hasNextRefreshTime() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 下次刷新时间
       * </pre>
       *
       * <code>optional int64 nextRefreshTime = 1;</code>
       * @return The nextRefreshTime.
       */
      @java.lang.Override
      public long getNextRefreshTime() {
        return nextRefreshTime_;
      }
      /**
       * <pre>
       * 下次刷新时间
       * </pre>
       *
       * <code>optional int64 nextRefreshTime = 1;</code>
       * @param value The nextRefreshTime to set.
       * @return This builder for chaining.
       */
      public Builder setNextRefreshTime(long value) {
        bitField0_ |= 0x00000001;
        nextRefreshTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 下次刷新时间
       * </pre>
       *
       * <code>optional int64 nextRefreshTime = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearNextRefreshTime() {
        bitField0_ = (bitField0_ & ~0x00000001);
        nextRefreshTime_ = 0L;
        onChanged();
        return this;
      }

      private int refreshTimes_ ;
      /**
       * <pre>
       * 还剩刷新次数
       * </pre>
       *
       * <code>optional int32 refreshTimes = 2;</code>
       * @return Whether the refreshTimes field is set.
       */
      @java.lang.Override
      public boolean hasRefreshTimes() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 还剩刷新次数
       * </pre>
       *
       * <code>optional int32 refreshTimes = 2;</code>
       * @return The refreshTimes.
       */
      @java.lang.Override
      public int getRefreshTimes() {
        return refreshTimes_;
      }
      /**
       * <pre>
       * 还剩刷新次数
       * </pre>
       *
       * <code>optional int32 refreshTimes = 2;</code>
       * @param value The refreshTimes to set.
       * @return This builder for chaining.
       */
      public Builder setRefreshTimes(int value) {
        bitField0_ |= 0x00000002;
        refreshTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 还剩刷新次数
       * </pre>
       *
       * <code>optional int32 refreshTimes = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefreshTimes() {
        bitField0_ = (bitField0_ & ~0x00000002);
        refreshTimes_ = 0;
        onChanged();
        return this;
      }

      private int maxRefreshTimes_ ;
      /**
       * <pre>
       * 最大刷新次数
       * </pre>
       *
       * <code>optional int32 maxRefreshTimes = 3;</code>
       * @return Whether the maxRefreshTimes field is set.
       */
      @java.lang.Override
      public boolean hasMaxRefreshTimes() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 最大刷新次数
       * </pre>
       *
       * <code>optional int32 maxRefreshTimes = 3;</code>
       * @return The maxRefreshTimes.
       */
      @java.lang.Override
      public int getMaxRefreshTimes() {
        return maxRefreshTimes_;
      }
      /**
       * <pre>
       * 最大刷新次数
       * </pre>
       *
       * <code>optional int32 maxRefreshTimes = 3;</code>
       * @param value The maxRefreshTimes to set.
       * @return This builder for chaining.
       */
      public Builder setMaxRefreshTimes(int value) {
        bitField0_ |= 0x00000004;
        maxRefreshTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最大刷新次数
       * </pre>
       *
       * <code>optional int32 maxRefreshTimes = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxRefreshTimes() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxRefreshTimes_ = 0;
        onChanged();
        return this;
      }

      private int freeTimes_ ;
      /**
       * <pre>
       * 剩余免费次数
       * </pre>
       *
       * <code>optional int32 freeTimes = 4;</code>
       * @return Whether the freeTimes field is set.
       */
      @java.lang.Override
      public boolean hasFreeTimes() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 剩余免费次数
       * </pre>
       *
       * <code>optional int32 freeTimes = 4;</code>
       * @return The freeTimes.
       */
      @java.lang.Override
      public int getFreeTimes() {
        return freeTimes_;
      }
      /**
       * <pre>
       * 剩余免费次数
       * </pre>
       *
       * <code>optional int32 freeTimes = 4;</code>
       * @param value The freeTimes to set.
       * @return This builder for chaining.
       */
      public Builder setFreeTimes(int value) {
        bitField0_ |= 0x00000008;
        freeTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 剩余免费次数
       * </pre>
       *
       * <code>optional int32 freeTimes = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearFreeTimes() {
        bitField0_ = (bitField0_ & ~0x00000008);
        freeTimes_ = 0;
        onChanged();
        return this;
      }

      private int maxFreeTimes_ ;
      /**
       * <pre>
       * 最大免费次数
       * </pre>
       *
       * <code>optional int32 maxFreeTimes = 5;</code>
       * @return Whether the maxFreeTimes field is set.
       */
      @java.lang.Override
      public boolean hasMaxFreeTimes() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 最大免费次数
       * </pre>
       *
       * <code>optional int32 maxFreeTimes = 5;</code>
       * @return The maxFreeTimes.
       */
      @java.lang.Override
      public int getMaxFreeTimes() {
        return maxFreeTimes_;
      }
      /**
       * <pre>
       * 最大免费次数
       * </pre>
       *
       * <code>optional int32 maxFreeTimes = 5;</code>
       * @param value The maxFreeTimes to set.
       * @return This builder for chaining.
       */
      public Builder setMaxFreeTimes(int value) {
        bitField0_ |= 0x00000010;
        maxFreeTimes_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最大免费次数
       * </pre>
       *
       * <code>optional int32 maxFreeTimes = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxFreeTimes() {
        bitField0_ = (bitField0_ & ~0x00000010);
        maxFreeTimes_ = 0;
        onChanged();
        return this;
      }

      private int refreshPrice_ ;
      /**
       * <pre>
       * 刷新所需价格
       * </pre>
       *
       * <code>optional int32 refreshPrice = 6;</code>
       * @return Whether the refreshPrice field is set.
       */
      @java.lang.Override
      public boolean hasRefreshPrice() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 刷新所需价格
       * </pre>
       *
       * <code>optional int32 refreshPrice = 6;</code>
       * @return The refreshPrice.
       */
      @java.lang.Override
      public int getRefreshPrice() {
        return refreshPrice_;
      }
      /**
       * <pre>
       * 刷新所需价格
       * </pre>
       *
       * <code>optional int32 refreshPrice = 6;</code>
       * @param value The refreshPrice to set.
       * @return This builder for chaining.
       */
      public Builder setRefreshPrice(int value) {
        bitField0_ |= 0x00000020;
        refreshPrice_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 刷新所需价格
       * </pre>
       *
       * <code>optional int32 refreshPrice = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearRefreshPrice() {
        bitField0_ = (bitField0_ & ~0x00000020);
        refreshPrice_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructMsg.StoreItemDTO> list_ =
        java.util.Collections.emptyList();
      private void ensureListIsMutable() {
        if (!((bitField0_ & 0x00000040) != 0)) {
          list_ = new java.util.ArrayList<com.yorha.proto.StructMsg.StoreItemDTO>(list_);
          bitField0_ |= 0x00000040;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> listBuilder_;

      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreItemDTO> getListList() {
        if (listBuilder_ == null) {
          return java.util.Collections.unmodifiableList(list_);
        } else {
          return listBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public int getListCount() {
        if (listBuilder_ == null) {
          return list_.size();
        } else {
          return listBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTO getList(int index) {
        if (listBuilder_ == null) {
          return list_.get(index);
        } else {
          return listBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder setList(
          int index, com.yorha.proto.StructMsg.StoreItemDTO value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.set(index, value);
          onChanged();
        } else {
          listBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder setList(
          int index, com.yorha.proto.StructMsg.StoreItemDTO.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.set(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder addList(com.yorha.proto.StructMsg.StoreItemDTO value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(value);
          onChanged();
        } else {
          listBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder addList(
          int index, com.yorha.proto.StructMsg.StoreItemDTO value) {
        if (listBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureListIsMutable();
          list_.add(index, value);
          onChanged();
        } else {
          listBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder addList(
          com.yorha.proto.StructMsg.StoreItemDTO.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder addList(
          int index, com.yorha.proto.StructMsg.StoreItemDTO.Builder builderForValue) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.add(index, builderForValue.build());
          onChanged();
        } else {
          listBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder addAllList(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.StoreItemDTO> values) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, list_);
          onChanged();
        } else {
          listBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder clearList() {
        if (listBuilder_ == null) {
          list_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000040);
          onChanged();
        } else {
          listBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public Builder removeList(int index) {
        if (listBuilder_ == null) {
          ensureListIsMutable();
          list_.remove(index);
          onChanged();
        } else {
          listBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTO.Builder getListBuilder(
          int index) {
        return getListFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getListOrBuilder(
          int index) {
        if (listBuilder_ == null) {
          return list_.get(index);  } else {
          return listBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> 
           getListOrBuilderList() {
        if (listBuilder_ != null) {
          return listBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(list_);
        }
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTO.Builder addListBuilder() {
        return getListFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTO.Builder addListBuilder(
          int index) {
        return getListFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance());
      }
      /**
       * <pre>
       * 商店商品信息
       * </pre>
       *
       * <code>repeated .com.yorha.proto.StoreItemDTO list = 7;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.StoreItemDTO.Builder> 
           getListBuilderList() {
        return getListFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> 
          getListFieldBuilder() {
        if (listBuilder_ == null) {
          listBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder>(
                  list_,
                  ((bitField0_ & 0x00000040) != 0),
                  getParentForChildren(),
                  isClean());
          list_ = null;
        }
        return listBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DiscountStoreDTO)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DiscountStoreDTO)
    private static final com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO();
    }

    public static com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DiscountStoreDTO>
        PARSER = new com.google.protobuf.AbstractParser<DiscountStoreDTO>() {
      @java.lang.Override
      public DiscountStoreDTO parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DiscountStoreDTO(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DiscountStoreDTO> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DiscountStoreDTO> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyDiscountStoreItem_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyDiscountStoreItem_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return Whether the sPassWord field is set.
     */
    boolean hasSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The sPassWord.
     */
    java.lang.String getSPassWord();
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The bytes for sPassWord.
     */
    com.google.protobuf.ByteString
        getSPassWordBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyDiscountStoreItem_C2S}
   */
  public static final class Player_BuyDiscountStoreItem_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyDiscountStoreItem_C2S)
      Player_BuyDiscountStoreItem_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyDiscountStoreItem_C2S.newBuilder() to construct.
    private Player_BuyDiscountStoreItem_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyDiscountStoreItem_C2S() {
      sPassWord_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyDiscountStoreItem_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyDiscountStoreItem_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              itemId_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              sPassWord_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ITEMID_FIELD_NUMBER = 1;
    private int itemId_;
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 商品模板id
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int SPASSWORD_FIELD_NUMBER = 2;
    private volatile java.lang.Object sPassWord_;
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return Whether the sPassWord field is set.
     */
    @java.lang.Override
    public boolean hasSPassWord() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The sPassWord.
     */
    @java.lang.Override
    public java.lang.String getSPassWord() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          sPassWord_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 二级密码
     * </pre>
     *
     * <code>optional string sPassWord = 2;</code>
     * @return The bytes for sPassWord.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getSPassWordBytes() {
      java.lang.Object ref = sPassWord_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        sPassWord_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, sPassWord_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, sPassWord_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S other = (com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S) obj;

      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasSPassWord() != other.hasSPassWord()) return false;
      if (hasSPassWord()) {
        if (!getSPassWord()
            .equals(other.getSPassWord())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasSPassWord()) {
        hash = (37 * hash) + SPASSWORD_FIELD_NUMBER;
        hash = (53 * hash) + getSPassWord().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyDiscountStoreItem_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyDiscountStoreItem_C2S)
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        sPassWord_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S build() {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S result = new com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.sPassWord_ = sPassWord_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.getDefaultInstance()) return this;
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasSPassWord()) {
          bitField0_ |= 0x00000002;
          sPassWord_ = other.sPassWord_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int itemId_ ;
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000001;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 商品模板id
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object sPassWord_ = "";
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return Whether the sPassWord field is set.
       */
      public boolean hasSPassWord() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return The sPassWord.
       */
      public java.lang.String getSPassWord() {
        java.lang.Object ref = sPassWord_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            sPassWord_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return The bytes for sPassWord.
       */
      public com.google.protobuf.ByteString
          getSPassWordBytes() {
        java.lang.Object ref = sPassWord_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          sPassWord_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @param value The sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWord(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearSPassWord() {
        bitField0_ = (bitField0_ & ~0x00000002);
        sPassWord_ = getDefaultInstance().getSPassWord();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 二级密码
       * </pre>
       *
       * <code>optional string sPassWord = 2;</code>
       * @param value The bytes for sPassWord to set.
       * @return This builder for chaining.
       */
      public Builder setSPassWordBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        sPassWord_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyDiscountStoreItem_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyDiscountStoreItem_C2S)
    private static final com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyDiscountStoreItem_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyDiscountStoreItem_C2S>() {
      @java.lang.Override
      public Player_BuyDiscountStoreItem_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyDiscountStoreItem_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyDiscountStoreItem_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyDiscountStoreItem_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_BuyDiscountStoreItem_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_BuyDiscountStoreItem_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    boolean hasDto();
    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     * @return The dto.
     */
    com.yorha.proto.StructMsg.StoreItemDTO getDto();
    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     */
    com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getDtoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_BuyDiscountStoreItem_S2C}
   */
  public static final class Player_BuyDiscountStoreItem_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_BuyDiscountStoreItem_S2C)
      Player_BuyDiscountStoreItem_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_BuyDiscountStoreItem_S2C.newBuilder() to construct.
    private Player_BuyDiscountStoreItem_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_BuyDiscountStoreItem_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_BuyDiscountStoreItem_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_BuyDiscountStoreItem_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructMsg.StoreItemDTO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = dto_.toBuilder();
              }
              dto_ = input.readMessage(com.yorha.proto.StructMsg.StoreItemDTO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(dto_);
                dto_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int DTO_FIELD_NUMBER = 1;
    private com.yorha.proto.StructMsg.StoreItemDTO dto_;
    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    @java.lang.Override
    public boolean hasDto() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     * @return The dto.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreItemDTO getDto() {
      return dto_ == null ? com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance() : dto_;
    }
    /**
     * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getDtoOrBuilder() {
      return dto_ == null ? com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance() : dto_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getDto());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDto());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C other = (com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C) obj;

      if (hasDto() != other.hasDto()) return false;
      if (hasDto()) {
        if (!getDto()
            .equals(other.getDto())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDto()) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDto().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_BuyDiscountStoreItem_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_BuyDiscountStoreItem_S2C)
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dtoBuilder_ == null) {
          dto_ = null;
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C build() {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C result = new com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (dtoBuilder_ == null) {
            result.dto_ = dto_;
          } else {
            result.dto_ = dtoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.getDefaultInstance()) return this;
        if (other.hasDto()) {
          mergeDto(other.getDto());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMsg.StoreItemDTO dto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> dtoBuilder_;
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       * @return Whether the dto field is set.
       */
      public boolean hasDto() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       * @return The dto.
       */
      public com.yorha.proto.StructMsg.StoreItemDTO getDto() {
        if (dtoBuilder_ == null) {
          return dto_ == null ? com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance() : dto_;
        } else {
          return dtoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public Builder setDto(com.yorha.proto.StructMsg.StoreItemDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dto_ = value;
          onChanged();
        } else {
          dtoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public Builder setDto(
          com.yorha.proto.StructMsg.StoreItemDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          dto_ = builderForValue.build();
          onChanged();
        } else {
          dtoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public Builder mergeDto(com.yorha.proto.StructMsg.StoreItemDTO value) {
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              dto_ != null &&
              dto_ != com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance()) {
            dto_ =
              com.yorha.proto.StructMsg.StoreItemDTO.newBuilder(dto_).mergeFrom(value).buildPartial();
          } else {
            dto_ = value;
          }
          onChanged();
        } else {
          dtoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = null;
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTO.Builder getDtoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDtoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      public com.yorha.proto.StructMsg.StoreItemDTOOrBuilder getDtoOrBuilder() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilder();
        } else {
          return dto_ == null ?
              com.yorha.proto.StructMsg.StoreItemDTO.getDefaultInstance() : dto_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.StoreItemDTO dto = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.StoreItemDTO, com.yorha.proto.StructMsg.StoreItemDTO.Builder, com.yorha.proto.StructMsg.StoreItemDTOOrBuilder>(
                  getDto(),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_BuyDiscountStoreItem_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_BuyDiscountStoreItem_S2C)
    private static final com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_BuyDiscountStoreItem_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_BuyDiscountStoreItem_S2C>() {
      @java.lang.Override
      public Player_BuyDiscountStoreItem_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_BuyDiscountStoreItem_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_BuyDiscountStoreItem_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_BuyDiscountStoreItem_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RefreshDiscountStore_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RefreshDiscountStore_C2S)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RefreshDiscountStore_C2S}
   */
  public static final class Player_RefreshDiscountStore_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RefreshDiscountStore_C2S)
      Player_RefreshDiscountStore_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RefreshDiscountStore_C2S.newBuilder() to construct.
    private Player_RefreshDiscountStore_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RefreshDiscountStore_C2S() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RefreshDiscountStore_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RefreshDiscountStore_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S other = (com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RefreshDiscountStore_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RefreshDiscountStore_C2S)
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.class, com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S build() {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S result = new com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RefreshDiscountStore_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RefreshDiscountStore_C2S)
    private static final com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RefreshDiscountStore_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_RefreshDiscountStore_C2S>() {
      @java.lang.Override
      public Player_RefreshDiscountStore_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RefreshDiscountStore_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RefreshDiscountStore_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RefreshDiscountStore_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_RefreshDiscountStore_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_RefreshDiscountStore_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    boolean hasDto();
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return The dto.
     */
    com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto();
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     */
    com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_RefreshDiscountStore_S2C}
   */
  public static final class Player_RefreshDiscountStore_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_RefreshDiscountStore_S2C)
      Player_RefreshDiscountStore_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_RefreshDiscountStore_S2C.newBuilder() to construct.
    private Player_RefreshDiscountStore_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_RefreshDiscountStore_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_RefreshDiscountStore_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_RefreshDiscountStore_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = dto_.toBuilder();
              }
              dto_ = input.readMessage(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(dto_);
                dto_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.Builder.class);
    }

    private int bitField0_;
    public static final int DTO_FIELD_NUMBER = 1;
    private com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO dto_;
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return Whether the dto field is set.
     */
    @java.lang.Override
    public boolean hasDto() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     * @return The dto.
     */
    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto() {
      return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
    }
    /**
     * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder() {
      return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getDto());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getDto());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C other = (com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C) obj;

      if (hasDto() != other.hasDto()) return false;
      if (hasDto()) {
        if (!getDto()
            .equals(other.getDto())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasDto()) {
        hash = (37 * hash) + DTO_FIELD_NUMBER;
        hash = (53 * hash) + getDto().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_RefreshDiscountStore_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_RefreshDiscountStore_S2C)
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.class, com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDtoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (dtoBuilder_ == null) {
          dto_ = null;
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerDiscountStore.internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C build() {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C buildPartial() {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C result = new com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (dtoBuilder_ == null) {
            result.dto_ = dto_;
          } else {
            result.dto_ = dtoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C) {
          return mergeFrom((com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C other) {
        if (other == com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C.getDefaultInstance()) return this;
        if (other.hasDto()) {
          mergeDto(other.getDto());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO dto_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder> dtoBuilder_;
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       * @return Whether the dto field is set.
       */
      public boolean hasDto() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       * @return The dto.
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO getDto() {
        if (dtoBuilder_ == null) {
          return dto_ == null ? com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
        } else {
          return dtoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder setDto(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO value) {
        if (dtoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          dto_ = value;
          onChanged();
        } else {
          dtoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder setDto(
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder builderForValue) {
        if (dtoBuilder_ == null) {
          dto_ = builderForValue.build();
          onChanged();
        } else {
          dtoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder mergeDto(com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO value) {
        if (dtoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              dto_ != null &&
              dto_ != com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance()) {
            dto_ =
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.newBuilder(dto_).mergeFrom(value).buildPartial();
          } else {
            dto_ = value;
          }
          onChanged();
        } else {
          dtoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public Builder clearDto() {
        if (dtoBuilder_ == null) {
          dto_ = null;
          onChanged();
        } else {
          dtoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder getDtoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getDtoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      public com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder getDtoOrBuilder() {
        if (dtoBuilder_ != null) {
          return dtoBuilder_.getMessageOrBuilder();
        } else {
          return dto_ == null ?
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.getDefaultInstance() : dto_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DiscountStoreDTO dto = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder> 
          getDtoFieldBuilder() {
        if (dtoBuilder_ == null) {
          dtoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTO.Builder, com.yorha.proto.PlayerDiscountStore.DiscountStoreDTOOrBuilder>(
                  getDto(),
                  getParentForChildren(),
                  isClean());
          dto_ = null;
        }
        return dtoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_RefreshDiscountStore_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_RefreshDiscountStore_S2C)
    private static final com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C();
    }

    public static com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_RefreshDiscountStore_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_RefreshDiscountStore_S2C>() {
      @java.lang.Override
      public Player_RefreshDiscountStore_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_RefreshDiscountStore_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_RefreshDiscountStore_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_RefreshDiscountStore_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerDiscountStore.Player_RefreshDiscountStore_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DiscountStoreDTO_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DiscountStoreDTO_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n2ss_proto/gen/player/cs/player_discount" +
      "_store.proto\022\017com.yorha.proto\032$ss_proto/" +
      "gen/common/struct_msg.proto\"!\n\037Player_Ge" +
      "tDiscountStoreInfo_C2S\"Q\n\037Player_GetDisc" +
      "ountStoreInfo_S2C\022.\n\003dto\030\001 \001(\0132!.com.yor" +
      "ha.proto.DiscountStoreDTO\"\306\001\n\020DiscountSt" +
      "oreDTO\022\027\n\017nextRefreshTime\030\001 \001(\003\022\024\n\014refre" +
      "shTimes\030\002 \001(\005\022\027\n\017maxRefreshTimes\030\003 \001(\005\022\021" +
      "\n\tfreeTimes\030\004 \001(\005\022\024\n\014maxFreeTimes\030\005 \001(\005\022" +
      "\024\n\014refreshPrice\030\006 \001(\005\022+\n\004list\030\007 \003(\0132\035.co" +
      "m.yorha.proto.StoreItemDTO\"D\n\037Player_Buy" +
      "DiscountStoreItem_C2S\022\016\n\006itemId\030\001 \001(\005\022\021\n" +
      "\tsPassWord\030\002 \001(\t\"M\n\037Player_BuyDiscountSt" +
      "oreItem_S2C\022*\n\003dto\030\001 \001(\0132\035.com.yorha.pro" +
      "to.StoreItemDTO\"!\n\037Player_RefreshDiscoun" +
      "tStore_C2S\"Q\n\037Player_RefreshDiscountStor" +
      "e_S2C\022.\n\003dto\030\001 \001(\0132!.com.yorha.proto.Dis" +
      "countStoreDTOB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetDiscountStoreInfo_S2C_descriptor,
        new java.lang.String[] { "Dto", });
    internal_static_com_yorha_proto_DiscountStoreDTO_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_DiscountStoreDTO_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DiscountStoreDTO_descriptor,
        new java.lang.String[] { "NextRefreshTime", "RefreshTimes", "MaxRefreshTimes", "FreeTimes", "MaxFreeTimes", "RefreshPrice", "List", });
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_C2S_descriptor,
        new java.lang.String[] { "ItemId", "SPassWord", });
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_BuyDiscountStoreItem_S2C_descriptor,
        new java.lang.String[] { "Dto", });
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RefreshDiscountStore_C2S_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_RefreshDiscountStore_S2C_descriptor,
        new java.lang.String[] { "Dto", });
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/dungeon/dungeon.proto

package com.yorha.proto;

public final class Dungeon {
  private Dungeon() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface DungeonSceneEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.DungeonSceneEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.DungeonType getType();

    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return Whether the dungeonId field is set.
     */
    boolean hasDungeonId();
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    int getDungeonId();

    /**
     * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
     * @return Whether the stage field is set.
     */
    boolean hasStage();
    /**
     * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
     * @return The stage.
     */
    com.yorha.proto.CommonEnum.DungeonStage getStage();

    /**
     * <code>optional int64 enterStageTsMs = 4;</code>
     * @return Whether the enterStageTsMs field is set.
     */
    boolean hasEnterStageTsMs();
    /**
     * <code>optional int64 enterStageTsMs = 4;</code>
     * @return The enterStageTsMs.
     */
    long getEnterStageTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.DungeonSceneEntity}
   */
  public static final class DungeonSceneEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.DungeonSceneEntity)
      DungeonSceneEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use DungeonSceneEntity.newBuilder() to construct.
    private DungeonSceneEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private DungeonSceneEntity() {
      type_ = 0;
      stage_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new DungeonSceneEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private DungeonSceneEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonType value = com.yorha.proto.CommonEnum.DungeonType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              dungeonId_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.DungeonStage value = com.yorha.proto.CommonEnum.DungeonStage.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                stage_ = rawValue;
              }
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              enterStageTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.Dungeon.internal_static_com_yorha_proto_DungeonSceneEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.Dungeon.internal_static_com_yorha_proto_DungeonSceneEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.Dungeon.DungeonSceneEntity.class, com.yorha.proto.Dungeon.DungeonSceneEntity.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
    }

    public static final int DUNGEONID_FIELD_NUMBER = 2;
    private int dungeonId_;
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return Whether the dungeonId field is set.
     */
    @java.lang.Override
    public boolean hasDungeonId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 dungeonId = 2;</code>
     * @return The dungeonId.
     */
    @java.lang.Override
    public int getDungeonId() {
      return dungeonId_;
    }

    public static final int STAGE_FIELD_NUMBER = 3;
    private int stage_;
    /**
     * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
     * @return Whether the stage field is set.
     */
    @java.lang.Override public boolean hasStage() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
     * @return The stage.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.DungeonStage getStage() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.DungeonStage result = com.yorha.proto.CommonEnum.DungeonStage.valueOf(stage_);
      return result == null ? com.yorha.proto.CommonEnum.DungeonStage.DS_NONE : result;
    }

    public static final int ENTERSTAGETSMS_FIELD_NUMBER = 4;
    private long enterStageTsMs_;
    /**
     * <code>optional int64 enterStageTsMs = 4;</code>
     * @return Whether the enterStageTsMs field is set.
     */
    @java.lang.Override
    public boolean hasEnterStageTsMs() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 enterStageTsMs = 4;</code>
     * @return The enterStageTsMs.
     */
    @java.lang.Override
    public long getEnterStageTsMs() {
      return enterStageTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, dungeonId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, stage_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, enterStageTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, dungeonId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, stage_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, enterStageTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.Dungeon.DungeonSceneEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.Dungeon.DungeonSceneEntity other = (com.yorha.proto.Dungeon.DungeonSceneEntity) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasDungeonId() != other.hasDungeonId()) return false;
      if (hasDungeonId()) {
        if (getDungeonId()
            != other.getDungeonId()) return false;
      }
      if (hasStage() != other.hasStage()) return false;
      if (hasStage()) {
        if (stage_ != other.stage_) return false;
      }
      if (hasEnterStageTsMs() != other.hasEnterStageTsMs()) return false;
      if (hasEnterStageTsMs()) {
        if (getEnterStageTsMs()
            != other.getEnterStageTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasDungeonId()) {
        hash = (37 * hash) + DUNGEONID_FIELD_NUMBER;
        hash = (53 * hash) + getDungeonId();
      }
      if (hasStage()) {
        hash = (37 * hash) + STAGE_FIELD_NUMBER;
        hash = (53 * hash) + stage_;
      }
      if (hasEnterStageTsMs()) {
        hash = (37 * hash) + ENTERSTAGETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEnterStageTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.Dungeon.DungeonSceneEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.Dungeon.DungeonSceneEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.DungeonSceneEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.DungeonSceneEntity)
        com.yorha.proto.Dungeon.DungeonSceneEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.Dungeon.internal_static_com_yorha_proto_DungeonSceneEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.Dungeon.internal_static_com_yorha_proto_DungeonSceneEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.Dungeon.DungeonSceneEntity.class, com.yorha.proto.Dungeon.DungeonSceneEntity.Builder.class);
      }

      // Construct using com.yorha.proto.Dungeon.DungeonSceneEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        dungeonId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        stage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        enterStageTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.Dungeon.internal_static_com_yorha_proto_DungeonSceneEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.Dungeon.DungeonSceneEntity getDefaultInstanceForType() {
        return com.yorha.proto.Dungeon.DungeonSceneEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.Dungeon.DungeonSceneEntity build() {
        com.yorha.proto.Dungeon.DungeonSceneEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.Dungeon.DungeonSceneEntity buildPartial() {
        com.yorha.proto.Dungeon.DungeonSceneEntity result = new com.yorha.proto.Dungeon.DungeonSceneEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.dungeonId_ = dungeonId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.stage_ = stage_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.enterStageTsMs_ = enterStageTsMs_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.Dungeon.DungeonSceneEntity) {
          return mergeFrom((com.yorha.proto.Dungeon.DungeonSceneEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.Dungeon.DungeonSceneEntity other) {
        if (other == com.yorha.proto.Dungeon.DungeonSceneEntity.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasDungeonId()) {
          setDungeonId(other.getDungeonId());
        }
        if (other.hasStage()) {
          setStage(other.getStage());
        }
        if (other.hasEnterStageTsMs()) {
          setEnterStageTsMs(other.getEnterStageTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.Dungeon.DungeonSceneEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.Dungeon.DungeonSceneEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonType result = com.yorha.proto.CommonEnum.DungeonType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonType.DT_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.DungeonType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int dungeonId_ ;
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return Whether the dungeonId field is set.
       */
      @java.lang.Override
      public boolean hasDungeonId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return The dungeonId.
       */
      @java.lang.Override
      public int getDungeonId() {
        return dungeonId_;
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @param value The dungeonId to set.
       * @return This builder for chaining.
       */
      public Builder setDungeonId(int value) {
        bitField0_ |= 0x00000002;
        dungeonId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 dungeonId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearDungeonId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        dungeonId_ = 0;
        onChanged();
        return this;
      }

      private int stage_ = 0;
      /**
       * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
       * @return Whether the stage field is set.
       */
      @java.lang.Override public boolean hasStage() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
       * @return The stage.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.DungeonStage getStage() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.DungeonStage result = com.yorha.proto.CommonEnum.DungeonStage.valueOf(stage_);
        return result == null ? com.yorha.proto.CommonEnum.DungeonStage.DS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
       * @param value The stage to set.
       * @return This builder for chaining.
       */
      public Builder setStage(com.yorha.proto.CommonEnum.DungeonStage value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        stage_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DungeonStage stage = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStage() {
        bitField0_ = (bitField0_ & ~0x00000004);
        stage_ = 0;
        onChanged();
        return this;
      }

      private long enterStageTsMs_ ;
      /**
       * <code>optional int64 enterStageTsMs = 4;</code>
       * @return Whether the enterStageTsMs field is set.
       */
      @java.lang.Override
      public boolean hasEnterStageTsMs() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 enterStageTsMs = 4;</code>
       * @return The enterStageTsMs.
       */
      @java.lang.Override
      public long getEnterStageTsMs() {
        return enterStageTsMs_;
      }
      /**
       * <code>optional int64 enterStageTsMs = 4;</code>
       * @param value The enterStageTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setEnterStageTsMs(long value) {
        bitField0_ |= 0x00000008;
        enterStageTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 enterStageTsMs = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearEnterStageTsMs() {
        bitField0_ = (bitField0_ & ~0x00000008);
        enterStageTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.DungeonSceneEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.DungeonSceneEntity)
    private static final com.yorha.proto.Dungeon.DungeonSceneEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.Dungeon.DungeonSceneEntity();
    }

    public static com.yorha.proto.Dungeon.DungeonSceneEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<DungeonSceneEntity>
        PARSER = new com.google.protobuf.AbstractParser<DungeonSceneEntity>() {
      @java.lang.Override
      public DungeonSceneEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new DungeonSceneEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<DungeonSceneEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<DungeonSceneEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.Dungeon.DungeonSceneEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_DungeonSceneEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_DungeonSceneEntity_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\"ss_proto/gen/dungeon/dungeon.proto\022\017co" +
      "m.yorha.proto\032%ss_proto/gen/common/commo" +
      "n_enum.proto\"\231\001\n\022DungeonSceneEntity\022*\n\004t" +
      "ype\030\001 \001(\0162\034.com.yorha.proto.DungeonType\022" +
      "\021\n\tdungeonId\030\002 \001(\005\022,\n\005stage\030\003 \001(\0162\035.com." +
      "yorha.proto.DungeonStage\022\026\n\016enterStageTs" +
      "Ms\030\004 \001(\003B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_DungeonSceneEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_DungeonSceneEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_DungeonSceneEntity_descriptor,
        new java.lang.String[] { "Type", "DungeonId", "Stage", "EnterStageTsMs", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

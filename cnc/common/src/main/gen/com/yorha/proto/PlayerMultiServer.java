// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/cs/player_multi_server.proto

package com.yorha.proto;

public final class PlayerMultiServer {
  private PlayerMultiServer() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface Player_GetZonesUnderSeason_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZonesUnderSeason_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return Whether the zoneSeason field is set.
     */
    boolean hasZoneSeason();
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return The zoneSeason.
     */
    com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZonesUnderSeason_C2S}
   */
  public static final class Player_GetZonesUnderSeason_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZonesUnderSeason_C2S)
      Player_GetZonesUnderSeason_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZonesUnderSeason_C2S.newBuilder() to construct.
    private Player_GetZonesUnderSeason_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZonesUnderSeason_C2S() {
      zoneSeason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZonesUnderSeason_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZonesUnderSeason_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ZoneSeason value = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                zoneSeason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.Builder.class);
    }

    private int bitField0_;
    public static final int ZONESEASON_FIELD_NUMBER = 1;
    private int zoneSeason_;
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return Whether the zoneSeason field is set.
     */
    @java.lang.Override public boolean hasZoneSeason() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
     * @return The zoneSeason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(zoneSeason_);
      return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, zoneSeason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, zoneSeason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S other = (com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S) obj;

      if (hasZoneSeason() != other.hasZoneSeason()) return false;
      if (hasZoneSeason()) {
        if (zoneSeason_ != other.zoneSeason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneSeason()) {
        hash = (37 * hash) + ZONESEASON_FIELD_NUMBER;
        hash = (53 * hash) + zoneSeason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZonesUnderSeason_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZonesUnderSeason_C2S)
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneSeason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S build() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S buildPartial() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S result = new com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.zoneSeason_ = zoneSeason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S) {
          return mergeFrom((com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S other) {
        if (other == com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S.getDefaultInstance()) return this;
        if (other.hasZoneSeason()) {
          setZoneSeason(other.getZoneSeason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneSeason_ = 0;
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return Whether the zoneSeason field is set.
       */
      @java.lang.Override public boolean hasZoneSeason() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return The zoneSeason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ZoneSeason getZoneSeason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ZoneSeason result = com.yorha.proto.CommonEnum.ZoneSeason.valueOf(zoneSeason_);
        return result == null ? com.yorha.proto.CommonEnum.ZoneSeason.ZS_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @param value The zoneSeason to set.
       * @return This builder for chaining.
       */
      public Builder setZoneSeason(com.yorha.proto.CommonEnum.ZoneSeason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        zoneSeason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ZoneSeason zoneSeason = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneSeason() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneSeason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZonesUnderSeason_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZonesUnderSeason_C2S)
    private static final com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S();
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZonesUnderSeason_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZonesUnderSeason_C2S>() {
      @java.lang.Override
      public Player_GetZonesUnderSeason_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZonesUnderSeason_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZonesUnderSeason_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZonesUnderSeason_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZonesUnderSeason_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZonesUnderSeason_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    int getZoneIdsCount();
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    boolean containsZoneIds(
        int key);
    /**
     * Use {@link #getZoneIdsMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getZoneIds();
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getZoneIdsMap();
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    int getZoneIdsOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    int getZoneIdsOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZonesUnderSeason_S2C}
   */
  public static final class Player_GetZonesUnderSeason_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZonesUnderSeason_S2C)
      Player_GetZonesUnderSeason_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZonesUnderSeason_S2C.newBuilder() to construct.
    private Player_GetZonesUnderSeason_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZonesUnderSeason_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZonesUnderSeason_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZonesUnderSeason_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneIds_ = com.google.protobuf.MapField.newMapField(
                    ZoneIdsDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              zoneIds__ = input.readMessage(
                  ZoneIdsDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              zoneIds_.getMutableMap().put(
                  zoneIds__.getKey(), zoneIds__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetZoneIds();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.Builder.class);
    }

    public static final int ZONEIDS_FIELD_NUMBER = 1;
    private static final class ZoneIdsDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> zoneIds_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetZoneIds() {
      if (zoneIds_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ZoneIdsDefaultEntryHolder.defaultEntry);
      }
      return zoneIds_;
    }

    public int getZoneIdsCount() {
      return internalGetZoneIds().getMap().size();
    }
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */

    @java.lang.Override
    public boolean containsZoneIds(
        int key) {
      
      return internalGetZoneIds().getMap().containsKey(key);
    }
    /**
     * Use {@link #getZoneIdsMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIds() {
      return getZoneIdsMap();
    }
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIdsMap() {
      return internalGetZoneIds().getMap();
    }
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public int getZoneIdsOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetZoneIds().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * key=zoneId, value=kvk zone(0==不在kvk中)
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
     */
    @java.lang.Override

    public int getZoneIdsOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetZoneIds().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetZoneIds(),
          ZoneIdsDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetZoneIds().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        zoneIds__ = ZoneIdsDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, zoneIds__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C other = (com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C) obj;

      if (!internalGetZoneIds().equals(
          other.internalGetZoneIds())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetZoneIds().getMap().isEmpty()) {
        hash = (37 * hash) + ZONEIDS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetZoneIds().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZonesUnderSeason_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZonesUnderSeason_S2C)
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableZoneIds();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableZoneIds().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C build() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C buildPartial() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C result = new com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C(this);
        int from_bitField0_ = bitField0_;
        result.zoneIds_ = internalGetZoneIds();
        result.zoneIds_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C) {
          return mergeFrom((com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C other) {
        if (other == com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C.getDefaultInstance()) return this;
        internalGetMutableZoneIds().mergeFrom(
            other.internalGetZoneIds());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> zoneIds_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetZoneIds() {
        if (zoneIds_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ZoneIdsDefaultEntryHolder.defaultEntry);
        }
        return zoneIds_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableZoneIds() {
        onChanged();;
        if (zoneIds_ == null) {
          zoneIds_ = com.google.protobuf.MapField.newMapField(
              ZoneIdsDefaultEntryHolder.defaultEntry);
        }
        if (!zoneIds_.isMutable()) {
          zoneIds_ = zoneIds_.copy();
        }
        return zoneIds_;
      }

      public int getZoneIdsCount() {
        return internalGetZoneIds().getMap().size();
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      @java.lang.Override
      public boolean containsZoneIds(
          int key) {
        
        return internalGetZoneIds().getMap().containsKey(key);
      }
      /**
       * Use {@link #getZoneIdsMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIds() {
        return getZoneIdsMap();
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getZoneIdsMap() {
        return internalGetZoneIds().getMap();
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public int getZoneIdsOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetZoneIds().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      @java.lang.Override

      public int getZoneIdsOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetZoneIds().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearZoneIds() {
        internalGetMutableZoneIds().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      public Builder removeZoneIds(
          int key) {
        
        internalGetMutableZoneIds().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableZoneIds() {
        return internalGetMutableZoneIds().getMutableMap();
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */
      public Builder putZoneIds(
          int key,
          int value) {
        
        
        internalGetMutableZoneIds().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * key=zoneId, value=kvk zone(0==不在kvk中)
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; zoneIds = 1;</code>
       */

      public Builder putAllZoneIds(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableZoneIds().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZonesUnderSeason_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZonesUnderSeason_S2C)
    private static final com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C();
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZonesUnderSeason_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZonesUnderSeason_S2C>() {
      @java.lang.Override
      public Player_GetZonesUnderSeason_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZonesUnderSeason_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZonesUnderSeason_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZonesUnderSeason_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMultiServer.Player_GetZonesUnderSeason_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZonesStatus_C2SOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZonesStatus_C2S)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @return A list containing the zoneIds.
     */
    java.util.List<java.lang.Integer> getZoneIdsList();
    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @return The count of zoneIds.
     */
    int getZoneIdsCount();
    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @param index The index of the element to return.
     * @return The zoneIds at the given index.
     */
    int getZoneIds(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZonesStatus_C2S}
   */
  public static final class Player_GetZonesStatus_C2S extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZonesStatus_C2S)
      Player_GetZonesStatus_C2SOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZonesStatus_C2S.newBuilder() to construct.
    private Player_GetZonesStatus_C2S(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZonesStatus_C2S() {
      zoneIds_ = emptyIntList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZonesStatus_C2S();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZonesStatus_C2S(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                zoneIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              zoneIds_.addInt(input.readInt32());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                zoneIds_ = newIntList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                zoneIds_.addInt(input.readInt32());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          zoneIds_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.Builder.class);
    }

    public static final int ZONEIDS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.IntList zoneIds_;
    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @return A list containing the zoneIds.
     */
    @java.lang.Override
    public java.util.List<java.lang.Integer>
        getZoneIdsList() {
      return zoneIds_;
    }
    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @return The count of zoneIds.
     */
    public int getZoneIdsCount() {
      return zoneIds_.size();
    }
    /**
     * <code>repeated int32 zoneIds = 1;</code>
     * @param index The index of the element to return.
     * @return The zoneIds at the given index.
     */
    public int getZoneIds(int index) {
      return zoneIds_.getInt(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < zoneIds_.size(); i++) {
        output.writeInt32(1, zoneIds_.getInt(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < zoneIds_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt32SizeNoTag(zoneIds_.getInt(i));
        }
        size += dataSize;
        size += 1 * getZoneIdsList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S other = (com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S) obj;

      if (!getZoneIdsList()
          .equals(other.getZoneIdsList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getZoneIdsCount() > 0) {
        hash = (37 * hash) + ZONEIDS_FIELD_NUMBER;
        hash = (53 * hash) + getZoneIdsList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZonesStatus_C2S}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZonesStatus_C2S)
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2SOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S build() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S buildPartial() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S result = new com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          zoneIds_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.zoneIds_ = zoneIds_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S) {
          return mergeFrom((com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S other) {
        if (other == com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S.getDefaultInstance()) return this;
        if (!other.zoneIds_.isEmpty()) {
          if (zoneIds_.isEmpty()) {
            zoneIds_ = other.zoneIds_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureZoneIdsIsMutable();
            zoneIds_.addAll(other.zoneIds_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.IntList zoneIds_ = emptyIntList();
      private void ensureZoneIdsIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          zoneIds_ = mutableCopy(zoneIds_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @return A list containing the zoneIds.
       */
      public java.util.List<java.lang.Integer>
          getZoneIdsList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(zoneIds_) : zoneIds_;
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @return The count of zoneIds.
       */
      public int getZoneIdsCount() {
        return zoneIds_.size();
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @param index The index of the element to return.
       * @return The zoneIds at the given index.
       */
      public int getZoneIds(int index) {
        return zoneIds_.getInt(index);
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @param index The index to set the value at.
       * @param value The zoneIds to set.
       * @return This builder for chaining.
       */
      public Builder setZoneIds(
          int index, int value) {
        ensureZoneIdsIsMutable();
        zoneIds_.setInt(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @param value The zoneIds to add.
       * @return This builder for chaining.
       */
      public Builder addZoneIds(int value) {
        ensureZoneIdsIsMutable();
        zoneIds_.addInt(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @param values The zoneIds to add.
       * @return This builder for chaining.
       */
      public Builder addAllZoneIds(
          java.lang.Iterable<? extends java.lang.Integer> values) {
        ensureZoneIdsIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, zoneIds_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int32 zoneIds = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneIds() {
        zoneIds_ = emptyIntList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZonesStatus_C2S)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZonesStatus_C2S)
    private static final com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S();
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZonesStatus_C2S>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZonesStatus_C2S>() {
      @java.lang.Override
      public Player_GetZonesStatus_C2S parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZonesStatus_C2S(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZonesStatus_C2S> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZonesStatus_C2S> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_C2S getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface Player_GetZonesStatus_S2COrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.Player_GetZonesStatus_S2C)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    int getServersStatusCount();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    boolean containsServersStatus(
        int key);
    /**
     * Use {@link #getServersStatusMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    getServersStatus();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    getServersStatusMap();
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
        int key,
        com.yorha.proto.StructMsg.ZoneStatus defaultValue);
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.Player_GetZonesStatus_S2C}
   */
  public static final class Player_GetZonesStatus_S2C extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.Player_GetZonesStatus_S2C)
      Player_GetZonesStatus_S2COrBuilder {
  private static final long serialVersionUID = 0L;
    // Use Player_GetZonesStatus_S2C.newBuilder() to construct.
    private Player_GetZonesStatus_S2C(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private Player_GetZonesStatus_S2C() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new Player_GetZonesStatus_S2C();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private Player_GetZonesStatus_S2C(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                serversStatus_ = com.google.protobuf.MapField.newMapField(
                    ServersStatusDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
              serversStatus__ = input.readMessage(
                  ServersStatusDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              serversStatus_.getMutableMap().put(
                  serversStatus__.getKey(), serversStatus__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetServersStatus();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.Builder.class);
    }

    public static final int SERVERSSTATUS_FIELD_NUMBER = 1;
    private static final class ServersStatusDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>newDefaultInstance(
                  com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.StructMsg.ZoneStatus.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> serversStatus_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
    internalGetServersStatus() {
      if (serversStatus_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            ServersStatusDefaultEntryHolder.defaultEntry);
      }
      return serversStatus_;
    }

    public int getServersStatusCount() {
      return internalGetServersStatus().getMap().size();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */

    @java.lang.Override
    public boolean containsServersStatus(
        int key) {
      
      return internalGetServersStatus().getMap().containsKey(key);
    }
    /**
     * Use {@link #getServersStatusMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatus() {
      return getServersStatusMap();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatusMap() {
      return internalGetServersStatus().getMap();
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
        int key,
        com.yorha.proto.StructMsg.ZoneStatus defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
          internalGetServersStatus().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 各服务器状态
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
          internalGetServersStatus().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetServersStatus(),
          ServersStatusDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> entry
           : internalGetServersStatus().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
        serversStatus__ = ServersStatusDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, serversStatus__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C)) {
        return super.equals(obj);
      }
      com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C other = (com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C) obj;

      if (!internalGetServersStatus().equals(
          other.internalGetServersStatus())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetServersStatus().getMap().isEmpty()) {
        hash = (37 * hash) + SERVERSSTATUS_FIELD_NUMBER;
        hash = (53 * hash) + internalGetServersStatus().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.Player_GetZonesStatus_S2C}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.Player_GetZonesStatus_S2C)
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2COrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetServersStatus();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableServersStatus();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.class, com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.Builder.class);
      }

      // Construct using com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableServersStatus().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.PlayerMultiServer.internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C getDefaultInstanceForType() {
        return com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C build() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C buildPartial() {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C result = new com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C(this);
        int from_bitField0_ = bitField0_;
        result.serversStatus_ = internalGetServersStatus();
        result.serversStatus_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C) {
          return mergeFrom((com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C other) {
        if (other == com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C.getDefaultInstance()) return this;
        internalGetMutableServersStatus().mergeFrom(
            other.internalGetServersStatus());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> serversStatus_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      internalGetServersStatus() {
        if (serversStatus_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              ServersStatusDefaultEntryHolder.defaultEntry);
        }
        return serversStatus_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      internalGetMutableServersStatus() {
        onChanged();;
        if (serversStatus_ == null) {
          serversStatus_ = com.google.protobuf.MapField.newMapField(
              ServersStatusDefaultEntryHolder.defaultEntry);
        }
        if (!serversStatus_.isMutable()) {
          serversStatus_ = serversStatus_.copy();
        }
        return serversStatus_;
      }

      public int getServersStatusCount() {
        return internalGetServersStatus().getMap().size();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      @java.lang.Override
      public boolean containsServersStatus(
          int key) {
        
        return internalGetServersStatus().getMap().containsKey(key);
      }
      /**
       * Use {@link #getServersStatusMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatus() {
        return getServersStatusMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> getServersStatusMap() {
        return internalGetServersStatus().getMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrDefault(
          int key,
          com.yorha.proto.StructMsg.ZoneStatus defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
            internalGetServersStatus().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.StructMsg.ZoneStatus getServersStatusOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> map =
            internalGetServersStatus().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearServersStatus() {
        internalGetMutableServersStatus().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      public Builder removeServersStatus(
          int key) {
        
        internalGetMutableServersStatus().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus>
      getMutableServersStatus() {
        return internalGetMutableServersStatus().getMutableMap();
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */
      public Builder putServersStatus(
          int key,
          com.yorha.proto.StructMsg.ZoneStatus value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableServersStatus().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 各服务器状态
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.ZoneStatus&gt; serversStatus = 1;</code>
       */

      public Builder putAllServersStatus(
          java.util.Map<java.lang.Integer, com.yorha.proto.StructMsg.ZoneStatus> values) {
        internalGetMutableServersStatus().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.Player_GetZonesStatus_S2C)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.Player_GetZonesStatus_S2C)
    private static final com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C();
    }

    public static com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<Player_GetZonesStatus_S2C>
        PARSER = new com.google.protobuf.AbstractParser<Player_GetZonesStatus_S2C>() {
      @java.lang.Override
      public Player_GetZonesStatus_S2C parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new Player_GetZonesStatus_S2C(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<Player_GetZonesStatus_S2C> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<Player_GetZonesStatus_S2C> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.PlayerMultiServer.Player_GetZonesStatus_S2C getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n0ss_proto/gen/player/cs/player_multi_se" +
      "rver.proto\022\017com.yorha.proto\032%ss_proto/ge" +
      "n/common/common_enum.proto\032$ss_proto/gen" +
      "/common/struct_msg.proto\"Q\n\036Player_GetZo" +
      "nesUnderSeason_C2S\022/\n\nzoneSeason\030\001 \001(\0162\033" +
      ".com.yorha.proto.ZoneSeason\"\237\001\n\036Player_G" +
      "etZonesUnderSeason_S2C\022M\n\007zoneIds\030\001 \003(\0132" +
      "<.com.yorha.proto.Player_GetZonesUnderSe" +
      "ason_S2C.ZoneIdsEntry\032.\n\014ZoneIdsEntry\022\013\n" +
      "\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028\001\",\n\031Player_" +
      "GetZonesStatus_C2S\022\017\n\007zoneIds\030\001 \003(\005\"\304\001\n\031" +
      "Player_GetZonesStatus_S2C\022T\n\rserversStat" +
      "us\030\001 \003(\0132=.com.yorha.proto.Player_GetZon" +
      "esStatus_S2C.ServersStatusEntry\032Q\n\022Serve" +
      "rsStatusEntry\022\013\n\003key\030\001 \001(\005\022*\n\005value\030\002 \001(" +
      "\0132\033.com.yorha.proto.ZoneStatus:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesUnderSeason_C2S_descriptor,
        new java.lang.String[] { "ZoneSeason", });
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor,
        new java.lang.String[] { "ZoneIds", });
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_descriptor =
      internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesUnderSeason_S2C_ZoneIdsEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesStatus_C2S_descriptor,
        new java.lang.String[] { "ZoneIds", });
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor,
        new java.lang.String[] { "ServersStatus", });
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_descriptor =
      internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_Player_GetZonesStatus_S2C_ServersStatusEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

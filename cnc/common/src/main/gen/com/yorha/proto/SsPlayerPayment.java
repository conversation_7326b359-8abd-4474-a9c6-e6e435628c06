// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_payment.proto

package com.yorha.proto;

public final class SsPlayerPayment {
  private SsPlayerPayment() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MidasCallbackAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasCallbackAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional int64 tsSec = 4;</code>
     * @return Whether the tsSec field is set.
     */
    boolean hasTsSec();
    /**
     * <code>optional int64 tsSec = 4;</code>
     * @return The tsSec.
     */
    long getTsSec();

    /**
     * <code>optional string payItem = 5;</code>
     * @return Whether the payItem field is set.
     */
    boolean hasPayItem();
    /**
     * <code>optional string payItem = 5;</code>
     * @return The payItem.
     */
    java.lang.String getPayItem();
    /**
     * <code>optional string payItem = 5;</code>
     * @return The bytes for payItem.
     */
    com.google.protobuf.ByteString
        getPayItemBytes();

    /**
     * <code>optional string productId = 6;</code>
     * @return Whether the productId field is set.
     */
    boolean hasProductId();
    /**
     * <code>optional string productId = 6;</code>
     * @return The productId.
     */
    java.lang.String getProductId();
    /**
     * <code>optional string productId = 6;</code>
     * @return The bytes for productId.
     */
    com.google.protobuf.ByteString
        getProductIdBytes();

    /**
     * <code>optional string token = 7;</code>
     * @return Whether the token field is set.
     */
    boolean hasToken();
    /**
     * <code>optional string token = 7;</code>
     * @return The token.
     */
    java.lang.String getToken();
    /**
     * <code>optional string token = 7;</code>
     * @return The bytes for token.
     */
    com.google.protobuf.ByteString
        getTokenBytes();

    /**
     * <code>optional string billno = 8;</code>
     * @return Whether the billno field is set.
     */
    boolean hasBillno();
    /**
     * <code>optional string billno = 8;</code>
     * @return The billno.
     */
    java.lang.String getBillno();
    /**
     * <code>optional string billno = 8;</code>
     * @return The bytes for billno.
     */
    com.google.protobuf.ByteString
        getBillnoBytes();

    /**
     * <code>optional string amt = 9;</code>
     * @return Whether the amt field is set.
     */
    boolean hasAmt();
    /**
     * <code>optional string amt = 9;</code>
     * @return The amt.
     */
    java.lang.String getAmt();
    /**
     * <code>optional string amt = 9;</code>
     * @return The bytes for amt.
     */
    com.google.protobuf.ByteString
        getAmtBytes();

    /**
     * <code>optional string appMeta = 10;</code>
     * @return Whether the appMeta field is set.
     */
    boolean hasAppMeta();
    /**
     * <code>optional string appMeta = 10;</code>
     * @return The appMeta.
     */
    java.lang.String getAppMeta();
    /**
     * <code>optional string appMeta = 10;</code>
     * @return The bytes for appMeta.
     */
    com.google.protobuf.ByteString
        getAppMetaBytes();

    /**
     * <code>optional string channelId = 11;</code>
     * @return Whether the channelId field is set.
     */
    boolean hasChannelId();
    /**
     * <code>optional string channelId = 11;</code>
     * @return The channelId.
     */
    java.lang.String getChannelId();
    /**
     * <code>optional string channelId = 11;</code>
     * @return The bytes for channelId.
     */
    com.google.protobuf.ByteString
        getChannelIdBytes();

    /**
     * <code>optional string clientVer = 12;</code>
     * @return Whether the clientVer field is set.
     */
    boolean hasClientVer();
    /**
     * <code>optional string clientVer = 12;</code>
     * @return The clientVer.
     */
    java.lang.String getClientVer();
    /**
     * <code>optional string clientVer = 12;</code>
     * @return The bytes for clientVer.
     */
    com.google.protobuf.ByteString
        getClientVerBytes();

    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return Whether the payamtCoins field is set.
     */
    boolean hasPayamtCoins();
    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return The payamtCoins.
     */
    java.lang.String getPayamtCoins();
    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return The bytes for payamtCoins.
     */
    com.google.protobuf.ByteString
        getPayamtCoinsBytes();

    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return Whether the pubacctPayamtCoins field is set.
     */
    boolean hasPubacctPayamtCoins();
    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return The pubacctPayamtCoins.
     */
    java.lang.String getPubacctPayamtCoins();
    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return The bytes for pubacctPayamtCoins.
     */
    com.google.protobuf.ByteString
        getPubacctPayamtCoinsBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasCallbackAsk}
   */
  public static final class MidasCallbackAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasCallbackAsk)
      MidasCallbackAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasCallbackAsk.newBuilder() to construct.
    private MidasCallbackAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasCallbackAsk() {
      openId_ = "";
      payItem_ = "";
      productId_ = "";
      token_ = "";
      billno_ = "";
      amt_ = "";
      appMeta_ = "";
      channelId_ = "";
      clientVer_ = "";
      payamtCoins_ = "";
      pubacctPayamtCoins_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasCallbackAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasCallbackAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              tsSec_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              payItem_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              productId_ = bs;
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              token_ = bs;
              break;
            }
            case 66: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000080;
              billno_ = bs;
              break;
            }
            case 74: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000100;
              amt_ = bs;
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              appMeta_ = bs;
              break;
            }
            case 90: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000400;
              channelId_ = bs;
              break;
            }
            case 98: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000800;
              clientVer_ = bs;
              break;
            }
            case 106: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00001000;
              payamtCoins_ = bs;
              break;
            }
            case 114: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00002000;
              pubacctPayamtCoins_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.class, com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ZONEID_FIELD_NUMBER = 2;
    private int zoneId_;
    /**
     * <code>optional int32 zoneId = 2;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 zoneId = 2;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int TSSEC_FIELD_NUMBER = 4;
    private long tsSec_;
    /**
     * <code>optional int64 tsSec = 4;</code>
     * @return Whether the tsSec field is set.
     */
    @java.lang.Override
    public boolean hasTsSec() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional int64 tsSec = 4;</code>
     * @return The tsSec.
     */
    @java.lang.Override
    public long getTsSec() {
      return tsSec_;
    }

    public static final int PAYITEM_FIELD_NUMBER = 5;
    private volatile java.lang.Object payItem_;
    /**
     * <code>optional string payItem = 5;</code>
     * @return Whether the payItem field is set.
     */
    @java.lang.Override
    public boolean hasPayItem() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <code>optional string payItem = 5;</code>
     * @return The payItem.
     */
    @java.lang.Override
    public java.lang.String getPayItem() {
      java.lang.Object ref = payItem_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          payItem_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string payItem = 5;</code>
     * @return The bytes for payItem.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPayItemBytes() {
      java.lang.Object ref = payItem_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payItem_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PRODUCTID_FIELD_NUMBER = 6;
    private volatile java.lang.Object productId_;
    /**
     * <code>optional string productId = 6;</code>
     * @return Whether the productId field is set.
     */
    @java.lang.Override
    public boolean hasProductId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <code>optional string productId = 6;</code>
     * @return The productId.
     */
    @java.lang.Override
    public java.lang.String getProductId() {
      java.lang.Object ref = productId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          productId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string productId = 6;</code>
     * @return The bytes for productId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getProductIdBytes() {
      java.lang.Object ref = productId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        productId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int TOKEN_FIELD_NUMBER = 7;
    private volatile java.lang.Object token_;
    /**
     * <code>optional string token = 7;</code>
     * @return Whether the token field is set.
     */
    @java.lang.Override
    public boolean hasToken() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <code>optional string token = 7;</code>
     * @return The token.
     */
    @java.lang.Override
    public java.lang.String getToken() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          token_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string token = 7;</code>
     * @return The bytes for token.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getTokenBytes() {
      java.lang.Object ref = token_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        token_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int BILLNO_FIELD_NUMBER = 8;
    private volatile java.lang.Object billno_;
    /**
     * <code>optional string billno = 8;</code>
     * @return Whether the billno field is set.
     */
    @java.lang.Override
    public boolean hasBillno() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <code>optional string billno = 8;</code>
     * @return The billno.
     */
    @java.lang.Override
    public java.lang.String getBillno() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          billno_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string billno = 8;</code>
     * @return The bytes for billno.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getBillnoBytes() {
      java.lang.Object ref = billno_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        billno_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int AMT_FIELD_NUMBER = 9;
    private volatile java.lang.Object amt_;
    /**
     * <code>optional string amt = 9;</code>
     * @return Whether the amt field is set.
     */
    @java.lang.Override
    public boolean hasAmt() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <code>optional string amt = 9;</code>
     * @return The amt.
     */
    @java.lang.Override
    public java.lang.String getAmt() {
      java.lang.Object ref = amt_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          amt_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string amt = 9;</code>
     * @return The bytes for amt.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAmtBytes() {
      java.lang.Object ref = amt_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        amt_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int APPMETA_FIELD_NUMBER = 10;
    private volatile java.lang.Object appMeta_;
    /**
     * <code>optional string appMeta = 10;</code>
     * @return Whether the appMeta field is set.
     */
    @java.lang.Override
    public boolean hasAppMeta() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <code>optional string appMeta = 10;</code>
     * @return The appMeta.
     */
    @java.lang.Override
    public java.lang.String getAppMeta() {
      java.lang.Object ref = appMeta_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          appMeta_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string appMeta = 10;</code>
     * @return The bytes for appMeta.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getAppMetaBytes() {
      java.lang.Object ref = appMeta_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        appMeta_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHANNELID_FIELD_NUMBER = 11;
    private volatile java.lang.Object channelId_;
    /**
     * <code>optional string channelId = 11;</code>
     * @return Whether the channelId field is set.
     */
    @java.lang.Override
    public boolean hasChannelId() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <code>optional string channelId = 11;</code>
     * @return The channelId.
     */
    @java.lang.Override
    public java.lang.String getChannelId() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          channelId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string channelId = 11;</code>
     * @return The bytes for channelId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChannelIdBytes() {
      java.lang.Object ref = channelId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        channelId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLIENTVER_FIELD_NUMBER = 12;
    private volatile java.lang.Object clientVer_;
    /**
     * <code>optional string clientVer = 12;</code>
     * @return Whether the clientVer field is set.
     */
    @java.lang.Override
    public boolean hasClientVer() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <code>optional string clientVer = 12;</code>
     * @return The clientVer.
     */
    @java.lang.Override
    public java.lang.String getClientVer() {
      java.lang.Object ref = clientVer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clientVer_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clientVer = 12;</code>
     * @return The bytes for clientVer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClientVerBytes() {
      java.lang.Object ref = clientVer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clientVer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PAYAMTCOINS_FIELD_NUMBER = 13;
    private volatile java.lang.Object payamtCoins_;
    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return Whether the payamtCoins field is set.
     */
    @java.lang.Override
    public boolean hasPayamtCoins() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return The payamtCoins.
     */
    @java.lang.Override
    public java.lang.String getPayamtCoins() {
      java.lang.Object ref = payamtCoins_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          payamtCoins_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string payamtCoins = 13;</code>
     * @return The bytes for payamtCoins.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPayamtCoinsBytes() {
      java.lang.Object ref = payamtCoins_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        payamtCoins_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PUBACCTPAYAMTCOINS_FIELD_NUMBER = 14;
    private volatile java.lang.Object pubacctPayamtCoins_;
    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return Whether the pubacctPayamtCoins field is set.
     */
    @java.lang.Override
    public boolean hasPubacctPayamtCoins() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return The pubacctPayamtCoins.
     */
    @java.lang.Override
    public java.lang.String getPubacctPayamtCoins() {
      java.lang.Object ref = pubacctPayamtCoins_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          pubacctPayamtCoins_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string pubacctPayamtCoins = 14;</code>
     * @return The bytes for pubacctPayamtCoins.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getPubacctPayamtCoinsBytes() {
      java.lang.Object ref = pubacctPayamtCoins_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        pubacctPayamtCoins_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, tsSec_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, payItem_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, productId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, token_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 8, billno_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 9, amt_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, appMeta_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 11, channelId_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 12, clientVer_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 13, payamtCoins_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 14, pubacctPayamtCoins_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, tsSec_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, payItem_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, productId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, token_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(8, billno_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(9, amt_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, appMeta_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(11, channelId_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(12, clientVer_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(13, payamtCoins_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(14, pubacctPayamtCoins_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerPayment.MidasCallbackAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerPayment.MidasCallbackAsk other = (com.yorha.proto.SsPlayerPayment.MidasCallbackAsk) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasTsSec() != other.hasTsSec()) return false;
      if (hasTsSec()) {
        if (getTsSec()
            != other.getTsSec()) return false;
      }
      if (hasPayItem() != other.hasPayItem()) return false;
      if (hasPayItem()) {
        if (!getPayItem()
            .equals(other.getPayItem())) return false;
      }
      if (hasProductId() != other.hasProductId()) return false;
      if (hasProductId()) {
        if (!getProductId()
            .equals(other.getProductId())) return false;
      }
      if (hasToken() != other.hasToken()) return false;
      if (hasToken()) {
        if (!getToken()
            .equals(other.getToken())) return false;
      }
      if (hasBillno() != other.hasBillno()) return false;
      if (hasBillno()) {
        if (!getBillno()
            .equals(other.getBillno())) return false;
      }
      if (hasAmt() != other.hasAmt()) return false;
      if (hasAmt()) {
        if (!getAmt()
            .equals(other.getAmt())) return false;
      }
      if (hasAppMeta() != other.hasAppMeta()) return false;
      if (hasAppMeta()) {
        if (!getAppMeta()
            .equals(other.getAppMeta())) return false;
      }
      if (hasChannelId() != other.hasChannelId()) return false;
      if (hasChannelId()) {
        if (!getChannelId()
            .equals(other.getChannelId())) return false;
      }
      if (hasClientVer() != other.hasClientVer()) return false;
      if (hasClientVer()) {
        if (!getClientVer()
            .equals(other.getClientVer())) return false;
      }
      if (hasPayamtCoins() != other.hasPayamtCoins()) return false;
      if (hasPayamtCoins()) {
        if (!getPayamtCoins()
            .equals(other.getPayamtCoins())) return false;
      }
      if (hasPubacctPayamtCoins() != other.hasPubacctPayamtCoins()) return false;
      if (hasPubacctPayamtCoins()) {
        if (!getPubacctPayamtCoins()
            .equals(other.getPubacctPayamtCoins())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasTsSec()) {
        hash = (37 * hash) + TSSEC_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTsSec());
      }
      if (hasPayItem()) {
        hash = (37 * hash) + PAYITEM_FIELD_NUMBER;
        hash = (53 * hash) + getPayItem().hashCode();
      }
      if (hasProductId()) {
        hash = (37 * hash) + PRODUCTID_FIELD_NUMBER;
        hash = (53 * hash) + getProductId().hashCode();
      }
      if (hasToken()) {
        hash = (37 * hash) + TOKEN_FIELD_NUMBER;
        hash = (53 * hash) + getToken().hashCode();
      }
      if (hasBillno()) {
        hash = (37 * hash) + BILLNO_FIELD_NUMBER;
        hash = (53 * hash) + getBillno().hashCode();
      }
      if (hasAmt()) {
        hash = (37 * hash) + AMT_FIELD_NUMBER;
        hash = (53 * hash) + getAmt().hashCode();
      }
      if (hasAppMeta()) {
        hash = (37 * hash) + APPMETA_FIELD_NUMBER;
        hash = (53 * hash) + getAppMeta().hashCode();
      }
      if (hasChannelId()) {
        hash = (37 * hash) + CHANNELID_FIELD_NUMBER;
        hash = (53 * hash) + getChannelId().hashCode();
      }
      if (hasClientVer()) {
        hash = (37 * hash) + CLIENTVER_FIELD_NUMBER;
        hash = (53 * hash) + getClientVer().hashCode();
      }
      if (hasPayamtCoins()) {
        hash = (37 * hash) + PAYAMTCOINS_FIELD_NUMBER;
        hash = (53 * hash) + getPayamtCoins().hashCode();
      }
      if (hasPubacctPayamtCoins()) {
        hash = (37 * hash) + PUBACCTPAYAMTCOINS_FIELD_NUMBER;
        hash = (53 * hash) + getPubacctPayamtCoins().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerPayment.MidasCallbackAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasCallbackAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasCallbackAsk)
        com.yorha.proto.SsPlayerPayment.MidasCallbackAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.class, com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        tsSec_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        payItem_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        productId_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        token_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        billno_ = "";
        bitField0_ = (bitField0_ & ~0x00000080);
        amt_ = "";
        bitField0_ = (bitField0_ & ~0x00000100);
        appMeta_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        channelId_ = "";
        bitField0_ = (bitField0_ & ~0x00000400);
        clientVer_ = "";
        bitField0_ = (bitField0_ & ~0x00000800);
        payamtCoins_ = "";
        bitField0_ = (bitField0_ & ~0x00001000);
        pubacctPayamtCoins_ = "";
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAsk build() {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAsk buildPartial() {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAsk result = new com.yorha.proto.SsPlayerPayment.MidasCallbackAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.tsSec_ = tsSec_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.payItem_ = payItem_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.productId_ = productId_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.token_ = token_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          to_bitField0_ |= 0x00000080;
        }
        result.billno_ = billno_;
        if (((from_bitField0_ & 0x00000100) != 0)) {
          to_bitField0_ |= 0x00000100;
        }
        result.amt_ = amt_;
        if (((from_bitField0_ & 0x00000200) != 0)) {
          to_bitField0_ |= 0x00000200;
        }
        result.appMeta_ = appMeta_;
        if (((from_bitField0_ & 0x00000400) != 0)) {
          to_bitField0_ |= 0x00000400;
        }
        result.channelId_ = channelId_;
        if (((from_bitField0_ & 0x00000800) != 0)) {
          to_bitField0_ |= 0x00000800;
        }
        result.clientVer_ = clientVer_;
        if (((from_bitField0_ & 0x00001000) != 0)) {
          to_bitField0_ |= 0x00001000;
        }
        result.payamtCoins_ = payamtCoins_;
        if (((from_bitField0_ & 0x00002000) != 0)) {
          to_bitField0_ |= 0x00002000;
        }
        result.pubacctPayamtCoins_ = pubacctPayamtCoins_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerPayment.MidasCallbackAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerPayment.MidasCallbackAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerPayment.MidasCallbackAsk other) {
        if (other == com.yorha.proto.SsPlayerPayment.MidasCallbackAsk.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasTsSec()) {
          setTsSec(other.getTsSec());
        }
        if (other.hasPayItem()) {
          bitField0_ |= 0x00000010;
          payItem_ = other.payItem_;
          onChanged();
        }
        if (other.hasProductId()) {
          bitField0_ |= 0x00000020;
          productId_ = other.productId_;
          onChanged();
        }
        if (other.hasToken()) {
          bitField0_ |= 0x00000040;
          token_ = other.token_;
          onChanged();
        }
        if (other.hasBillno()) {
          bitField0_ |= 0x00000080;
          billno_ = other.billno_;
          onChanged();
        }
        if (other.hasAmt()) {
          bitField0_ |= 0x00000100;
          amt_ = other.amt_;
          onChanged();
        }
        if (other.hasAppMeta()) {
          bitField0_ |= 0x00000200;
          appMeta_ = other.appMeta_;
          onChanged();
        }
        if (other.hasChannelId()) {
          bitField0_ |= 0x00000400;
          channelId_ = other.channelId_;
          onChanged();
        }
        if (other.hasClientVer()) {
          bitField0_ |= 0x00000800;
          clientVer_ = other.clientVer_;
          onChanged();
        }
        if (other.hasPayamtCoins()) {
          bitField0_ |= 0x00001000;
          payamtCoins_ = other.payamtCoins_;
          onChanged();
        }
        if (other.hasPubacctPayamtCoins()) {
          bitField0_ |= 0x00002000;
          pubacctPayamtCoins_ = other.pubacctPayamtCoins_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerPayment.MidasCallbackAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <code>optional int32 zoneId = 2;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 zoneId = 2;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <code>optional int32 zoneId = 2;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000002;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private long tsSec_ ;
      /**
       * <code>optional int64 tsSec = 4;</code>
       * @return Whether the tsSec field is set.
       */
      @java.lang.Override
      public boolean hasTsSec() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional int64 tsSec = 4;</code>
       * @return The tsSec.
       */
      @java.lang.Override
      public long getTsSec() {
        return tsSec_;
      }
      /**
       * <code>optional int64 tsSec = 4;</code>
       * @param value The tsSec to set.
       * @return This builder for chaining.
       */
      public Builder setTsSec(long value) {
        bitField0_ |= 0x00000008;
        tsSec_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 tsSec = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearTsSec() {
        bitField0_ = (bitField0_ & ~0x00000008);
        tsSec_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object payItem_ = "";
      /**
       * <code>optional string payItem = 5;</code>
       * @return Whether the payItem field is set.
       */
      public boolean hasPayItem() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <code>optional string payItem = 5;</code>
       * @return The payItem.
       */
      public java.lang.String getPayItem() {
        java.lang.Object ref = payItem_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            payItem_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string payItem = 5;</code>
       * @return The bytes for payItem.
       */
      public com.google.protobuf.ByteString
          getPayItemBytes() {
        java.lang.Object ref = payItem_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payItem_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string payItem = 5;</code>
       * @param value The payItem to set.
       * @return This builder for chaining.
       */
      public Builder setPayItem(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        payItem_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string payItem = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayItem() {
        bitField0_ = (bitField0_ & ~0x00000010);
        payItem_ = getDefaultInstance().getPayItem();
        onChanged();
        return this;
      }
      /**
       * <code>optional string payItem = 5;</code>
       * @param value The bytes for payItem to set.
       * @return This builder for chaining.
       */
      public Builder setPayItemBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        payItem_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object productId_ = "";
      /**
       * <code>optional string productId = 6;</code>
       * @return Whether the productId field is set.
       */
      public boolean hasProductId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <code>optional string productId = 6;</code>
       * @return The productId.
       */
      public java.lang.String getProductId() {
        java.lang.Object ref = productId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            productId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string productId = 6;</code>
       * @return The bytes for productId.
       */
      public com.google.protobuf.ByteString
          getProductIdBytes() {
        java.lang.Object ref = productId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          productId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string productId = 6;</code>
       * @param value The productId to set.
       * @return This builder for chaining.
       */
      public Builder setProductId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        productId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string productId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearProductId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        productId_ = getDefaultInstance().getProductId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string productId = 6;</code>
       * @param value The bytes for productId to set.
       * @return This builder for chaining.
       */
      public Builder setProductIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        productId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object token_ = "";
      /**
       * <code>optional string token = 7;</code>
       * @return Whether the token field is set.
       */
      public boolean hasToken() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <code>optional string token = 7;</code>
       * @return The token.
       */
      public java.lang.String getToken() {
        java.lang.Object ref = token_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            token_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string token = 7;</code>
       * @return The bytes for token.
       */
      public com.google.protobuf.ByteString
          getTokenBytes() {
        java.lang.Object ref = token_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          token_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string token = 7;</code>
       * @param value The token to set.
       * @return This builder for chaining.
       */
      public Builder setToken(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        token_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearToken() {
        bitField0_ = (bitField0_ & ~0x00000040);
        token_ = getDefaultInstance().getToken();
        onChanged();
        return this;
      }
      /**
       * <code>optional string token = 7;</code>
       * @param value The bytes for token to set.
       * @return This builder for chaining.
       */
      public Builder setTokenBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        token_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object billno_ = "";
      /**
       * <code>optional string billno = 8;</code>
       * @return Whether the billno field is set.
       */
      public boolean hasBillno() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <code>optional string billno = 8;</code>
       * @return The billno.
       */
      public java.lang.String getBillno() {
        java.lang.Object ref = billno_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            billno_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string billno = 8;</code>
       * @return The bytes for billno.
       */
      public com.google.protobuf.ByteString
          getBillnoBytes() {
        java.lang.Object ref = billno_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          billno_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string billno = 8;</code>
       * @param value The billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillno(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        billno_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearBillno() {
        bitField0_ = (bitField0_ & ~0x00000080);
        billno_ = getDefaultInstance().getBillno();
        onChanged();
        return this;
      }
      /**
       * <code>optional string billno = 8;</code>
       * @param value The bytes for billno to set.
       * @return This builder for chaining.
       */
      public Builder setBillnoBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000080;
        billno_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object amt_ = "";
      /**
       * <code>optional string amt = 9;</code>
       * @return Whether the amt field is set.
       */
      public boolean hasAmt() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <code>optional string amt = 9;</code>
       * @return The amt.
       */
      public java.lang.String getAmt() {
        java.lang.Object ref = amt_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            amt_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string amt = 9;</code>
       * @return The bytes for amt.
       */
      public com.google.protobuf.ByteString
          getAmtBytes() {
        java.lang.Object ref = amt_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          amt_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string amt = 9;</code>
       * @param value The amt to set.
       * @return This builder for chaining.
       */
      public Builder setAmt(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        amt_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string amt = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearAmt() {
        bitField0_ = (bitField0_ & ~0x00000100);
        amt_ = getDefaultInstance().getAmt();
        onChanged();
        return this;
      }
      /**
       * <code>optional string amt = 9;</code>
       * @param value The bytes for amt to set.
       * @return This builder for chaining.
       */
      public Builder setAmtBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000100;
        amt_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object appMeta_ = "";
      /**
       * <code>optional string appMeta = 10;</code>
       * @return Whether the appMeta field is set.
       */
      public boolean hasAppMeta() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <code>optional string appMeta = 10;</code>
       * @return The appMeta.
       */
      public java.lang.String getAppMeta() {
        java.lang.Object ref = appMeta_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            appMeta_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string appMeta = 10;</code>
       * @return The bytes for appMeta.
       */
      public com.google.protobuf.ByteString
          getAppMetaBytes() {
        java.lang.Object ref = appMeta_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          appMeta_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string appMeta = 10;</code>
       * @param value The appMeta to set.
       * @return This builder for chaining.
       */
      public Builder setAppMeta(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        appMeta_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string appMeta = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearAppMeta() {
        bitField0_ = (bitField0_ & ~0x00000200);
        appMeta_ = getDefaultInstance().getAppMeta();
        onChanged();
        return this;
      }
      /**
       * <code>optional string appMeta = 10;</code>
       * @param value The bytes for appMeta to set.
       * @return This builder for chaining.
       */
      public Builder setAppMetaBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        appMeta_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object channelId_ = "";
      /**
       * <code>optional string channelId = 11;</code>
       * @return Whether the channelId field is set.
       */
      public boolean hasChannelId() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <code>optional string channelId = 11;</code>
       * @return The channelId.
       */
      public java.lang.String getChannelId() {
        java.lang.Object ref = channelId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            channelId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string channelId = 11;</code>
       * @return The bytes for channelId.
       */
      public com.google.protobuf.ByteString
          getChannelIdBytes() {
        java.lang.Object ref = channelId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          channelId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string channelId = 11;</code>
       * @param value The channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        channelId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearChannelId() {
        bitField0_ = (bitField0_ & ~0x00000400);
        channelId_ = getDefaultInstance().getChannelId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string channelId = 11;</code>
       * @param value The bytes for channelId to set.
       * @return This builder for chaining.
       */
      public Builder setChannelIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000400;
        channelId_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object clientVer_ = "";
      /**
       * <code>optional string clientVer = 12;</code>
       * @return Whether the clientVer field is set.
       */
      public boolean hasClientVer() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <code>optional string clientVer = 12;</code>
       * @return The clientVer.
       */
      public java.lang.String getClientVer() {
        java.lang.Object ref = clientVer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clientVer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clientVer = 12;</code>
       * @return The bytes for clientVer.
       */
      public com.google.protobuf.ByteString
          getClientVerBytes() {
        java.lang.Object ref = clientVer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clientVer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clientVer = 12;</code>
       * @param value The clientVer to set.
       * @return This builder for chaining.
       */
      public Builder setClientVer(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        clientVer_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clientVer = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearClientVer() {
        bitField0_ = (bitField0_ & ~0x00000800);
        clientVer_ = getDefaultInstance().getClientVer();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clientVer = 12;</code>
       * @param value The bytes for clientVer to set.
       * @return This builder for chaining.
       */
      public Builder setClientVerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000800;
        clientVer_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object payamtCoins_ = "";
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @return Whether the payamtCoins field is set.
       */
      public boolean hasPayamtCoins() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @return The payamtCoins.
       */
      public java.lang.String getPayamtCoins() {
        java.lang.Object ref = payamtCoins_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            payamtCoins_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @return The bytes for payamtCoins.
       */
      public com.google.protobuf.ByteString
          getPayamtCoinsBytes() {
        java.lang.Object ref = payamtCoins_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          payamtCoins_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @param value The payamtCoins to set.
       * @return This builder for chaining.
       */
      public Builder setPayamtCoins(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        payamtCoins_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearPayamtCoins() {
        bitField0_ = (bitField0_ & ~0x00001000);
        payamtCoins_ = getDefaultInstance().getPayamtCoins();
        onChanged();
        return this;
      }
      /**
       * <code>optional string payamtCoins = 13;</code>
       * @param value The bytes for payamtCoins to set.
       * @return This builder for chaining.
       */
      public Builder setPayamtCoinsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00001000;
        payamtCoins_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object pubacctPayamtCoins_ = "";
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @return Whether the pubacctPayamtCoins field is set.
       */
      public boolean hasPubacctPayamtCoins() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @return The pubacctPayamtCoins.
       */
      public java.lang.String getPubacctPayamtCoins() {
        java.lang.Object ref = pubacctPayamtCoins_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            pubacctPayamtCoins_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @return The bytes for pubacctPayamtCoins.
       */
      public com.google.protobuf.ByteString
          getPubacctPayamtCoinsBytes() {
        java.lang.Object ref = pubacctPayamtCoins_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          pubacctPayamtCoins_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @param value The pubacctPayamtCoins to set.
       * @return This builder for chaining.
       */
      public Builder setPubacctPayamtCoins(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        pubacctPayamtCoins_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearPubacctPayamtCoins() {
        bitField0_ = (bitField0_ & ~0x00002000);
        pubacctPayamtCoins_ = getDefaultInstance().getPubacctPayamtCoins();
        onChanged();
        return this;
      }
      /**
       * <code>optional string pubacctPayamtCoins = 14;</code>
       * @param value The bytes for pubacctPayamtCoins to set.
       * @return This builder for chaining.
       */
      public Builder setPubacctPayamtCoinsBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00002000;
        pubacctPayamtCoins_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasCallbackAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasCallbackAsk)
    private static final com.yorha.proto.SsPlayerPayment.MidasCallbackAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerPayment.MidasCallbackAsk();
    }

    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasCallbackAsk>
        PARSER = new com.google.protobuf.AbstractParser<MidasCallbackAsk>() {
      @java.lang.Override
      public MidasCallbackAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasCallbackAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasCallbackAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasCallbackAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerPayment.MidasCallbackAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MidasCallbackAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MidasCallbackAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool ok = 1;</code>
     * @return Whether the ok field is set.
     */
    boolean hasOk();
    /**
     * <code>optional bool ok = 1;</code>
     * @return The ok.
     */
    boolean getOk();

    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return Whether the extraMsg field is set.
     */
    boolean hasExtraMsg();
    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return The extraMsg.
     */
    java.lang.String getExtraMsg();
    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return The bytes for extraMsg.
     */
    com.google.protobuf.ByteString
        getExtraMsgBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MidasCallbackAns}
   */
  public static final class MidasCallbackAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MidasCallbackAns)
      MidasCallbackAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MidasCallbackAns.newBuilder() to construct.
    private MidasCallbackAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MidasCallbackAns() {
      extraMsg_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MidasCallbackAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MidasCallbackAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              ok_ = input.readBool();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              extraMsg_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerPayment.MidasCallbackAns.class, com.yorha.proto.SsPlayerPayment.MidasCallbackAns.Builder.class);
    }

    private int bitField0_;
    public static final int OK_FIELD_NUMBER = 1;
    private boolean ok_;
    /**
     * <code>optional bool ok = 1;</code>
     * @return Whether the ok field is set.
     */
    @java.lang.Override
    public boolean hasOk() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool ok = 1;</code>
     * @return The ok.
     */
    @java.lang.Override
    public boolean getOk() {
      return ok_;
    }

    public static final int EXTRAMSG_FIELD_NUMBER = 2;
    private volatile java.lang.Object extraMsg_;
    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return Whether the extraMsg field is set.
     */
    @java.lang.Override
    public boolean hasExtraMsg() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return The extraMsg.
     */
    @java.lang.Override
    public java.lang.String getExtraMsg() {
      java.lang.Object ref = extraMsg_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          extraMsg_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 返给米大师的msg，为空则使用默认值
     * </pre>
     *
     * <code>optional string extraMsg = 2;</code>
     * @return The bytes for extraMsg.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getExtraMsgBytes() {
      java.lang.Object ref = extraMsg_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        extraMsg_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, ok_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, extraMsg_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, ok_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, extraMsg_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerPayment.MidasCallbackAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerPayment.MidasCallbackAns other = (com.yorha.proto.SsPlayerPayment.MidasCallbackAns) obj;

      if (hasOk() != other.hasOk()) return false;
      if (hasOk()) {
        if (getOk()
            != other.getOk()) return false;
      }
      if (hasExtraMsg() != other.hasExtraMsg()) return false;
      if (hasExtraMsg()) {
        if (!getExtraMsg()
            .equals(other.getExtraMsg())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOk()) {
        hash = (37 * hash) + OK_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getOk());
      }
      if (hasExtraMsg()) {
        hash = (37 * hash) + EXTRAMSG_FIELD_NUMBER;
        hash = (53 * hash) + getExtraMsg().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerPayment.MidasCallbackAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MidasCallbackAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MidasCallbackAns)
        com.yorha.proto.SsPlayerPayment.MidasCallbackAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerPayment.MidasCallbackAns.class, com.yorha.proto.SsPlayerPayment.MidasCallbackAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerPayment.MidasCallbackAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        ok_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        extraMsg_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerPayment.internal_static_com_yorha_proto_MidasCallbackAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerPayment.MidasCallbackAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAns build() {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerPayment.MidasCallbackAns buildPartial() {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAns result = new com.yorha.proto.SsPlayerPayment.MidasCallbackAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.ok_ = ok_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.extraMsg_ = extraMsg_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerPayment.MidasCallbackAns) {
          return mergeFrom((com.yorha.proto.SsPlayerPayment.MidasCallbackAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerPayment.MidasCallbackAns other) {
        if (other == com.yorha.proto.SsPlayerPayment.MidasCallbackAns.getDefaultInstance()) return this;
        if (other.hasOk()) {
          setOk(other.getOk());
        }
        if (other.hasExtraMsg()) {
          bitField0_ |= 0x00000002;
          extraMsg_ = other.extraMsg_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerPayment.MidasCallbackAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerPayment.MidasCallbackAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean ok_ ;
      /**
       * <code>optional bool ok = 1;</code>
       * @return Whether the ok field is set.
       */
      @java.lang.Override
      public boolean hasOk() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool ok = 1;</code>
       * @return The ok.
       */
      @java.lang.Override
      public boolean getOk() {
        return ok_;
      }
      /**
       * <code>optional bool ok = 1;</code>
       * @param value The ok to set.
       * @return This builder for chaining.
       */
      public Builder setOk(boolean value) {
        bitField0_ |= 0x00000001;
        ok_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool ok = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOk() {
        bitField0_ = (bitField0_ & ~0x00000001);
        ok_ = false;
        onChanged();
        return this;
      }

      private java.lang.Object extraMsg_ = "";
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @return Whether the extraMsg field is set.
       */
      public boolean hasExtraMsg() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @return The extraMsg.
       */
      public java.lang.String getExtraMsg() {
        java.lang.Object ref = extraMsg_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            extraMsg_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @return The bytes for extraMsg.
       */
      public com.google.protobuf.ByteString
          getExtraMsgBytes() {
        java.lang.Object ref = extraMsg_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          extraMsg_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @param value The extraMsg to set.
       * @return This builder for chaining.
       */
      public Builder setExtraMsg(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        extraMsg_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExtraMsg() {
        bitField0_ = (bitField0_ & ~0x00000002);
        extraMsg_ = getDefaultInstance().getExtraMsg();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 返给米大师的msg，为空则使用默认值
       * </pre>
       *
       * <code>optional string extraMsg = 2;</code>
       * @param value The bytes for extraMsg to set.
       * @return This builder for chaining.
       */
      public Builder setExtraMsgBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        extraMsg_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MidasCallbackAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MidasCallbackAns)
    private static final com.yorha.proto.SsPlayerPayment.MidasCallbackAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerPayment.MidasCallbackAns();
    }

    public static com.yorha.proto.SsPlayerPayment.MidasCallbackAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MidasCallbackAns>
        PARSER = new com.google.protobuf.AbstractParser<MidasCallbackAns>() {
      @java.lang.Override
      public MidasCallbackAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MidasCallbackAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MidasCallbackAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MidasCallbackAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerPayment.MidasCallbackAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasCallbackAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasCallbackAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MidasCallbackAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MidasCallbackAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n.ss_proto/gen/player/ss/ss_player_payme" +
      "nt.proto\022\017com.yorha.proto\"\213\002\n\020MidasCallb" +
      "ackAsk\022\016\n\006openId\030\001 \001(\t\022\016\n\006zoneId\030\002 \001(\005\022\020" +
      "\n\010playerId\030\003 \001(\003\022\r\n\005tsSec\030\004 \001(\003\022\017\n\007payIt" +
      "em\030\005 \001(\t\022\021\n\tproductId\030\006 \001(\t\022\r\n\005token\030\007 \001" +
      "(\t\022\016\n\006billno\030\010 \001(\t\022\013\n\003amt\030\t \001(\t\022\017\n\007appMe" +
      "ta\030\n \001(\t\022\021\n\tchannelId\030\013 \001(\t\022\021\n\tclientVer" +
      "\030\014 \001(\t\022\023\n\013payamtCoins\030\r \001(\t\022\032\n\022pubacctPa" +
      "yamtCoins\030\016 \001(\t\"0\n\020MidasCallbackAns\022\n\n\002o" +
      "k\030\001 \001(\010\022\020\n\010extraMsg\030\002 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
        });
    internal_static_com_yorha_proto_MidasCallbackAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_MidasCallbackAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasCallbackAsk_descriptor,
        new java.lang.String[] { "OpenId", "ZoneId", "PlayerId", "TsSec", "PayItem", "ProductId", "Token", "Billno", "Amt", "AppMeta", "ChannelId", "ClientVer", "PayamtCoins", "PubacctPayamtCoins", });
    internal_static_com_yorha_proto_MidasCallbackAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_MidasCallbackAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MidasCallbackAns_descriptor,
        new java.lang.String[] { "Ok", "ExtraMsg", });
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_scene.proto

package com.yorha.proto;

public final class SsPlayerScene {
  private SsPlayerScene() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface OnOccupySuccessCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OnOccupySuccessCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 地块类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
     * @return Whether the areaType field is set.
     */
    boolean hasAreaType();
    /**
     * <pre>
     * 地块类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
     * @return The areaType.
     */
    com.yorha.proto.CommonEnum.MapAreaType getAreaType();
  }
  /**
   * Protobuf type {@code com.yorha.proto.OnOccupySuccessCmd}
   */
  public static final class OnOccupySuccessCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OnOccupySuccessCmd)
      OnOccupySuccessCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OnOccupySuccessCmd.newBuilder() to construct.
    private OnOccupySuccessCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OnOccupySuccessCmd() {
      areaType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OnOccupySuccessCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OnOccupySuccessCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              partId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              templateId_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapAreaType value = com.yorha.proto.CommonEnum.MapAreaType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                areaType_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_OnOccupySuccessCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.class, com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.Builder.class);
    }

    private int bitField0_;
    public static final int PARTID_FIELD_NUMBER = 1;
    private int partId_;
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 地块id
     * </pre>
     *
     * <code>optional int32 partId = 1;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_;
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int AREATYPE_FIELD_NUMBER = 3;
    private int areaType_;
    /**
     * <pre>
     * 地块类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
     * @return Whether the areaType field is set.
     */
    @java.lang.Override public boolean hasAreaType() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 地块类型
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
     * @return The areaType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapAreaType getAreaType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapAreaType result = com.yorha.proto.CommonEnum.MapAreaType.valueOf(areaType_);
      return result == null ? com.yorha.proto.CommonEnum.MapAreaType.AREA_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, areaType_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, partId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, areaType_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd other = (com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd) obj;

      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasAreaType() != other.hasAreaType()) return false;
      if (hasAreaType()) {
        if (areaType_ != other.areaType_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasAreaType()) {
        hash = (37 * hash) + AREATYPE_FIELD_NUMBER;
        hash = (53 * hash) + areaType_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.OnOccupySuccessCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OnOccupySuccessCmd)
        com.yorha.proto.SsPlayerScene.OnOccupySuccessCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_OnOccupySuccessCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.class, com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        areaType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd build() {
        com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd buildPartial() {
        com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd result = new com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.areaType_ = areaType_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd other) {
        if (other == com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd.getDefaultInstance()) return this;
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasAreaType()) {
          setAreaType(other.getAreaType());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int partId_ ;
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000001;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 地块id
       * </pre>
       *
       * <code>optional int32 partId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000002;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private int areaType_ = 0;
      /**
       * <pre>
       * 地块类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
       * @return Whether the areaType field is set.
       */
      @java.lang.Override public boolean hasAreaType() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 地块类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
       * @return The areaType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapAreaType getAreaType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapAreaType result = com.yorha.proto.CommonEnum.MapAreaType.valueOf(areaType_);
        return result == null ? com.yorha.proto.CommonEnum.MapAreaType.AREA_NONE : result;
      }
      /**
       * <pre>
       * 地块类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
       * @param value The areaType to set.
       * @return This builder for chaining.
       */
      public Builder setAreaType(com.yorha.proto.CommonEnum.MapAreaType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        areaType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 地块类型
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapAreaType areaType = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearAreaType() {
        bitField0_ = (bitField0_ & ~0x00000004);
        areaType_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OnOccupySuccessCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OnOccupySuccessCmd)
    private static final com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd();
    }

    public static com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OnOccupySuccessCmd>
        PARSER = new com.google.protobuf.AbstractParser<OnOccupySuccessCmd>() {
      @java.lang.Override
      public OnOccupySuccessCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OnOccupySuccessCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OnOccupySuccessCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OnOccupySuccessCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerScene.OnOccupySuccessCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ComsumeAssetAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ComsumeAssetAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     * @return Whether the package field is set.
     */
    boolean hasPackage();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     * @return The package.
     */
    com.yorha.proto.Struct.YoAssetPackage getPackage();
    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     */
    com.yorha.proto.Struct.YoAssetPackageOrBuilder getPackageOrBuilder();

    /**
     * <code>optional .com.yorha.proto.Reason reason = 2;</code>
     * @return Whether the reason field is set.
     */
    boolean hasReason();
    /**
     * <code>optional .com.yorha.proto.Reason reason = 2;</code>
     * @return The reason.
     */
    com.yorha.proto.CommonEnum.Reason getReason();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ComsumeAssetAsk}
   */
  public static final class ComsumeAssetAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ComsumeAssetAsk)
      ComsumeAssetAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ComsumeAssetAsk.newBuilder() to construct.
    private ComsumeAssetAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ComsumeAssetAsk() {
      reason_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ComsumeAssetAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ComsumeAssetAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.YoAssetPackage.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = package_.toBuilder();
              }
              package_ = input.readMessage(com.yorha.proto.Struct.YoAssetPackage.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(package_);
                package_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.Reason value = com.yorha.proto.CommonEnum.Reason.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                reason_ = rawValue;
              }
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.class, com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PACKAGE_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.YoAssetPackage package_;
    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     * @return Whether the package field is set.
     */
    @java.lang.Override
    public boolean hasPackage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     * @return The package.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.YoAssetPackage getPackage() {
      return package_ == null ? com.yorha.proto.Struct.YoAssetPackage.getDefaultInstance() : package_;
    }
    /**
     * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.YoAssetPackageOrBuilder getPackageOrBuilder() {
      return package_ == null ? com.yorha.proto.Struct.YoAssetPackage.getDefaultInstance() : package_;
    }

    public static final int REASON_FIELD_NUMBER = 2;
    private int reason_;
    /**
     * <code>optional .com.yorha.proto.Reason reason = 2;</code>
     * @return Whether the reason field is set.
     */
    @java.lang.Override public boolean hasReason() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.Reason reason = 2;</code>
     * @return The reason.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.Reason getReason() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.Reason result = com.yorha.proto.CommonEnum.Reason.valueOf(reason_);
      return result == null ? com.yorha.proto.CommonEnum.Reason.ICR_NONE : result;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPackage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, reason_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPackage());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, reason_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerScene.ComsumeAssetAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerScene.ComsumeAssetAsk other = (com.yorha.proto.SsPlayerScene.ComsumeAssetAsk) obj;

      if (hasPackage() != other.hasPackage()) return false;
      if (hasPackage()) {
        if (!getPackage()
            .equals(other.getPackage())) return false;
      }
      if (hasReason() != other.hasReason()) return false;
      if (hasReason()) {
        if (reason_ != other.reason_) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPackage()) {
        hash = (37 * hash) + PACKAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPackage().hashCode();
      }
      if (hasReason()) {
        hash = (37 * hash) + REASON_FIELD_NUMBER;
        hash = (53 * hash) + reason_;
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerScene.ComsumeAssetAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ComsumeAssetAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ComsumeAssetAsk)
        com.yorha.proto.SsPlayerScene.ComsumeAssetAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.class, com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPackageFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (packageBuilder_ == null) {
          package_ = null;
        } else {
          packageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        reason_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAsk build() {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAsk buildPartial() {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAsk result = new com.yorha.proto.SsPlayerScene.ComsumeAssetAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (packageBuilder_ == null) {
            result.package_ = package_;
          } else {
            result.package_ = packageBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.reason_ = reason_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerScene.ComsumeAssetAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerScene.ComsumeAssetAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerScene.ComsumeAssetAsk other) {
        if (other == com.yorha.proto.SsPlayerScene.ComsumeAssetAsk.getDefaultInstance()) return this;
        if (other.hasPackage()) {
          mergePackage(other.getPackage());
        }
        if (other.hasReason()) {
          setReason(other.getReason());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerScene.ComsumeAssetAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.YoAssetPackage package_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.YoAssetPackage, com.yorha.proto.Struct.YoAssetPackage.Builder, com.yorha.proto.Struct.YoAssetPackageOrBuilder> packageBuilder_;
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       * @return Whether the package field is set.
       */
      public boolean hasPackage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       * @return The package.
       */
      public com.yorha.proto.Struct.YoAssetPackage getPackage() {
        if (packageBuilder_ == null) {
          return package_ == null ? com.yorha.proto.Struct.YoAssetPackage.getDefaultInstance() : package_;
        } else {
          return packageBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public Builder setPackage(com.yorha.proto.Struct.YoAssetPackage value) {
        if (packageBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          package_ = value;
          onChanged();
        } else {
          packageBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public Builder setPackage(
          com.yorha.proto.Struct.YoAssetPackage.Builder builderForValue) {
        if (packageBuilder_ == null) {
          package_ = builderForValue.build();
          onChanged();
        } else {
          packageBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public Builder mergePackage(com.yorha.proto.Struct.YoAssetPackage value) {
        if (packageBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              package_ != null &&
              package_ != com.yorha.proto.Struct.YoAssetPackage.getDefaultInstance()) {
            package_ =
              com.yorha.proto.Struct.YoAssetPackage.newBuilder(package_).mergeFrom(value).buildPartial();
          } else {
            package_ = value;
          }
          onChanged();
        } else {
          packageBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public Builder clearPackage() {
        if (packageBuilder_ == null) {
          package_ = null;
          onChanged();
        } else {
          packageBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public com.yorha.proto.Struct.YoAssetPackage.Builder getPackageBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPackageFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      public com.yorha.proto.Struct.YoAssetPackageOrBuilder getPackageOrBuilder() {
        if (packageBuilder_ != null) {
          return packageBuilder_.getMessageOrBuilder();
        } else {
          return package_ == null ?
              com.yorha.proto.Struct.YoAssetPackage.getDefaultInstance() : package_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.YoAssetPackage package = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.YoAssetPackage, com.yorha.proto.Struct.YoAssetPackage.Builder, com.yorha.proto.Struct.YoAssetPackageOrBuilder> 
          getPackageFieldBuilder() {
        if (packageBuilder_ == null) {
          packageBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.YoAssetPackage, com.yorha.proto.Struct.YoAssetPackage.Builder, com.yorha.proto.Struct.YoAssetPackageOrBuilder>(
                  getPackage(),
                  getParentForChildren(),
                  isClean());
          package_ = null;
        }
        return packageBuilder_;
      }

      private int reason_ = 0;
      /**
       * <code>optional .com.yorha.proto.Reason reason = 2;</code>
       * @return Whether the reason field is set.
       */
      @java.lang.Override public boolean hasReason() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.Reason reason = 2;</code>
       * @return The reason.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.Reason getReason() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.Reason result = com.yorha.proto.CommonEnum.Reason.valueOf(reason_);
        return result == null ? com.yorha.proto.CommonEnum.Reason.ICR_NONE : result;
      }
      /**
       * <code>optional .com.yorha.proto.Reason reason = 2;</code>
       * @param value The reason to set.
       * @return This builder for chaining.
       */
      public Builder setReason(com.yorha.proto.CommonEnum.Reason value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        reason_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.Reason reason = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearReason() {
        bitField0_ = (bitField0_ & ~0x00000002);
        reason_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ComsumeAssetAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ComsumeAssetAsk)
    private static final com.yorha.proto.SsPlayerScene.ComsumeAssetAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerScene.ComsumeAssetAsk();
    }

    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ComsumeAssetAsk>
        PARSER = new com.google.protobuf.AbstractParser<ComsumeAssetAsk>() {
      @java.lang.Override
      public ComsumeAssetAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ComsumeAssetAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ComsumeAssetAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ComsumeAssetAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerScene.ComsumeAssetAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ComsumeAssetAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ComsumeAssetAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional bool success = 1;</code>
     * @return Whether the success field is set.
     */
    boolean hasSuccess();
    /**
     * <code>optional bool success = 1;</code>
     * @return The success.
     */
    boolean getSuccess();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ComsumeAssetAns}
   */
  public static final class ComsumeAssetAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ComsumeAssetAns)
      ComsumeAssetAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ComsumeAssetAns.newBuilder() to construct.
    private ComsumeAssetAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ComsumeAssetAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ComsumeAssetAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ComsumeAssetAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              success_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerScene.ComsumeAssetAns.class, com.yorha.proto.SsPlayerScene.ComsumeAssetAns.Builder.class);
    }

    private int bitField0_;
    public static final int SUCCESS_FIELD_NUMBER = 1;
    private boolean success_;
    /**
     * <code>optional bool success = 1;</code>
     * @return Whether the success field is set.
     */
    @java.lang.Override
    public boolean hasSuccess() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional bool success = 1;</code>
     * @return The success.
     */
    @java.lang.Override
    public boolean getSuccess() {
      return success_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, success_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, success_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerScene.ComsumeAssetAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerScene.ComsumeAssetAns other = (com.yorha.proto.SsPlayerScene.ComsumeAssetAns) obj;

      if (hasSuccess() != other.hasSuccess()) return false;
      if (hasSuccess()) {
        if (getSuccess()
            != other.getSuccess()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSuccess()) {
        hash = (37 * hash) + SUCCESS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getSuccess());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerScene.ComsumeAssetAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ComsumeAssetAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ComsumeAssetAns)
        com.yorha.proto.SsPlayerScene.ComsumeAssetAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerScene.ComsumeAssetAns.class, com.yorha.proto.SsPlayerScene.ComsumeAssetAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerScene.ComsumeAssetAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        success_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerScene.internal_static_com_yorha_proto_ComsumeAssetAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerScene.ComsumeAssetAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAns build() {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerScene.ComsumeAssetAns buildPartial() {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAns result = new com.yorha.proto.SsPlayerScene.ComsumeAssetAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.success_ = success_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerScene.ComsumeAssetAns) {
          return mergeFrom((com.yorha.proto.SsPlayerScene.ComsumeAssetAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerScene.ComsumeAssetAns other) {
        if (other == com.yorha.proto.SsPlayerScene.ComsumeAssetAns.getDefaultInstance()) return this;
        if (other.hasSuccess()) {
          setSuccess(other.getSuccess());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerScene.ComsumeAssetAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerScene.ComsumeAssetAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean success_ ;
      /**
       * <code>optional bool success = 1;</code>
       * @return Whether the success field is set.
       */
      @java.lang.Override
      public boolean hasSuccess() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional bool success = 1;</code>
       * @return The success.
       */
      @java.lang.Override
      public boolean getSuccess() {
        return success_;
      }
      /**
       * <code>optional bool success = 1;</code>
       * @param value The success to set.
       * @return This builder for chaining.
       */
      public Builder setSuccess(boolean value) {
        bitField0_ |= 0x00000001;
        success_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional bool success = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSuccess() {
        bitField0_ = (bitField0_ & ~0x00000001);
        success_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ComsumeAssetAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ComsumeAssetAns)
    private static final com.yorha.proto.SsPlayerScene.ComsumeAssetAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerScene.ComsumeAssetAns();
    }

    public static com.yorha.proto.SsPlayerScene.ComsumeAssetAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ComsumeAssetAns>
        PARSER = new com.google.protobuf.AbstractParser<ComsumeAssetAns>() {
      @java.lang.Override
      public ComsumeAssetAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ComsumeAssetAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ComsumeAssetAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ComsumeAssetAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerScene.ComsumeAssetAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OnOccupySuccessCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ComsumeAssetAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ComsumeAssetAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ComsumeAssetAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n,ss_proto/gen/player/ss/ss_player_scene" +
      ".proto\022\017com.yorha.proto\032%ss_proto/gen/co" +
      "mmon/common_enum.proto\032 ss_proto/gen/com" +
      "mon/struct.proto\"h\n\022OnOccupySuccessCmd\022\016" +
      "\n\006partId\030\001 \001(\005\022\022\n\ntemplateId\030\002 \001(\005\022.\n\010ar" +
      "eaType\030\003 \001(\0162\034.com.yorha.proto.MapAreaTy" +
      "pe\"l\n\017ComsumeAssetAsk\0220\n\007package\030\001 \001(\0132\037" +
      ".com.yorha.proto.YoAssetPackage\022\'\n\006reaso" +
      "n\030\002 \001(\0162\027.com.yorha.proto.Reason\"\"\n\017Coms" +
      "umeAssetAns\022\017\n\007success\030\001 \001(\010B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
        });
    internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_OnOccupySuccessCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OnOccupySuccessCmd_descriptor,
        new java.lang.String[] { "PartId", "TemplateId", "AreaType", });
    internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ComsumeAssetAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ComsumeAssetAsk_descriptor,
        new java.lang.String[] { "Package", "Reason", });
    internal_static_com_yorha_proto_ComsumeAssetAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ComsumeAssetAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ComsumeAssetAns_descriptor,
        new java.lang.String[] { "Success", });
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

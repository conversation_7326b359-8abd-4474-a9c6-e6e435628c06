// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/player/ss/ss_player_idip.proto

package com.yorha.proto;

public final class SsPlayerIdip {
  private SsPlayerIdip() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ResourceAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ResourceAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return Whether the currencyType field is set.
     */
    boolean hasCurrencyType();
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return The currencyType.
     */
    com.yorha.proto.CommonEnum.CurrencyType getCurrencyType();

    /**
     * <code>optional int32 value = 3;</code>
     * @return Whether the value field is set.
     */
    boolean hasValue();
    /**
     * <code>optional int32 value = 3;</code>
     * @return The value.
     */
    int getValue();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ResourceAsk}
   */
  public static final class ResourceAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ResourceAsk)
      ResourceAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResourceAsk.newBuilder() to construct.
    private ResourceAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResourceAsk() {
      openId_ = "";
      currencyType_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResourceAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResourceAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 16: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.CurrencyType value = com.yorha.proto.CommonEnum.CurrencyType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(2, rawValue);
              } else {
                bitField0_ |= 0x00000002;
                currencyType_ = rawValue;
              }
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              value_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ResourceAsk.class, com.yorha.proto.SsPlayerIdip.ResourceAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CURRENCYTYPE_FIELD_NUMBER = 2;
    private int currencyType_;
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return Whether the currencyType field is set.
     */
    @java.lang.Override public boolean hasCurrencyType() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
     * @return The currencyType.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.CurrencyType getCurrencyType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(currencyType_);
      return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
    }

    public static final int VALUE_FIELD_NUMBER = 3;
    private int value_;
    /**
     * <code>optional int32 value = 3;</code>
     * @return Whether the value field is set.
     */
    @java.lang.Override
    public boolean hasValue() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int32 value = 3;</code>
     * @return The value.
     */
    @java.lang.Override
    public int getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeEnum(2, currencyType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, value_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(2, currencyType_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ResourceAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ResourceAsk other = (com.yorha.proto.SsPlayerIdip.ResourceAsk) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasCurrencyType() != other.hasCurrencyType()) return false;
      if (hasCurrencyType()) {
        if (currencyType_ != other.currencyType_) return false;
      }
      if (hasValue() != other.hasValue()) return false;
      if (hasValue()) {
        if (getValue()
            != other.getValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasCurrencyType()) {
        hash = (37 * hash) + CURRENCYTYPE_FIELD_NUMBER;
        hash = (53 * hash) + currencyType_;
      }
      if (hasValue()) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ResourceAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ResourceAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ResourceAsk)
        com.yorha.proto.SsPlayerIdip.ResourceAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ResourceAsk.class, com.yorha.proto.SsPlayerIdip.ResourceAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ResourceAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        currencyType_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ResourceAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAsk build() {
        com.yorha.proto.SsPlayerIdip.ResourceAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAsk buildPartial() {
        com.yorha.proto.SsPlayerIdip.ResourceAsk result = new com.yorha.proto.SsPlayerIdip.ResourceAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.currencyType_ = currencyType_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.value_ = value_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ResourceAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ResourceAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ResourceAsk other) {
        if (other == com.yorha.proto.SsPlayerIdip.ResourceAsk.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasCurrencyType()) {
          setCurrencyType(other.getCurrencyType());
        }
        if (other.hasValue()) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ResourceAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ResourceAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private int currencyType_ = 0;
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return Whether the currencyType field is set.
       */
      @java.lang.Override public boolean hasCurrencyType() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return The currencyType.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.CurrencyType getCurrencyType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.CurrencyType result = com.yorha.proto.CommonEnum.CurrencyType.valueOf(currencyType_);
        return result == null ? com.yorha.proto.CommonEnum.CurrencyType.CT_None : result;
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @param value The currencyType to set.
       * @return This builder for chaining.
       */
      public Builder setCurrencyType(com.yorha.proto.CommonEnum.CurrencyType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000002;
        currencyType_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.CurrencyType currencyType = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrencyType() {
        bitField0_ = (bitField0_ & ~0x00000002);
        currencyType_ = 0;
        onChanged();
        return this;
      }

      private int value_ ;
      /**
       * <code>optional int32 value = 3;</code>
       * @return Whether the value field is set.
       */
      @java.lang.Override
      public boolean hasValue() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int32 value = 3;</code>
       * @return The value.
       */
      @java.lang.Override
      public int getValue() {
        return value_;
      }
      /**
       * <code>optional int32 value = 3;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(int value) {
        bitField0_ |= 0x00000004;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 value = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        bitField0_ = (bitField0_ & ~0x00000004);
        value_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ResourceAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ResourceAsk)
    private static final com.yorha.proto.SsPlayerIdip.ResourceAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ResourceAsk();
    }

    public static com.yorha.proto.SsPlayerIdip.ResourceAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ResourceAsk>
        PARSER = new com.google.protobuf.AbstractParser<ResourceAsk>() {
      @java.lang.Override
      public ResourceAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResourceAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResourceAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResourceAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ResourceAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ResourceAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ResourceAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int64 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    boolean hasBeforeValue();
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int64 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    long getBeforeValue();

    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int64 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    boolean hasAfterValue();
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int64 afterValue = 2;</code>
     * @return The afterValue.
     */
    long getAfterValue();

    /**
     * <pre>
     * 处理特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    boolean hasExceptionId();
    /**
     * <pre>
     * 处理特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    int getExceptionId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ResourceAns}
   */
  public static final class ResourceAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ResourceAns)
      ResourceAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ResourceAns.newBuilder() to construct.
    private ResourceAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ResourceAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ResourceAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ResourceAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeValue_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterValue_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              exceptionId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ResourceAns.class, com.yorha.proto.SsPlayerIdip.ResourceAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFOREVALUE_FIELD_NUMBER = 1;
    private long beforeValue_;
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int64 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    @java.lang.Override
    public boolean hasBeforeValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int64 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    @java.lang.Override
    public long getBeforeValue() {
      return beforeValue_;
    }

    public static final int AFTERVALUE_FIELD_NUMBER = 2;
    private long afterValue_;
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int64 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    @java.lang.Override
    public boolean hasAfterValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int64 afterValue = 2;</code>
     * @return The afterValue.
     */
    @java.lang.Override
    public long getAfterValue() {
      return afterValue_;
    }

    public static final int EXCEPTIONID_FIELD_NUMBER = 3;
    private int exceptionId_;
    /**
     * <pre>
     * 处理特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    @java.lang.Override
    public boolean hasExceptionId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 处理特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    @java.lang.Override
    public int getExceptionId() {
      return exceptionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, exceptionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, exceptionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ResourceAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ResourceAns other = (com.yorha.proto.SsPlayerIdip.ResourceAns) obj;

      if (hasBeforeValue() != other.hasBeforeValue()) return false;
      if (hasBeforeValue()) {
        if (getBeforeValue()
            != other.getBeforeValue()) return false;
      }
      if (hasAfterValue() != other.hasAfterValue()) return false;
      if (hasAfterValue()) {
        if (getAfterValue()
            != other.getAfterValue()) return false;
      }
      if (hasExceptionId() != other.hasExceptionId()) return false;
      if (hasExceptionId()) {
        if (getExceptionId()
            != other.getExceptionId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeValue()) {
        hash = (37 * hash) + BEFOREVALUE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getBeforeValue());
      }
      if (hasAfterValue()) {
        hash = (37 * hash) + AFTERVALUE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getAfterValue());
      }
      if (hasExceptionId()) {
        hash = (37 * hash) + EXCEPTIONID_FIELD_NUMBER;
        hash = (53 * hash) + getExceptionId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ResourceAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ResourceAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ResourceAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ResourceAns)
        com.yorha.proto.SsPlayerIdip.ResourceAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ResourceAns.class, com.yorha.proto.SsPlayerIdip.ResourceAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ResourceAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterValue_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        exceptionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ResourceAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ResourceAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAns build() {
        com.yorha.proto.SsPlayerIdip.ResourceAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ResourceAns buildPartial() {
        com.yorha.proto.SsPlayerIdip.ResourceAns result = new com.yorha.proto.SsPlayerIdip.ResourceAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeValue_ = beforeValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterValue_ = afterValue_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.exceptionId_ = exceptionId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ResourceAns) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ResourceAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ResourceAns other) {
        if (other == com.yorha.proto.SsPlayerIdip.ResourceAns.getDefaultInstance()) return this;
        if (other.hasBeforeValue()) {
          setBeforeValue(other.getBeforeValue());
        }
        if (other.hasAfterValue()) {
          setAfterValue(other.getAfterValue());
        }
        if (other.hasExceptionId()) {
          setExceptionId(other.getExceptionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ResourceAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ResourceAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long beforeValue_ ;
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int64 beforeValue = 1;</code>
       * @return Whether the beforeValue field is set.
       */
      @java.lang.Override
      public boolean hasBeforeValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int64 beforeValue = 1;</code>
       * @return The beforeValue.
       */
      @java.lang.Override
      public long getBeforeValue() {
        return beforeValue_;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int64 beforeValue = 1;</code>
       * @param value The beforeValue to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeValue(long value) {
        bitField0_ |= 0x00000001;
        beforeValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int64 beforeValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeValue_ = 0L;
        onChanged();
        return this;
      }

      private long afterValue_ ;
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int64 afterValue = 2;</code>
       * @return Whether the afterValue field is set.
       */
      @java.lang.Override
      public boolean hasAfterValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int64 afterValue = 2;</code>
       * @return The afterValue.
       */
      @java.lang.Override
      public long getAfterValue() {
        return afterValue_;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int64 afterValue = 2;</code>
       * @param value The afterValue to set.
       * @return This builder for chaining.
       */
      public Builder setAfterValue(long value) {
        bitField0_ |= 0x00000002;
        afterValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int64 afterValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterValue_ = 0L;
        onChanged();
        return this;
      }

      private int exceptionId_ ;
      /**
       * <pre>
       * 处理特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return Whether the exceptionId field is set.
       */
      @java.lang.Override
      public boolean hasExceptionId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 处理特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return The exceptionId.
       */
      @java.lang.Override
      public int getExceptionId() {
        return exceptionId_;
      }
      /**
       * <pre>
       * 处理特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @param value The exceptionId to set.
       * @return This builder for chaining.
       */
      public Builder setExceptionId(int value) {
        bitField0_ |= 0x00000004;
        exceptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 处理特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExceptionId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        exceptionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ResourceAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ResourceAns)
    private static final com.yorha.proto.SsPlayerIdip.ResourceAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ResourceAns();
    }

    public static com.yorha.proto.SsPlayerIdip.ResourceAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ResourceAns>
        PARSER = new com.google.protobuf.AbstractParser<ResourceAns>() {
      @java.lang.Override
      public ResourceAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ResourceAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ResourceAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ResourceAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ResourceAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConsumeDiamondAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConsumeDiamondAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改值
     * </pre>
     *
     * <code>optional int32 fixValue = 1;</code>
     * @return Whether the fixValue field is set.
     */
    boolean hasFixValue();
    /**
     * <pre>
     * 修改值
     * </pre>
     *
     * <code>optional int32 fixValue = 1;</code>
     * @return The fixValue.
     */
    int getFixValue();

    /**
     * <code>optional string openId = 2;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 2;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 2;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConsumeDiamondAsk}
   */
  public static final class ConsumeDiamondAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConsumeDiamondAsk)
      ConsumeDiamondAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConsumeDiamondAsk.newBuilder() to construct.
    private ConsumeDiamondAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConsumeDiamondAsk() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConsumeDiamondAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConsumeDiamondAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              fixValue_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              openId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.class, com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.Builder.class);
    }

    private int bitField0_;
    public static final int FIXVALUE_FIELD_NUMBER = 1;
    private int fixValue_;
    /**
     * <pre>
     * 修改值
     * </pre>
     *
     * <code>optional int32 fixValue = 1;</code>
     * @return Whether the fixValue field is set.
     */
    @java.lang.Override
    public boolean hasFixValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改值
     * </pre>
     *
     * <code>optional int32 fixValue = 1;</code>
     * @return The fixValue.
     */
    @java.lang.Override
    public int getFixValue() {
      return fixValue_;
    }

    public static final int OPENID_FIELD_NUMBER = 2;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 2;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string openId = 2;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 2;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, fixValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, openId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, fixValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, openId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk other = (com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk) obj;

      if (hasFixValue() != other.hasFixValue()) return false;
      if (hasFixValue()) {
        if (getFixValue()
            != other.getFixValue()) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasFixValue()) {
        hash = (37 * hash) + FIXVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getFixValue();
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConsumeDiamondAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConsumeDiamondAsk)
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.class, com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        fixValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk build() {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk buildPartial() {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk result = new com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.fixValue_ = fixValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.openId_ = openId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk other) {
        if (other == com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk.getDefaultInstance()) return this;
        if (other.hasFixValue()) {
          setFixValue(other.getFixValue());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000002;
          openId_ = other.openId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int fixValue_ ;
      /**
       * <pre>
       * 修改值
       * </pre>
       *
       * <code>optional int32 fixValue = 1;</code>
       * @return Whether the fixValue field is set.
       */
      @java.lang.Override
      public boolean hasFixValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改值
       * </pre>
       *
       * <code>optional int32 fixValue = 1;</code>
       * @return The fixValue.
       */
      @java.lang.Override
      public int getFixValue() {
        return fixValue_;
      }
      /**
       * <pre>
       * 修改值
       * </pre>
       *
       * <code>optional int32 fixValue = 1;</code>
       * @param value The fixValue to set.
       * @return This builder for chaining.
       */
      public Builder setFixValue(int value) {
        bitField0_ |= 0x00000001;
        fixValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改值
       * </pre>
       *
       * <code>optional int32 fixValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearFixValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        fixValue_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 2;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 2;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 2;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        openId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConsumeDiamondAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConsumeDiamondAsk)
    private static final com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk();
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConsumeDiamondAsk>
        PARSER = new com.google.protobuf.AbstractParser<ConsumeDiamondAsk>() {
      @java.lang.Override
      public ConsumeDiamondAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConsumeDiamondAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConsumeDiamondAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConsumeDiamondAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConsumeDiamondAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConsumeDiamondAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    boolean hasBeforeValue();
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    int getBeforeValue();

    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    boolean hasAfterValue();
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    int getAfterValue();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConsumeDiamondAns}
   */
  public static final class ConsumeDiamondAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConsumeDiamondAns)
      ConsumeDiamondAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConsumeDiamondAns.newBuilder() to construct.
    private ConsumeDiamondAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConsumeDiamondAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConsumeDiamondAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConsumeDiamondAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeValue_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterValue_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.class, com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFOREVALUE_FIELD_NUMBER = 1;
    private int beforeValue_;
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    @java.lang.Override
    public boolean hasBeforeValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    @java.lang.Override
    public int getBeforeValue() {
      return beforeValue_;
    }

    public static final int AFTERVALUE_FIELD_NUMBER = 2;
    private int afterValue_;
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    @java.lang.Override
    public boolean hasAfterValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    @java.lang.Override
    public int getAfterValue() {
      return afterValue_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, afterValue_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, afterValue_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns other = (com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns) obj;

      if (hasBeforeValue() != other.hasBeforeValue()) return false;
      if (hasBeforeValue()) {
        if (getBeforeValue()
            != other.getBeforeValue()) return false;
      }
      if (hasAfterValue() != other.hasAfterValue()) return false;
      if (hasAfterValue()) {
        if (getAfterValue()
            != other.getAfterValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeValue()) {
        hash = (37 * hash) + BEFOREVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeValue();
      }
      if (hasAfterValue()) {
        hash = (37 * hash) + AFTERVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getAfterValue();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConsumeDiamondAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConsumeDiamondAns)
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.class, com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns build() {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns buildPartial() {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns result = new com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeValue_ = beforeValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterValue_ = afterValue_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns other) {
        if (other == com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns.getDefaultInstance()) return this;
        if (other.hasBeforeValue()) {
          setBeforeValue(other.getBeforeValue());
        }
        if (other.hasAfterValue()) {
          setAfterValue(other.getAfterValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beforeValue_ ;
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return Whether the beforeValue field is set.
       */
      @java.lang.Override
      public boolean hasBeforeValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return The beforeValue.
       */
      @java.lang.Override
      public int getBeforeValue() {
        return beforeValue_;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @param value The beforeValue to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeValue(int value) {
        bitField0_ |= 0x00000001;
        beforeValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeValue_ = 0;
        onChanged();
        return this;
      }

      private int afterValue_ ;
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return Whether the afterValue field is set.
       */
      @java.lang.Override
      public boolean hasAfterValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return The afterValue.
       */
      @java.lang.Override
      public int getAfterValue() {
        return afterValue_;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @param value The afterValue to set.
       * @return This builder for chaining.
       */
      public Builder setAfterValue(int value) {
        bitField0_ |= 0x00000002;
        afterValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterValue_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConsumeDiamondAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConsumeDiamondAns)
    private static final com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns();
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConsumeDiamondAns>
        PARSER = new com.google.protobuf.AbstractParser<ConsumeDiamondAns>() {
      @java.lang.Override
      public ConsumeDiamondAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConsumeDiamondAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConsumeDiamondAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConsumeDiamondAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ConsumeDiamondAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConsumeItemsAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConsumeItemsAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 要删除的道具ID
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    boolean hasItemId();
    /**
     * <pre>
     * 要删除的道具ID
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    int getItemId();

    /**
     * <pre>
     * 要删除的道具数量
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return Whether the itemNum field is set.
     */
    boolean hasItemNum();
    /**
     * <pre>
     * 要删除的道具数量
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return The itemNum.
     */
    int getItemNum();

    /**
     * <code>optional string openId = 3;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 3;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 3;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConsumeItemsAsk}
   */
  public static final class ConsumeItemsAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConsumeItemsAsk)
      ConsumeItemsAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConsumeItemsAsk.newBuilder() to construct.
    private ConsumeItemsAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConsumeItemsAsk() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConsumeItemsAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConsumeItemsAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              itemId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              itemNum_ = input.readInt32();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              openId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.class, com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ITEMID_FIELD_NUMBER = 1;
    private int itemId_;
    /**
     * <pre>
     * 要删除的道具ID
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return Whether the itemId field is set.
     */
    @java.lang.Override
    public boolean hasItemId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 要删除的道具ID
     * </pre>
     *
     * <code>optional int32 itemId = 1;</code>
     * @return The itemId.
     */
    @java.lang.Override
    public int getItemId() {
      return itemId_;
    }

    public static final int ITEMNUM_FIELD_NUMBER = 2;
    private int itemNum_;
    /**
     * <pre>
     * 要删除的道具数量
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return Whether the itemNum field is set.
     */
    @java.lang.Override
    public boolean hasItemNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 要删除的道具数量
     * </pre>
     *
     * <code>optional int32 itemNum = 2;</code>
     * @return The itemNum.
     */
    @java.lang.Override
    public int getItemNum() {
      return itemNum_;
    }

    public static final int OPENID_FIELD_NUMBER = 3;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 3;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string openId = 3;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 3;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, itemNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, openId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, itemId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, itemNum_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, openId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk other = (com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk) obj;

      if (hasItemId() != other.hasItemId()) return false;
      if (hasItemId()) {
        if (getItemId()
            != other.getItemId()) return false;
      }
      if (hasItemNum() != other.hasItemNum()) return false;
      if (hasItemNum()) {
        if (getItemNum()
            != other.getItemNum()) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasItemId()) {
        hash = (37 * hash) + ITEMID_FIELD_NUMBER;
        hash = (53 * hash) + getItemId();
      }
      if (hasItemNum()) {
        hash = (37 * hash) + ITEMNUM_FIELD_NUMBER;
        hash = (53 * hash) + getItemNum();
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConsumeItemsAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConsumeItemsAsk)
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.class, com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        itemId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        itemNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk build() {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk buildPartial() {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk result = new com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.itemId_ = itemId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.itemNum_ = itemNum_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.openId_ = openId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk other) {
        if (other == com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk.getDefaultInstance()) return this;
        if (other.hasItemId()) {
          setItemId(other.getItemId());
        }
        if (other.hasItemNum()) {
          setItemNum(other.getItemNum());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000004;
          openId_ = other.openId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int itemId_ ;
      /**
       * <pre>
       * 要删除的道具ID
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return Whether the itemId field is set.
       */
      @java.lang.Override
      public boolean hasItemId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 要删除的道具ID
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return The itemId.
       */
      @java.lang.Override
      public int getItemId() {
        return itemId_;
      }
      /**
       * <pre>
       * 要删除的道具ID
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @param value The itemId to set.
       * @return This builder for chaining.
       */
      public Builder setItemId(int value) {
        bitField0_ |= 0x00000001;
        itemId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 要删除的道具ID
       * </pre>
       *
       * <code>optional int32 itemId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        itemId_ = 0;
        onChanged();
        return this;
      }

      private int itemNum_ ;
      /**
       * <pre>
       * 要删除的道具数量
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return Whether the itemNum field is set.
       */
      @java.lang.Override
      public boolean hasItemNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 要删除的道具数量
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return The itemNum.
       */
      @java.lang.Override
      public int getItemNum() {
        return itemNum_;
      }
      /**
       * <pre>
       * 要删除的道具数量
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @param value The itemNum to set.
       * @return This builder for chaining.
       */
      public Builder setItemNum(int value) {
        bitField0_ |= 0x00000002;
        itemNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 要删除的道具数量
       * </pre>
       *
       * <code>optional int32 itemNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearItemNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        itemNum_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 3;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string openId = 3;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 3;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 3;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 3;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        openId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConsumeItemsAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConsumeItemsAsk)
    private static final com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk();
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConsumeItemsAsk>
        PARSER = new com.google.protobuf.AbstractParser<ConsumeItemsAsk>() {
      @java.lang.Override
      public ConsumeItemsAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConsumeItemsAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConsumeItemsAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConsumeItemsAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ConsumeItemsAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConsumeItemsAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConsumeItemsAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    boolean hasBeforeValue();
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    int getBeforeValue();

    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    boolean hasAfterValue();
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    int getAfterValue();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConsumeItemsAns}
   */
  public static final class ConsumeItemsAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConsumeItemsAns)
      ConsumeItemsAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConsumeItemsAns.newBuilder() to construct.
    private ConsumeItemsAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConsumeItemsAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConsumeItemsAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConsumeItemsAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeValue_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterValue_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.class, com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFOREVALUE_FIELD_NUMBER = 1;
    private int beforeValue_;
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    @java.lang.Override
    public boolean hasBeforeValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    @java.lang.Override
    public int getBeforeValue() {
      return beforeValue_;
    }

    public static final int AFTERVALUE_FIELD_NUMBER = 2;
    private int afterValue_;
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    @java.lang.Override
    public boolean hasAfterValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    @java.lang.Override
    public int getAfterValue() {
      return afterValue_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, afterValue_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, afterValue_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ConsumeItemsAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ConsumeItemsAns other = (com.yorha.proto.SsPlayerIdip.ConsumeItemsAns) obj;

      if (hasBeforeValue() != other.hasBeforeValue()) return false;
      if (hasBeforeValue()) {
        if (getBeforeValue()
            != other.getBeforeValue()) return false;
      }
      if (hasAfterValue() != other.hasAfterValue()) return false;
      if (hasAfterValue()) {
        if (getAfterValue()
            != other.getAfterValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeValue()) {
        hash = (37 * hash) + BEFOREVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeValue();
      }
      if (hasAfterValue()) {
        hash = (37 * hash) + AFTERVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getAfterValue();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ConsumeItemsAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConsumeItemsAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConsumeItemsAns)
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.class, com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ConsumeItemsAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAns build() {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ConsumeItemsAns buildPartial() {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAns result = new com.yorha.proto.SsPlayerIdip.ConsumeItemsAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeValue_ = beforeValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterValue_ = afterValue_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ConsumeItemsAns) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ConsumeItemsAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ConsumeItemsAns other) {
        if (other == com.yorha.proto.SsPlayerIdip.ConsumeItemsAns.getDefaultInstance()) return this;
        if (other.hasBeforeValue()) {
          setBeforeValue(other.getBeforeValue());
        }
        if (other.hasAfterValue()) {
          setAfterValue(other.getAfterValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ConsumeItemsAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ConsumeItemsAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beforeValue_ ;
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return Whether the beforeValue field is set.
       */
      @java.lang.Override
      public boolean hasBeforeValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return The beforeValue.
       */
      @java.lang.Override
      public int getBeforeValue() {
        return beforeValue_;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @param value The beforeValue to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeValue(int value) {
        bitField0_ |= 0x00000001;
        beforeValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeValue_ = 0;
        onChanged();
        return this;
      }

      private int afterValue_ ;
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return Whether the afterValue field is set.
       */
      @java.lang.Override
      public boolean hasAfterValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return The afterValue.
       */
      @java.lang.Override
      public int getAfterValue() {
        return afterValue_;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @param value The afterValue to set.
       * @return This builder for chaining.
       */
      public Builder setAfterValue(int value) {
        bitField0_ |= 0x00000002;
        afterValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterValue_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConsumeItemsAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConsumeItemsAns)
    private static final com.yorha.proto.SsPlayerIdip.ConsumeItemsAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ConsumeItemsAns();
    }

    public static com.yorha.proto.SsPlayerIdip.ConsumeItemsAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConsumeItemsAns>
        PARSER = new com.google.protobuf.AbstractParser<ConsumeItemsAns>() {
      @java.lang.Override
      public ConsumeItemsAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConsumeItemsAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConsumeItemsAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConsumeItemsAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ConsumeItemsAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface PullMidasCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.PullMidasCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return Whether the chargeSer field is set.
     */
    boolean hasChargeSer();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return The chargeSer.
     */
    java.lang.String getChargeSer();
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return The bytes for chargeSer.
     */
    com.google.protobuf.ByteString
        getChargeSerBytes();

    /**
     * <pre>
     * 订单金额
     * </pre>
     *
     * <code>optional int32 chargeNum = 2;</code>
     * @return Whether the chargeNum field is set.
     */
    boolean hasChargeNum();
    /**
     * <pre>
     * 订单金额
     * </pre>
     *
     * <code>optional int32 chargeNum = 2;</code>
     * @return The chargeNum.
     */
    int getChargeNum();
  }
  /**
   * Protobuf type {@code com.yorha.proto.PullMidasCmd}
   */
  public static final class PullMidasCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.PullMidasCmd)
      PullMidasCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use PullMidasCmd.newBuilder() to construct.
    private PullMidasCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private PullMidasCmd() {
      chargeSer_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new PullMidasCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private PullMidasCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              chargeSer_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              chargeNum_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_PullMidasCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_PullMidasCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.PullMidasCmd.class, com.yorha.proto.SsPlayerIdip.PullMidasCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CHARGESER_FIELD_NUMBER = 1;
    private volatile java.lang.Object chargeSer_;
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return Whether the chargeSer field is set.
     */
    @java.lang.Override
    public boolean hasChargeSer() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return The chargeSer.
     */
    @java.lang.Override
    public java.lang.String getChargeSer() {
      java.lang.Object ref = chargeSer_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          chargeSer_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 订单号
     * </pre>
     *
     * <code>optional string chargeSer = 1;</code>
     * @return The bytes for chargeSer.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getChargeSerBytes() {
      java.lang.Object ref = chargeSer_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        chargeSer_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CHARGENUM_FIELD_NUMBER = 2;
    private int chargeNum_;
    /**
     * <pre>
     * 订单金额
     * </pre>
     *
     * <code>optional int32 chargeNum = 2;</code>
     * @return Whether the chargeNum field is set.
     */
    @java.lang.Override
    public boolean hasChargeNum() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 订单金额
     * </pre>
     *
     * <code>optional int32 chargeNum = 2;</code>
     * @return The chargeNum.
     */
    @java.lang.Override
    public int getChargeNum() {
      return chargeNum_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, chargeSer_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, chargeNum_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, chargeSer_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, chargeNum_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.PullMidasCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.PullMidasCmd other = (com.yorha.proto.SsPlayerIdip.PullMidasCmd) obj;

      if (hasChargeSer() != other.hasChargeSer()) return false;
      if (hasChargeSer()) {
        if (!getChargeSer()
            .equals(other.getChargeSer())) return false;
      }
      if (hasChargeNum() != other.hasChargeNum()) return false;
      if (hasChargeNum()) {
        if (getChargeNum()
            != other.getChargeNum()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasChargeSer()) {
        hash = (37 * hash) + CHARGESER_FIELD_NUMBER;
        hash = (53 * hash) + getChargeSer().hashCode();
      }
      if (hasChargeNum()) {
        hash = (37 * hash) + CHARGENUM_FIELD_NUMBER;
        hash = (53 * hash) + getChargeNum();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.PullMidasCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.PullMidasCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.PullMidasCmd)
        com.yorha.proto.SsPlayerIdip.PullMidasCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_PullMidasCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_PullMidasCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.PullMidasCmd.class, com.yorha.proto.SsPlayerIdip.PullMidasCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.PullMidasCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        chargeSer_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_PullMidasCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.PullMidasCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.PullMidasCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.PullMidasCmd build() {
        com.yorha.proto.SsPlayerIdip.PullMidasCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.PullMidasCmd buildPartial() {
        com.yorha.proto.SsPlayerIdip.PullMidasCmd result = new com.yorha.proto.SsPlayerIdip.PullMidasCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.chargeSer_ = chargeSer_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.chargeNum_ = chargeNum_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.PullMidasCmd) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.PullMidasCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.PullMidasCmd other) {
        if (other == com.yorha.proto.SsPlayerIdip.PullMidasCmd.getDefaultInstance()) return this;
        if (other.hasChargeSer()) {
          bitField0_ |= 0x00000001;
          chargeSer_ = other.chargeSer_;
          onChanged();
        }
        if (other.hasChargeNum()) {
          setChargeNum(other.getChargeNum());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.PullMidasCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.PullMidasCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object chargeSer_ = "";
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @return Whether the chargeSer field is set.
       */
      public boolean hasChargeSer() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @return The chargeSer.
       */
      public java.lang.String getChargeSer() {
        java.lang.Object ref = chargeSer_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            chargeSer_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @return The bytes for chargeSer.
       */
      public com.google.protobuf.ByteString
          getChargeSerBytes() {
        java.lang.Object ref = chargeSer_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          chargeSer_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @param value The chargeSer to set.
       * @return This builder for chaining.
       */
      public Builder setChargeSer(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chargeSer_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeSer() {
        bitField0_ = (bitField0_ & ~0x00000001);
        chargeSer_ = getDefaultInstance().getChargeSer();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单号
       * </pre>
       *
       * <code>optional string chargeSer = 1;</code>
       * @param value The bytes for chargeSer to set.
       * @return This builder for chaining.
       */
      public Builder setChargeSerBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        chargeSer_ = value;
        onChanged();
        return this;
      }

      private int chargeNum_ ;
      /**
       * <pre>
       * 订单金额
       * </pre>
       *
       * <code>optional int32 chargeNum = 2;</code>
       * @return Whether the chargeNum field is set.
       */
      @java.lang.Override
      public boolean hasChargeNum() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 订单金额
       * </pre>
       *
       * <code>optional int32 chargeNum = 2;</code>
       * @return The chargeNum.
       */
      @java.lang.Override
      public int getChargeNum() {
        return chargeNum_;
      }
      /**
       * <pre>
       * 订单金额
       * </pre>
       *
       * <code>optional int32 chargeNum = 2;</code>
       * @param value The chargeNum to set.
       * @return This builder for chaining.
       */
      public Builder setChargeNum(int value) {
        bitField0_ |= 0x00000002;
        chargeNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 订单金额
       * </pre>
       *
       * <code>optional int32 chargeNum = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearChargeNum() {
        bitField0_ = (bitField0_ & ~0x00000002);
        chargeNum_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.PullMidasCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.PullMidasCmd)
    private static final com.yorha.proto.SsPlayerIdip.PullMidasCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.PullMidasCmd();
    }

    public static com.yorha.proto.SsPlayerIdip.PullMidasCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<PullMidasCmd>
        PARSER = new com.google.protobuf.AbstractParser<PullMidasCmd>() {
      @java.lang.Override
      public PullMidasCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new PullMidasCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<PullMidasCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<PullMidasCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.PullMidasCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifyVipExpAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifyVipExpAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();

    /**
     * <pre>
     * 经验值
     * </pre>
     *
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    boolean hasValue();
    /**
     * <pre>
     * 经验值
     * </pre>
     *
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    int getValue();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifyVipExpAsk}
   */
  public static final class ModifyVipExpAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifyVipExpAsk)
      ModifyVipExpAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifyVipExpAsk.newBuilder() to construct.
    private ModifyVipExpAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifyVipExpAsk() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifyVipExpAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifyVipExpAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              openId_ = bs;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              value_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.class, com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPENID_FIELD_NUMBER = 1;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 1;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 1;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private int value_;
    /**
     * <pre>
     * 经验值
     * </pre>
     *
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    @java.lang.Override
    public boolean hasValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 经验值
     * </pre>
     *
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    @java.lang.Override
    public int getValue() {
      return value_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, value_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, openId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, value_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk other = (com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk) obj;

      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (hasValue() != other.hasValue()) return false;
      if (hasValue()) {
        if (getValue()
            != other.getValue()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      if (hasValue()) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifyVipExpAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifyVipExpAsk)
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.class, com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        value_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk build() {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk buildPartial() {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk result = new com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.openId_ = openId_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.value_ = value_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk other) {
        if (other == com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk.getDefaultInstance()) return this;
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000001;
          openId_ = other.openId_;
          onChanged();
        }
        if (other.hasValue()) {
          setValue(other.getValue());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 1;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 1;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        openId_ = value;
        onChanged();
        return this;
      }

      private int value_ ;
      /**
       * <pre>
       * 经验值
       * </pre>
       *
       * <code>optional int32 value = 2;</code>
       * @return Whether the value field is set.
       */
      @java.lang.Override
      public boolean hasValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 经验值
       * </pre>
       *
       * <code>optional int32 value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public int getValue() {
        return value_;
      }
      /**
       * <pre>
       * 经验值
       * </pre>
       *
       * <code>optional int32 value = 2;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(int value) {
        bitField0_ |= 0x00000002;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 经验值
       * </pre>
       *
       * <code>optional int32 value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifyVipExpAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifyVipExpAsk)
    private static final com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk();
    }

    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifyVipExpAsk>
        PARSER = new com.google.protobuf.AbstractParser<ModifyVipExpAsk>() {
      @java.lang.Override
      public ModifyVipExpAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifyVipExpAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifyVipExpAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifyVipExpAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ModifyVipExpAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifyVipExpAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifyVipExpAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前等级
     * </pre>
     *
     * <code>optional int32 beforeLevel = 1;</code>
     * @return Whether the beforeLevel field is set.
     */
    boolean hasBeforeLevel();
    /**
     * <pre>
     * 修改前等级
     * </pre>
     *
     * <code>optional int32 beforeLevel = 1;</code>
     * @return The beforeLevel.
     */
    int getBeforeLevel();

    /**
     * <pre>
     * 修改后等级
     * </pre>
     *
     * <code>optional int32 afterLevel = 2;</code>
     * @return Whether the afterLevel field is set.
     */
    boolean hasAfterLevel();
    /**
     * <pre>
     * 修改后等级
     * </pre>
     *
     * <code>optional int32 afterLevel = 2;</code>
     * @return The afterLevel.
     */
    int getAfterLevel();

    /**
     * <pre>
     * 修改前经验
     * </pre>
     *
     * <code>optional int32 beforeExp = 3;</code>
     * @return Whether the beforeExp field is set.
     */
    boolean hasBeforeExp();
    /**
     * <pre>
     * 修改前经验
     * </pre>
     *
     * <code>optional int32 beforeExp = 3;</code>
     * @return The beforeExp.
     */
    int getBeforeExp();

    /**
     * <pre>
     * 修改后经验
     * </pre>
     *
     * <code>optional int32 afterExp = 4;</code>
     * @return Whether the afterExp field is set.
     */
    boolean hasAfterExp();
    /**
     * <pre>
     * 修改后经验
     * </pre>
     *
     * <code>optional int32 afterExp = 4;</code>
     * @return The afterExp.
     */
    int getAfterExp();

    /**
     * <pre>
     * 异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 5;</code>
     * @return Whether the exceptionId field is set.
     */
    boolean hasExceptionId();
    /**
     * <pre>
     * 异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 5;</code>
     * @return The exceptionId.
     */
    int getExceptionId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifyVipExpAns}
   */
  public static final class ModifyVipExpAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifyVipExpAns)
      ModifyVipExpAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifyVipExpAns.newBuilder() to construct.
    private ModifyVipExpAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifyVipExpAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifyVipExpAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifyVipExpAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeLevel_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterLevel_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              beforeExp_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              afterExp_ = input.readInt32();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              exceptionId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.class, com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFORELEVEL_FIELD_NUMBER = 1;
    private int beforeLevel_;
    /**
     * <pre>
     * 修改前等级
     * </pre>
     *
     * <code>optional int32 beforeLevel = 1;</code>
     * @return Whether the beforeLevel field is set.
     */
    @java.lang.Override
    public boolean hasBeforeLevel() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前等级
     * </pre>
     *
     * <code>optional int32 beforeLevel = 1;</code>
     * @return The beforeLevel.
     */
    @java.lang.Override
    public int getBeforeLevel() {
      return beforeLevel_;
    }

    public static final int AFTERLEVEL_FIELD_NUMBER = 2;
    private int afterLevel_;
    /**
     * <pre>
     * 修改后等级
     * </pre>
     *
     * <code>optional int32 afterLevel = 2;</code>
     * @return Whether the afterLevel field is set.
     */
    @java.lang.Override
    public boolean hasAfterLevel() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后等级
     * </pre>
     *
     * <code>optional int32 afterLevel = 2;</code>
     * @return The afterLevel.
     */
    @java.lang.Override
    public int getAfterLevel() {
      return afterLevel_;
    }

    public static final int BEFOREEXP_FIELD_NUMBER = 3;
    private int beforeExp_;
    /**
     * <pre>
     * 修改前经验
     * </pre>
     *
     * <code>optional int32 beforeExp = 3;</code>
     * @return Whether the beforeExp field is set.
     */
    @java.lang.Override
    public boolean hasBeforeExp() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 修改前经验
     * </pre>
     *
     * <code>optional int32 beforeExp = 3;</code>
     * @return The beforeExp.
     */
    @java.lang.Override
    public int getBeforeExp() {
      return beforeExp_;
    }

    public static final int AFTEREXP_FIELD_NUMBER = 4;
    private int afterExp_;
    /**
     * <pre>
     * 修改后经验
     * </pre>
     *
     * <code>optional int32 afterExp = 4;</code>
     * @return Whether the afterExp field is set.
     */
    @java.lang.Override
    public boolean hasAfterExp() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 修改后经验
     * </pre>
     *
     * <code>optional int32 afterExp = 4;</code>
     * @return The afterExp.
     */
    @java.lang.Override
    public int getAfterExp() {
      return afterExp_;
    }

    public static final int EXCEPTIONID_FIELD_NUMBER = 5;
    private int exceptionId_;
    /**
     * <pre>
     * 异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 5;</code>
     * @return Whether the exceptionId field is set.
     */
    @java.lang.Override
    public boolean hasExceptionId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 5;</code>
     * @return The exceptionId.
     */
    @java.lang.Override
    public int getExceptionId() {
      return exceptionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, beforeLevel_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, afterLevel_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, beforeExp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, afterExp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt32(5, exceptionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beforeLevel_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, afterLevel_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, beforeExp_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, afterExp_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(5, exceptionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ModifyVipExpAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ModifyVipExpAns other = (com.yorha.proto.SsPlayerIdip.ModifyVipExpAns) obj;

      if (hasBeforeLevel() != other.hasBeforeLevel()) return false;
      if (hasBeforeLevel()) {
        if (getBeforeLevel()
            != other.getBeforeLevel()) return false;
      }
      if (hasAfterLevel() != other.hasAfterLevel()) return false;
      if (hasAfterLevel()) {
        if (getAfterLevel()
            != other.getAfterLevel()) return false;
      }
      if (hasBeforeExp() != other.hasBeforeExp()) return false;
      if (hasBeforeExp()) {
        if (getBeforeExp()
            != other.getBeforeExp()) return false;
      }
      if (hasAfterExp() != other.hasAfterExp()) return false;
      if (hasAfterExp()) {
        if (getAfterExp()
            != other.getAfterExp()) return false;
      }
      if (hasExceptionId() != other.hasExceptionId()) return false;
      if (hasExceptionId()) {
        if (getExceptionId()
            != other.getExceptionId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeLevel()) {
        hash = (37 * hash) + BEFORELEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeLevel();
      }
      if (hasAfterLevel()) {
        hash = (37 * hash) + AFTERLEVEL_FIELD_NUMBER;
        hash = (53 * hash) + getAfterLevel();
      }
      if (hasBeforeExp()) {
        hash = (37 * hash) + BEFOREEXP_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeExp();
      }
      if (hasAfterExp()) {
        hash = (37 * hash) + AFTEREXP_FIELD_NUMBER;
        hash = (53 * hash) + getAfterExp();
      }
      if (hasExceptionId()) {
        hash = (37 * hash) + EXCEPTIONID_FIELD_NUMBER;
        hash = (53 * hash) + getExceptionId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ModifyVipExpAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifyVipExpAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifyVipExpAns)
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.class, com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterLevel_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        beforeExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        afterExp_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        exceptionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifyVipExpAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAns build() {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifyVipExpAns buildPartial() {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAns result = new com.yorha.proto.SsPlayerIdip.ModifyVipExpAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeLevel_ = beforeLevel_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterLevel_ = afterLevel_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.beforeExp_ = beforeExp_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.afterExp_ = afterExp_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.exceptionId_ = exceptionId_;
          to_bitField0_ |= 0x00000010;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ModifyVipExpAns) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ModifyVipExpAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ModifyVipExpAns other) {
        if (other == com.yorha.proto.SsPlayerIdip.ModifyVipExpAns.getDefaultInstance()) return this;
        if (other.hasBeforeLevel()) {
          setBeforeLevel(other.getBeforeLevel());
        }
        if (other.hasAfterLevel()) {
          setAfterLevel(other.getAfterLevel());
        }
        if (other.hasBeforeExp()) {
          setBeforeExp(other.getBeforeExp());
        }
        if (other.hasAfterExp()) {
          setAfterExp(other.getAfterExp());
        }
        if (other.hasExceptionId()) {
          setExceptionId(other.getExceptionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ModifyVipExpAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ModifyVipExpAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beforeLevel_ ;
      /**
       * <pre>
       * 修改前等级
       * </pre>
       *
       * <code>optional int32 beforeLevel = 1;</code>
       * @return Whether the beforeLevel field is set.
       */
      @java.lang.Override
      public boolean hasBeforeLevel() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前等级
       * </pre>
       *
       * <code>optional int32 beforeLevel = 1;</code>
       * @return The beforeLevel.
       */
      @java.lang.Override
      public int getBeforeLevel() {
        return beforeLevel_;
      }
      /**
       * <pre>
       * 修改前等级
       * </pre>
       *
       * <code>optional int32 beforeLevel = 1;</code>
       * @param value The beforeLevel to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeLevel(int value) {
        bitField0_ |= 0x00000001;
        beforeLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前等级
       * </pre>
       *
       * <code>optional int32 beforeLevel = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeLevel() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeLevel_ = 0;
        onChanged();
        return this;
      }

      private int afterLevel_ ;
      /**
       * <pre>
       * 修改后等级
       * </pre>
       *
       * <code>optional int32 afterLevel = 2;</code>
       * @return Whether the afterLevel field is set.
       */
      @java.lang.Override
      public boolean hasAfterLevel() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后等级
       * </pre>
       *
       * <code>optional int32 afterLevel = 2;</code>
       * @return The afterLevel.
       */
      @java.lang.Override
      public int getAfterLevel() {
        return afterLevel_;
      }
      /**
       * <pre>
       * 修改后等级
       * </pre>
       *
       * <code>optional int32 afterLevel = 2;</code>
       * @param value The afterLevel to set.
       * @return This builder for chaining.
       */
      public Builder setAfterLevel(int value) {
        bitField0_ |= 0x00000002;
        afterLevel_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后等级
       * </pre>
       *
       * <code>optional int32 afterLevel = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterLevel() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterLevel_ = 0;
        onChanged();
        return this;
      }

      private int beforeExp_ ;
      /**
       * <pre>
       * 修改前经验
       * </pre>
       *
       * <code>optional int32 beforeExp = 3;</code>
       * @return Whether the beforeExp field is set.
       */
      @java.lang.Override
      public boolean hasBeforeExp() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 修改前经验
       * </pre>
       *
       * <code>optional int32 beforeExp = 3;</code>
       * @return The beforeExp.
       */
      @java.lang.Override
      public int getBeforeExp() {
        return beforeExp_;
      }
      /**
       * <pre>
       * 修改前经验
       * </pre>
       *
       * <code>optional int32 beforeExp = 3;</code>
       * @param value The beforeExp to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeExp(int value) {
        bitField0_ |= 0x00000004;
        beforeExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前经验
       * </pre>
       *
       * <code>optional int32 beforeExp = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeExp() {
        bitField0_ = (bitField0_ & ~0x00000004);
        beforeExp_ = 0;
        onChanged();
        return this;
      }

      private int afterExp_ ;
      /**
       * <pre>
       * 修改后经验
       * </pre>
       *
       * <code>optional int32 afterExp = 4;</code>
       * @return Whether the afterExp field is set.
       */
      @java.lang.Override
      public boolean hasAfterExp() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 修改后经验
       * </pre>
       *
       * <code>optional int32 afterExp = 4;</code>
       * @return The afterExp.
       */
      @java.lang.Override
      public int getAfterExp() {
        return afterExp_;
      }
      /**
       * <pre>
       * 修改后经验
       * </pre>
       *
       * <code>optional int32 afterExp = 4;</code>
       * @param value The afterExp to set.
       * @return This builder for chaining.
       */
      public Builder setAfterExp(int value) {
        bitField0_ |= 0x00000008;
        afterExp_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后经验
       * </pre>
       *
       * <code>optional int32 afterExp = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterExp() {
        bitField0_ = (bitField0_ & ~0x00000008);
        afterExp_ = 0;
        onChanged();
        return this;
      }

      private int exceptionId_ ;
      /**
       * <pre>
       * 异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 5;</code>
       * @return Whether the exceptionId field is set.
       */
      @java.lang.Override
      public boolean hasExceptionId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 5;</code>
       * @return The exceptionId.
       */
      @java.lang.Override
      public int getExceptionId() {
        return exceptionId_;
      }
      /**
       * <pre>
       * 异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 5;</code>
       * @param value The exceptionId to set.
       * @return This builder for chaining.
       */
      public Builder setExceptionId(int value) {
        bitField0_ |= 0x00000010;
        exceptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearExceptionId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        exceptionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifyVipExpAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifyVipExpAns)
    private static final com.yorha.proto.SsPlayerIdip.ModifyVipExpAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ModifyVipExpAns();
    }

    public static com.yorha.proto.SsPlayerIdip.ModifyVipExpAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifyVipExpAns>
        PARSER = new com.google.protobuf.AbstractParser<ModifyVipExpAns>() {
      @java.lang.Override
      public ModifyVipExpAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifyVipExpAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifyVipExpAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifyVipExpAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ModifyVipExpAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifySoldierAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifySoldierAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    boolean hasSoldierId();
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    int getSoldierId();

    /**
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    boolean hasValue();
    /**
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    int getValue();

    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    boolean hasPlayerId();
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    long getPlayerId();

    /**
     * <code>optional string openId = 4;</code>
     * @return Whether the openId field is set.
     */
    boolean hasOpenId();
    /**
     * <code>optional string openId = 4;</code>
     * @return The openId.
     */
    java.lang.String getOpenId();
    /**
     * <code>optional string openId = 4;</code>
     * @return The bytes for openId.
     */
    com.google.protobuf.ByteString
        getOpenIdBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifySoldierAsk}
   */
  public static final class ModifySoldierAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifySoldierAsk)
      ModifySoldierAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifySoldierAsk.newBuilder() to construct.
    private ModifySoldierAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifySoldierAsk() {
      openId_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifySoldierAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifySoldierAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              soldierId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              value_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              playerId_ = input.readInt64();
              break;
            }
            case 34: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              openId_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.class, com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.Builder.class);
    }

    private int bitField0_;
    public static final int SOLDIERID_FIELD_NUMBER = 1;
    private int soldierId_;
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return Whether the soldierId field is set.
     */
    @java.lang.Override
    public boolean hasSoldierId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 soldierId = 1;</code>
     * @return The soldierId.
     */
    @java.lang.Override
    public int getSoldierId() {
      return soldierId_;
    }

    public static final int VALUE_FIELD_NUMBER = 2;
    private int value_;
    /**
     * <code>optional int32 value = 2;</code>
     * @return Whether the value field is set.
     */
    @java.lang.Override
    public boolean hasValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 value = 2;</code>
     * @return The value.
     */
    @java.lang.Override
    public int getValue() {
      return value_;
    }

    public static final int PLAYERID_FIELD_NUMBER = 3;
    private long playerId_;
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return Whether the playerId field is set.
     */
    @java.lang.Override
    public boolean hasPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional int64 playerId = 3;</code>
     * @return The playerId.
     */
    @java.lang.Override
    public long getPlayerId() {
      return playerId_;
    }

    public static final int OPENID_FIELD_NUMBER = 4;
    private volatile java.lang.Object openId_;
    /**
     * <code>optional string openId = 4;</code>
     * @return Whether the openId field is set.
     */
    @java.lang.Override
    public boolean hasOpenId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <code>optional string openId = 4;</code>
     * @return The openId.
     */
    @java.lang.Override
    public java.lang.String getOpenId() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          openId_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string openId = 4;</code>
     * @return The bytes for openId.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getOpenIdBytes() {
      java.lang.Object ref = openId_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        openId_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, value_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 4, openId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, soldierId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, value_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, playerId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(4, openId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ModifySoldierAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ModifySoldierAsk other = (com.yorha.proto.SsPlayerIdip.ModifySoldierAsk) obj;

      if (hasSoldierId() != other.hasSoldierId()) return false;
      if (hasSoldierId()) {
        if (getSoldierId()
            != other.getSoldierId()) return false;
      }
      if (hasValue() != other.hasValue()) return false;
      if (hasValue()) {
        if (getValue()
            != other.getValue()) return false;
      }
      if (hasPlayerId() != other.hasPlayerId()) return false;
      if (hasPlayerId()) {
        if (getPlayerId()
            != other.getPlayerId()) return false;
      }
      if (hasOpenId() != other.hasOpenId()) return false;
      if (hasOpenId()) {
        if (!getOpenId()
            .equals(other.getOpenId())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasSoldierId()) {
        hash = (37 * hash) + SOLDIERID_FIELD_NUMBER;
        hash = (53 * hash) + getSoldierId();
      }
      if (hasValue()) {
        hash = (37 * hash) + VALUE_FIELD_NUMBER;
        hash = (53 * hash) + getValue();
      }
      if (hasPlayerId()) {
        hash = (37 * hash) + PLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getPlayerId());
      }
      if (hasOpenId()) {
        hash = (37 * hash) + OPENID_FIELD_NUMBER;
        hash = (53 * hash) + getOpenId().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ModifySoldierAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifySoldierAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifySoldierAsk)
        com.yorha.proto.SsPlayerIdip.ModifySoldierAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.class, com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        soldierId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        value_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        playerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        openId_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAsk build() {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAsk buildPartial() {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAsk result = new com.yorha.proto.SsPlayerIdip.ModifySoldierAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.soldierId_ = soldierId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.value_ = value_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.playerId_ = playerId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.openId_ = openId_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ModifySoldierAsk) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ModifySoldierAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ModifySoldierAsk other) {
        if (other == com.yorha.proto.SsPlayerIdip.ModifySoldierAsk.getDefaultInstance()) return this;
        if (other.hasSoldierId()) {
          setSoldierId(other.getSoldierId());
        }
        if (other.hasValue()) {
          setValue(other.getValue());
        }
        if (other.hasPlayerId()) {
          setPlayerId(other.getPlayerId());
        }
        if (other.hasOpenId()) {
          bitField0_ |= 0x00000008;
          openId_ = other.openId_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ModifySoldierAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int soldierId_ ;
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return Whether the soldierId field is set.
       */
      @java.lang.Override
      public boolean hasSoldierId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return The soldierId.
       */
      @java.lang.Override
      public int getSoldierId() {
        return soldierId_;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @param value The soldierId to set.
       * @return This builder for chaining.
       */
      public Builder setSoldierId(int value) {
        bitField0_ |= 0x00000001;
        soldierId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 soldierId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearSoldierId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        soldierId_ = 0;
        onChanged();
        return this;
      }

      private int value_ ;
      /**
       * <code>optional int32 value = 2;</code>
       * @return Whether the value field is set.
       */
      @java.lang.Override
      public boolean hasValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @return The value.
       */
      @java.lang.Override
      public int getValue() {
        return value_;
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @param value The value to set.
       * @return This builder for chaining.
       */
      public Builder setValue(int value) {
        bitField0_ |= 0x00000002;
        value_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 value = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        value_ = 0;
        onChanged();
        return this;
      }

      private long playerId_ ;
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return Whether the playerId field is set.
       */
      @java.lang.Override
      public boolean hasPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return The playerId.
       */
      @java.lang.Override
      public long getPlayerId() {
        return playerId_;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @param value The playerId to set.
       * @return This builder for chaining.
       */
      public Builder setPlayerId(long value) {
        bitField0_ |= 0x00000004;
        playerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 playerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        playerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object openId_ = "";
      /**
       * <code>optional string openId = 4;</code>
       * @return Whether the openId field is set.
       */
      public boolean hasOpenId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return The openId.
       */
      public java.lang.String getOpenId() {
        java.lang.Object ref = openId_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            openId_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return The bytes for openId.
       */
      public com.google.protobuf.ByteString
          getOpenIdBytes() {
        java.lang.Object ref = openId_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          openId_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string openId = 4;</code>
       * @param value The openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenId(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        openId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOpenId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        openId_ = getDefaultInstance().getOpenId();
        onChanged();
        return this;
      }
      /**
       * <code>optional string openId = 4;</code>
       * @param value The bytes for openId to set.
       * @return This builder for chaining.
       */
      public Builder setOpenIdBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        openId_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifySoldierAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifySoldierAsk)
    private static final com.yorha.proto.SsPlayerIdip.ModifySoldierAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ModifySoldierAsk();
    }

    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifySoldierAsk>
        PARSER = new com.google.protobuf.AbstractParser<ModifySoldierAsk>() {
      @java.lang.Override
      public ModifySoldierAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifySoldierAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifySoldierAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifySoldierAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ModifySoldierAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ModifySoldierAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ModifySoldierAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    boolean hasBeforeValue();
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    int getBeforeValue();

    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    boolean hasAfterValue();
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    int getAfterValue();

    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    boolean hasExceptionId();
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    int getExceptionId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ModifySoldierAns}
   */
  public static final class ModifySoldierAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ModifySoldierAns)
      ModifySoldierAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ModifySoldierAns.newBuilder() to construct.
    private ModifySoldierAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ModifySoldierAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ModifySoldierAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ModifySoldierAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              beforeValue_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              afterValue_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              exceptionId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsPlayerIdip.ModifySoldierAns.class, com.yorha.proto.SsPlayerIdip.ModifySoldierAns.Builder.class);
    }

    private int bitField0_;
    public static final int BEFOREVALUE_FIELD_NUMBER = 1;
    private int beforeValue_;
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return Whether the beforeValue field is set.
     */
    @java.lang.Override
    public boolean hasBeforeValue() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 修改前数量
     * </pre>
     *
     * <code>optional int32 beforeValue = 1;</code>
     * @return The beforeValue.
     */
    @java.lang.Override
    public int getBeforeValue() {
      return beforeValue_;
    }

    public static final int AFTERVALUE_FIELD_NUMBER = 2;
    private int afterValue_;
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return Whether the afterValue field is set.
     */
    @java.lang.Override
    public boolean hasAfterValue() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 修改后数量
     * </pre>
     *
     * <code>optional int32 afterValue = 2;</code>
     * @return The afterValue.
     */
    @java.lang.Override
    public int getAfterValue() {
      return afterValue_;
    }

    public static final int EXCEPTIONID_FIELD_NUMBER = 3;
    private int exceptionId_;
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return Whether the exceptionId field is set.
     */
    @java.lang.Override
    public boolean hasExceptionId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 特殊异常
     * </pre>
     *
     * <code>optional int32 exceptionId = 3;</code>
     * @return The exceptionId.
     */
    @java.lang.Override
    public int getExceptionId() {
      return exceptionId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, exceptionId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, beforeValue_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, afterValue_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, exceptionId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsPlayerIdip.ModifySoldierAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsPlayerIdip.ModifySoldierAns other = (com.yorha.proto.SsPlayerIdip.ModifySoldierAns) obj;

      if (hasBeforeValue() != other.hasBeforeValue()) return false;
      if (hasBeforeValue()) {
        if (getBeforeValue()
            != other.getBeforeValue()) return false;
      }
      if (hasAfterValue() != other.hasAfterValue()) return false;
      if (hasAfterValue()) {
        if (getAfterValue()
            != other.getAfterValue()) return false;
      }
      if (hasExceptionId() != other.hasExceptionId()) return false;
      if (hasExceptionId()) {
        if (getExceptionId()
            != other.getExceptionId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasBeforeValue()) {
        hash = (37 * hash) + BEFOREVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeValue();
      }
      if (hasAfterValue()) {
        hash = (37 * hash) + AFTERVALUE_FIELD_NUMBER;
        hash = (53 * hash) + getAfterValue();
      }
      if (hasExceptionId()) {
        hash = (37 * hash) + EXCEPTIONID_FIELD_NUMBER;
        hash = (53 * hash) + getExceptionId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsPlayerIdip.ModifySoldierAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ModifySoldierAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ModifySoldierAns)
        com.yorha.proto.SsPlayerIdip.ModifySoldierAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsPlayerIdip.ModifySoldierAns.class, com.yorha.proto.SsPlayerIdip.ModifySoldierAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsPlayerIdip.ModifySoldierAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        beforeValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        afterValue_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        exceptionId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsPlayerIdip.internal_static_com_yorha_proto_ModifySoldierAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAns getDefaultInstanceForType() {
        return com.yorha.proto.SsPlayerIdip.ModifySoldierAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAns build() {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsPlayerIdip.ModifySoldierAns buildPartial() {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAns result = new com.yorha.proto.SsPlayerIdip.ModifySoldierAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.beforeValue_ = beforeValue_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.afterValue_ = afterValue_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.exceptionId_ = exceptionId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsPlayerIdip.ModifySoldierAns) {
          return mergeFrom((com.yorha.proto.SsPlayerIdip.ModifySoldierAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsPlayerIdip.ModifySoldierAns other) {
        if (other == com.yorha.proto.SsPlayerIdip.ModifySoldierAns.getDefaultInstance()) return this;
        if (other.hasBeforeValue()) {
          setBeforeValue(other.getBeforeValue());
        }
        if (other.hasAfterValue()) {
          setAfterValue(other.getAfterValue());
        }
        if (other.hasExceptionId()) {
          setExceptionId(other.getExceptionId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsPlayerIdip.ModifySoldierAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsPlayerIdip.ModifySoldierAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int beforeValue_ ;
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return Whether the beforeValue field is set.
       */
      @java.lang.Override
      public boolean hasBeforeValue() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return The beforeValue.
       */
      @java.lang.Override
      public int getBeforeValue() {
        return beforeValue_;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @param value The beforeValue to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeValue(int value) {
        bitField0_ |= 0x00000001;
        beforeValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改前数量
       * </pre>
       *
       * <code>optional int32 beforeValue = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeValue() {
        bitField0_ = (bitField0_ & ~0x00000001);
        beforeValue_ = 0;
        onChanged();
        return this;
      }

      private int afterValue_ ;
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return Whether the afterValue field is set.
       */
      @java.lang.Override
      public boolean hasAfterValue() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return The afterValue.
       */
      @java.lang.Override
      public int getAfterValue() {
        return afterValue_;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @param value The afterValue to set.
       * @return This builder for chaining.
       */
      public Builder setAfterValue(int value) {
        bitField0_ |= 0x00000002;
        afterValue_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 修改后数量
       * </pre>
       *
       * <code>optional int32 afterValue = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearAfterValue() {
        bitField0_ = (bitField0_ & ~0x00000002);
        afterValue_ = 0;
        onChanged();
        return this;
      }

      private int exceptionId_ ;
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return Whether the exceptionId field is set.
       */
      @java.lang.Override
      public boolean hasExceptionId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return The exceptionId.
       */
      @java.lang.Override
      public int getExceptionId() {
        return exceptionId_;
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @param value The exceptionId to set.
       * @return This builder for chaining.
       */
      public Builder setExceptionId(int value) {
        bitField0_ |= 0x00000004;
        exceptionId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 特殊异常
       * </pre>
       *
       * <code>optional int32 exceptionId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearExceptionId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        exceptionId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ModifySoldierAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ModifySoldierAns)
    private static final com.yorha.proto.SsPlayerIdip.ModifySoldierAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsPlayerIdip.ModifySoldierAns();
    }

    public static com.yorha.proto.SsPlayerIdip.ModifySoldierAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ModifySoldierAns>
        PARSER = new com.google.protobuf.AbstractParser<ModifySoldierAns>() {
      @java.lang.Override
      public ModifySoldierAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ModifySoldierAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ModifySoldierAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ModifySoldierAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsPlayerIdip.ModifySoldierAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ResourceAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ResourceAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ResourceAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ResourceAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConsumeDiamondAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConsumeDiamondAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConsumeItemsAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConsumeItemsAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConsumeItemsAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_PullMidasCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_PullMidasCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifyVipExpAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifyVipExpAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifyVipExpAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifySoldierAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifySoldierAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ModifySoldierAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ModifySoldierAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/player/ss/ss_player_idip." +
      "proto\022\017com.yorha.proto\032%ss_proto/gen/com" +
      "mon/common_enum.proto\"a\n\013ResourceAsk\022\016\n\006" +
      "openId\030\001 \001(\t\0223\n\014currencyType\030\002 \001(\0162\035.com" +
      ".yorha.proto.CurrencyType\022\r\n\005value\030\003 \001(\005" +
      "\"K\n\013ResourceAns\022\023\n\013beforeValue\030\001 \001(\003\022\022\n\n" +
      "afterValue\030\002 \001(\003\022\023\n\013exceptionId\030\003 \001(\005\"5\n" +
      "\021ConsumeDiamondAsk\022\020\n\010fixValue\030\001 \001(\005\022\016\n\006" +
      "openId\030\002 \001(\t\"<\n\021ConsumeDiamondAns\022\023\n\013bef" +
      "oreValue\030\001 \001(\005\022\022\n\nafterValue\030\002 \001(\005\"B\n\017Co" +
      "nsumeItemsAsk\022\016\n\006itemId\030\001 \001(\005\022\017\n\007itemNum" +
      "\030\002 \001(\005\022\016\n\006openId\030\003 \001(\t\":\n\017ConsumeItemsAn" +
      "s\022\023\n\013beforeValue\030\001 \001(\005\022\022\n\nafterValue\030\002 \001" +
      "(\005\"4\n\014PullMidasCmd\022\021\n\tchargeSer\030\001 \001(\t\022\021\n" +
      "\tchargeNum\030\002 \001(\005\"0\n\017ModifyVipExpAsk\022\016\n\006o" +
      "penId\030\001 \001(\t\022\r\n\005value\030\002 \001(\005\"t\n\017ModifyVipE" +
      "xpAns\022\023\n\013beforeLevel\030\001 \001(\005\022\022\n\nafterLevel" +
      "\030\002 \001(\005\022\021\n\tbeforeExp\030\003 \001(\005\022\020\n\010afterExp\030\004 " +
      "\001(\005\022\023\n\013exceptionId\030\005 \001(\005\"V\n\020ModifySoldie" +
      "rAsk\022\021\n\tsoldierId\030\001 \001(\005\022\r\n\005value\030\002 \001(\005\022\020" +
      "\n\010playerId\030\003 \001(\003\022\016\n\006openId\030\004 \001(\t\"P\n\020Modi" +
      "fySoldierAns\022\023\n\013beforeValue\030\001 \001(\005\022\022\n\naft" +
      "erValue\030\002 \001(\005\022\023\n\013exceptionId\030\003 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_ResourceAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ResourceAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ResourceAsk_descriptor,
        new java.lang.String[] { "OpenId", "CurrencyType", "Value", });
    internal_static_com_yorha_proto_ResourceAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_ResourceAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ResourceAns_descriptor,
        new java.lang.String[] { "BeforeValue", "AfterValue", "ExceptionId", });
    internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ConsumeDiamondAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConsumeDiamondAsk_descriptor,
        new java.lang.String[] { "FixValue", "OpenId", });
    internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_ConsumeDiamondAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConsumeDiamondAns_descriptor,
        new java.lang.String[] { "BeforeValue", "AfterValue", });
    internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_ConsumeItemsAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConsumeItemsAsk_descriptor,
        new java.lang.String[] { "ItemId", "ItemNum", "OpenId", });
    internal_static_com_yorha_proto_ConsumeItemsAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_ConsumeItemsAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConsumeItemsAns_descriptor,
        new java.lang.String[] { "BeforeValue", "AfterValue", });
    internal_static_com_yorha_proto_PullMidasCmd_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_PullMidasCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_PullMidasCmd_descriptor,
        new java.lang.String[] { "ChargeSer", "ChargeNum", });
    internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_ModifyVipExpAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifyVipExpAsk_descriptor,
        new java.lang.String[] { "OpenId", "Value", });
    internal_static_com_yorha_proto_ModifyVipExpAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_ModifyVipExpAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifyVipExpAns_descriptor,
        new java.lang.String[] { "BeforeLevel", "AfterLevel", "BeforeExp", "AfterExp", "ExceptionId", });
    internal_static_com_yorha_proto_ModifySoldierAsk_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_ModifySoldierAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifySoldierAsk_descriptor,
        new java.lang.String[] { "SoldierId", "Value", "PlayerId", "OpenId", });
    internal_static_com_yorha_proto_ModifySoldierAns_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_ModifySoldierAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ModifySoldierAns_descriptor,
        new java.lang.String[] { "BeforeValue", "AfterValue", "ExceptionId", });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_kingdom.proto

package com.yorha.proto;

public final class SsSceneKingdom {
  private SsSceneKingdom() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface KingAppointAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingAppointAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 发起任命的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return Whether the operatorId field is set.
     */
    boolean hasOperatorId();
    /**
     * <pre>
     * 发起任命的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return The operatorId.
     */
    long getOperatorId();

    /**
     * <pre>
     * 发起任命的玩家职位id
     * </pre>
     *
     * <code>optional int32 operatorOfficeId = 2;</code>
     * @return Whether the operatorOfficeId field is set.
     */
    boolean hasOperatorOfficeId();
    /**
     * <pre>
     * 发起任命的玩家职位id
     * </pre>
     *
     * <code>optional int32 operatorOfficeId = 2;</code>
     * @return The operatorOfficeId.
     */
    int getOperatorOfficeId();

    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 3;</code>
     * @return Whether the kingdomOfficeId field is set.
     */
    boolean hasKingdomOfficeId();
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 3;</code>
     * @return The kingdomOfficeId.
     */
    int getKingdomOfficeId();

    /**
     * <pre>
     * 被任命的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 4;</code>
     * @return Whether the toPlayerId field is set.
     */
    boolean hasToPlayerId();
    /**
     * <pre>
     * 被任命的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 4;</code>
     * @return The toPlayerId.
     */
    long getToPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingAppointAsk}
   */
  public static final class KingAppointAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingAppointAsk)
      KingAppointAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingAppointAsk.newBuilder() to construct.
    private KingAppointAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingAppointAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingAppointAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingAppointAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              operatorId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              operatorOfficeId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              kingdomOfficeId_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              toPlayerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingAppointAsk.class, com.yorha.proto.SsSceneKingdom.KingAppointAsk.Builder.class);
    }

    private int bitField0_;
    public static final int OPERATORID_FIELD_NUMBER = 1;
    private long operatorId_;
    /**
     * <pre>
     * 发起任命的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return Whether the operatorId field is set.
     */
    @java.lang.Override
    public boolean hasOperatorId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 发起任命的玩家id
     * </pre>
     *
     * <code>optional int64 operatorId = 1;</code>
     * @return The operatorId.
     */
    @java.lang.Override
    public long getOperatorId() {
      return operatorId_;
    }

    public static final int OPERATOROFFICEID_FIELD_NUMBER = 2;
    private int operatorOfficeId_;
    /**
     * <pre>
     * 发起任命的玩家职位id
     * </pre>
     *
     * <code>optional int32 operatorOfficeId = 2;</code>
     * @return Whether the operatorOfficeId field is set.
     */
    @java.lang.Override
    public boolean hasOperatorOfficeId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 发起任命的玩家职位id
     * </pre>
     *
     * <code>optional int32 operatorOfficeId = 2;</code>
     * @return The operatorOfficeId.
     */
    @java.lang.Override
    public int getOperatorOfficeId() {
      return operatorOfficeId_;
    }

    public static final int KINGDOMOFFICEID_FIELD_NUMBER = 3;
    private int kingdomOfficeId_;
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 3;</code>
     * @return Whether the kingdomOfficeId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomOfficeId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 职位id
     * </pre>
     *
     * <code>optional int32 kingdomOfficeId = 3;</code>
     * @return The kingdomOfficeId.
     */
    @java.lang.Override
    public int getKingdomOfficeId() {
      return kingdomOfficeId_;
    }

    public static final int TOPLAYERID_FIELD_NUMBER = 4;
    private long toPlayerId_;
    /**
     * <pre>
     * 被任命的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 4;</code>
     * @return Whether the toPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasToPlayerId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 被任命的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 4;</code>
     * @return The toPlayerId.
     */
    @java.lang.Override
    public long getToPlayerId() {
      return toPlayerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, operatorId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, operatorOfficeId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, kingdomOfficeId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, toPlayerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, operatorId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, operatorOfficeId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, kingdomOfficeId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, toPlayerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingAppointAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingAppointAsk other = (com.yorha.proto.SsSceneKingdom.KingAppointAsk) obj;

      if (hasOperatorId() != other.hasOperatorId()) return false;
      if (hasOperatorId()) {
        if (getOperatorId()
            != other.getOperatorId()) return false;
      }
      if (hasOperatorOfficeId() != other.hasOperatorOfficeId()) return false;
      if (hasOperatorOfficeId()) {
        if (getOperatorOfficeId()
            != other.getOperatorOfficeId()) return false;
      }
      if (hasKingdomOfficeId() != other.hasKingdomOfficeId()) return false;
      if (hasKingdomOfficeId()) {
        if (getKingdomOfficeId()
            != other.getKingdomOfficeId()) return false;
      }
      if (hasToPlayerId() != other.hasToPlayerId()) return false;
      if (hasToPlayerId()) {
        if (getToPlayerId()
            != other.getToPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOperatorId()) {
        hash = (37 * hash) + OPERATORID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOperatorId());
      }
      if (hasOperatorOfficeId()) {
        hash = (37 * hash) + OPERATOROFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getOperatorOfficeId();
      }
      if (hasKingdomOfficeId()) {
        hash = (37 * hash) + KINGDOMOFFICEID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomOfficeId();
      }
      if (hasToPlayerId()) {
        hash = (37 * hash) + TOPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingAppointAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingAppointAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingAppointAsk)
        com.yorha.proto.SsSceneKingdom.KingAppointAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingAppointAsk.class, com.yorha.proto.SsSceneKingdom.KingAppointAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingAppointAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        operatorId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        operatorOfficeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        kingdomOfficeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        toPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingAppointAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAsk build() {
        com.yorha.proto.SsSceneKingdom.KingAppointAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingAppointAsk result = new com.yorha.proto.SsSceneKingdom.KingAppointAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.operatorId_ = operatorId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.operatorOfficeId_ = operatorOfficeId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.kingdomOfficeId_ = kingdomOfficeId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.toPlayerId_ = toPlayerId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingAppointAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingAppointAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingAppointAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingAppointAsk.getDefaultInstance()) return this;
        if (other.hasOperatorId()) {
          setOperatorId(other.getOperatorId());
        }
        if (other.hasOperatorOfficeId()) {
          setOperatorOfficeId(other.getOperatorOfficeId());
        }
        if (other.hasKingdomOfficeId()) {
          setKingdomOfficeId(other.getKingdomOfficeId());
        }
        if (other.hasToPlayerId()) {
          setToPlayerId(other.getToPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingAppointAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingAppointAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long operatorId_ ;
      /**
       * <pre>
       * 发起任命的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return Whether the operatorId field is set.
       */
      @java.lang.Override
      public boolean hasOperatorId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 发起任命的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return The operatorId.
       */
      @java.lang.Override
      public long getOperatorId() {
        return operatorId_;
      }
      /**
       * <pre>
       * 发起任命的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @param value The operatorId to set.
       * @return This builder for chaining.
       */
      public Builder setOperatorId(long value) {
        bitField0_ |= 0x00000001;
        operatorId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发起任命的玩家id
       * </pre>
       *
       * <code>optional int64 operatorId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperatorId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        operatorId_ = 0L;
        onChanged();
        return this;
      }

      private int operatorOfficeId_ ;
      /**
       * <pre>
       * 发起任命的玩家职位id
       * </pre>
       *
       * <code>optional int32 operatorOfficeId = 2;</code>
       * @return Whether the operatorOfficeId field is set.
       */
      @java.lang.Override
      public boolean hasOperatorOfficeId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 发起任命的玩家职位id
       * </pre>
       *
       * <code>optional int32 operatorOfficeId = 2;</code>
       * @return The operatorOfficeId.
       */
      @java.lang.Override
      public int getOperatorOfficeId() {
        return operatorOfficeId_;
      }
      /**
       * <pre>
       * 发起任命的玩家职位id
       * </pre>
       *
       * <code>optional int32 operatorOfficeId = 2;</code>
       * @param value The operatorOfficeId to set.
       * @return This builder for chaining.
       */
      public Builder setOperatorOfficeId(int value) {
        bitField0_ |= 0x00000002;
        operatorOfficeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 发起任命的玩家职位id
       * </pre>
       *
       * <code>optional int32 operatorOfficeId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearOperatorOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        operatorOfficeId_ = 0;
        onChanged();
        return this;
      }

      private int kingdomOfficeId_ ;
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 3;</code>
       * @return Whether the kingdomOfficeId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomOfficeId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 3;</code>
       * @return The kingdomOfficeId.
       */
      @java.lang.Override
      public int getKingdomOfficeId() {
        return kingdomOfficeId_;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 3;</code>
       * @param value The kingdomOfficeId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomOfficeId(int value) {
        bitField0_ |= 0x00000004;
        kingdomOfficeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 职位id
       * </pre>
       *
       * <code>optional int32 kingdomOfficeId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomOfficeId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        kingdomOfficeId_ = 0;
        onChanged();
        return this;
      }

      private long toPlayerId_ ;
      /**
       * <pre>
       * 被任命的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 4;</code>
       * @return Whether the toPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasToPlayerId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 被任命的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 4;</code>
       * @return The toPlayerId.
       */
      @java.lang.Override
      public long getToPlayerId() {
        return toPlayerId_;
      }
      /**
       * <pre>
       * 被任命的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 4;</code>
       * @param value The toPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setToPlayerId(long value) {
        bitField0_ |= 0x00000008;
        toPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被任命的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        toPlayerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingAppointAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingAppointAsk)
    private static final com.yorha.proto.SsSceneKingdom.KingAppointAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingAppointAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.KingAppointAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingAppointAsk>
        PARSER = new com.google.protobuf.AbstractParser<KingAppointAsk>() {
      @java.lang.Override
      public KingAppointAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingAppointAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingAppointAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingAppointAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingAppointAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingAppointAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingAppointAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingAppointAns}
   */
  public static final class KingAppointAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingAppointAns)
      KingAppointAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingAppointAns.newBuilder() to construct.
    private KingAppointAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingAppointAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingAppointAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingAppointAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingAppointAns.class, com.yorha.proto.SsSceneKingdom.KingAppointAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingAppointAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingAppointAns other = (com.yorha.proto.SsSceneKingdom.KingAppointAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingAppointAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingAppointAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingAppointAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingAppointAns)
        com.yorha.proto.SsSceneKingdom.KingAppointAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingAppointAns.class, com.yorha.proto.SsSceneKingdom.KingAppointAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingAppointAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingAppointAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingAppointAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAns build() {
        com.yorha.proto.SsSceneKingdom.KingAppointAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingAppointAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingAppointAns result = new com.yorha.proto.SsSceneKingdom.KingAppointAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingAppointAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingAppointAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingAppointAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingAppointAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingAppointAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingAppointAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingAppointAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingAppointAns)
    private static final com.yorha.proto.SsSceneKingdom.KingAppointAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingAppointAns();
    }

    public static com.yorha.proto.SsSceneKingdom.KingAppointAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingAppointAns>
        PARSER = new com.google.protobuf.AbstractParser<KingAppointAns>() {
      @java.lang.Override
      public KingAppointAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingAppointAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingAppointAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingAppointAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingAppointAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingOpenBuffAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingOpenBuffAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 增益id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return Whether the kingdomBuffId field is set.
     */
    boolean hasKingdomBuffId();
    /**
     * <pre>
     * 增益id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return The kingdomBuffId.
     */
    int getKingdomBuffId();

    /**
     * <code>optional string name = 2;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 2;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 2;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingOpenBuffAsk}
   */
  public static final class KingOpenBuffAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingOpenBuffAsk)
      KingOpenBuffAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingOpenBuffAsk.newBuilder() to construct.
    private KingOpenBuffAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingOpenBuffAsk() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingOpenBuffAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingOpenBuffAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomBuffId_ = input.readInt32();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              name_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.class, com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMBUFFID_FIELD_NUMBER = 1;
    private int kingdomBuffId_;
    /**
     * <pre>
     * 增益id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return Whether the kingdomBuffId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomBuffId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 增益id
     * </pre>
     *
     * <code>optional int32 kingdomBuffId = 1;</code>
     * @return The kingdomBuffId.
     */
    @java.lang.Override
    public int getKingdomBuffId() {
      return kingdomBuffId_;
    }

    public static final int NAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 2;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 2;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomBuffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, name_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomBuffId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, name_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk other = (com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk) obj;

      if (hasKingdomBuffId() != other.hasKingdomBuffId()) return false;
      if (hasKingdomBuffId()) {
        if (getKingdomBuffId()
            != other.getKingdomBuffId()) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomBuffId()) {
        hash = (37 * hash) + KINGDOMBUFFID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomBuffId();
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingOpenBuffAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingOpenBuffAsk)
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.class, com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomBuffId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk build() {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk result = new com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomBuffId_ = kingdomBuffId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.name_ = name_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk.getDefaultInstance()) return this;
        if (other.hasKingdomBuffId()) {
          setKingdomBuffId(other.getKingdomBuffId());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000002;
          name_ = other.name_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomBuffId_ ;
      /**
       * <pre>
       * 增益id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return Whether the kingdomBuffId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomBuffId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 增益id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return The kingdomBuffId.
       */
      @java.lang.Override
      public int getKingdomBuffId() {
        return kingdomBuffId_;
      }
      /**
       * <pre>
       * 增益id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @param value The kingdomBuffId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomBuffId(int value) {
        bitField0_ |= 0x00000001;
        kingdomBuffId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 增益id
       * </pre>
       *
       * <code>optional int32 kingdomBuffId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomBuffId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomBuffId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 2;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string name = 2;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 2;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 2;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        name_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingOpenBuffAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingOpenBuffAsk)
    private static final com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingOpenBuffAsk>
        PARSER = new com.google.protobuf.AbstractParser<KingOpenBuffAsk>() {
      @java.lang.Override
      public KingOpenBuffAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingOpenBuffAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingOpenBuffAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingOpenBuffAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingOpenBuffAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingOpenBuffAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingOpenBuffAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingOpenBuffAns}
   */
  public static final class KingOpenBuffAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingOpenBuffAns)
      KingOpenBuffAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingOpenBuffAns.newBuilder() to construct.
    private KingOpenBuffAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingOpenBuffAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingOpenBuffAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingOpenBuffAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.class, com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingOpenBuffAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingOpenBuffAns other = (com.yorha.proto.SsSceneKingdom.KingOpenBuffAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingOpenBuffAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingOpenBuffAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingOpenBuffAns)
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.class, com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingOpenBuffAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAns build() {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingOpenBuffAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAns result = new com.yorha.proto.SsSceneKingdom.KingOpenBuffAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingOpenBuffAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingOpenBuffAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingOpenBuffAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingOpenBuffAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingOpenBuffAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingOpenBuffAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingOpenBuffAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingOpenBuffAns)
    private static final com.yorha.proto.SsSceneKingdom.KingOpenBuffAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingOpenBuffAns();
    }

    public static com.yorha.proto.SsSceneKingdom.KingOpenBuffAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingOpenBuffAns>
        PARSER = new com.google.protobuf.AbstractParser<KingOpenBuffAns>() {
      @java.lang.Override
      public KingOpenBuffAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingOpenBuffAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingOpenBuffAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingOpenBuffAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingOpenBuffAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingSendGiftAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingSendGiftAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return Whether the kingdomGiftId field is set.
     */
    boolean hasKingdomGiftId();
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return The kingdomGiftId.
     */
    int getKingdomGiftId();

    /**
     * <pre>
     * 收礼的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    boolean hasToPlayerId();
    /**
     * <pre>
     * 收礼的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    long getToPlayerId();

    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();

    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     * @return Whether the cardHead field is set.
     */
    boolean hasCardHead();
    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     * @return The cardHead.
     */
    com.yorha.proto.Struct.PlayerCardHead getCardHead();
    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     */
    com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingSendGiftAsk}
   */
  public static final class KingSendGiftAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingSendGiftAsk)
      KingSendGiftAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingSendGiftAsk.newBuilder() to construct.
    private KingSendGiftAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingSendGiftAsk() {
      clanSimpleName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingSendGiftAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingSendGiftAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomGiftId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toPlayerId_ = input.readInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              clanSimpleName_ = bs;
              break;
            }
            case 34: {
              com.yorha.proto.Struct.PlayerCardHead.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = cardHead_.toBuilder();
              }
              cardHead_ = input.readMessage(com.yorha.proto.Struct.PlayerCardHead.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardHead_);
                cardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.class, com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMGIFTID_FIELD_NUMBER = 1;
    private int kingdomGiftId_;
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return Whether the kingdomGiftId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomGiftId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 kingdomGiftId = 1;</code>
     * @return The kingdomGiftId.
     */
    @java.lang.Override
    public int getKingdomGiftId() {
      return kingdomGiftId_;
    }

    public static final int TOPLAYERID_FIELD_NUMBER = 2;
    private long toPlayerId_;
    /**
     * <pre>
     * 收礼的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return Whether the toPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasToPlayerId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 收礼的玩家id
     * </pre>
     *
     * <code>optional int64 toPlayerId = 2;</code>
     * @return The toPlayerId.
     */
    @java.lang.Override
    public long getToPlayerId() {
      return toPlayerId_;
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 收礼的玩家联盟简称
     * </pre>
     *
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CARDHEAD_FIELD_NUMBER = 4;
    private com.yorha.proto.Struct.PlayerCardHead cardHead_;
    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     * @return Whether the cardHead field is set.
     */
    @java.lang.Override
    public boolean hasCardHead() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     * @return The cardHead.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }
    /**
     * <pre>
     * 收礼的玩家头像
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
      return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomGiftId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toPlayerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getCardHead());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomGiftId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toPlayerId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, clanSimpleName_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCardHead());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingSendGiftAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingSendGiftAsk other = (com.yorha.proto.SsSceneKingdom.KingSendGiftAsk) obj;

      if (hasKingdomGiftId() != other.hasKingdomGiftId()) return false;
      if (hasKingdomGiftId()) {
        if (getKingdomGiftId()
            != other.getKingdomGiftId()) return false;
      }
      if (hasToPlayerId() != other.hasToPlayerId()) return false;
      if (hasToPlayerId()) {
        if (getToPlayerId()
            != other.getToPlayerId()) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (hasCardHead() != other.hasCardHead()) return false;
      if (hasCardHead()) {
        if (!getCardHead()
            .equals(other.getCardHead())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomGiftId()) {
        hash = (37 * hash) + KINGDOMGIFTID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomGiftId();
      }
      if (hasToPlayerId()) {
        hash = (37 * hash) + TOPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToPlayerId());
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      if (hasCardHead()) {
        hash = (37 * hash) + CARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getCardHead().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingSendGiftAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingSendGiftAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingSendGiftAsk)
        com.yorha.proto.SsSceneKingdom.KingSendGiftAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.class, com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomGiftId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAsk build() {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAsk result = new com.yorha.proto.SsSceneKingdom.KingSendGiftAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomGiftId_ = kingdomGiftId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toPlayerId_ = toPlayerId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.clanSimpleName_ = clanSimpleName_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (cardHeadBuilder_ == null) {
            result.cardHead_ = cardHead_;
          } else {
            result.cardHead_ = cardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingSendGiftAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingSendGiftAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingSendGiftAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingSendGiftAsk.getDefaultInstance()) return this;
        if (other.hasKingdomGiftId()) {
          setKingdomGiftId(other.getKingdomGiftId());
        }
        if (other.hasToPlayerId()) {
          setToPlayerId(other.getToPlayerId());
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000004;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        if (other.hasCardHead()) {
          mergeCardHead(other.getCardHead());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingSendGiftAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomGiftId_ ;
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return Whether the kingdomGiftId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomGiftId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return The kingdomGiftId.
       */
      @java.lang.Override
      public int getKingdomGiftId() {
        return kingdomGiftId_;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @param value The kingdomGiftId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomGiftId(int value) {
        bitField0_ |= 0x00000001;
        kingdomGiftId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 kingdomGiftId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomGiftId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomGiftId_ = 0;
        onChanged();
        return this;
      }

      private long toPlayerId_ ;
      /**
       * <pre>
       * 收礼的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return Whether the toPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasToPlayerId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 收礼的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return The toPlayerId.
       */
      @java.lang.Override
      public long getToPlayerId() {
        return toPlayerId_;
      }
      /**
       * <pre>
       * 收礼的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @param value The toPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setToPlayerId(long value) {
        bitField0_ |= 0x00000002;
        toPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家id
       * </pre>
       *
       * <code>optional int64 toPlayerId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toPlayerId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家联盟简称
       * </pre>
       *
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.PlayerCardHead cardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> cardHeadBuilder_;
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       * @return Whether the cardHead field is set.
       */
      public boolean hasCardHead() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       * @return The cardHead.
       */
      public com.yorha.proto.Struct.PlayerCardHead getCardHead() {
        if (cardHeadBuilder_ == null) {
          return cardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        } else {
          return cardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public Builder setCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardHead_ = value;
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public Builder setCardHead(
          com.yorha.proto.Struct.PlayerCardHead.Builder builderForValue) {
        if (cardHeadBuilder_ == null) {
          cardHead_ = builderForValue.build();
          onChanged();
        } else {
          cardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public Builder mergeCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (cardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              cardHead_ != null &&
              cardHead_ != com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance()) {
            cardHead_ =
              com.yorha.proto.Struct.PlayerCardHead.newBuilder(cardHead_).mergeFrom(value).buildPartial();
          } else {
            cardHead_ = value;
          }
          onChanged();
        } else {
          cardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public Builder clearCardHead() {
        if (cardHeadBuilder_ == null) {
          cardHead_ = null;
          onChanged();
        } else {
          cardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHead.Builder getCardHeadBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getCardHeadOrBuilder() {
        if (cardHeadBuilder_ != null) {
          return cardHeadBuilder_.getMessageOrBuilder();
        } else {
          return cardHead_ == null ?
              com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : cardHead_;
        }
      }
      /**
       * <pre>
       * 收礼的玩家头像
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead cardHead = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> 
          getCardHeadFieldBuilder() {
        if (cardHeadBuilder_ == null) {
          cardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder>(
                  getCardHead(),
                  getParentForChildren(),
                  isClean());
          cardHead_ = null;
        }
        return cardHeadBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingSendGiftAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingSendGiftAsk)
    private static final com.yorha.proto.SsSceneKingdom.KingSendGiftAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingSendGiftAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingSendGiftAsk>
        PARSER = new com.google.protobuf.AbstractParser<KingSendGiftAsk>() {
      @java.lang.Override
      public KingSendGiftAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingSendGiftAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingSendGiftAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingSendGiftAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingSendGiftAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingSendGiftAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingSendGiftAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingSendGiftAns}
   */
  public static final class KingSendGiftAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingSendGiftAns)
      KingSendGiftAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingSendGiftAns.newBuilder() to construct.
    private KingSendGiftAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingSendGiftAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingSendGiftAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingSendGiftAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingSendGiftAns.class, com.yorha.proto.SsSceneKingdom.KingSendGiftAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingSendGiftAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingSendGiftAns other = (com.yorha.proto.SsSceneKingdom.KingSendGiftAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingSendGiftAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingSendGiftAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingSendGiftAns)
        com.yorha.proto.SsSceneKingdom.KingSendGiftAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingSendGiftAns.class, com.yorha.proto.SsSceneKingdom.KingSendGiftAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingSendGiftAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingSendGiftAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingSendGiftAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAns build() {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingSendGiftAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAns result = new com.yorha.proto.SsSceneKingdom.KingSendGiftAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingSendGiftAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingSendGiftAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingSendGiftAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingSendGiftAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingSendGiftAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingSendGiftAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingSendGiftAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingSendGiftAns)
    private static final com.yorha.proto.SsSceneKingdom.KingSendGiftAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingSendGiftAns();
    }

    public static com.yorha.proto.SsSceneKingdom.KingSendGiftAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingSendGiftAns>
        PARSER = new com.google.protobuf.AbstractParser<KingSendGiftAns>() {
      @java.lang.Override
      public KingSendGiftAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingSendGiftAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingSendGiftAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingSendGiftAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingSendGiftAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingCheckCanUseSkillAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingCheckCanUseSkillAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    boolean hasKingdomSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    int getKingdomSkillId();

    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    boolean hasToTargetId();
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    long getToTargetId();

    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingCheckCanUseSkillAsk}
   */
  public static final class KingCheckCanUseSkillAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingCheckCanUseSkillAsk)
      KingCheckCanUseSkillAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingCheckCanUseSkillAsk.newBuilder() to construct.
    private KingCheckCanUseSkillAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingCheckCanUseSkillAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingCheckCanUseSkillAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingCheckCanUseSkillAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomSkillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toTargetId_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.class, com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMSKILLID_FIELD_NUMBER = 1;
    private int kingdomSkillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    @java.lang.Override
    public int getKingdomSkillId() {
      return kingdomSkillId_;
    }

    public static final int TOTARGETID_FIELD_NUMBER = 2;
    private long toTargetId_;
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    @java.lang.Override
    public boolean hasToTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    @java.lang.Override
    public long getToTargetId() {
      return toTargetId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 3;
    private int zoneId_;
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 3;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk other = (com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk) obj;

      if (hasKingdomSkillId() != other.hasKingdomSkillId()) return false;
      if (hasKingdomSkillId()) {
        if (getKingdomSkillId()
            != other.getKingdomSkillId()) return false;
      }
      if (hasToTargetId() != other.hasToTargetId()) return false;
      if (hasToTargetId()) {
        if (getToTargetId()
            != other.getToTargetId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomSkillId()) {
        hash = (37 * hash) + KINGDOMSKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomSkillId();
      }
      if (hasToTargetId()) {
        hash = (37 * hash) + TOTARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToTargetId());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingCheckCanUseSkillAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingCheckCanUseSkillAsk)
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.class, com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomSkillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toTargetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk build() {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk result = new com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomSkillId_ = kingdomSkillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toTargetId_ = toTargetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk.getDefaultInstance()) return this;
        if (other.hasKingdomSkillId()) {
          setKingdomSkillId(other.getKingdomSkillId());
        }
        if (other.hasToTargetId()) {
          setToTargetId(other.getToTargetId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomSkillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return Whether the kingdomSkillId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return The kingdomSkillId.
       */
      @java.lang.Override
      public int getKingdomSkillId() {
        return kingdomSkillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @param value The kingdomSkillId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomSkillId(int value) {
        bitField0_ |= 0x00000001;
        kingdomSkillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomSkillId_ = 0;
        onChanged();
        return this;
      }

      private long toTargetId_ ;
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return Whether the toTargetId field is set.
       */
      @java.lang.Override
      public boolean hasToTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return The toTargetId.
       */
      @java.lang.Override
      public long getToTargetId() {
        return toTargetId_;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @param value The toTargetId to set.
       * @return This builder for chaining.
       */
      public Builder setToTargetId(long value) {
        bitField0_ |= 0x00000002;
        toTargetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toTargetId_ = 0L;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000004;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingCheckCanUseSkillAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingCheckCanUseSkillAsk)
    private static final com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingCheckCanUseSkillAsk>
        PARSER = new com.google.protobuf.AbstractParser<KingCheckCanUseSkillAsk>() {
      @java.lang.Override
      public KingCheckCanUseSkillAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingCheckCanUseSkillAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingCheckCanUseSkillAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingCheckCanUseSkillAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingCheckCanUseSkillAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingCheckCanUseSkillAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingCheckCanUseSkillAns}
   */
  public static final class KingCheckCanUseSkillAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingCheckCanUseSkillAns)
      KingCheckCanUseSkillAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingCheckCanUseSkillAns.newBuilder() to construct.
    private KingCheckCanUseSkillAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingCheckCanUseSkillAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingCheckCanUseSkillAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingCheckCanUseSkillAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.class, com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns other = (com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingCheckCanUseSkillAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingCheckCanUseSkillAns)
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.class, com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns build() {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns result = new com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingCheckCanUseSkillAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingCheckCanUseSkillAns)
    private static final com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns();
    }

    public static com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingCheckCanUseSkillAns>
        PARSER = new com.google.protobuf.AbstractParser<KingCheckCanUseSkillAns>() {
      @java.lang.Override
      public KingCheckCanUseSkillAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingCheckCanUseSkillAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingCheckCanUseSkillAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingCheckCanUseSkillAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingCheckCanUseSkillAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingUseSkillAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingUseSkillAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    boolean hasKingdomSkillId();
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    int getKingdomSkillId();

    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    boolean hasToTargetId();
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    long getToTargetId();

    /**
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    boolean hasName();
    /**
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    java.lang.String getName();
    /**
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    com.google.protobuf.ByteString
        getNameBytes();

    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingUseSkillAsk}
   */
  public static final class KingUseSkillAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingUseSkillAsk)
      KingUseSkillAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingUseSkillAsk.newBuilder() to construct.
    private KingUseSkillAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingUseSkillAsk() {
      name_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingUseSkillAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingUseSkillAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              kingdomSkillId_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              toTargetId_ = input.readInt64();
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              name_ = bs;
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.class, com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.Builder.class);
    }

    private int bitField0_;
    public static final int KINGDOMSKILLID_FIELD_NUMBER = 1;
    private int kingdomSkillId_;
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return Whether the kingdomSkillId field is set.
     */
    @java.lang.Override
    public boolean hasKingdomSkillId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 技能id
     * </pre>
     *
     * <code>optional int32 kingdomSkillId = 1;</code>
     * @return The kingdomSkillId.
     */
    @java.lang.Override
    public int getKingdomSkillId() {
      return kingdomSkillId_;
    }

    public static final int TOTARGETID_FIELD_NUMBER = 2;
    private long toTargetId_;
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return Whether the toTargetId field is set.
     */
    @java.lang.Override
    public boolean hasToTargetId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 目标id
     * </pre>
     *
     * <code>optional int64 toTargetId = 2;</code>
     * @return The toTargetId.
     */
    @java.lang.Override
    public long getToTargetId() {
      return toTargetId_;
    }

    public static final int NAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object name_;
    /**
     * <code>optional string name = 3;</code>
     * @return Whether the name field is set.
     */
    @java.lang.Override
    public boolean hasName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The name.
     */
    @java.lang.Override
    public java.lang.String getName() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          name_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string name = 3;</code>
     * @return The bytes for name.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getNameBytes() {
      java.lang.Object ref = name_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        name_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int ZONEID_FIELD_NUMBER = 4;
    private int zoneId_;
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 目标所在场景的zoneid。不是原服id
     * </pre>
     *
     * <code>optional int32 zoneId = 4;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, name_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt32(4, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, kingdomSkillId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, toTargetId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, name_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingUseSkillAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingUseSkillAsk other = (com.yorha.proto.SsSceneKingdom.KingUseSkillAsk) obj;

      if (hasKingdomSkillId() != other.hasKingdomSkillId()) return false;
      if (hasKingdomSkillId()) {
        if (getKingdomSkillId()
            != other.getKingdomSkillId()) return false;
      }
      if (hasToTargetId() != other.hasToTargetId()) return false;
      if (hasToTargetId()) {
        if (getToTargetId()
            != other.getToTargetId()) return false;
      }
      if (hasName() != other.hasName()) return false;
      if (hasName()) {
        if (!getName()
            .equals(other.getName())) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingdomSkillId()) {
        hash = (37 * hash) + KINGDOMSKILLID_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomSkillId();
      }
      if (hasToTargetId()) {
        hash = (37 * hash) + TOTARGETID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getToTargetId());
      }
      if (hasName()) {
        hash = (37 * hash) + NAME_FIELD_NUMBER;
        hash = (53 * hash) + getName().hashCode();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingUseSkillAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingUseSkillAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingUseSkillAsk)
        com.yorha.proto.SsSceneKingdom.KingUseSkillAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.class, com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        kingdomSkillId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        toTargetId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        name_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAsk build() {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAsk result = new com.yorha.proto.SsSceneKingdom.KingUseSkillAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.kingdomSkillId_ = kingdomSkillId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.toTargetId_ = toTargetId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.name_ = name_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000008;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingUseSkillAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingUseSkillAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingUseSkillAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingUseSkillAsk.getDefaultInstance()) return this;
        if (other.hasKingdomSkillId()) {
          setKingdomSkillId(other.getKingdomSkillId());
        }
        if (other.hasToTargetId()) {
          setToTargetId(other.getToTargetId());
        }
        if (other.hasName()) {
          bitField0_ |= 0x00000004;
          name_ = other.name_;
          onChanged();
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingUseSkillAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int kingdomSkillId_ ;
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return Whether the kingdomSkillId field is set.
       */
      @java.lang.Override
      public boolean hasKingdomSkillId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return The kingdomSkillId.
       */
      @java.lang.Override
      public int getKingdomSkillId() {
        return kingdomSkillId_;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @param value The kingdomSkillId to set.
       * @return This builder for chaining.
       */
      public Builder setKingdomSkillId(int value) {
        bitField0_ |= 0x00000001;
        kingdomSkillId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 技能id
       * </pre>
       *
       * <code>optional int32 kingdomSkillId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearKingdomSkillId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        kingdomSkillId_ = 0;
        onChanged();
        return this;
      }

      private long toTargetId_ ;
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return Whether the toTargetId field is set.
       */
      @java.lang.Override
      public boolean hasToTargetId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return The toTargetId.
       */
      @java.lang.Override
      public long getToTargetId() {
        return toTargetId_;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @param value The toTargetId to set.
       * @return This builder for chaining.
       */
      public Builder setToTargetId(long value) {
        bitField0_ |= 0x00000002;
        toTargetId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标id
       * </pre>
       *
       * <code>optional int64 toTargetId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearToTargetId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        toTargetId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object name_ = "";
      /**
       * <code>optional string name = 3;</code>
       * @return Whether the name field is set.
       */
      public boolean hasName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string name = 3;</code>
       * @return The name.
       */
      public java.lang.String getName() {
        java.lang.Object ref = name_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            name_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string name = 3;</code>
       * @return The bytes for name.
       */
      public com.google.protobuf.ByteString
          getNameBytes() {
        java.lang.Object ref = name_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          name_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string name = 3;</code>
       * @param value The name to set.
       * @return This builder for chaining.
       */
      public Builder setName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        name_ = getDefaultInstance().getName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string name = 3;</code>
       * @param value The bytes for name to set.
       * @return This builder for chaining.
       */
      public Builder setNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        name_ = value;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000008;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 目标所在场景的zoneid。不是原服id
       * </pre>
       *
       * <code>optional int32 zoneId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingUseSkillAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingUseSkillAsk)
    private static final com.yorha.proto.SsSceneKingdom.KingUseSkillAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingUseSkillAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingUseSkillAsk>
        PARSER = new com.google.protobuf.AbstractParser<KingUseSkillAsk>() {
      @java.lang.Override
      public KingUseSkillAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingUseSkillAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingUseSkillAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingUseSkillAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingUseSkillAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface KingUseSkillAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.KingUseSkillAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.KingUseSkillAns}
   */
  public static final class KingUseSkillAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.KingUseSkillAns)
      KingUseSkillAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use KingUseSkillAns.newBuilder() to construct.
    private KingUseSkillAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private KingUseSkillAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new KingUseSkillAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private KingUseSkillAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.KingUseSkillAns.class, com.yorha.proto.SsSceneKingdom.KingUseSkillAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.KingUseSkillAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.KingUseSkillAns other = (com.yorha.proto.SsSceneKingdom.KingUseSkillAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.KingUseSkillAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.KingUseSkillAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.KingUseSkillAns)
        com.yorha.proto.SsSceneKingdom.KingUseSkillAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.KingUseSkillAns.class, com.yorha.proto.SsSceneKingdom.KingUseSkillAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.KingUseSkillAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_KingUseSkillAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.KingUseSkillAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAns build() {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.KingUseSkillAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAns result = new com.yorha.proto.SsSceneKingdom.KingUseSkillAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.KingUseSkillAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.KingUseSkillAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.KingUseSkillAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.KingUseSkillAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.KingUseSkillAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.KingUseSkillAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.KingUseSkillAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.KingUseSkillAns)
    private static final com.yorha.proto.SsSceneKingdom.KingUseSkillAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.KingUseSkillAns();
    }

    public static com.yorha.proto.SsSceneKingdom.KingUseSkillAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<KingUseSkillAns>
        PARSER = new com.google.protobuf.AbstractParser<KingUseSkillAns>() {
      @java.lang.Override
      public KingUseSkillAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new KingUseSkillAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<KingUseSkillAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<KingUseSkillAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.KingUseSkillAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchHistoryKingAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchHistoryKingAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    int getPage();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchHistoryKingAsk}
   */
  public static final class FetchHistoryKingAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchHistoryKingAsk)
      FetchHistoryKingAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchHistoryKingAsk.newBuilder() to construct.
    private FetchHistoryKingAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchHistoryKingAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchHistoryKingAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchHistoryKingAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.class, com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 1;
    private int page_;
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, page_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, page_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk other = (com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchHistoryKingAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchHistoryKingAsk)
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.class, com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk build() {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk result = new com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchHistoryKingAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchHistoryKingAsk)
    private static final com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchHistoryKingAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchHistoryKingAsk>() {
      @java.lang.Override
      public FetchHistoryKingAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchHistoryKingAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchHistoryKingAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchHistoryKingAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchHistoryKingAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchHistoryKingAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    boolean hasPage();
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    int getPage();

    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return Whether the totalPage field is set.
     */
    boolean hasTotalPage();
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return The totalPage.
     */
    int getTotalPage();

    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> 
        getKingInfosList();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index);
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    int getKingInfosCount();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
        getKingInfosOrBuilderList();
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
        int index);

    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return Whether the curKingInfo field is set.
     */
    boolean hasCurKingInfo();
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return The curKingInfo.
     */
    com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo();
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     */
    com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchHistoryKingAns}
   */
  public static final class FetchHistoryKingAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchHistoryKingAns)
      FetchHistoryKingAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchHistoryKingAns.newBuilder() to construct.
    private FetchHistoryKingAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchHistoryKingAns() {
      kingInfos_ = java.util.Collections.emptyList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchHistoryKingAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchHistoryKingAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              page_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              totalPage_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                kingInfos_ = new java.util.ArrayList<com.yorha.proto.StructMsg.SimpleKingInfo>();
                mutable_bitField0_ |= 0x00000004;
              }
              kingInfos_.add(
                  input.readMessage(com.yorha.proto.StructMsg.SimpleKingInfo.PARSER, extensionRegistry));
              break;
            }
            case 34: {
              com.yorha.proto.StructMsg.SimpleKingInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = curKingInfo_.toBuilder();
              }
              curKingInfo_ = input.readMessage(com.yorha.proto.StructMsg.SimpleKingInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(curKingInfo_);
                curKingInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000004) != 0)) {
          kingInfos_ = java.util.Collections.unmodifiableList(kingInfos_);
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.class, com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.Builder.class);
    }

    private int bitField0_;
    public static final int PAGE_FIELD_NUMBER = 1;
    private int page_;
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return Whether the page field is set.
     */
    @java.lang.Override
    public boolean hasPage() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 当前页数
     * </pre>
     *
     * <code>optional int32 page = 1;</code>
     * @return The page.
     */
    @java.lang.Override
    public int getPage() {
      return page_;
    }

    public static final int TOTALPAGE_FIELD_NUMBER = 2;
    private int totalPage_;
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return Whether the totalPage field is set.
     */
    @java.lang.Override
    public boolean hasTotalPage() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 总共页数
     * </pre>
     *
     * <code>optional int32 totalPage = 2;</code>
     * @return The totalPage.
     */
    @java.lang.Override
    public int getTotalPage() {
      return totalPage_;
    }

    public static final int KINGINFOS_FIELD_NUMBER = 3;
    private java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> kingInfos_;
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> getKingInfosList() {
      return kingInfos_;
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
        getKingInfosOrBuilderList() {
      return kingInfos_;
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public int getKingInfosCount() {
      return kingInfos_.size();
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index) {
      return kingInfos_.get(index);
    }
    /**
     * <pre>
     * 简要的国王数据list
     * </pre>
     *
     * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
        int index) {
      return kingInfos_.get(index);
    }

    public static final int CURKINGINFO_FIELD_NUMBER = 4;
    private com.yorha.proto.StructMsg.SimpleKingInfo curKingInfo_;
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return Whether the curKingInfo field is set.
     */
    @java.lang.Override
    public boolean hasCurKingInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     * @return The curKingInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo() {
      return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
    }
    /**
     * <pre>
     * 当前的国王数据，有可能是空的，请按需显示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder() {
      return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, totalPage_);
      }
      for (int i = 0; i < kingInfos_.size(); i++) {
        output.writeMessage(3, kingInfos_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(4, getCurKingInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, page_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, totalPage_);
      }
      for (int i = 0; i < kingInfos_.size(); i++) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, kingInfos_.get(i));
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getCurKingInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns other = (com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns) obj;

      if (hasPage() != other.hasPage()) return false;
      if (hasPage()) {
        if (getPage()
            != other.getPage()) return false;
      }
      if (hasTotalPage() != other.hasTotalPage()) return false;
      if (hasTotalPage()) {
        if (getTotalPage()
            != other.getTotalPage()) return false;
      }
      if (!getKingInfosList()
          .equals(other.getKingInfosList())) return false;
      if (hasCurKingInfo() != other.hasCurKingInfo()) return false;
      if (hasCurKingInfo()) {
        if (!getCurKingInfo()
            .equals(other.getCurKingInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPage()) {
        hash = (37 * hash) + PAGE_FIELD_NUMBER;
        hash = (53 * hash) + getPage();
      }
      if (hasTotalPage()) {
        hash = (37 * hash) + TOTALPAGE_FIELD_NUMBER;
        hash = (53 * hash) + getTotalPage();
      }
      if (getKingInfosCount() > 0) {
        hash = (37 * hash) + KINGINFOS_FIELD_NUMBER;
        hash = (53 * hash) + getKingInfosList().hashCode();
      }
      if (hasCurKingInfo()) {
        hash = (37 * hash) + CURKINGINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCurKingInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchHistoryKingAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchHistoryKingAns)
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.class, com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getKingInfosFieldBuilder();
          getCurKingInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        page_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        totalPage_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        if (kingInfosBuilder_ == null) {
          kingInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
        } else {
          kingInfosBuilder_.clear();
        }
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = null;
        } else {
          curKingInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns build() {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns result = new com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.page_ = page_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.totalPage_ = totalPage_;
          to_bitField0_ |= 0x00000002;
        }
        if (kingInfosBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0)) {
            kingInfos_ = java.util.Collections.unmodifiableList(kingInfos_);
            bitField0_ = (bitField0_ & ~0x00000004);
          }
          result.kingInfos_ = kingInfos_;
        } else {
          result.kingInfos_ = kingInfosBuilder_.build();
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (curKingInfoBuilder_ == null) {
            result.curKingInfo_ = curKingInfo_;
          } else {
            result.curKingInfo_ = curKingInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns.getDefaultInstance()) return this;
        if (other.hasPage()) {
          setPage(other.getPage());
        }
        if (other.hasTotalPage()) {
          setTotalPage(other.getTotalPage());
        }
        if (kingInfosBuilder_ == null) {
          if (!other.kingInfos_.isEmpty()) {
            if (kingInfos_.isEmpty()) {
              kingInfos_ = other.kingInfos_;
              bitField0_ = (bitField0_ & ~0x00000004);
            } else {
              ensureKingInfosIsMutable();
              kingInfos_.addAll(other.kingInfos_);
            }
            onChanged();
          }
        } else {
          if (!other.kingInfos_.isEmpty()) {
            if (kingInfosBuilder_.isEmpty()) {
              kingInfosBuilder_.dispose();
              kingInfosBuilder_ = null;
              kingInfos_ = other.kingInfos_;
              bitField0_ = (bitField0_ & ~0x00000004);
              kingInfosBuilder_ = 
                com.google.protobuf.GeneratedMessageV3.alwaysUseFieldBuilders ?
                   getKingInfosFieldBuilder() : null;
            } else {
              kingInfosBuilder_.addAllMessages(other.kingInfos_);
            }
          }
        }
        if (other.hasCurKingInfo()) {
          mergeCurKingInfo(other.getCurKingInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int page_ ;
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return Whether the page field is set.
       */
      @java.lang.Override
      public boolean hasPage() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return The page.
       */
      @java.lang.Override
      public int getPage() {
        return page_;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @param value The page to set.
       * @return This builder for chaining.
       */
      public Builder setPage(int value) {
        bitField0_ |= 0x00000001;
        page_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前页数
       * </pre>
       *
       * <code>optional int32 page = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearPage() {
        bitField0_ = (bitField0_ & ~0x00000001);
        page_ = 0;
        onChanged();
        return this;
      }

      private int totalPage_ ;
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return Whether the totalPage field is set.
       */
      @java.lang.Override
      public boolean hasTotalPage() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return The totalPage.
       */
      @java.lang.Override
      public int getTotalPage() {
        return totalPage_;
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @param value The totalPage to set.
       * @return This builder for chaining.
       */
      public Builder setTotalPage(int value) {
        bitField0_ |= 0x00000002;
        totalPage_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 总共页数
       * </pre>
       *
       * <code>optional int32 totalPage = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTotalPage() {
        bitField0_ = (bitField0_ & ~0x00000002);
        totalPage_ = 0;
        onChanged();
        return this;
      }

      private java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> kingInfos_ =
        java.util.Collections.emptyList();
      private void ensureKingInfosIsMutable() {
        if (!((bitField0_ & 0x00000004) != 0)) {
          kingInfos_ = new java.util.ArrayList<com.yorha.proto.StructMsg.SimpleKingInfo>(kingInfos_);
          bitField0_ |= 0x00000004;
         }
      }

      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> kingInfosBuilder_;

      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo> getKingInfosList() {
        if (kingInfosBuilder_ == null) {
          return java.util.Collections.unmodifiableList(kingInfos_);
        } else {
          return kingInfosBuilder_.getMessageList();
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public int getKingInfosCount() {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.size();
        } else {
          return kingInfosBuilder_.getCount();
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo getKingInfos(int index) {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.get(index);
        } else {
          return kingInfosBuilder_.getMessage(index);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder setKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.set(index, value);
          onChanged();
        } else {
          kingInfosBuilder_.setMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder setKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.set(index, builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.setMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.add(value);
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (kingInfosBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          ensureKingInfosIsMutable();
          kingInfos_.add(index, value);
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(index, value);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.add(builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addKingInfos(
          int index, com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.add(index, builderForValue.build());
          onChanged();
        } else {
          kingInfosBuilder_.addMessage(index, builderForValue.build());
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder addAllKingInfos(
          java.lang.Iterable<? extends com.yorha.proto.StructMsg.SimpleKingInfo> values) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          com.google.protobuf.AbstractMessageLite.Builder.addAll(
              values, kingInfos_);
          onChanged();
        } else {
          kingInfosBuilder_.addAllMessages(values);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder clearKingInfos() {
        if (kingInfosBuilder_ == null) {
          kingInfos_ = java.util.Collections.emptyList();
          bitField0_ = (bitField0_ & ~0x00000004);
          onChanged();
        } else {
          kingInfosBuilder_.clear();
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public Builder removeKingInfos(int index) {
        if (kingInfosBuilder_ == null) {
          ensureKingInfosIsMutable();
          kingInfos_.remove(index);
          onChanged();
        } else {
          kingInfosBuilder_.remove(index);
        }
        return this;
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder getKingInfosBuilder(
          int index) {
        return getKingInfosFieldBuilder().getBuilder(index);
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getKingInfosOrBuilder(
          int index) {
        if (kingInfosBuilder_ == null) {
          return kingInfos_.get(index);  } else {
          return kingInfosBuilder_.getMessageOrBuilder(index);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<? extends com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
           getKingInfosOrBuilderList() {
        if (kingInfosBuilder_ != null) {
          return kingInfosBuilder_.getMessageOrBuilderList();
        } else {
          return java.util.Collections.unmodifiableList(kingInfos_);
        }
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder addKingInfosBuilder() {
        return getKingInfosFieldBuilder().addBuilder(
            com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder addKingInfosBuilder(
          int index) {
        return getKingInfosFieldBuilder().addBuilder(
            index, com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance());
      }
      /**
       * <pre>
       * 简要的国王数据list
       * </pre>
       *
       * <code>repeated .com.yorha.proto.SimpleKingInfo kingInfos = 3;</code>
       */
      public java.util.List<com.yorha.proto.StructMsg.SimpleKingInfo.Builder> 
           getKingInfosBuilderList() {
        return getKingInfosFieldBuilder().getBuilderList();
      }
      private com.google.protobuf.RepeatedFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
          getKingInfosFieldBuilder() {
        if (kingInfosBuilder_ == null) {
          kingInfosBuilder_ = new com.google.protobuf.RepeatedFieldBuilderV3<
              com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder>(
                  kingInfos_,
                  ((bitField0_ & 0x00000004) != 0),
                  getParentForChildren(),
                  isClean());
          kingInfos_ = null;
        }
        return kingInfosBuilder_;
      }

      private com.yorha.proto.StructMsg.SimpleKingInfo curKingInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> curKingInfoBuilder_;
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       * @return Whether the curKingInfo field is set.
       */
      public boolean hasCurKingInfo() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       * @return The curKingInfo.
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo getCurKingInfo() {
        if (curKingInfoBuilder_ == null) {
          return curKingInfo_ == null ? com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
        } else {
          return curKingInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder setCurKingInfo(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (curKingInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          curKingInfo_ = value;
          onChanged();
        } else {
          curKingInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder setCurKingInfo(
          com.yorha.proto.StructMsg.SimpleKingInfo.Builder builderForValue) {
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = builderForValue.build();
          onChanged();
        } else {
          curKingInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder mergeCurKingInfo(com.yorha.proto.StructMsg.SimpleKingInfo value) {
        if (curKingInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              curKingInfo_ != null &&
              curKingInfo_ != com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance()) {
            curKingInfo_ =
              com.yorha.proto.StructMsg.SimpleKingInfo.newBuilder(curKingInfo_).mergeFrom(value).buildPartial();
          } else {
            curKingInfo_ = value;
          }
          onChanged();
        } else {
          curKingInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public Builder clearCurKingInfo() {
        if (curKingInfoBuilder_ == null) {
          curKingInfo_ = null;
          onChanged();
        } else {
          curKingInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfo.Builder getCurKingInfoBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getCurKingInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      public com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder getCurKingInfoOrBuilder() {
        if (curKingInfoBuilder_ != null) {
          return curKingInfoBuilder_.getMessageOrBuilder();
        } else {
          return curKingInfo_ == null ?
              com.yorha.proto.StructMsg.SimpleKingInfo.getDefaultInstance() : curKingInfo_;
        }
      }
      /**
       * <pre>
       * 当前的国王数据，有可能是空的，请按需显示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SimpleKingInfo curKingInfo = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder> 
          getCurKingInfoFieldBuilder() {
        if (curKingInfoBuilder_ == null) {
          curKingInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMsg.SimpleKingInfo, com.yorha.proto.StructMsg.SimpleKingInfo.Builder, com.yorha.proto.StructMsg.SimpleKingInfoOrBuilder>(
                  getCurKingInfo(),
                  getParentForChildren(),
                  isClean());
          curKingInfo_ = null;
        }
        return curKingInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchHistoryKingAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchHistoryKingAns)
    private static final com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchHistoryKingAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchHistoryKingAns>() {
      @java.lang.Override
      public FetchHistoryKingAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchHistoryKingAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchHistoryKingAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchHistoryKingAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchHistoryKingAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchKingdomGiftAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchKingdomGiftAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return Whether the isFetchingLeftNum field is set.
     */
    boolean hasIsFetchingLeftNum();
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return The isFetchingLeftNum.
     */
    boolean getIsFetchingLeftNum();

    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return Whether the giftId field is set.
     */
    boolean hasGiftId();
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return The giftId.
     */
    int getGiftId();

    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return Whether the checkPlayerId field is set.
     */
    boolean hasCheckPlayerId();
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return The checkPlayerId.
     */
    long getCheckPlayerId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchKingdomGiftAsk}
   */
  public static final class FetchKingdomGiftAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchKingdomGiftAsk)
      FetchKingdomGiftAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchKingdomGiftAsk.newBuilder() to construct.
    private FetchKingdomGiftAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchKingdomGiftAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchKingdomGiftAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchKingdomGiftAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              isFetchingLeftNum_ = input.readBool();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              giftId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              checkPlayerId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.class, com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ISFETCHINGLEFTNUM_FIELD_NUMBER = 1;
    private boolean isFetchingLeftNum_;
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return Whether the isFetchingLeftNum field is set.
     */
    @java.lang.Override
    public boolean hasIsFetchingLeftNum() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 拉取剩余数量时，传true
     * </pre>
     *
     * <code>optional bool isFetchingLeftNum = 1;</code>
     * @return The isFetchingLeftNum.
     */
    @java.lang.Override
    public boolean getIsFetchingLeftNum() {
      return isFetchingLeftNum_;
    }

    public static final int GIFTID_FIELD_NUMBER = 2;
    private int giftId_;
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return Whether the giftId field is set.
     */
    @java.lang.Override
    public boolean hasGiftId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 礼物id
     * </pre>
     *
     * <code>optional int32 giftId = 2;</code>
     * @return The giftId.
     */
    @java.lang.Override
    public int getGiftId() {
      return giftId_;
    }

    public static final int CHECKPLAYERID_FIELD_NUMBER = 3;
    private long checkPlayerId_;
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return Whether the checkPlayerId field is set.
     */
    @java.lang.Override
    public boolean hasCheckPlayerId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 检查的玩家id
     * </pre>
     *
     * <code>optional int64 checkPlayerId = 3;</code>
     * @return The checkPlayerId.
     */
    @java.lang.Override
    public long getCheckPlayerId() {
      return checkPlayerId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeBool(1, isFetchingLeftNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, giftId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, checkPlayerId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(1, isFetchingLeftNum_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, giftId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, checkPlayerId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk other = (com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk) obj;

      if (hasIsFetchingLeftNum() != other.hasIsFetchingLeftNum()) return false;
      if (hasIsFetchingLeftNum()) {
        if (getIsFetchingLeftNum()
            != other.getIsFetchingLeftNum()) return false;
      }
      if (hasGiftId() != other.hasGiftId()) return false;
      if (hasGiftId()) {
        if (getGiftId()
            != other.getGiftId()) return false;
      }
      if (hasCheckPlayerId() != other.hasCheckPlayerId()) return false;
      if (hasCheckPlayerId()) {
        if (getCheckPlayerId()
            != other.getCheckPlayerId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasIsFetchingLeftNum()) {
        hash = (37 * hash) + ISFETCHINGLEFTNUM_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsFetchingLeftNum());
      }
      if (hasGiftId()) {
        hash = (37 * hash) + GIFTID_FIELD_NUMBER;
        hash = (53 * hash) + getGiftId();
      }
      if (hasCheckPlayerId()) {
        hash = (37 * hash) + CHECKPLAYERID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCheckPlayerId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchKingdomGiftAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchKingdomGiftAsk)
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.class, com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        isFetchingLeftNum_ = false;
        bitField0_ = (bitField0_ & ~0x00000001);
        giftId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        checkPlayerId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk build() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk result = new com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.isFetchingLeftNum_ = isFetchingLeftNum_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.giftId_ = giftId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.checkPlayerId_ = checkPlayerId_;
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk.getDefaultInstance()) return this;
        if (other.hasIsFetchingLeftNum()) {
          setIsFetchingLeftNum(other.getIsFetchingLeftNum());
        }
        if (other.hasGiftId()) {
          setGiftId(other.getGiftId());
        }
        if (other.hasCheckPlayerId()) {
          setCheckPlayerId(other.getCheckPlayerId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private boolean isFetchingLeftNum_ ;
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return Whether the isFetchingLeftNum field is set.
       */
      @java.lang.Override
      public boolean hasIsFetchingLeftNum() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return The isFetchingLeftNum.
       */
      @java.lang.Override
      public boolean getIsFetchingLeftNum() {
        return isFetchingLeftNum_;
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @param value The isFetchingLeftNum to set.
       * @return This builder for chaining.
       */
      public Builder setIsFetchingLeftNum(boolean value) {
        bitField0_ |= 0x00000001;
        isFetchingLeftNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 拉取剩余数量时，传true
       * </pre>
       *
       * <code>optional bool isFetchingLeftNum = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsFetchingLeftNum() {
        bitField0_ = (bitField0_ & ~0x00000001);
        isFetchingLeftNum_ = false;
        onChanged();
        return this;
      }

      private int giftId_ ;
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return Whether the giftId field is set.
       */
      @java.lang.Override
      public boolean hasGiftId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return The giftId.
       */
      @java.lang.Override
      public int getGiftId() {
        return giftId_;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @param value The giftId to set.
       * @return This builder for chaining.
       */
      public Builder setGiftId(int value) {
        bitField0_ |= 0x00000002;
        giftId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 礼物id
       * </pre>
       *
       * <code>optional int32 giftId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearGiftId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        giftId_ = 0;
        onChanged();
        return this;
      }

      private long checkPlayerId_ ;
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return Whether the checkPlayerId field is set.
       */
      @java.lang.Override
      public boolean hasCheckPlayerId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return The checkPlayerId.
       */
      @java.lang.Override
      public long getCheckPlayerId() {
        return checkPlayerId_;
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @param value The checkPlayerId to set.
       * @return This builder for chaining.
       */
      public Builder setCheckPlayerId(long value) {
        bitField0_ |= 0x00000004;
        checkPlayerId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 检查的玩家id
       * </pre>
       *
       * <code>optional int64 checkPlayerId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCheckPlayerId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        checkPlayerId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchKingdomGiftAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchKingdomGiftAsk)
    private static final com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchKingdomGiftAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchKingdomGiftAsk>() {
      @java.lang.Override
      public FetchKingdomGiftAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchKingdomGiftAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchKingdomGiftAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchKingdomGiftAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchKingdomGiftAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchKingdomGiftAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    int getLeftNumCount();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    boolean containsLeftNum(
        int key);
    /**
     * Use {@link #getLeftNumMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getLeftNum();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    java.util.Map<java.lang.Integer, java.lang.Integer>
    getLeftNumMap();
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    int getLeftNumOrDefault(
        int key,
        int defaultValue);
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    int getLeftNumOrThrow(
        int key);

    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     * @return Whether the giftInfo field is set.
     */
    boolean hasGiftInfo();
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     * @return The giftInfo.
     */
    com.yorha.proto.Zone.KingdomGiftInfo getGiftInfo();
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     */
    com.yorha.proto.Zone.KingdomGiftInfoOrBuilder getGiftInfoOrBuilder();

    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return Whether the canGiveGift field is set.
     */
    boolean hasCanGiveGift();
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return The canGiveGift.
     */
    boolean getCanGiveGift();
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchKingdomGiftAns}
   */
  public static final class FetchKingdomGiftAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchKingdomGiftAns)
      FetchKingdomGiftAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchKingdomGiftAns.newBuilder() to construct.
    private FetchKingdomGiftAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchKingdomGiftAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchKingdomGiftAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchKingdomGiftAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                leftNum_ = com.google.protobuf.MapField.newMapField(
                    LeftNumDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
              leftNum__ = input.readMessage(
                  LeftNumDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              leftNum_.getMutableMap().put(
                  leftNum__.getKey(), leftNum__.getValue());
              break;
            }
            case 18: {
              com.yorha.proto.Zone.KingdomGiftInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = giftInfo_.toBuilder();
              }
              giftInfo_ = input.readMessage(com.yorha.proto.Zone.KingdomGiftInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(giftInfo_);
                giftInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              canGiveGift_ = input.readBool();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetLeftNum();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.class, com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.Builder.class);
    }

    private int bitField0_;
    public static final int LEFTNUM_FIELD_NUMBER = 1;
    private static final class LeftNumDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, java.lang.Integer> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, java.lang.Integer>newDefaultInstance(
                  com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0);
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, java.lang.Integer> leftNum_;
    private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
    internalGetLeftNum() {
      if (leftNum_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            LeftNumDefaultEntryHolder.defaultEntry);
      }
      return leftNum_;
    }

    public int getLeftNumCount() {
      return internalGetLeftNum().getMap().size();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */

    @java.lang.Override
    public boolean containsLeftNum(
        int key) {
      
      return internalGetLeftNum().getMap().containsKey(key);
    }
    /**
     * Use {@link #getLeftNumMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNum() {
      return getLeftNumMap();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNumMap() {
      return internalGetLeftNum().getMap();
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public int getLeftNumOrDefault(
        int key,
        int defaultValue) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetLeftNum().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 礼物剩余数量
     * </pre>
     *
     * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
     */
    @java.lang.Override

    public int getLeftNumOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, java.lang.Integer> map =
          internalGetLeftNum().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    public static final int GIFTINFO_FIELD_NUMBER = 2;
    private com.yorha.proto.Zone.KingdomGiftInfo giftInfo_;
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     * @return Whether the giftInfo field is set.
     */
    @java.lang.Override
    public boolean hasGiftInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     * @return The giftInfo.
     */
    @java.lang.Override
    public com.yorha.proto.Zone.KingdomGiftInfo getGiftInfo() {
      return giftInfo_ == null ? com.yorha.proto.Zone.KingdomGiftInfo.getDefaultInstance() : giftInfo_;
    }
    /**
     * <pre>
     * 礼物简要信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Zone.KingdomGiftInfoOrBuilder getGiftInfoOrBuilder() {
      return giftInfo_ == null ? com.yorha.proto.Zone.KingdomGiftInfo.getDefaultInstance() : giftInfo_;
    }

    public static final int CANGIVEGIFT_FIELD_NUMBER = 3;
    private boolean canGiveGift_;
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return Whether the canGiveGift field is set.
     */
    @java.lang.Override
    public boolean hasCanGiveGift() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 是否能给这个玩家礼物
     * </pre>
     *
     * <code>optional bool canGiveGift = 3;</code>
     * @return The canGiveGift.
     */
    @java.lang.Override
    public boolean getCanGiveGift() {
      return canGiveGift_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetLeftNum(),
          LeftNumDefaultEntryHolder.defaultEntry,
          1);
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(2, getGiftInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeBool(3, canGiveGift_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, java.lang.Integer> entry
           : internalGetLeftNum().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, java.lang.Integer>
        leftNum__ = LeftNumDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, leftNum__);
      }
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getGiftInfo());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(3, canGiveGift_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns other = (com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns) obj;

      if (!internalGetLeftNum().equals(
          other.internalGetLeftNum())) return false;
      if (hasGiftInfo() != other.hasGiftInfo()) return false;
      if (hasGiftInfo()) {
        if (!getGiftInfo()
            .equals(other.getGiftInfo())) return false;
      }
      if (hasCanGiveGift() != other.hasCanGiveGift()) return false;
      if (hasCanGiveGift()) {
        if (getCanGiveGift()
            != other.getCanGiveGift()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetLeftNum().getMap().isEmpty()) {
        hash = (37 * hash) + LEFTNUM_FIELD_NUMBER;
        hash = (53 * hash) + internalGetLeftNum().hashCode();
      }
      if (hasGiftInfo()) {
        hash = (37 * hash) + GIFTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getGiftInfo().hashCode();
      }
      if (hasCanGiveGift()) {
        hash = (37 * hash) + CANGIVEGIFT_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getCanGiveGift());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchKingdomGiftAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchKingdomGiftAns)
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetLeftNum();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableLeftNum();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.class, com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getGiftInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableLeftNum().clear();
        if (giftInfoBuilder_ == null) {
          giftInfo_ = null;
        } else {
          giftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        canGiveGift_ = false;
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns build() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns result = new com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        result.leftNum_ = internalGetLeftNum();
        result.leftNum_.makeImmutable();
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (giftInfoBuilder_ == null) {
            result.giftInfo_ = giftInfo_;
          } else {
            result.giftInfo_ = giftInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.canGiveGift_ = canGiveGift_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns.getDefaultInstance()) return this;
        internalGetMutableLeftNum().mergeFrom(
            other.internalGetLeftNum());
        if (other.hasGiftInfo()) {
          mergeGiftInfo(other.getGiftInfo());
        }
        if (other.hasCanGiveGift()) {
          setCanGiveGift(other.getCanGiveGift());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, java.lang.Integer> leftNum_;
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetLeftNum() {
        if (leftNum_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              LeftNumDefaultEntryHolder.defaultEntry);
        }
        return leftNum_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, java.lang.Integer>
      internalGetMutableLeftNum() {
        onChanged();;
        if (leftNum_ == null) {
          leftNum_ = com.google.protobuf.MapField.newMapField(
              LeftNumDefaultEntryHolder.defaultEntry);
        }
        if (!leftNum_.isMutable()) {
          leftNum_ = leftNum_.copy();
        }
        return leftNum_;
      }

      public int getLeftNumCount() {
        return internalGetLeftNum().getMap().size();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      @java.lang.Override
      public boolean containsLeftNum(
          int key) {
        
        return internalGetLeftNum().getMap().containsKey(key);
      }
      /**
       * Use {@link #getLeftNumMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNum() {
        return getLeftNumMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, java.lang.Integer> getLeftNumMap() {
        return internalGetLeftNum().getMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public int getLeftNumOrDefault(
          int key,
          int defaultValue) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetLeftNum().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      @java.lang.Override

      public int getLeftNumOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, java.lang.Integer> map =
            internalGetLeftNum().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearLeftNum() {
        internalGetMutableLeftNum().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      public Builder removeLeftNum(
          int key) {
        
        internalGetMutableLeftNum().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, java.lang.Integer>
      getMutableLeftNum() {
        return internalGetMutableLeftNum().getMutableMap();
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */
      public Builder putLeftNum(
          int key,
          int value) {
        
        
        internalGetMutableLeftNum().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 礼物剩余数量
       * </pre>
       *
       * <code>map&lt;int32, int32&gt; leftNum = 1;</code>
       */

      public Builder putAllLeftNum(
          java.util.Map<java.lang.Integer, java.lang.Integer> values) {
        internalGetMutableLeftNum().getMutableMap()
            .putAll(values);
        return this;
      }

      private com.yorha.proto.Zone.KingdomGiftInfo giftInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Zone.KingdomGiftInfo, com.yorha.proto.Zone.KingdomGiftInfo.Builder, com.yorha.proto.Zone.KingdomGiftInfoOrBuilder> giftInfoBuilder_;
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       * @return Whether the giftInfo field is set.
       */
      public boolean hasGiftInfo() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       * @return The giftInfo.
       */
      public com.yorha.proto.Zone.KingdomGiftInfo getGiftInfo() {
        if (giftInfoBuilder_ == null) {
          return giftInfo_ == null ? com.yorha.proto.Zone.KingdomGiftInfo.getDefaultInstance() : giftInfo_;
        } else {
          return giftInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public Builder setGiftInfo(com.yorha.proto.Zone.KingdomGiftInfo value) {
        if (giftInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          giftInfo_ = value;
          onChanged();
        } else {
          giftInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public Builder setGiftInfo(
          com.yorha.proto.Zone.KingdomGiftInfo.Builder builderForValue) {
        if (giftInfoBuilder_ == null) {
          giftInfo_ = builderForValue.build();
          onChanged();
        } else {
          giftInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public Builder mergeGiftInfo(com.yorha.proto.Zone.KingdomGiftInfo value) {
        if (giftInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              giftInfo_ != null &&
              giftInfo_ != com.yorha.proto.Zone.KingdomGiftInfo.getDefaultInstance()) {
            giftInfo_ =
              com.yorha.proto.Zone.KingdomGiftInfo.newBuilder(giftInfo_).mergeFrom(value).buildPartial();
          } else {
            giftInfo_ = value;
          }
          onChanged();
        } else {
          giftInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public Builder clearGiftInfo() {
        if (giftInfoBuilder_ == null) {
          giftInfo_ = null;
          onChanged();
        } else {
          giftInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public com.yorha.proto.Zone.KingdomGiftInfo.Builder getGiftInfoBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getGiftInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      public com.yorha.proto.Zone.KingdomGiftInfoOrBuilder getGiftInfoOrBuilder() {
        if (giftInfoBuilder_ != null) {
          return giftInfoBuilder_.getMessageOrBuilder();
        } else {
          return giftInfo_ == null ?
              com.yorha.proto.Zone.KingdomGiftInfo.getDefaultInstance() : giftInfo_;
        }
      }
      /**
       * <pre>
       * 礼物简要信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.KingdomGiftInfo giftInfo = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Zone.KingdomGiftInfo, com.yorha.proto.Zone.KingdomGiftInfo.Builder, com.yorha.proto.Zone.KingdomGiftInfoOrBuilder> 
          getGiftInfoFieldBuilder() {
        if (giftInfoBuilder_ == null) {
          giftInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Zone.KingdomGiftInfo, com.yorha.proto.Zone.KingdomGiftInfo.Builder, com.yorha.proto.Zone.KingdomGiftInfoOrBuilder>(
                  getGiftInfo(),
                  getParentForChildren(),
                  isClean());
          giftInfo_ = null;
        }
        return giftInfoBuilder_;
      }

      private boolean canGiveGift_ ;
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return Whether the canGiveGift field is set.
       */
      @java.lang.Override
      public boolean hasCanGiveGift() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return The canGiveGift.
       */
      @java.lang.Override
      public boolean getCanGiveGift() {
        return canGiveGift_;
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @param value The canGiveGift to set.
       * @return This builder for chaining.
       */
      public Builder setCanGiveGift(boolean value) {
        bitField0_ |= 0x00000004;
        canGiveGift_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否能给这个玩家礼物
       * </pre>
       *
       * <code>optional bool canGiveGift = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearCanGiveGift() {
        bitField0_ = (bitField0_ & ~0x00000004);
        canGiveGift_ = false;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchKingdomGiftAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchKingdomGiftAns)
    private static final com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchKingdomGiftAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchKingdomGiftAns>() {
      @java.lang.Override
      public FetchKingdomGiftAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchKingdomGiftAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchKingdomGiftAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchKingdomGiftAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchKingdomGiftAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchKingdomOfficeAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchKingdomOfficeAsk)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchKingdomOfficeAsk}
   */
  public static final class FetchKingdomOfficeAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchKingdomOfficeAsk)
      FetchKingdomOfficeAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchKingdomOfficeAsk.newBuilder() to construct.
    private FetchKingdomOfficeAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchKingdomOfficeAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchKingdomOfficeAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchKingdomOfficeAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.class, com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk other = (com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchKingdomOfficeAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchKingdomOfficeAsk)
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.class, com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk build() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk result = new com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchKingdomOfficeAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchKingdomOfficeAsk)
    private static final com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchKingdomOfficeAsk>
        PARSER = new com.google.protobuf.AbstractParser<FetchKingdomOfficeAsk>() {
      @java.lang.Override
      public FetchKingdomOfficeAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchKingdomOfficeAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchKingdomOfficeAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchKingdomOfficeAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface FetchKingdomOfficeAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.FetchKingdomOfficeAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    int getOfficeInfoCount();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    boolean containsOfficeInfo(
        int key);
    /**
     * Use {@link #getOfficeInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    getOfficeInfo();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    getOfficeInfoMap();
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
        int key,
        com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue);
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.FetchKingdomOfficeAns}
   */
  public static final class FetchKingdomOfficeAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.FetchKingdomOfficeAns)
      FetchKingdomOfficeAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use FetchKingdomOfficeAns.newBuilder() to construct.
    private FetchKingdomOfficeAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private FetchKingdomOfficeAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new FetchKingdomOfficeAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private FetchKingdomOfficeAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                officeInfo_ = com.google.protobuf.MapField.newMapField(
                    OfficeInfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
              officeInfo__ = input.readMessage(
                  OfficeInfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              officeInfo_.getMutableMap().put(
                  officeInfo__.getKey(), officeInfo__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetOfficeInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.class, com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.Builder.class);
    }

    public static final int OFFICEINFO_FIELD_NUMBER = 1;
    private static final class OfficeInfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>newDefaultInstance(
                  com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.ZonePB.KingdomOfficeInfoPB.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> officeInfo_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
    internalGetOfficeInfo() {
      if (officeInfo_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            OfficeInfoDefaultEntryHolder.defaultEntry);
      }
      return officeInfo_;
    }

    public int getOfficeInfoCount() {
      return internalGetOfficeInfo().getMap().size();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */

    @java.lang.Override
    public boolean containsOfficeInfo(
        int key) {
      
      return internalGetOfficeInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getOfficeInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfo() {
      return getOfficeInfoMap();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfoMap() {
      return internalGetOfficeInfo().getMap();
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
        int key,
        com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
          internalGetOfficeInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <pre>
     * 职位id -&gt; 职位简要信息(职位id, 玩家id)
     * </pre>
     *
     * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
          internalGetOfficeInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetOfficeInfo(),
          OfficeInfoDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> entry
           : internalGetOfficeInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
        officeInfo__ = OfficeInfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, officeInfo__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns other = (com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns) obj;

      if (!internalGetOfficeInfo().equals(
          other.internalGetOfficeInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetOfficeInfo().getMap().isEmpty()) {
        hash = (37 * hash) + OFFICEINFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetOfficeInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.FetchKingdomOfficeAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.FetchKingdomOfficeAns)
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetOfficeInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableOfficeInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.class, com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableOfficeInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneKingdom.internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns build() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns buildPartial() {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns result = new com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns(this);
        int from_bitField0_ = bitField0_;
        result.officeInfo_ = internalGetOfficeInfo();
        result.officeInfo_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns) {
          return mergeFrom((com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns other) {
        if (other == com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns.getDefaultInstance()) return this;
        internalGetMutableOfficeInfo().mergeFrom(
            other.internalGetOfficeInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> officeInfo_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      internalGetOfficeInfo() {
        if (officeInfo_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              OfficeInfoDefaultEntryHolder.defaultEntry);
        }
        return officeInfo_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      internalGetMutableOfficeInfo() {
        onChanged();;
        if (officeInfo_ == null) {
          officeInfo_ = com.google.protobuf.MapField.newMapField(
              OfficeInfoDefaultEntryHolder.defaultEntry);
        }
        if (!officeInfo_.isMutable()) {
          officeInfo_ = officeInfo_.copy();
        }
        return officeInfo_;
      }

      public int getOfficeInfoCount() {
        return internalGetOfficeInfo().getMap().size();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      @java.lang.Override
      public boolean containsOfficeInfo(
          int key) {
        
        return internalGetOfficeInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getOfficeInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfo() {
        return getOfficeInfoMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> getOfficeInfoMap() {
        return internalGetOfficeInfo().getMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrDefault(
          int key,
          com.yorha.proto.ZonePB.KingdomOfficeInfoPB defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
            internalGetOfficeInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.ZonePB.KingdomOfficeInfoPB getOfficeInfoOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> map =
            internalGetOfficeInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearOfficeInfo() {
        internalGetMutableOfficeInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      public Builder removeOfficeInfo(
          int key) {
        
        internalGetMutableOfficeInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB>
      getMutableOfficeInfo() {
        return internalGetMutableOfficeInfo().getMutableMap();
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */
      public Builder putOfficeInfo(
          int key,
          com.yorha.proto.ZonePB.KingdomOfficeInfoPB value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableOfficeInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <pre>
       * 职位id -&gt; 职位简要信息(职位id, 玩家id)
       * </pre>
       *
       * <code>map&lt;int32, .com.yorha.proto.KingdomOfficeInfoPB&gt; officeInfo = 1;</code>
       */

      public Builder putAllOfficeInfo(
          java.util.Map<java.lang.Integer, com.yorha.proto.ZonePB.KingdomOfficeInfoPB> values) {
        internalGetMutableOfficeInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.FetchKingdomOfficeAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.FetchKingdomOfficeAns)
    private static final com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns();
    }

    public static com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<FetchKingdomOfficeAns>
        PARSER = new com.google.protobuf.AbstractParser<FetchKingdomOfficeAns>() {
      @java.lang.Override
      public FetchKingdomOfficeAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new FetchKingdomOfficeAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<FetchKingdomOfficeAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<FetchKingdomOfficeAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneKingdom.FetchKingdomOfficeAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingAppointAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingAppointAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingAppointAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingAppointAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingOpenBuffAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingOpenBuffAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingOpenBuffAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingSendGiftAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingSendGiftAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingSendGiftAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingSendGiftAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingCheckCanUseSkillAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingUseSkillAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingUseSkillAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_KingUseSkillAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_KingUseSkillAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchHistoryKingAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchHistoryKingAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomGiftAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomGiftAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomOfficeAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomOfficeAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/scene/ss_scene_kingdom.pr" +
      "oto\022\017com.yorha.proto\032\036cs_proto/gen/zone/" +
      "zonePB.proto\032 ss_proto/gen/common/struct" +
      ".proto\032$ss_proto/gen/common/struct_msg.p" +
      "roto\032\034ss_proto/gen/zone/zone.proto\"k\n\016Ki" +
      "ngAppointAsk\022\022\n\noperatorId\030\001 \001(\003\022\030\n\020oper" +
      "atorOfficeId\030\002 \001(\005\022\027\n\017kingdomOfficeId\030\003 " +
      "\001(\005\022\022\n\ntoPlayerId\030\004 \001(\003\"\020\n\016KingAppointAn" +
      "s\"6\n\017KingOpenBuffAsk\022\025\n\rkingdomBuffId\030\001 " +
      "\001(\005\022\014\n\004name\030\002 \001(\t\"\021\n\017KingOpenBuffAns\"\207\001\n" +
      "\017KingSendGiftAsk\022\025\n\rkingdomGiftId\030\001 \001(\005\022" +
      "\022\n\ntoPlayerId\030\002 \001(\003\022\026\n\016clanSimpleName\030\003 " +
      "\001(\t\0221\n\010cardHead\030\004 \001(\0132\037.com.yorha.proto." +
      "PlayerCardHead\"\021\n\017KingSendGiftAns\"U\n\027Kin" +
      "gCheckCanUseSkillAsk\022\026\n\016kingdomSkillId\030\001" +
      " \001(\005\022\022\n\ntoTargetId\030\002 \001(\003\022\016\n\006zoneId\030\003 \001(\005" +
      "\"\031\n\027KingCheckCanUseSkillAns\"[\n\017KingUseSk" +
      "illAsk\022\026\n\016kingdomSkillId\030\001 \001(\005\022\022\n\ntoTarg" +
      "etId\030\002 \001(\003\022\014\n\004name\030\003 \001(\t\022\016\n\006zoneId\030\004 \001(\005" +
      "\"\021\n\017KingUseSkillAns\"#\n\023FetchHistoryKingA" +
      "sk\022\014\n\004page\030\001 \001(\005\"\240\001\n\023FetchHistoryKingAns" +
      "\022\014\n\004page\030\001 \001(\005\022\021\n\ttotalPage\030\002 \001(\005\0222\n\tkin" +
      "gInfos\030\003 \003(\0132\037.com.yorha.proto.SimpleKin" +
      "gInfo\0224\n\013curKingInfo\030\004 \001(\0132\037.com.yorha.p" +
      "roto.SimpleKingInfo\"W\n\023FetchKingdomGiftA" +
      "sk\022\031\n\021isFetchingLeftNum\030\001 \001(\010\022\016\n\006giftId\030" +
      "\002 \001(\005\022\025\n\rcheckPlayerId\030\003 \001(\003\"\322\001\n\023FetchKi" +
      "ngdomGiftAns\022B\n\007leftNum\030\001 \003(\01321.com.yorh" +
      "a.proto.FetchKingdomGiftAns.LeftNumEntry" +
      "\0222\n\010giftInfo\030\002 \001(\0132 .com.yorha.proto.Kin" +
      "gdomGiftInfo\022\023\n\013canGiveGift\030\003 \001(\010\032.\n\014Lef" +
      "tNumEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001(\005:\0028" +
      "\001\"\027\n\025FetchKingdomOfficeAsk\"\274\001\n\025FetchKing" +
      "domOfficeAns\022J\n\nofficeInfo\030\001 \003(\01326.com.y" +
      "orha.proto.FetchKingdomOfficeAns.OfficeI" +
      "nfoEntry\032W\n\017OfficeInfoEntry\022\013\n\003key\030\001 \001(\005" +
      "\0223\n\005value\030\002 \001(\0132$.com.yorha.proto.Kingdo" +
      "mOfficeInfoPB:\0028\001B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.ZonePB.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructMsg.getDescriptor(),
          com.yorha.proto.Zone.getDescriptor(),
        });
    internal_static_com_yorha_proto_KingAppointAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_KingAppointAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingAppointAsk_descriptor,
        new java.lang.String[] { "OperatorId", "OperatorOfficeId", "KingdomOfficeId", "ToPlayerId", });
    internal_static_com_yorha_proto_KingAppointAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_KingAppointAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingAppointAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_KingOpenBuffAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingOpenBuffAsk_descriptor,
        new java.lang.String[] { "KingdomBuffId", "Name", });
    internal_static_com_yorha_proto_KingOpenBuffAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_KingOpenBuffAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingOpenBuffAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_KingSendGiftAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_KingSendGiftAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingSendGiftAsk_descriptor,
        new java.lang.String[] { "KingdomGiftId", "ToPlayerId", "ClanSimpleName", "CardHead", });
    internal_static_com_yorha_proto_KingSendGiftAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_KingSendGiftAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingSendGiftAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingCheckCanUseSkillAsk_descriptor,
        new java.lang.String[] { "KingdomSkillId", "ToTargetId", "ZoneId", });
    internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_KingCheckCanUseSkillAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingCheckCanUseSkillAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_KingUseSkillAsk_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_KingUseSkillAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingUseSkillAsk_descriptor,
        new java.lang.String[] { "KingdomSkillId", "ToTargetId", "Name", "ZoneId", });
    internal_static_com_yorha_proto_KingUseSkillAns_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_KingUseSkillAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_KingUseSkillAns_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_FetchHistoryKingAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchHistoryKingAsk_descriptor,
        new java.lang.String[] { "Page", });
    internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_FetchHistoryKingAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchHistoryKingAns_descriptor,
        new java.lang.String[] { "Page", "TotalPage", "KingInfos", "CurKingInfo", });
    internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_FetchKingdomGiftAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomGiftAsk_descriptor,
        new java.lang.String[] { "IsFetchingLeftNum", "GiftId", "CheckPlayerId", });
    internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_FetchKingdomGiftAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor,
        new java.lang.String[] { "LeftNum", "GiftInfo", "CanGiveGift", });
    internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_descriptor =
      internal_static_com_yorha_proto_FetchKingdomGiftAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomGiftAns_LeftNumEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor =
      getDescriptor().getMessageTypes().get(14);
    internal_static_com_yorha_proto_FetchKingdomOfficeAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomOfficeAsk_descriptor,
        new java.lang.String[] { });
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor =
      getDescriptor().getMessageTypes().get(15);
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor,
        new java.lang.String[] { "OfficeInfo", });
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_descriptor =
      internal_static_com_yorha_proto_FetchKingdomOfficeAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_FetchKingdomOfficeAns_OfficeInfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    com.yorha.proto.ZonePB.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructMsg.getDescriptor();
    com.yorha.proto.Zone.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

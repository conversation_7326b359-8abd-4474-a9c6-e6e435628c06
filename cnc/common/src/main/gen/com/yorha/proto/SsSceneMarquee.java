// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_marquee.proto

package com.yorha.proto;

public final class SsSceneMarquee {
  private SsSceneMarquee() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SendSceneMarqueeCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendSceneMarqueeCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 marqueeId = 1;</code>
     * @return Whether the marqueeId field is set.
     */
    boolean hasMarqueeId();
    /**
     * <code>optional int32 marqueeId = 1;</code>
     * @return The marqueeId.
     */
    int getMarqueeId();

    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     * @return Whether the displayData field is set.
     */
    boolean hasDisplayData();
    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     * @return The displayData.
     */
    com.yorha.proto.Struct.DisplayData getDisplayData();
    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     */
    com.yorha.proto.Struct.DisplayDataOrBuilder getDisplayDataOrBuilder();

    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     * @return Whether the clanFlagInfo field is set.
     */
    boolean hasClanFlagInfo();
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     * @return The clanFlagInfo.
     */
    com.yorha.proto.StructClan.ClanFlagInfo getClanFlagInfo();
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     */
    com.yorha.proto.StructClan.ClanFlagInfoOrBuilder getClanFlagInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendSceneMarqueeCmd}
   */
  public static final class SendSceneMarqueeCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendSceneMarqueeCmd)
      SendSceneMarqueeCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendSceneMarqueeCmd.newBuilder() to construct.
    private SendSceneMarqueeCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendSceneMarqueeCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendSceneMarqueeCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendSceneMarqueeCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              marqueeId_ = input.readInt32();
              break;
            }
            case 18: {
              com.yorha.proto.Struct.DisplayData.Builder subBuilder = null;
              if (((bitField0_ & 0x00000002) != 0)) {
                subBuilder = displayData_.toBuilder();
              }
              displayData_ = input.readMessage(com.yorha.proto.Struct.DisplayData.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(displayData_);
                displayData_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000002;
              break;
            }
            case 26: {
              com.yorha.proto.StructClan.ClanFlagInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000004) != 0)) {
                subBuilder = clanFlagInfo_.toBuilder();
              }
              clanFlagInfo_ = input.readMessage(com.yorha.proto.StructClan.ClanFlagInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(clanFlagInfo_);
                clanFlagInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000004;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneMarqueeCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.class, com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MARQUEEID_FIELD_NUMBER = 1;
    private int marqueeId_;
    /**
     * <code>optional int32 marqueeId = 1;</code>
     * @return Whether the marqueeId field is set.
     */
    @java.lang.Override
    public boolean hasMarqueeId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 marqueeId = 1;</code>
     * @return The marqueeId.
     */
    @java.lang.Override
    public int getMarqueeId() {
      return marqueeId_;
    }

    public static final int DISPLAYDATA_FIELD_NUMBER = 2;
    private com.yorha.proto.Struct.DisplayData displayData_;
    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     * @return Whether the displayData field is set.
     */
    @java.lang.Override
    public boolean hasDisplayData() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     * @return The displayData.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.DisplayData getDisplayData() {
      return displayData_ == null ? com.yorha.proto.Struct.DisplayData.getDefaultInstance() : displayData_;
    }
    /**
     * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.DisplayDataOrBuilder getDisplayDataOrBuilder() {
      return displayData_ == null ? com.yorha.proto.Struct.DisplayData.getDefaultInstance() : displayData_;
    }

    public static final int CLANFLAGINFO_FIELD_NUMBER = 3;
    private com.yorha.proto.StructClan.ClanFlagInfo clanFlagInfo_;
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     * @return Whether the clanFlagInfo field is set.
     */
    @java.lang.Override
    public boolean hasClanFlagInfo() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     * @return The clanFlagInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructClan.ClanFlagInfo getClanFlagInfo() {
      return clanFlagInfo_ == null ? com.yorha.proto.StructClan.ClanFlagInfo.getDefaultInstance() : clanFlagInfo_;
    }
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructClan.ClanFlagInfoOrBuilder getClanFlagInfoOrBuilder() {
      return clanFlagInfo_ == null ? com.yorha.proto.StructClan.ClanFlagInfo.getDefaultInstance() : clanFlagInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, marqueeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeMessage(2, getDisplayData());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeMessage(3, getClanFlagInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, marqueeId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(2, getDisplayData());
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(3, getClanFlagInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd other = (com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd) obj;

      if (hasMarqueeId() != other.hasMarqueeId()) return false;
      if (hasMarqueeId()) {
        if (getMarqueeId()
            != other.getMarqueeId()) return false;
      }
      if (hasDisplayData() != other.hasDisplayData()) return false;
      if (hasDisplayData()) {
        if (!getDisplayData()
            .equals(other.getDisplayData())) return false;
      }
      if (hasClanFlagInfo() != other.hasClanFlagInfo()) return false;
      if (hasClanFlagInfo()) {
        if (!getClanFlagInfo()
            .equals(other.getClanFlagInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMarqueeId()) {
        hash = (37 * hash) + MARQUEEID_FIELD_NUMBER;
        hash = (53 * hash) + getMarqueeId();
      }
      if (hasDisplayData()) {
        hash = (37 * hash) + DISPLAYDATA_FIELD_NUMBER;
        hash = (53 * hash) + getDisplayData().hashCode();
      }
      if (hasClanFlagInfo()) {
        hash = (37 * hash) + CLANFLAGINFO_FIELD_NUMBER;
        hash = (53 * hash) + getClanFlagInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendSceneMarqueeCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendSceneMarqueeCmd)
        com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneMarqueeCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.class, com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getDisplayDataFieldBuilder();
          getClanFlagInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        marqueeId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        if (displayDataBuilder_ == null) {
          displayData_ = null;
        } else {
          displayDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        if (clanFlagInfoBuilder_ == null) {
          clanFlagInfo_ = null;
        } else {
          clanFlagInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd build() {
        com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd buildPartial() {
        com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd result = new com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.marqueeId_ = marqueeId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          if (displayDataBuilder_ == null) {
            result.displayData_ = displayData_;
          } else {
            result.displayData_ = displayDataBuilder_.build();
          }
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          if (clanFlagInfoBuilder_ == null) {
            result.clanFlagInfo_ = clanFlagInfo_;
          } else {
            result.clanFlagInfo_ = clanFlagInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000004;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd) {
          return mergeFrom((com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd other) {
        if (other == com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd.getDefaultInstance()) return this;
        if (other.hasMarqueeId()) {
          setMarqueeId(other.getMarqueeId());
        }
        if (other.hasDisplayData()) {
          mergeDisplayData(other.getDisplayData());
        }
        if (other.hasClanFlagInfo()) {
          mergeClanFlagInfo(other.getClanFlagInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int marqueeId_ ;
      /**
       * <code>optional int32 marqueeId = 1;</code>
       * @return Whether the marqueeId field is set.
       */
      @java.lang.Override
      public boolean hasMarqueeId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 marqueeId = 1;</code>
       * @return The marqueeId.
       */
      @java.lang.Override
      public int getMarqueeId() {
        return marqueeId_;
      }
      /**
       * <code>optional int32 marqueeId = 1;</code>
       * @param value The marqueeId to set.
       * @return This builder for chaining.
       */
      public Builder setMarqueeId(int value) {
        bitField0_ |= 0x00000001;
        marqueeId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 marqueeId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearMarqueeId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        marqueeId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.DisplayData displayData_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.DisplayData, com.yorha.proto.Struct.DisplayData.Builder, com.yorha.proto.Struct.DisplayDataOrBuilder> displayDataBuilder_;
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       * @return Whether the displayData field is set.
       */
      public boolean hasDisplayData() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       * @return The displayData.
       */
      public com.yorha.proto.Struct.DisplayData getDisplayData() {
        if (displayDataBuilder_ == null) {
          return displayData_ == null ? com.yorha.proto.Struct.DisplayData.getDefaultInstance() : displayData_;
        } else {
          return displayDataBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public Builder setDisplayData(com.yorha.proto.Struct.DisplayData value) {
        if (displayDataBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          displayData_ = value;
          onChanged();
        } else {
          displayDataBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public Builder setDisplayData(
          com.yorha.proto.Struct.DisplayData.Builder builderForValue) {
        if (displayDataBuilder_ == null) {
          displayData_ = builderForValue.build();
          onChanged();
        } else {
          displayDataBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public Builder mergeDisplayData(com.yorha.proto.Struct.DisplayData value) {
        if (displayDataBuilder_ == null) {
          if (((bitField0_ & 0x00000002) != 0) &&
              displayData_ != null &&
              displayData_ != com.yorha.proto.Struct.DisplayData.getDefaultInstance()) {
            displayData_ =
              com.yorha.proto.Struct.DisplayData.newBuilder(displayData_).mergeFrom(value).buildPartial();
          } else {
            displayData_ = value;
          }
          onChanged();
        } else {
          displayDataBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000002;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public Builder clearDisplayData() {
        if (displayDataBuilder_ == null) {
          displayData_ = null;
          onChanged();
        } else {
          displayDataBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public com.yorha.proto.Struct.DisplayData.Builder getDisplayDataBuilder() {
        bitField0_ |= 0x00000002;
        onChanged();
        return getDisplayDataFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      public com.yorha.proto.Struct.DisplayDataOrBuilder getDisplayDataOrBuilder() {
        if (displayDataBuilder_ != null) {
          return displayDataBuilder_.getMessageOrBuilder();
        } else {
          return displayData_ == null ?
              com.yorha.proto.Struct.DisplayData.getDefaultInstance() : displayData_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.DisplayData displayData = 2;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.DisplayData, com.yorha.proto.Struct.DisplayData.Builder, com.yorha.proto.Struct.DisplayDataOrBuilder> 
          getDisplayDataFieldBuilder() {
        if (displayDataBuilder_ == null) {
          displayDataBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.DisplayData, com.yorha.proto.Struct.DisplayData.Builder, com.yorha.proto.Struct.DisplayDataOrBuilder>(
                  getDisplayData(),
                  getParentForChildren(),
                  isClean());
          displayData_ = null;
        }
        return displayDataBuilder_;
      }

      private com.yorha.proto.StructClan.ClanFlagInfo clanFlagInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClan.ClanFlagInfo, com.yorha.proto.StructClan.ClanFlagInfo.Builder, com.yorha.proto.StructClan.ClanFlagInfoOrBuilder> clanFlagInfoBuilder_;
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       * @return Whether the clanFlagInfo field is set.
       */
      public boolean hasClanFlagInfo() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       * @return The clanFlagInfo.
       */
      public com.yorha.proto.StructClan.ClanFlagInfo getClanFlagInfo() {
        if (clanFlagInfoBuilder_ == null) {
          return clanFlagInfo_ == null ? com.yorha.proto.StructClan.ClanFlagInfo.getDefaultInstance() : clanFlagInfo_;
        } else {
          return clanFlagInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public Builder setClanFlagInfo(com.yorha.proto.StructClan.ClanFlagInfo value) {
        if (clanFlagInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          clanFlagInfo_ = value;
          onChanged();
        } else {
          clanFlagInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public Builder setClanFlagInfo(
          com.yorha.proto.StructClan.ClanFlagInfo.Builder builderForValue) {
        if (clanFlagInfoBuilder_ == null) {
          clanFlagInfo_ = builderForValue.build();
          onChanged();
        } else {
          clanFlagInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public Builder mergeClanFlagInfo(com.yorha.proto.StructClan.ClanFlagInfo value) {
        if (clanFlagInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000004) != 0) &&
              clanFlagInfo_ != null &&
              clanFlagInfo_ != com.yorha.proto.StructClan.ClanFlagInfo.getDefaultInstance()) {
            clanFlagInfo_ =
              com.yorha.proto.StructClan.ClanFlagInfo.newBuilder(clanFlagInfo_).mergeFrom(value).buildPartial();
          } else {
            clanFlagInfo_ = value;
          }
          onChanged();
        } else {
          clanFlagInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000004;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public Builder clearClanFlagInfo() {
        if (clanFlagInfoBuilder_ == null) {
          clanFlagInfo_ = null;
          onChanged();
        } else {
          clanFlagInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public com.yorha.proto.StructClan.ClanFlagInfo.Builder getClanFlagInfoBuilder() {
        bitField0_ |= 0x00000004;
        onChanged();
        return getClanFlagInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      public com.yorha.proto.StructClan.ClanFlagInfoOrBuilder getClanFlagInfoOrBuilder() {
        if (clanFlagInfoBuilder_ != null) {
          return clanFlagInfoBuilder_.getMessageOrBuilder();
        } else {
          return clanFlagInfo_ == null ?
              com.yorha.proto.StructClan.ClanFlagInfo.getDefaultInstance() : clanFlagInfo_;
        }
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfo clanFlagInfo = 3;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClan.ClanFlagInfo, com.yorha.proto.StructClan.ClanFlagInfo.Builder, com.yorha.proto.StructClan.ClanFlagInfoOrBuilder> 
          getClanFlagInfoFieldBuilder() {
        if (clanFlagInfoBuilder_ == null) {
          clanFlagInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructClan.ClanFlagInfo, com.yorha.proto.StructClan.ClanFlagInfo.Builder, com.yorha.proto.StructClan.ClanFlagInfoOrBuilder>(
                  getClanFlagInfo(),
                  getParentForChildren(),
                  isClean());
          clanFlagInfo_ = null;
        }
        return clanFlagInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendSceneMarqueeCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendSceneMarqueeCmd)
    private static final com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd();
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendSceneMarqueeCmd>
        PARSER = new com.google.protobuf.AbstractParser<SendSceneMarqueeCmd>() {
      @java.lang.Override
      public SendSceneMarqueeCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendSceneMarqueeCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendSceneMarqueeCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendSceneMarqueeCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMarquee.SendSceneMarqueeCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendSceneLoopMarqueeWithIdIpAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int32 oneLoopOfMs = 1;</code>
     * @return Whether the oneLoopOfMs field is set.
     */
    boolean hasOneLoopOfMs();
    /**
     * <code>optional int32 oneLoopOfMs = 1;</code>
     * @return The oneLoopOfMs.
     */
    int getOneLoopOfMs();

    /**
     * <code>optional int32 period = 2;</code>
     * @return Whether the period field is set.
     */
    boolean hasPeriod();
    /**
     * <code>optional int32 period = 2;</code>
     * @return The period.
     */
    int getPeriod();

    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    int getDisplayDataCount();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    boolean containsDisplayData(
        int key);
    /**
     * Use {@link #getDisplayDataMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
    getDisplayData();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
    getDisplayDataMap();
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */

    com.yorha.proto.Struct.DisplayData getDisplayDataOrDefault(
        int key,
        com.yorha.proto.Struct.DisplayData defaultValue);
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */

    com.yorha.proto.Struct.DisplayData getDisplayDataOrThrow(
        int key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk}
   */
  public static final class SendSceneLoopMarqueeWithIdIpAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk)
      SendSceneLoopMarqueeWithIdIpAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendSceneLoopMarqueeWithIdIpAsk.newBuilder() to construct.
    private SendSceneLoopMarqueeWithIdIpAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendSceneLoopMarqueeWithIdIpAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendSceneLoopMarqueeWithIdIpAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendSceneLoopMarqueeWithIdIpAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              oneLoopOfMs_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              period_ = input.readInt32();
              break;
            }
            case 26: {
              if (!((mutable_bitField0_ & 0x00000004) != 0)) {
                displayData_ = com.google.protobuf.MapField.newMapField(
                    DisplayDataDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000004;
              }
              com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
              displayData__ = input.readMessage(
                  DisplayDataDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              displayData_.getMutableMap().put(
                  displayData__.getKey(), displayData__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 3:
          return internalGetDisplayData();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.class, com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ONELOOPOFMS_FIELD_NUMBER = 1;
    private int oneLoopOfMs_;
    /**
     * <code>optional int32 oneLoopOfMs = 1;</code>
     * @return Whether the oneLoopOfMs field is set.
     */
    @java.lang.Override
    public boolean hasOneLoopOfMs() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int32 oneLoopOfMs = 1;</code>
     * @return The oneLoopOfMs.
     */
    @java.lang.Override
    public int getOneLoopOfMs() {
      return oneLoopOfMs_;
    }

    public static final int PERIOD_FIELD_NUMBER = 2;
    private int period_;
    /**
     * <code>optional int32 period = 2;</code>
     * @return Whether the period field is set.
     */
    @java.lang.Override
    public boolean hasPeriod() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 period = 2;</code>
     * @return The period.
     */
    @java.lang.Override
    public int getPeriod() {
      return period_;
    }

    public static final int DISPLAYDATA_FIELD_NUMBER = 3;
    private static final class DisplayDataDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Integer, com.yorha.proto.Struct.DisplayData> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Integer, com.yorha.proto.Struct.DisplayData>newDefaultInstance(
                  com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT32,
                  0,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.Struct.DisplayData.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Integer, com.yorha.proto.Struct.DisplayData> displayData_;
    private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
    internalGetDisplayData() {
      if (displayData_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            DisplayDataDefaultEntryHolder.defaultEntry);
      }
      return displayData_;
    }

    public int getDisplayDataCount() {
      return internalGetDisplayData().getMap().size();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */

    @java.lang.Override
    public boolean containsDisplayData(
        int key) {
      
      return internalGetDisplayData().getMap().containsKey(key);
    }
    /**
     * Use {@link #getDisplayDataMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> getDisplayData() {
      return getDisplayDataMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> getDisplayDataMap() {
      return internalGetDisplayData().getMap();
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.DisplayData getDisplayDataOrDefault(
        int key,
        com.yorha.proto.Struct.DisplayData defaultValue) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> map =
          internalGetDisplayData().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
     */
    @java.lang.Override

    public com.yorha.proto.Struct.DisplayData getDisplayDataOrThrow(
        int key) {
      
      java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> map =
          internalGetDisplayData().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, oneLoopOfMs_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, period_);
      }
      com.google.protobuf.GeneratedMessageV3
        .serializeIntegerMapTo(
          output,
          internalGetDisplayData(),
          DisplayDataDefaultEntryHolder.defaultEntry,
          3);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, oneLoopOfMs_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, period_);
      }
      for (java.util.Map.Entry<java.lang.Integer, com.yorha.proto.Struct.DisplayData> entry
           : internalGetDisplayData().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
        displayData__ = DisplayDataDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(3, displayData__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk other = (com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk) obj;

      if (hasOneLoopOfMs() != other.hasOneLoopOfMs()) return false;
      if (hasOneLoopOfMs()) {
        if (getOneLoopOfMs()
            != other.getOneLoopOfMs()) return false;
      }
      if (hasPeriod() != other.hasPeriod()) return false;
      if (hasPeriod()) {
        if (getPeriod()
            != other.getPeriod()) return false;
      }
      if (!internalGetDisplayData().equals(
          other.internalGetDisplayData())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasOneLoopOfMs()) {
        hash = (37 * hash) + ONELOOPOFMS_FIELD_NUMBER;
        hash = (53 * hash) + getOneLoopOfMs();
      }
      if (hasPeriod()) {
        hash = (37 * hash) + PERIOD_FIELD_NUMBER;
        hash = (53 * hash) + getPeriod();
      }
      if (!internalGetDisplayData().getMap().isEmpty()) {
        hash = (37 * hash) + DISPLAYDATA_FIELD_NUMBER;
        hash = (53 * hash) + internalGetDisplayData().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk)
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetDisplayData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 3:
            return internalGetMutableDisplayData();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.class, com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        oneLoopOfMs_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        period_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        internalGetMutableDisplayData().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk build() {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk buildPartial() {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk result = new com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.oneLoopOfMs_ = oneLoopOfMs_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.period_ = period_;
          to_bitField0_ |= 0x00000002;
        }
        result.displayData_ = internalGetDisplayData();
        result.displayData_.makeImmutable();
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk other) {
        if (other == com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.getDefaultInstance()) return this;
        if (other.hasOneLoopOfMs()) {
          setOneLoopOfMs(other.getOneLoopOfMs());
        }
        if (other.hasPeriod()) {
          setPeriod(other.getPeriod());
        }
        internalGetMutableDisplayData().mergeFrom(
            other.internalGetDisplayData());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int oneLoopOfMs_ ;
      /**
       * <code>optional int32 oneLoopOfMs = 1;</code>
       * @return Whether the oneLoopOfMs field is set.
       */
      @java.lang.Override
      public boolean hasOneLoopOfMs() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int32 oneLoopOfMs = 1;</code>
       * @return The oneLoopOfMs.
       */
      @java.lang.Override
      public int getOneLoopOfMs() {
        return oneLoopOfMs_;
      }
      /**
       * <code>optional int32 oneLoopOfMs = 1;</code>
       * @param value The oneLoopOfMs to set.
       * @return This builder for chaining.
       */
      public Builder setOneLoopOfMs(int value) {
        bitField0_ |= 0x00000001;
        oneLoopOfMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 oneLoopOfMs = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearOneLoopOfMs() {
        bitField0_ = (bitField0_ & ~0x00000001);
        oneLoopOfMs_ = 0;
        onChanged();
        return this;
      }

      private int period_ ;
      /**
       * <code>optional int32 period = 2;</code>
       * @return Whether the period field is set.
       */
      @java.lang.Override
      public boolean hasPeriod() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 period = 2;</code>
       * @return The period.
       */
      @java.lang.Override
      public int getPeriod() {
        return period_;
      }
      /**
       * <code>optional int32 period = 2;</code>
       * @param value The period to set.
       * @return This builder for chaining.
       */
      public Builder setPeriod(int value) {
        bitField0_ |= 0x00000002;
        period_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 period = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPeriod() {
        bitField0_ = (bitField0_ & ~0x00000002);
        period_ = 0;
        onChanged();
        return this;
      }

      private com.google.protobuf.MapField<
          java.lang.Integer, com.yorha.proto.Struct.DisplayData> displayData_;
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
      internalGetDisplayData() {
        if (displayData_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              DisplayDataDefaultEntryHolder.defaultEntry);
        }
        return displayData_;
      }
      private com.google.protobuf.MapField<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
      internalGetMutableDisplayData() {
        onChanged();;
        if (displayData_ == null) {
          displayData_ = com.google.protobuf.MapField.newMapField(
              DisplayDataDefaultEntryHolder.defaultEntry);
        }
        if (!displayData_.isMutable()) {
          displayData_ = displayData_.copy();
        }
        return displayData_;
      }

      public int getDisplayDataCount() {
        return internalGetDisplayData().getMap().size();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */

      @java.lang.Override
      public boolean containsDisplayData(
          int key) {
        
        return internalGetDisplayData().getMap().containsKey(key);
      }
      /**
       * Use {@link #getDisplayDataMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> getDisplayData() {
        return getDisplayDataMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> getDisplayDataMap() {
        return internalGetDisplayData().getMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.DisplayData getDisplayDataOrDefault(
          int key,
          com.yorha.proto.Struct.DisplayData defaultValue) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> map =
            internalGetDisplayData().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */
      @java.lang.Override

      public com.yorha.proto.Struct.DisplayData getDisplayDataOrThrow(
          int key) {
        
        java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> map =
            internalGetDisplayData().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearDisplayData() {
        internalGetMutableDisplayData().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */

      public Builder removeDisplayData(
          int key) {
        
        internalGetMutableDisplayData().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData>
      getMutableDisplayData() {
        return internalGetMutableDisplayData().getMutableMap();
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */
      public Builder putDisplayData(
          int key,
          com.yorha.proto.Struct.DisplayData value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableDisplayData().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int32, .com.yorha.proto.DisplayData&gt; displayData = 3;</code>
       */

      public Builder putAllDisplayData(
          java.util.Map<java.lang.Integer, com.yorha.proto.Struct.DisplayData> values) {
        internalGetMutableDisplayData().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAsk)
    private static final com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk();
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAsk>
        PARSER = new com.google.protobuf.AbstractParser<SendSceneLoopMarqueeWithIdIpAsk>() {
      @java.lang.Override
      public SendSceneLoopMarqueeWithIdIpAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendSceneLoopMarqueeWithIdIpAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SendSceneLoopMarqueeWithIdIpAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns}
   */
  public static final class SendSceneLoopMarqueeWithIdIpAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns)
      SendSceneLoopMarqueeWithIdIpAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendSceneLoopMarqueeWithIdIpAns.newBuilder() to construct.
    private SendSceneLoopMarqueeWithIdIpAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendSceneLoopMarqueeWithIdIpAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendSceneLoopMarqueeWithIdIpAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendSceneLoopMarqueeWithIdIpAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.class, com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns other = (com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns)
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.class, com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMarquee.internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns build() {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns buildPartial() {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns result = new com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns) {
          return mergeFrom((com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns other) {
        if (other == com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendSceneLoopMarqueeWithIdIpAns)
    private static final com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns();
    }

    public static com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAns>
        PARSER = new com.google.protobuf.AbstractParser<SendSceneLoopMarqueeWithIdIpAns>() {
      @java.lang.Override
      public SendSceneLoopMarqueeWithIdIpAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendSceneLoopMarqueeWithIdIpAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendSceneLoopMarqueeWithIdIpAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendSceneMarqueeCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n)ss_proto/gen/scene/ss_scene_marquee.pr" +
      "oto\022\017com.yorha.proto\032 ss_proto/gen/commo" +
      "n/struct.proto\032%ss_proto/gen/common/stru" +
      "ct_clan.proto\"\220\001\n\023SendSceneMarqueeCmd\022\021\n" +
      "\tmarqueeId\030\001 \001(\005\0221\n\013displayData\030\002 \001(\0132\034." +
      "com.yorha.proto.DisplayData\0223\n\014clanFlagI" +
      "nfo\030\003 \001(\0132\035.com.yorha.proto.ClanFlagInfo" +
      "\"\360\001\n\037SendSceneLoopMarqueeWithIdIpAsk\022\023\n\013" +
      "oneLoopOfMs\030\001 \001(\005\022\016\n\006period\030\002 \001(\005\022V\n\013dis" +
      "playData\030\003 \003(\0132A.com.yorha.proto.SendSce" +
      "neLoopMarqueeWithIdIpAsk.DisplayDataEntr" +
      "y\032P\n\020DisplayDataEntry\022\013\n\003key\030\001 \001(\005\022+\n\005va" +
      "lue\030\002 \001(\0132\034.com.yorha.proto.DisplayData:" +
      "\0028\001\"!\n\037SendSceneLoopMarqueeWithIdIpAnsB\002" +
      "H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructClan.getDescriptor(),
        });
    internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SendSceneMarqueeCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendSceneMarqueeCmd_descriptor,
        new java.lang.String[] { "MarqueeId", "DisplayData", "ClanFlagInfo", });
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor,
        new java.lang.String[] { "OneLoopOfMs", "Period", "DisplayData", });
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_descriptor =
      internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAsk_DisplayDataEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendSceneLoopMarqueeWithIdIpAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructClan.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

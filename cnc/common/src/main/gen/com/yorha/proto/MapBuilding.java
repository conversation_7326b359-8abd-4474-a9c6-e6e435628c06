// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/mapBuilding/map_building.proto

package com.yorha.proto;

public final class MapBuilding {
  private MapBuilding() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface MapBuildingEntityOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MapBuildingEntity)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    com.yorha.proto.Struct.Point getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    boolean hasPartId();
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    int getPartId();

    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     * @return Whether the innerArmy field is set.
     */
    boolean hasInnerArmy();
    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     * @return The innerArmy.
     */
    com.yorha.proto.Struct.CityInnerArmy getInnerArmy();
    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     */
    com.yorha.proto.Struct.CityInnerArmyOrBuilder getInnerArmyOrBuilder();

    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     * @return Whether the occupyinfo field is set.
     */
    boolean hasOccupyinfo();
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     * @return The occupyinfo.
     */
    com.yorha.proto.MapBuilding.OccupyInfo getOccupyinfo();
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     */
    com.yorha.proto.MapBuilding.OccupyInfoOrBuilder getOccupyinfoOrBuilder();

    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     * @return Whether the troop field is set.
     */
    boolean hasTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     * @return The troop.
     */
    com.yorha.proto.StructPlayer.Troop getTroop();
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     */
    com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder();

    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     * @return Whether the battle field is set.
     */
    boolean hasBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     * @return The battle.
     */
    com.yorha.proto.StructBattle.Battle getBattle();
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     */
    com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder();

    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     * @return Whether the buffSys field is set.
     */
    boolean hasBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     * @return The buffSys.
     */
    com.yorha.proto.StructBattle.BuffSys getBuffSys();
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     */
    com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder();

    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     * @return Whether the constructInfo field is set.
     */
    boolean hasConstructInfo();
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     * @return The constructInfo.
     */
    com.yorha.proto.MapBuilding.ConstructInfo getConstructInfo();
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     */
    com.yorha.proto.MapBuilding.ConstructInfoOrBuilder getConstructInfoOrBuilder();

    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     * @return Whether the devBuffSys field is set.
     */
    boolean hasDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     * @return The devBuffSys.
     */
    com.yorha.proto.StructBattle.SceneDevBuffSys getDevBuffSys();
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     */
    com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder getDevBuffSysOrBuilder();

    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     * @return Whether the safeGuard field is set.
     */
    boolean hasSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     * @return The safeGuard.
     */
    com.yorha.proto.Struct.SpecialSafeGuard getSafeGuard();
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     */
    com.yorha.proto.Struct.SpecialSafeGuardOrBuilder getSafeGuardOrBuilder();

    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     * @return Whether the recommendSoldierTypeList field is set.
     */
    boolean hasRecommendSoldierTypeList();
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     * @return The recommendSoldierTypeList.
     */
    com.yorha.proto.Basic.Int32List getRecommendSoldierTypeList();
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     */
    com.yorha.proto.Basic.Int32ListOrBuilder getRecommendSoldierTypeListOrBuilder();

    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return Whether the passingArmyNum field is set.
     */
    boolean hasPassingArmyNum();
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return The passingArmyNum.
     */
    int getPassingArmyNum();

    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     * @return Whether the expression field is set.
     */
    boolean hasExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     * @return The expression.
     */
    com.yorha.proto.Struct.Expression getExpression();
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     */
    com.yorha.proto.Struct.ExpressionOrBuilder getExpressionOrBuilder();

    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     * @return Whether the kingdomModel field is set.
     */
    boolean hasKingdomModel();
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     * @return The kingdomModel.
     */
    com.yorha.proto.MapBuilding.MapBuildingKingdomModel getKingdomModel();
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     */
    com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder getKingdomModelOrBuilder();

    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    boolean hasArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     * @return The arrow.
     */
    com.yorha.proto.Struct.Int64ArmyArrowItemMap getArrow();
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     */
    com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder getArrowOrBuilder();
  }
  /**
   * <pre>
   * 地图中立建筑
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.MapBuildingEntity}
   */
  public static final class MapBuildingEntity extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MapBuildingEntity)
      MapBuildingEntityOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MapBuildingEntity.newBuilder() to construct.
    private MapBuildingEntity(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MapBuildingEntity() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MapBuildingEntity();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MapBuildingEntity(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.Point.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.Struct.Point.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              partId_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              templateId_ = input.readInt32();
              break;
            }
            case 34: {
              com.yorha.proto.Struct.CityInnerArmy.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = innerArmy_.toBuilder();
              }
              innerArmy_ = input.readMessage(com.yorha.proto.Struct.CityInnerArmy.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(innerArmy_);
                innerArmy_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 42: {
              com.yorha.proto.MapBuilding.OccupyInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000010) != 0)) {
                subBuilder = occupyinfo_.toBuilder();
              }
              occupyinfo_ = input.readMessage(com.yorha.proto.MapBuilding.OccupyInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(occupyinfo_);
                occupyinfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000010;
              break;
            }
            case 50: {
              com.yorha.proto.StructPlayer.Troop.Builder subBuilder = null;
              if (((bitField0_ & 0x00000020) != 0)) {
                subBuilder = troop_.toBuilder();
              }
              troop_ = input.readMessage(com.yorha.proto.StructPlayer.Troop.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(troop_);
                troop_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000020;
              break;
            }
            case 58: {
              com.yorha.proto.StructBattle.Battle.Builder subBuilder = null;
              if (((bitField0_ & 0x00000040) != 0)) {
                subBuilder = battle_.toBuilder();
              }
              battle_ = input.readMessage(com.yorha.proto.StructBattle.Battle.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(battle_);
                battle_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000040;
              break;
            }
            case 66: {
              com.yorha.proto.StructBattle.BuffSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = buffSys_.toBuilder();
              }
              buffSys_ = input.readMessage(com.yorha.proto.StructBattle.BuffSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(buffSys_);
                buffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            case 74: {
              com.yorha.proto.MapBuilding.ConstructInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) != 0)) {
                subBuilder = constructInfo_.toBuilder();
              }
              constructInfo_ = input.readMessage(com.yorha.proto.MapBuilding.ConstructInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(constructInfo_);
                constructInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            case 90: {
              com.yorha.proto.StructBattle.SceneDevBuffSys.Builder subBuilder = null;
              if (((bitField0_ & 0x00000200) != 0)) {
                subBuilder = devBuffSys_.toBuilder();
              }
              devBuffSys_ = input.readMessage(com.yorha.proto.StructBattle.SceneDevBuffSys.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(devBuffSys_);
                devBuffSys_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000200;
              break;
            }
            case 98: {
              com.yorha.proto.Struct.SpecialSafeGuard.Builder subBuilder = null;
              if (((bitField0_ & 0x00000400) != 0)) {
                subBuilder = safeGuard_.toBuilder();
              }
              safeGuard_ = input.readMessage(com.yorha.proto.Struct.SpecialSafeGuard.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(safeGuard_);
                safeGuard_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000400;
              break;
            }
            case 106: {
              com.yorha.proto.Basic.Int32List.Builder subBuilder = null;
              if (((bitField0_ & 0x00000800) != 0)) {
                subBuilder = recommendSoldierTypeList_.toBuilder();
              }
              recommendSoldierTypeList_ = input.readMessage(com.yorha.proto.Basic.Int32List.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(recommendSoldierTypeList_);
                recommendSoldierTypeList_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000800;
              break;
            }
            case 112: {
              bitField0_ |= 0x00001000;
              passingArmyNum_ = input.readInt32();
              break;
            }
            case 122: {
              com.yorha.proto.Struct.Expression.Builder subBuilder = null;
              if (((bitField0_ & 0x00002000) != 0)) {
                subBuilder = expression_.toBuilder();
              }
              expression_ = input.readMessage(com.yorha.proto.Struct.Expression.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(expression_);
                expression_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00002000;
              break;
            }
            case 130: {
              com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder subBuilder = null;
              if (((bitField0_ & 0x00004000) != 0)) {
                subBuilder = kingdomModel_.toBuilder();
              }
              kingdomModel_ = input.readMessage(com.yorha.proto.MapBuilding.MapBuildingKingdomModel.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(kingdomModel_);
                kingdomModel_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00004000;
              break;
            }
            case 186: {
              com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder subBuilder = null;
              if (((bitField0_ & 0x00008000) != 0)) {
                subBuilder = arrow_.toBuilder();
              }
              arrow_ = input.readMessage(com.yorha.proto.Struct.Int64ArmyArrowItemMap.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(arrow_);
                arrow_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00008000;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingEntity_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingEntity_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuilding.MapBuildingEntity.class, com.yorha.proto.MapBuilding.MapBuildingEntity.Builder.class);
    }

    private int bitField0_;
    public static final int POINT_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.Point point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Point getPoint() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.Point point = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
    }

    public static final int PARTID_FIELD_NUMBER = 2;
    private int partId_;
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return Whether the partId field is set.
     */
    @java.lang.Override
    public boolean hasPartId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 所属片id
     * </pre>
     *
     * <code>optional int32 partId = 2;</code>
     * @return The partId.
     */
    @java.lang.Override
    public int getPartId() {
      return partId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 3;
    private int templateId_;
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 建筑配置id
     * </pre>
     *
     * <code>optional int32 templateId = 3;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int INNERARMY_FIELD_NUMBER = 4;
    private com.yorha.proto.Struct.CityInnerArmy innerArmy_;
    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     * @return Whether the innerArmy field is set.
     */
    @java.lang.Override
    public boolean hasInnerArmy() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     * @return The innerArmy.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.CityInnerArmy getInnerArmy() {
      return innerArmy_ == null ? com.yorha.proto.Struct.CityInnerArmy.getDefaultInstance() : innerArmy_;
    }
    /**
     * <pre>
     * 城内军队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.CityInnerArmyOrBuilder getInnerArmyOrBuilder() {
      return innerArmy_ == null ? com.yorha.proto.Struct.CityInnerArmy.getDefaultInstance() : innerArmy_;
    }

    public static final int OCCUPYINFO_FIELD_NUMBER = 5;
    private com.yorha.proto.MapBuilding.OccupyInfo occupyinfo_;
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     * @return Whether the occupyinfo field is set.
     */
    @java.lang.Override
    public boolean hasOccupyinfo() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     * @return The occupyinfo.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.OccupyInfo getOccupyinfo() {
      return occupyinfo_ == null ? com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance() : occupyinfo_;
    }
    /**
     * <pre>
     * 占领数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.OccupyInfoOrBuilder getOccupyinfoOrBuilder() {
      return occupyinfo_ == null ? com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance() : occupyinfo_;
    }

    public static final int TROOP_FIELD_NUMBER = 6;
    private com.yorha.proto.StructPlayer.Troop troop_;
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     * @return Whether the troop field is set.
     */
    @java.lang.Override
    public boolean hasTroop() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     * @return The troop.
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.Troop getTroop() {
      return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
    }
    /**
     * <pre>
     * 战斗时部队情况
     * </pre>
     *
     * <code>optional .com.yorha.proto.Troop troop = 6;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder() {
      return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
    }

    public static final int BATTLE_FIELD_NUMBER = 7;
    private com.yorha.proto.StructBattle.Battle battle_;
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     * @return Whether the battle field is set.
     */
    @java.lang.Override
    public boolean hasBattle() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     * @return The battle.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.Battle getBattle() {
      return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
    }
    /**
     * <pre>
     * 战斗状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.Battle battle = 7;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder() {
      return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
    }

    public static final int BUFFSYS_FIELD_NUMBER = 8;
    private com.yorha.proto.StructBattle.BuffSys buffSys_;
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     * @return Whether the buffSys field is set.
     */
    @java.lang.Override
    public boolean hasBuffSys() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     * @return The buffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BuffSys getBuffSys() {
      return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
    }
    /**
     * <pre>
     * buff增益
     * </pre>
     *
     * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder() {
      return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
    }

    public static final int CONSTRUCTINFO_FIELD_NUMBER = 9;
    private com.yorha.proto.MapBuilding.ConstructInfo constructInfo_;
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     * @return Whether the constructInfo field is set.
     */
    @java.lang.Override
    public boolean hasConstructInfo() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     * @return The constructInfo.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.ConstructInfo getConstructInfo() {
      return constructInfo_ == null ? com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance() : constructInfo_;
    }
    /**
     * <pre>
     * 联盟建筑建造数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.ConstructInfoOrBuilder getConstructInfoOrBuilder() {
      return constructInfo_ == null ? com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance() : constructInfo_;
    }

    public static final int DEVBUFFSYS_FIELD_NUMBER = 11;
    private com.yorha.proto.StructBattle.SceneDevBuffSys devBuffSys_;
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     * @return Whether the devBuffSys field is set.
     */
    @java.lang.Override
    public boolean hasDevBuffSys() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     * @return The devBuffSys.
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.SceneDevBuffSys getDevBuffSys() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattle.SceneDevBuffSys.getDefaultInstance() : devBuffSys_;
    }
    /**
     * <pre>
     * devbuff 只用于展示
     * </pre>
     *
     * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder getDevBuffSysOrBuilder() {
      return devBuffSys_ == null ? com.yorha.proto.StructBattle.SceneDevBuffSys.getDefaultInstance() : devBuffSys_;
    }

    public static final int SAFEGUARD_FIELD_NUMBER = 12;
    private com.yorha.proto.Struct.SpecialSafeGuard safeGuard_;
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     * @return Whether the safeGuard field is set.
     */
    @java.lang.Override
    public boolean hasSafeGuard() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     * @return The safeGuard.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.SpecialSafeGuard getSafeGuard() {
      return safeGuard_ == null ? com.yorha.proto.Struct.SpecialSafeGuard.getDefaultInstance() : safeGuard_;
    }
    /**
     * <pre>
     * 特殊罩子（非和平护盾）
     * </pre>
     *
     * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.SpecialSafeGuardOrBuilder getSafeGuardOrBuilder() {
      return safeGuard_ == null ? com.yorha.proto.Struct.SpecialSafeGuard.getDefaultInstance() : safeGuard_;
    }

    public static final int RECOMMENDSOLDIERTYPELIST_FIELD_NUMBER = 13;
    private com.yorha.proto.Basic.Int32List recommendSoldierTypeList_;
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     * @return Whether the recommendSoldierTypeList field is set.
     */
    @java.lang.Override
    public boolean hasRecommendSoldierTypeList() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     * @return The recommendSoldierTypeList.
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int32List getRecommendSoldierTypeList() {
      return recommendSoldierTypeList_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : recommendSoldierTypeList_;
    }
    /**
     * <pre>
     * 推荐兵种列表
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Basic.Int32ListOrBuilder getRecommendSoldierTypeListOrBuilder() {
      return recommendSoldierTypeList_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : recommendSoldierTypeList_;
    }

    public static final int PASSINGARMYNUM_FIELD_NUMBER = 14;
    private int passingArmyNum_;
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return Whether the passingArmyNum field is set.
     */
    @java.lang.Override
    public boolean hasPassingArmyNum() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 正在通关的人数
     * </pre>
     *
     * <code>optional int32 passingArmyNum = 14;</code>
     * @return The passingArmyNum.
     */
    @java.lang.Override
    public int getPassingArmyNum() {
      return passingArmyNum_;
    }

    public static final int EXPRESSION_FIELD_NUMBER = 15;
    private com.yorha.proto.Struct.Expression expression_;
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     * @return Whether the expression field is set.
     */
    @java.lang.Override
    public boolean hasExpression() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     * @return The expression.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Expression getExpression() {
      return expression_ == null ? com.yorha.proto.Struct.Expression.getDefaultInstance() : expression_;
    }
    /**
     * <pre>
     * 表情
     * </pre>
     *
     * <code>optional .com.yorha.proto.Expression expression = 15;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.ExpressionOrBuilder getExpressionOrBuilder() {
      return expression_ == null ? com.yorha.proto.Struct.Expression.getDefaultInstance() : expression_;
    }

    public static final int KINGDOMMODEL_FIELD_NUMBER = 16;
    private com.yorha.proto.MapBuilding.MapBuildingKingdomModel kingdomModel_;
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     * @return Whether the kingdomModel field is set.
     */
    @java.lang.Override
    public boolean hasKingdomModel() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     * @return The kingdomModel.
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingKingdomModel getKingdomModel() {
      return kingdomModel_ == null ? com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance() : kingdomModel_;
    }
    /**
     * <pre>
     * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
     */
    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder getKingdomModelOrBuilder() {
      return kingdomModel_ == null ? com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance() : kingdomModel_;
    }

    public static final int ARROW_FIELD_NUMBER = 23;
    private com.yorha.proto.Struct.Int64ArmyArrowItemMap arrow_;
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     * @return Whether the arrow field is set.
     */
    @java.lang.Override
    public boolean hasArrow() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     * @return The arrow.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Int64ArmyArrowItemMap getArrow() {
      return arrow_ == null ? com.yorha.proto.Struct.Int64ArmyArrowItemMap.getDefaultInstance() : arrow_;
    }
    /**
     * <pre>
     * 小箭头
     * </pre>
     *
     * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder getArrowOrBuilder() {
      return arrow_ == null ? com.yorha.proto.Struct.Int64ArmyArrowItemMap.getDefaultInstance() : arrow_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getInnerArmy());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeMessage(5, getOccupyinfo());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeMessage(6, getTroop());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeMessage(7, getBattle());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(8, getBuffSys());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(9, getConstructInfo());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeMessage(11, getDevBuffSys());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeMessage(12, getSafeGuard());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeMessage(13, getRecommendSoldierTypeList());
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeInt32(14, passingArmyNum_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeMessage(15, getExpression());
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeMessage(16, getKingdomModel());
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeMessage(23, getArrow());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getPoint());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, partId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, templateId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getInnerArmy());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(5, getOccupyinfo());
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(6, getTroop());
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(7, getBattle());
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(8, getBuffSys());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(9, getConstructInfo());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getDevBuffSys());
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(12, getSafeGuard());
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(13, getRecommendSoldierTypeList());
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(14, passingArmyNum_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(15, getExpression());
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(16, getKingdomModel());
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(23, getArrow());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuilding.MapBuildingEntity)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuilding.MapBuildingEntity other = (com.yorha.proto.MapBuilding.MapBuildingEntity) obj;

      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasPartId() != other.hasPartId()) return false;
      if (hasPartId()) {
        if (getPartId()
            != other.getPartId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasInnerArmy() != other.hasInnerArmy()) return false;
      if (hasInnerArmy()) {
        if (!getInnerArmy()
            .equals(other.getInnerArmy())) return false;
      }
      if (hasOccupyinfo() != other.hasOccupyinfo()) return false;
      if (hasOccupyinfo()) {
        if (!getOccupyinfo()
            .equals(other.getOccupyinfo())) return false;
      }
      if (hasTroop() != other.hasTroop()) return false;
      if (hasTroop()) {
        if (!getTroop()
            .equals(other.getTroop())) return false;
      }
      if (hasBattle() != other.hasBattle()) return false;
      if (hasBattle()) {
        if (!getBattle()
            .equals(other.getBattle())) return false;
      }
      if (hasBuffSys() != other.hasBuffSys()) return false;
      if (hasBuffSys()) {
        if (!getBuffSys()
            .equals(other.getBuffSys())) return false;
      }
      if (hasConstructInfo() != other.hasConstructInfo()) return false;
      if (hasConstructInfo()) {
        if (!getConstructInfo()
            .equals(other.getConstructInfo())) return false;
      }
      if (hasDevBuffSys() != other.hasDevBuffSys()) return false;
      if (hasDevBuffSys()) {
        if (!getDevBuffSys()
            .equals(other.getDevBuffSys())) return false;
      }
      if (hasSafeGuard() != other.hasSafeGuard()) return false;
      if (hasSafeGuard()) {
        if (!getSafeGuard()
            .equals(other.getSafeGuard())) return false;
      }
      if (hasRecommendSoldierTypeList() != other.hasRecommendSoldierTypeList()) return false;
      if (hasRecommendSoldierTypeList()) {
        if (!getRecommendSoldierTypeList()
            .equals(other.getRecommendSoldierTypeList())) return false;
      }
      if (hasPassingArmyNum() != other.hasPassingArmyNum()) return false;
      if (hasPassingArmyNum()) {
        if (getPassingArmyNum()
            != other.getPassingArmyNum()) return false;
      }
      if (hasExpression() != other.hasExpression()) return false;
      if (hasExpression()) {
        if (!getExpression()
            .equals(other.getExpression())) return false;
      }
      if (hasKingdomModel() != other.hasKingdomModel()) return false;
      if (hasKingdomModel()) {
        if (!getKingdomModel()
            .equals(other.getKingdomModel())) return false;
      }
      if (hasArrow() != other.hasArrow()) return false;
      if (hasArrow()) {
        if (!getArrow()
            .equals(other.getArrow())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasPartId()) {
        hash = (37 * hash) + PARTID_FIELD_NUMBER;
        hash = (53 * hash) + getPartId();
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasInnerArmy()) {
        hash = (37 * hash) + INNERARMY_FIELD_NUMBER;
        hash = (53 * hash) + getInnerArmy().hashCode();
      }
      if (hasOccupyinfo()) {
        hash = (37 * hash) + OCCUPYINFO_FIELD_NUMBER;
        hash = (53 * hash) + getOccupyinfo().hashCode();
      }
      if (hasTroop()) {
        hash = (37 * hash) + TROOP_FIELD_NUMBER;
        hash = (53 * hash) + getTroop().hashCode();
      }
      if (hasBattle()) {
        hash = (37 * hash) + BATTLE_FIELD_NUMBER;
        hash = (53 * hash) + getBattle().hashCode();
      }
      if (hasBuffSys()) {
        hash = (37 * hash) + BUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getBuffSys().hashCode();
      }
      if (hasConstructInfo()) {
        hash = (37 * hash) + CONSTRUCTINFO_FIELD_NUMBER;
        hash = (53 * hash) + getConstructInfo().hashCode();
      }
      if (hasDevBuffSys()) {
        hash = (37 * hash) + DEVBUFFSYS_FIELD_NUMBER;
        hash = (53 * hash) + getDevBuffSys().hashCode();
      }
      if (hasSafeGuard()) {
        hash = (37 * hash) + SAFEGUARD_FIELD_NUMBER;
        hash = (53 * hash) + getSafeGuard().hashCode();
      }
      if (hasRecommendSoldierTypeList()) {
        hash = (37 * hash) + RECOMMENDSOLDIERTYPELIST_FIELD_NUMBER;
        hash = (53 * hash) + getRecommendSoldierTypeList().hashCode();
      }
      if (hasPassingArmyNum()) {
        hash = (37 * hash) + PASSINGARMYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getPassingArmyNum();
      }
      if (hasExpression()) {
        hash = (37 * hash) + EXPRESSION_FIELD_NUMBER;
        hash = (53 * hash) + getExpression().hashCode();
      }
      if (hasKingdomModel()) {
        hash = (37 * hash) + KINGDOMMODEL_FIELD_NUMBER;
        hash = (53 * hash) + getKingdomModel().hashCode();
      }
      if (hasArrow()) {
        hash = (37 * hash) + ARROW_FIELD_NUMBER;
        hash = (53 * hash) + getArrow().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingEntity parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuilding.MapBuildingEntity prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 地图中立建筑
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.MapBuildingEntity}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MapBuildingEntity)
        com.yorha.proto.MapBuilding.MapBuildingEntityOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingEntity_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingEntity_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuilding.MapBuildingEntity.class, com.yorha.proto.MapBuilding.MapBuildingEntity.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuilding.MapBuildingEntity.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getInnerArmyFieldBuilder();
          getOccupyinfoFieldBuilder();
          getTroopFieldBuilder();
          getBattleFieldBuilder();
          getBuffSysFieldBuilder();
          getConstructInfoFieldBuilder();
          getDevBuffSysFieldBuilder();
          getSafeGuardFieldBuilder();
          getRecommendSoldierTypeListFieldBuilder();
          getExpressionFieldBuilder();
          getKingdomModelFieldBuilder();
          getArrowFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        partId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (innerArmyBuilder_ == null) {
          innerArmy_ = null;
        } else {
          innerArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = null;
        } else {
          occupyinfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        if (troopBuilder_ == null) {
          troop_ = null;
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        if (battleBuilder_ == null) {
          battle_ = null;
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (constructInfoBuilder_ == null) {
          constructInfo_ = null;
        } else {
          constructInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = null;
        } else {
          recommendSoldierTypeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000800);
        passingArmyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        if (expressionBuilder_ == null) {
          expression_ = null;
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = null;
        } else {
          kingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        if (arrowBuilder_ == null) {
          arrow_ = null;
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingEntity_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingEntity getDefaultInstanceForType() {
        return com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingEntity build() {
        com.yorha.proto.MapBuilding.MapBuildingEntity result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingEntity buildPartial() {
        com.yorha.proto.MapBuilding.MapBuildingEntity result = new com.yorha.proto.MapBuilding.MapBuildingEntity(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.partId_ = partId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (innerArmyBuilder_ == null) {
            result.innerArmy_ = innerArmy_;
          } else {
            result.innerArmy_ = innerArmyBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          if (occupyinfoBuilder_ == null) {
            result.occupyinfo_ = occupyinfo_;
          } else {
            result.occupyinfo_ = occupyinfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          if (troopBuilder_ == null) {
            result.troop_ = troop_;
          } else {
            result.troop_ = troopBuilder_.build();
          }
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          if (battleBuilder_ == null) {
            result.battle_ = battle_;
          } else {
            result.battle_ = battleBuilder_.build();
          }
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (buffSysBuilder_ == null) {
            result.buffSys_ = buffSys_;
          } else {
            result.buffSys_ = buffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          if (constructInfoBuilder_ == null) {
            result.constructInfo_ = constructInfo_;
          } else {
            result.constructInfo_ = constructInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          if (devBuffSysBuilder_ == null) {
            result.devBuffSys_ = devBuffSys_;
          } else {
            result.devBuffSys_ = devBuffSysBuilder_.build();
          }
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          if (safeGuardBuilder_ == null) {
            result.safeGuard_ = safeGuard_;
          } else {
            result.safeGuard_ = safeGuardBuilder_.build();
          }
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          if (recommendSoldierTypeListBuilder_ == null) {
            result.recommendSoldierTypeList_ = recommendSoldierTypeList_;
          } else {
            result.recommendSoldierTypeList_ = recommendSoldierTypeListBuilder_.build();
          }
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.passingArmyNum_ = passingArmyNum_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          if (expressionBuilder_ == null) {
            result.expression_ = expression_;
          } else {
            result.expression_ = expressionBuilder_.build();
          }
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          if (kingdomModelBuilder_ == null) {
            result.kingdomModel_ = kingdomModel_;
          } else {
            result.kingdomModel_ = kingdomModelBuilder_.build();
          }
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          if (arrowBuilder_ == null) {
            result.arrow_ = arrow_;
          } else {
            result.arrow_ = arrowBuilder_.build();
          }
          to_bitField0_ |= 0x00008000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuilding.MapBuildingEntity) {
          return mergeFrom((com.yorha.proto.MapBuilding.MapBuildingEntity)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuilding.MapBuildingEntity other) {
        if (other == com.yorha.proto.MapBuilding.MapBuildingEntity.getDefaultInstance()) return this;
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasPartId()) {
          setPartId(other.getPartId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasInnerArmy()) {
          mergeInnerArmy(other.getInnerArmy());
        }
        if (other.hasOccupyinfo()) {
          mergeOccupyinfo(other.getOccupyinfo());
        }
        if (other.hasTroop()) {
          mergeTroop(other.getTroop());
        }
        if (other.hasBattle()) {
          mergeBattle(other.getBattle());
        }
        if (other.hasBuffSys()) {
          mergeBuffSys(other.getBuffSys());
        }
        if (other.hasConstructInfo()) {
          mergeConstructInfo(other.getConstructInfo());
        }
        if (other.hasDevBuffSys()) {
          mergeDevBuffSys(other.getDevBuffSys());
        }
        if (other.hasSafeGuard()) {
          mergeSafeGuard(other.getSafeGuard());
        }
        if (other.hasRecommendSoldierTypeList()) {
          mergeRecommendSoldierTypeList(other.getRecommendSoldierTypeList());
        }
        if (other.hasPassingArmyNum()) {
          setPassingArmyNum(other.getPassingArmyNum());
        }
        if (other.hasExpression()) {
          mergeExpression(other.getExpression());
        }
        if (other.hasKingdomModel()) {
          mergeKingdomModel(other.getKingdomModel());
        }
        if (other.hasArrow()) {
          mergeArrow(other.getArrow());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuilding.MapBuildingEntity parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuilding.MapBuildingEntity) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.Point point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       * @return The point.
       */
      public com.yorha.proto.Struct.Point getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder setPoint(
          com.yorha.proto.Struct.Point.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder mergePoint(com.yorha.proto.Struct.Point value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.Struct.Point.getDefaultInstance()) {
            point_ =
              com.yorha.proto.Struct.Point.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.Point.Builder getPointBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      public com.yorha.proto.Struct.PointOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.Struct.Point.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.Point point = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Point, com.yorha.proto.Struct.Point.Builder, com.yorha.proto.Struct.PointOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private int partId_ ;
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return Whether the partId field is set.
       */
      @java.lang.Override
      public boolean hasPartId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return The partId.
       */
      @java.lang.Override
      public int getPartId() {
        return partId_;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @param value The partId to set.
       * @return This builder for chaining.
       */
      public Builder setPartId(int value) {
        bitField0_ |= 0x00000002;
        partId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属片id
       * </pre>
       *
       * <code>optional int32 partId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearPartId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        partId_ = 0;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000004;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑配置id
       * </pre>
       *
       * <code>optional int32 templateId = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.CityInnerArmy innerArmy_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.CityInnerArmy, com.yorha.proto.Struct.CityInnerArmy.Builder, com.yorha.proto.Struct.CityInnerArmyOrBuilder> innerArmyBuilder_;
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       * @return Whether the innerArmy field is set.
       */
      public boolean hasInnerArmy() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       * @return The innerArmy.
       */
      public com.yorha.proto.Struct.CityInnerArmy getInnerArmy() {
        if (innerArmyBuilder_ == null) {
          return innerArmy_ == null ? com.yorha.proto.Struct.CityInnerArmy.getDefaultInstance() : innerArmy_;
        } else {
          return innerArmyBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public Builder setInnerArmy(com.yorha.proto.Struct.CityInnerArmy value) {
        if (innerArmyBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          innerArmy_ = value;
          onChanged();
        } else {
          innerArmyBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public Builder setInnerArmy(
          com.yorha.proto.Struct.CityInnerArmy.Builder builderForValue) {
        if (innerArmyBuilder_ == null) {
          innerArmy_ = builderForValue.build();
          onChanged();
        } else {
          innerArmyBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public Builder mergeInnerArmy(com.yorha.proto.Struct.CityInnerArmy value) {
        if (innerArmyBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              innerArmy_ != null &&
              innerArmy_ != com.yorha.proto.Struct.CityInnerArmy.getDefaultInstance()) {
            innerArmy_ =
              com.yorha.proto.Struct.CityInnerArmy.newBuilder(innerArmy_).mergeFrom(value).buildPartial();
          } else {
            innerArmy_ = value;
          }
          onChanged();
        } else {
          innerArmyBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public Builder clearInnerArmy() {
        if (innerArmyBuilder_ == null) {
          innerArmy_ = null;
          onChanged();
        } else {
          innerArmyBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public com.yorha.proto.Struct.CityInnerArmy.Builder getInnerArmyBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getInnerArmyFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      public com.yorha.proto.Struct.CityInnerArmyOrBuilder getInnerArmyOrBuilder() {
        if (innerArmyBuilder_ != null) {
          return innerArmyBuilder_.getMessageOrBuilder();
        } else {
          return innerArmy_ == null ?
              com.yorha.proto.Struct.CityInnerArmy.getDefaultInstance() : innerArmy_;
        }
      }
      /**
       * <pre>
       * 城内军队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.CityInnerArmy innerArmy = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.CityInnerArmy, com.yorha.proto.Struct.CityInnerArmy.Builder, com.yorha.proto.Struct.CityInnerArmyOrBuilder> 
          getInnerArmyFieldBuilder() {
        if (innerArmyBuilder_ == null) {
          innerArmyBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.CityInnerArmy, com.yorha.proto.Struct.CityInnerArmy.Builder, com.yorha.proto.Struct.CityInnerArmyOrBuilder>(
                  getInnerArmy(),
                  getParentForChildren(),
                  isClean());
          innerArmy_ = null;
        }
        return innerArmyBuilder_;
      }

      private com.yorha.proto.MapBuilding.OccupyInfo occupyinfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.OccupyInfo, com.yorha.proto.MapBuilding.OccupyInfo.Builder, com.yorha.proto.MapBuilding.OccupyInfoOrBuilder> occupyinfoBuilder_;
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       * @return Whether the occupyinfo field is set.
       */
      public boolean hasOccupyinfo() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       * @return The occupyinfo.
       */
      public com.yorha.proto.MapBuilding.OccupyInfo getOccupyinfo() {
        if (occupyinfoBuilder_ == null) {
          return occupyinfo_ == null ? com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance() : occupyinfo_;
        } else {
          return occupyinfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public Builder setOccupyinfo(com.yorha.proto.MapBuilding.OccupyInfo value) {
        if (occupyinfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          occupyinfo_ = value;
          onChanged();
        } else {
          occupyinfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public Builder setOccupyinfo(
          com.yorha.proto.MapBuilding.OccupyInfo.Builder builderForValue) {
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = builderForValue.build();
          onChanged();
        } else {
          occupyinfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public Builder mergeOccupyinfo(com.yorha.proto.MapBuilding.OccupyInfo value) {
        if (occupyinfoBuilder_ == null) {
          if (((bitField0_ & 0x00000010) != 0) &&
              occupyinfo_ != null &&
              occupyinfo_ != com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance()) {
            occupyinfo_ =
              com.yorha.proto.MapBuilding.OccupyInfo.newBuilder(occupyinfo_).mergeFrom(value).buildPartial();
          } else {
            occupyinfo_ = value;
          }
          onChanged();
        } else {
          occupyinfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000010;
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public Builder clearOccupyinfo() {
        if (occupyinfoBuilder_ == null) {
          occupyinfo_ = null;
          onChanged();
        } else {
          occupyinfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000010);
        return this;
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public com.yorha.proto.MapBuilding.OccupyInfo.Builder getOccupyinfoBuilder() {
        bitField0_ |= 0x00000010;
        onChanged();
        return getOccupyinfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      public com.yorha.proto.MapBuilding.OccupyInfoOrBuilder getOccupyinfoOrBuilder() {
        if (occupyinfoBuilder_ != null) {
          return occupyinfoBuilder_.getMessageOrBuilder();
        } else {
          return occupyinfo_ == null ?
              com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance() : occupyinfo_;
        }
      }
      /**
       * <pre>
       * 占领数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyInfo occupyinfo = 5;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.OccupyInfo, com.yorha.proto.MapBuilding.OccupyInfo.Builder, com.yorha.proto.MapBuilding.OccupyInfoOrBuilder> 
          getOccupyinfoFieldBuilder() {
        if (occupyinfoBuilder_ == null) {
          occupyinfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuilding.OccupyInfo, com.yorha.proto.MapBuilding.OccupyInfo.Builder, com.yorha.proto.MapBuilding.OccupyInfoOrBuilder>(
                  getOccupyinfo(),
                  getParentForChildren(),
                  isClean());
          occupyinfo_ = null;
        }
        return occupyinfoBuilder_;
      }

      private com.yorha.proto.StructPlayer.Troop troop_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder> troopBuilder_;
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       * @return Whether the troop field is set.
       */
      public boolean hasTroop() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       * @return The troop.
       */
      public com.yorha.proto.StructPlayer.Troop getTroop() {
        if (troopBuilder_ == null) {
          return troop_ == null ? com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
        } else {
          return troopBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public Builder setTroop(com.yorha.proto.StructPlayer.Troop value) {
        if (troopBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          troop_ = value;
          onChanged();
        } else {
          troopBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public Builder setTroop(
          com.yorha.proto.StructPlayer.Troop.Builder builderForValue) {
        if (troopBuilder_ == null) {
          troop_ = builderForValue.build();
          onChanged();
        } else {
          troopBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public Builder mergeTroop(com.yorha.proto.StructPlayer.Troop value) {
        if (troopBuilder_ == null) {
          if (((bitField0_ & 0x00000020) != 0) &&
              troop_ != null &&
              troop_ != com.yorha.proto.StructPlayer.Troop.getDefaultInstance()) {
            troop_ =
              com.yorha.proto.StructPlayer.Troop.newBuilder(troop_).mergeFrom(value).buildPartial();
          } else {
            troop_ = value;
          }
          onChanged();
        } else {
          troopBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000020;
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public Builder clearTroop() {
        if (troopBuilder_ == null) {
          troop_ = null;
          onChanged();
        } else {
          troopBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000020);
        return this;
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public com.yorha.proto.StructPlayer.Troop.Builder getTroopBuilder() {
        bitField0_ |= 0x00000020;
        onChanged();
        return getTroopFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      public com.yorha.proto.StructPlayer.TroopOrBuilder getTroopOrBuilder() {
        if (troopBuilder_ != null) {
          return troopBuilder_.getMessageOrBuilder();
        } else {
          return troop_ == null ?
              com.yorha.proto.StructPlayer.Troop.getDefaultInstance() : troop_;
        }
      }
      /**
       * <pre>
       * 战斗时部队情况
       * </pre>
       *
       * <code>optional .com.yorha.proto.Troop troop = 6;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder> 
          getTroopFieldBuilder() {
        if (troopBuilder_ == null) {
          troopBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPlayer.Troop, com.yorha.proto.StructPlayer.Troop.Builder, com.yorha.proto.StructPlayer.TroopOrBuilder>(
                  getTroop(),
                  getParentForChildren(),
                  isClean());
          troop_ = null;
        }
        return troopBuilder_;
      }

      private com.yorha.proto.StructBattle.Battle battle_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder> battleBuilder_;
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       * @return Whether the battle field is set.
       */
      public boolean hasBattle() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       * @return The battle.
       */
      public com.yorha.proto.StructBattle.Battle getBattle() {
        if (battleBuilder_ == null) {
          return battle_ == null ? com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
        } else {
          return battleBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public Builder setBattle(com.yorha.proto.StructBattle.Battle value) {
        if (battleBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          battle_ = value;
          onChanged();
        } else {
          battleBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public Builder setBattle(
          com.yorha.proto.StructBattle.Battle.Builder builderForValue) {
        if (battleBuilder_ == null) {
          battle_ = builderForValue.build();
          onChanged();
        } else {
          battleBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public Builder mergeBattle(com.yorha.proto.StructBattle.Battle value) {
        if (battleBuilder_ == null) {
          if (((bitField0_ & 0x00000040) != 0) &&
              battle_ != null &&
              battle_ != com.yorha.proto.StructBattle.Battle.getDefaultInstance()) {
            battle_ =
              com.yorha.proto.StructBattle.Battle.newBuilder(battle_).mergeFrom(value).buildPartial();
          } else {
            battle_ = value;
          }
          onChanged();
        } else {
          battleBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000040;
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public Builder clearBattle() {
        if (battleBuilder_ == null) {
          battle_ = null;
          onChanged();
        } else {
          battleBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000040);
        return this;
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public com.yorha.proto.StructBattle.Battle.Builder getBattleBuilder() {
        bitField0_ |= 0x00000040;
        onChanged();
        return getBattleFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      public com.yorha.proto.StructBattle.BattleOrBuilder getBattleOrBuilder() {
        if (battleBuilder_ != null) {
          return battleBuilder_.getMessageOrBuilder();
        } else {
          return battle_ == null ?
              com.yorha.proto.StructBattle.Battle.getDefaultInstance() : battle_;
        }
      }
      /**
       * <pre>
       * 战斗状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.Battle battle = 7;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder> 
          getBattleFieldBuilder() {
        if (battleBuilder_ == null) {
          battleBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.Battle, com.yorha.proto.StructBattle.Battle.Builder, com.yorha.proto.StructBattle.BattleOrBuilder>(
                  getBattle(),
                  getParentForChildren(),
                  isClean());
          battle_ = null;
        }
        return battleBuilder_;
      }

      private com.yorha.proto.StructBattle.BuffSys buffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder> buffSysBuilder_;
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       * @return Whether the buffSys field is set.
       */
      public boolean hasBuffSys() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       * @return The buffSys.
       */
      public com.yorha.proto.StructBattle.BuffSys getBuffSys() {
        if (buffSysBuilder_ == null) {
          return buffSys_ == null ? com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
        } else {
          return buffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public Builder setBuffSys(com.yorha.proto.StructBattle.BuffSys value) {
        if (buffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          buffSys_ = value;
          onChanged();
        } else {
          buffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public Builder setBuffSys(
          com.yorha.proto.StructBattle.BuffSys.Builder builderForValue) {
        if (buffSysBuilder_ == null) {
          buffSys_ = builderForValue.build();
          onChanged();
        } else {
          buffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public Builder mergeBuffSys(com.yorha.proto.StructBattle.BuffSys value) {
        if (buffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              buffSys_ != null &&
              buffSys_ != com.yorha.proto.StructBattle.BuffSys.getDefaultInstance()) {
            buffSys_ =
              com.yorha.proto.StructBattle.BuffSys.newBuilder(buffSys_).mergeFrom(value).buildPartial();
          } else {
            buffSys_ = value;
          }
          onChanged();
        } else {
          buffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public Builder clearBuffSys() {
        if (buffSysBuilder_ == null) {
          buffSys_ = null;
          onChanged();
        } else {
          buffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public com.yorha.proto.StructBattle.BuffSys.Builder getBuffSysBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      public com.yorha.proto.StructBattle.BuffSysOrBuilder getBuffSysOrBuilder() {
        if (buffSysBuilder_ != null) {
          return buffSysBuilder_.getMessageOrBuilder();
        } else {
          return buffSys_ == null ?
              com.yorha.proto.StructBattle.BuffSys.getDefaultInstance() : buffSys_;
        }
      }
      /**
       * <pre>
       * buff增益
       * </pre>
       *
       * <code>optional .com.yorha.proto.BuffSys buffSys = 8;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder> 
          getBuffSysFieldBuilder() {
        if (buffSysBuilder_ == null) {
          buffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.BuffSys, com.yorha.proto.StructBattle.BuffSys.Builder, com.yorha.proto.StructBattle.BuffSysOrBuilder>(
                  getBuffSys(),
                  getParentForChildren(),
                  isClean());
          buffSys_ = null;
        }
        return buffSysBuilder_;
      }

      private com.yorha.proto.MapBuilding.ConstructInfo constructInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.ConstructInfo, com.yorha.proto.MapBuilding.ConstructInfo.Builder, com.yorha.proto.MapBuilding.ConstructInfoOrBuilder> constructInfoBuilder_;
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       * @return Whether the constructInfo field is set.
       */
      public boolean hasConstructInfo() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       * @return The constructInfo.
       */
      public com.yorha.proto.MapBuilding.ConstructInfo getConstructInfo() {
        if (constructInfoBuilder_ == null) {
          return constructInfo_ == null ? com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance() : constructInfo_;
        } else {
          return constructInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public Builder setConstructInfo(com.yorha.proto.MapBuilding.ConstructInfo value) {
        if (constructInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          constructInfo_ = value;
          onChanged();
        } else {
          constructInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public Builder setConstructInfo(
          com.yorha.proto.MapBuilding.ConstructInfo.Builder builderForValue) {
        if (constructInfoBuilder_ == null) {
          constructInfo_ = builderForValue.build();
          onChanged();
        } else {
          constructInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public Builder mergeConstructInfo(com.yorha.proto.MapBuilding.ConstructInfo value) {
        if (constructInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
              constructInfo_ != null &&
              constructInfo_ != com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance()) {
            constructInfo_ =
              com.yorha.proto.MapBuilding.ConstructInfo.newBuilder(constructInfo_).mergeFrom(value).buildPartial();
          } else {
            constructInfo_ = value;
          }
          onChanged();
        } else {
          constructInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public Builder clearConstructInfo() {
        if (constructInfoBuilder_ == null) {
          constructInfo_ = null;
          onChanged();
        } else {
          constructInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public com.yorha.proto.MapBuilding.ConstructInfo.Builder getConstructInfoBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getConstructInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      public com.yorha.proto.MapBuilding.ConstructInfoOrBuilder getConstructInfoOrBuilder() {
        if (constructInfoBuilder_ != null) {
          return constructInfoBuilder_.getMessageOrBuilder();
        } else {
          return constructInfo_ == null ?
              com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance() : constructInfo_;
        }
      }
      /**
       * <pre>
       * 联盟建筑建造数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ConstructInfo constructInfo = 9;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.ConstructInfo, com.yorha.proto.MapBuilding.ConstructInfo.Builder, com.yorha.proto.MapBuilding.ConstructInfoOrBuilder> 
          getConstructInfoFieldBuilder() {
        if (constructInfoBuilder_ == null) {
          constructInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuilding.ConstructInfo, com.yorha.proto.MapBuilding.ConstructInfo.Builder, com.yorha.proto.MapBuilding.ConstructInfoOrBuilder>(
                  getConstructInfo(),
                  getParentForChildren(),
                  isClean());
          constructInfo_ = null;
        }
        return constructInfoBuilder_;
      }

      private com.yorha.proto.StructBattle.SceneDevBuffSys devBuffSys_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.SceneDevBuffSys, com.yorha.proto.StructBattle.SceneDevBuffSys.Builder, com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder> devBuffSysBuilder_;
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       * @return Whether the devBuffSys field is set.
       */
      public boolean hasDevBuffSys() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       * @return The devBuffSys.
       */
      public com.yorha.proto.StructBattle.SceneDevBuffSys getDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          return devBuffSys_ == null ? com.yorha.proto.StructBattle.SceneDevBuffSys.getDefaultInstance() : devBuffSys_;
        } else {
          return devBuffSysBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public Builder setDevBuffSys(com.yorha.proto.StructBattle.SceneDevBuffSys value) {
        if (devBuffSysBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          devBuffSys_ = value;
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public Builder setDevBuffSys(
          com.yorha.proto.StructBattle.SceneDevBuffSys.Builder builderForValue) {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = builderForValue.build();
          onChanged();
        } else {
          devBuffSysBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public Builder mergeDevBuffSys(com.yorha.proto.StructBattle.SceneDevBuffSys value) {
        if (devBuffSysBuilder_ == null) {
          if (((bitField0_ & 0x00000200) != 0) &&
              devBuffSys_ != null &&
              devBuffSys_ != com.yorha.proto.StructBattle.SceneDevBuffSys.getDefaultInstance()) {
            devBuffSys_ =
              com.yorha.proto.StructBattle.SceneDevBuffSys.newBuilder(devBuffSys_).mergeFrom(value).buildPartial();
          } else {
            devBuffSys_ = value;
          }
          onChanged();
        } else {
          devBuffSysBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000200;
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public Builder clearDevBuffSys() {
        if (devBuffSysBuilder_ == null) {
          devBuffSys_ = null;
          onChanged();
        } else {
          devBuffSysBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public com.yorha.proto.StructBattle.SceneDevBuffSys.Builder getDevBuffSysBuilder() {
        bitField0_ |= 0x00000200;
        onChanged();
        return getDevBuffSysFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      public com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder getDevBuffSysOrBuilder() {
        if (devBuffSysBuilder_ != null) {
          return devBuffSysBuilder_.getMessageOrBuilder();
        } else {
          return devBuffSys_ == null ?
              com.yorha.proto.StructBattle.SceneDevBuffSys.getDefaultInstance() : devBuffSys_;
        }
      }
      /**
       * <pre>
       * devbuff 只用于展示
       * </pre>
       *
       * <code>optional .com.yorha.proto.SceneDevBuffSys devBuffSys = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructBattle.SceneDevBuffSys, com.yorha.proto.StructBattle.SceneDevBuffSys.Builder, com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder> 
          getDevBuffSysFieldBuilder() {
        if (devBuffSysBuilder_ == null) {
          devBuffSysBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructBattle.SceneDevBuffSys, com.yorha.proto.StructBattle.SceneDevBuffSys.Builder, com.yorha.proto.StructBattle.SceneDevBuffSysOrBuilder>(
                  getDevBuffSys(),
                  getParentForChildren(),
                  isClean());
          devBuffSys_ = null;
        }
        return devBuffSysBuilder_;
      }

      private com.yorha.proto.Struct.SpecialSafeGuard safeGuard_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.SpecialSafeGuard, com.yorha.proto.Struct.SpecialSafeGuard.Builder, com.yorha.proto.Struct.SpecialSafeGuardOrBuilder> safeGuardBuilder_;
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       * @return Whether the safeGuard field is set.
       */
      public boolean hasSafeGuard() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       * @return The safeGuard.
       */
      public com.yorha.proto.Struct.SpecialSafeGuard getSafeGuard() {
        if (safeGuardBuilder_ == null) {
          return safeGuard_ == null ? com.yorha.proto.Struct.SpecialSafeGuard.getDefaultInstance() : safeGuard_;
        } else {
          return safeGuardBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public Builder setSafeGuard(com.yorha.proto.Struct.SpecialSafeGuard value) {
        if (safeGuardBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          safeGuard_ = value;
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public Builder setSafeGuard(
          com.yorha.proto.Struct.SpecialSafeGuard.Builder builderForValue) {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = builderForValue.build();
          onChanged();
        } else {
          safeGuardBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public Builder mergeSafeGuard(com.yorha.proto.Struct.SpecialSafeGuard value) {
        if (safeGuardBuilder_ == null) {
          if (((bitField0_ & 0x00000400) != 0) &&
              safeGuard_ != null &&
              safeGuard_ != com.yorha.proto.Struct.SpecialSafeGuard.getDefaultInstance()) {
            safeGuard_ =
              com.yorha.proto.Struct.SpecialSafeGuard.newBuilder(safeGuard_).mergeFrom(value).buildPartial();
          } else {
            safeGuard_ = value;
          }
          onChanged();
        } else {
          safeGuardBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000400;
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public Builder clearSafeGuard() {
        if (safeGuardBuilder_ == null) {
          safeGuard_ = null;
          onChanged();
        } else {
          safeGuardBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000400);
        return this;
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public com.yorha.proto.Struct.SpecialSafeGuard.Builder getSafeGuardBuilder() {
        bitField0_ |= 0x00000400;
        onChanged();
        return getSafeGuardFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      public com.yorha.proto.Struct.SpecialSafeGuardOrBuilder getSafeGuardOrBuilder() {
        if (safeGuardBuilder_ != null) {
          return safeGuardBuilder_.getMessageOrBuilder();
        } else {
          return safeGuard_ == null ?
              com.yorha.proto.Struct.SpecialSafeGuard.getDefaultInstance() : safeGuard_;
        }
      }
      /**
       * <pre>
       * 特殊罩子（非和平护盾）
       * </pre>
       *
       * <code>optional .com.yorha.proto.SpecialSafeGuard safeGuard = 12;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.SpecialSafeGuard, com.yorha.proto.Struct.SpecialSafeGuard.Builder, com.yorha.proto.Struct.SpecialSafeGuardOrBuilder> 
          getSafeGuardFieldBuilder() {
        if (safeGuardBuilder_ == null) {
          safeGuardBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.SpecialSafeGuard, com.yorha.proto.Struct.SpecialSafeGuard.Builder, com.yorha.proto.Struct.SpecialSafeGuardOrBuilder>(
                  getSafeGuard(),
                  getParentForChildren(),
                  isClean());
          safeGuard_ = null;
        }
        return safeGuardBuilder_;
      }

      private com.yorha.proto.Basic.Int32List recommendSoldierTypeList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder> recommendSoldierTypeListBuilder_;
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       * @return Whether the recommendSoldierTypeList field is set.
       */
      public boolean hasRecommendSoldierTypeList() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       * @return The recommendSoldierTypeList.
       */
      public com.yorha.proto.Basic.Int32List getRecommendSoldierTypeList() {
        if (recommendSoldierTypeListBuilder_ == null) {
          return recommendSoldierTypeList_ == null ? com.yorha.proto.Basic.Int32List.getDefaultInstance() : recommendSoldierTypeList_;
        } else {
          return recommendSoldierTypeListBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public Builder setRecommendSoldierTypeList(com.yorha.proto.Basic.Int32List value) {
        if (recommendSoldierTypeListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          recommendSoldierTypeList_ = value;
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public Builder setRecommendSoldierTypeList(
          com.yorha.proto.Basic.Int32List.Builder builderForValue) {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = builderForValue.build();
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public Builder mergeRecommendSoldierTypeList(com.yorha.proto.Basic.Int32List value) {
        if (recommendSoldierTypeListBuilder_ == null) {
          if (((bitField0_ & 0x00000800) != 0) &&
              recommendSoldierTypeList_ != null &&
              recommendSoldierTypeList_ != com.yorha.proto.Basic.Int32List.getDefaultInstance()) {
            recommendSoldierTypeList_ =
              com.yorha.proto.Basic.Int32List.newBuilder(recommendSoldierTypeList_).mergeFrom(value).buildPartial();
          } else {
            recommendSoldierTypeList_ = value;
          }
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000800;
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public Builder clearRecommendSoldierTypeList() {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeList_ = null;
          onChanged();
        } else {
          recommendSoldierTypeListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public com.yorha.proto.Basic.Int32List.Builder getRecommendSoldierTypeListBuilder() {
        bitField0_ |= 0x00000800;
        onChanged();
        return getRecommendSoldierTypeListFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      public com.yorha.proto.Basic.Int32ListOrBuilder getRecommendSoldierTypeListOrBuilder() {
        if (recommendSoldierTypeListBuilder_ != null) {
          return recommendSoldierTypeListBuilder_.getMessageOrBuilder();
        } else {
          return recommendSoldierTypeList_ == null ?
              com.yorha.proto.Basic.Int32List.getDefaultInstance() : recommendSoldierTypeList_;
        }
      }
      /**
       * <pre>
       * 推荐兵种列表
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int32List recommendSoldierTypeList = 13;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder> 
          getRecommendSoldierTypeListFieldBuilder() {
        if (recommendSoldierTypeListBuilder_ == null) {
          recommendSoldierTypeListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Basic.Int32List, com.yorha.proto.Basic.Int32List.Builder, com.yorha.proto.Basic.Int32ListOrBuilder>(
                  getRecommendSoldierTypeList(),
                  getParentForChildren(),
                  isClean());
          recommendSoldierTypeList_ = null;
        }
        return recommendSoldierTypeListBuilder_;
      }

      private int passingArmyNum_ ;
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return Whether the passingArmyNum field is set.
       */
      @java.lang.Override
      public boolean hasPassingArmyNum() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return The passingArmyNum.
       */
      @java.lang.Override
      public int getPassingArmyNum() {
        return passingArmyNum_;
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @param value The passingArmyNum to set.
       * @return This builder for chaining.
       */
      public Builder setPassingArmyNum(int value) {
        bitField0_ |= 0x00001000;
        passingArmyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 正在通关的人数
       * </pre>
       *
       * <code>optional int32 passingArmyNum = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearPassingArmyNum() {
        bitField0_ = (bitField0_ & ~0x00001000);
        passingArmyNum_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.Struct.Expression expression_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Expression, com.yorha.proto.Struct.Expression.Builder, com.yorha.proto.Struct.ExpressionOrBuilder> expressionBuilder_;
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       * @return Whether the expression field is set.
       */
      public boolean hasExpression() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       * @return The expression.
       */
      public com.yorha.proto.Struct.Expression getExpression() {
        if (expressionBuilder_ == null) {
          return expression_ == null ? com.yorha.proto.Struct.Expression.getDefaultInstance() : expression_;
        } else {
          return expressionBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public Builder setExpression(com.yorha.proto.Struct.Expression value) {
        if (expressionBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          expression_ = value;
          onChanged();
        } else {
          expressionBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public Builder setExpression(
          com.yorha.proto.Struct.Expression.Builder builderForValue) {
        if (expressionBuilder_ == null) {
          expression_ = builderForValue.build();
          onChanged();
        } else {
          expressionBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public Builder mergeExpression(com.yorha.proto.Struct.Expression value) {
        if (expressionBuilder_ == null) {
          if (((bitField0_ & 0x00002000) != 0) &&
              expression_ != null &&
              expression_ != com.yorha.proto.Struct.Expression.getDefaultInstance()) {
            expression_ =
              com.yorha.proto.Struct.Expression.newBuilder(expression_).mergeFrom(value).buildPartial();
          } else {
            expression_ = value;
          }
          onChanged();
        } else {
          expressionBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00002000;
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public Builder clearExpression() {
        if (expressionBuilder_ == null) {
          expression_ = null;
          onChanged();
        } else {
          expressionBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00002000);
        return this;
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public com.yorha.proto.Struct.Expression.Builder getExpressionBuilder() {
        bitField0_ |= 0x00002000;
        onChanged();
        return getExpressionFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      public com.yorha.proto.Struct.ExpressionOrBuilder getExpressionOrBuilder() {
        if (expressionBuilder_ != null) {
          return expressionBuilder_.getMessageOrBuilder();
        } else {
          return expression_ == null ?
              com.yorha.proto.Struct.Expression.getDefaultInstance() : expression_;
        }
      }
      /**
       * <pre>
       * 表情
       * </pre>
       *
       * <code>optional .com.yorha.proto.Expression expression = 15;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Expression, com.yorha.proto.Struct.Expression.Builder, com.yorha.proto.Struct.ExpressionOrBuilder> 
          getExpressionFieldBuilder() {
        if (expressionBuilder_ == null) {
          expressionBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Expression, com.yorha.proto.Struct.Expression.Builder, com.yorha.proto.Struct.ExpressionOrBuilder>(
                  getExpression(),
                  getParentForChildren(),
                  isClean());
          expression_ = null;
        }
        return expressionBuilder_;
      }

      private com.yorha.proto.MapBuilding.MapBuildingKingdomModel kingdomModel_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.MapBuildingKingdomModel, com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder, com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder> kingdomModelBuilder_;
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       * @return Whether the kingdomModel field is set.
       */
      public boolean hasKingdomModel() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       * @return The kingdomModel.
       */
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModel getKingdomModel() {
        if (kingdomModelBuilder_ == null) {
          return kingdomModel_ == null ? com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance() : kingdomModel_;
        } else {
          return kingdomModelBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public Builder setKingdomModel(com.yorha.proto.MapBuilding.MapBuildingKingdomModel value) {
        if (kingdomModelBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          kingdomModel_ = value;
          onChanged();
        } else {
          kingdomModelBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public Builder setKingdomModel(
          com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder builderForValue) {
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = builderForValue.build();
          onChanged();
        } else {
          kingdomModelBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public Builder mergeKingdomModel(com.yorha.proto.MapBuilding.MapBuildingKingdomModel value) {
        if (kingdomModelBuilder_ == null) {
          if (((bitField0_ & 0x00004000) != 0) &&
              kingdomModel_ != null &&
              kingdomModel_ != com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance()) {
            kingdomModel_ =
              com.yorha.proto.MapBuilding.MapBuildingKingdomModel.newBuilder(kingdomModel_).mergeFrom(value).buildPartial();
          } else {
            kingdomModel_ = value;
          }
          onChanged();
        } else {
          kingdomModelBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00004000;
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public Builder clearKingdomModel() {
        if (kingdomModelBuilder_ == null) {
          kingdomModel_ = null;
          onChanged();
        } else {
          kingdomModelBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00004000);
        return this;
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder getKingdomModelBuilder() {
        bitField0_ |= 0x00004000;
        onChanged();
        return getKingdomModelFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder getKingdomModelOrBuilder() {
        if (kingdomModelBuilder_ != null) {
          return kingdomModelBuilder_.getMessageOrBuilder();
        } else {
          return kingdomModel_ == null ?
              com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance() : kingdomModel_;
        }
      }
      /**
       * <pre>
       * 城市建筑上的王国信息，当前只有中心建筑（五级城）会使用
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingKingdomModel kingdomModel = 16;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.MapBuilding.MapBuildingKingdomModel, com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder, com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder> 
          getKingdomModelFieldBuilder() {
        if (kingdomModelBuilder_ == null) {
          kingdomModelBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.MapBuilding.MapBuildingKingdomModel, com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder, com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder>(
                  getKingdomModel(),
                  getParentForChildren(),
                  isClean());
          kingdomModel_ = null;
        }
        return kingdomModelBuilder_;
      }

      private com.yorha.proto.Struct.Int64ArmyArrowItemMap arrow_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Int64ArmyArrowItemMap, com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder, com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder> arrowBuilder_;
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       * @return Whether the arrow field is set.
       */
      public boolean hasArrow() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       * @return The arrow.
       */
      public com.yorha.proto.Struct.Int64ArmyArrowItemMap getArrow() {
        if (arrowBuilder_ == null) {
          return arrow_ == null ? com.yorha.proto.Struct.Int64ArmyArrowItemMap.getDefaultInstance() : arrow_;
        } else {
          return arrowBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public Builder setArrow(com.yorha.proto.Struct.Int64ArmyArrowItemMap value) {
        if (arrowBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          arrow_ = value;
          onChanged();
        } else {
          arrowBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public Builder setArrow(
          com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder builderForValue) {
        if (arrowBuilder_ == null) {
          arrow_ = builderForValue.build();
          onChanged();
        } else {
          arrowBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public Builder mergeArrow(com.yorha.proto.Struct.Int64ArmyArrowItemMap value) {
        if (arrowBuilder_ == null) {
          if (((bitField0_ & 0x00008000) != 0) &&
              arrow_ != null &&
              arrow_ != com.yorha.proto.Struct.Int64ArmyArrowItemMap.getDefaultInstance()) {
            arrow_ =
              com.yorha.proto.Struct.Int64ArmyArrowItemMap.newBuilder(arrow_).mergeFrom(value).buildPartial();
          } else {
            arrow_ = value;
          }
          onChanged();
        } else {
          arrowBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00008000;
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public Builder clearArrow() {
        if (arrowBuilder_ == null) {
          arrow_ = null;
          onChanged();
        } else {
          arrowBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00008000);
        return this;
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder getArrowBuilder() {
        bitField0_ |= 0x00008000;
        onChanged();
        return getArrowFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      public com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder getArrowOrBuilder() {
        if (arrowBuilder_ != null) {
          return arrowBuilder_.getMessageOrBuilder();
        } else {
          return arrow_ == null ?
              com.yorha.proto.Struct.Int64ArmyArrowItemMap.getDefaultInstance() : arrow_;
        }
      }
      /**
       * <pre>
       * 小箭头
       * </pre>
       *
       * <code>optional .com.yorha.proto.Int64ArmyArrowItemMap arrow = 23;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.Int64ArmyArrowItemMap, com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder, com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder> 
          getArrowFieldBuilder() {
        if (arrowBuilder_ == null) {
          arrowBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.Int64ArmyArrowItemMap, com.yorha.proto.Struct.Int64ArmyArrowItemMap.Builder, com.yorha.proto.Struct.Int64ArmyArrowItemMapOrBuilder>(
                  getArrow(),
                  getParentForChildren(),
                  isClean());
          arrow_ = null;
        }
        return arrowBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MapBuildingEntity)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MapBuildingEntity)
    private static final com.yorha.proto.MapBuilding.MapBuildingEntity DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuilding.MapBuildingEntity();
    }

    public static com.yorha.proto.MapBuilding.MapBuildingEntity getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MapBuildingEntity>
        PARSER = new com.google.protobuf.AbstractParser<MapBuildingEntity>() {
      @java.lang.Override
      public MapBuildingEntity parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MapBuildingEntity(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MapBuildingEntity> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MapBuildingEntity> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingEntity getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface OccupyInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.OccupyInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.OccupyState getState();

    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    boolean hasStateStartTsMs();
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return The stateStartTsMs.
     */
    long getStateStartTsMs();

    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    boolean hasStateEndTsMs();
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return The stateEndTsMs.
     */
    long getStateEndTsMs();

    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return Whether the ownerClanId field is set.
     */
    boolean hasOwnerClanId();
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return The ownerClanId.
     */
    long getOwnerClanId();

    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    boolean hasShowClanSimpleName();
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The showClanSimpleName.
     */
    java.lang.String getShowClanSimpleName();
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The bytes for showClanSimpleName.
     */
    com.google.protobuf.ByteString
        getShowClanSimpleNameBytes();

    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return Whether the showClanName field is set.
     */
    boolean hasShowClanName();
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The showClanName.
     */
    java.lang.String getShowClanName();
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The bytes for showClanName.
     */
    com.google.protobuf.ByteString
        getShowClanNameBytes();

    /**
     * <pre>
     * 所属联盟占领成功时间
     * </pre>
     *
     * <code>optional int64 ownerOccupyTsMs = 7;</code>
     * @return Whether the ownerOccupyTsMs field is set.
     */
    boolean hasOwnerOccupyTsMs();
    /**
     * <pre>
     * 所属联盟占领成功时间
     * </pre>
     *
     * <code>optional int64 ownerOccupyTsMs = 7;</code>
     * @return The ownerOccupyTsMs.
     */
    long getOwnerOccupyTsMs();

    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return Whether the occupyNum field is set.
     */
    boolean hasOccupyNum();
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return The occupyNum.
     */
    int getOccupyNum();

    /**
     * <pre>
     * 当前正在占领的开始时间
     * </pre>
     *
     * <code>optional int64 occupyTsMs = 9;</code>
     * @return Whether the occupyTsMs field is set.
     */
    boolean hasOccupyTsMs();
    /**
     * <pre>
     * 当前正在占领的开始时间
     * </pre>
     *
     * <code>optional int64 occupyTsMs = 9;</code>
     * @return The occupyTsMs.
     */
    long getOccupyTsMs();

    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return Whether the occupyNumCalcTsMs field is set.
     */
    boolean hasOccupyNumCalcTsMs();
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return The occupyNumCalcTsMs.
     */
    long getOccupyNumCalcTsMs();

    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return Whether the occupySpeed field is set.
     */
    boolean hasOccupySpeed();
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return The occupySpeed.
     */
    int getOccupySpeed();

    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return Whether the occupyClanId field is set.
     */
    boolean hasOccupyClanId();
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return The occupyClanId.
     */
    long getOccupyClanId();

    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return Whether the showColor field is set.
     */
    boolean hasShowColor();
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return The showColor.
     */
    int getShowColor();

    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return Whether the fisrtOwnTsMs field is set.
     */
    boolean hasFisrtOwnTsMs();
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return The fisrtOwnTsMs.
     */
    long getFisrtOwnTsMs();

    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return Whether the rebuildNumCalcTsMs field is set.
     */
    boolean hasRebuildNumCalcTsMs();
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return The rebuildNumCalcTsMs.
     */
    long getRebuildNumCalcTsMs();

    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return Whether the rebuildSpeed field is set.
     */
    boolean hasRebuildSpeed();
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return The rebuildSpeed.
     */
    int getRebuildSpeed();

    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return Whether the rebuildNum field is set.
     */
    boolean hasRebuildNum();
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return The rebuildNum.
     */
    int getRebuildNum();

    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return Whether the fileNumCalcTsMs field is set.
     */
    boolean hasFileNumCalcTsMs();
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return The fileNumCalcTsMs.
     */
    long getFileNumCalcTsMs();

    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return Whether the alreadyFireNum field is set.
     */
    boolean hasAlreadyFireNum();
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return The alreadyFireNum.
     */
    int getAlreadyFireNum();

    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return Whether the wouldOverBurn field is set.
     */
    boolean hasWouldOverBurn();
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return The wouldOverBurn.
     */
    boolean getWouldOverBurn();

    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return Whether the rebuildTotalWork field is set.
     */
    boolean hasRebuildTotalWork();
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return The rebuildTotalWork.
     */
    int getRebuildTotalWork();

    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return Whether the mustBurnEndTsMs field is set.
     */
    boolean hasMustBurnEndTsMs();
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return The mustBurnEndTsMs.
     */
    long getMustBurnEndTsMs();

    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return Whether the flagColor field is set.
     */
    boolean hasFlagColor();
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return The flagColor.
     */
    int getFlagColor();

    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return Whether the flagShading field is set.
     */
    boolean hasFlagShading();
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return The flagShading.
     */
    int getFlagShading();

    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return Whether the flagSign field is set.
     */
    boolean hasFlagSign();
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return The flagSign.
     */
    int getFlagSign();

    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return Whether the lastHitPlayerName field is set.
     */
    boolean hasLastHitPlayerName();
    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return The lastHitPlayerName.
     */
    java.lang.String getLastHitPlayerName();
    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return The bytes for lastHitPlayerName.
     */
    com.google.protobuf.ByteString
        getLastHitPlayerNameBytes();

    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return Whether the fireSpeed field is set.
     */
    boolean hasFireSpeed();
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return The fireSpeed.
     */
    int getFireSpeed();

    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return Whether the triggerFireClanId field is set.
     */
    boolean hasTriggerFireClanId();
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return The triggerFireClanId.
     */
    long getTriggerFireClanId();

    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return Whether the nationFlagId field is set.
     */
    boolean hasNationFlagId();
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return The nationFlagId.
     */
    int getNationFlagId();

    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return The zoneId.
     */
    int getZoneId();

    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return Whether the zoneColor field is set.
     */
    boolean hasZoneColor();
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return The zoneColor.
     */
    int getZoneColor();
  }
  /**
   * <pre>
   * 占领信息
   * </pre>
   *
   * Protobuf type {@code com.yorha.proto.OccupyInfo}
   */
  public static final class OccupyInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.OccupyInfo)
      OccupyInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use OccupyInfo.newBuilder() to construct.
    private OccupyInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private OccupyInfo() {
      state_ = 0;
      showClanSimpleName_ = "";
      showClanName_ = "";
      lastHitPlayerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new OccupyInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private OccupyInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.OccupyState value = com.yorha.proto.CommonEnum.OccupyState.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                state_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              stateStartTsMs_ = input.readInt64();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              stateEndTsMs_ = input.readInt64();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              ownerClanId_ = input.readInt64();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000010;
              showClanSimpleName_ = bs;
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              showClanName_ = bs;
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              ownerOccupyTsMs_ = input.readInt64();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              occupyNum_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              occupyTsMs_ = input.readInt64();
              break;
            }
            case 80: {
              bitField0_ |= 0x00000200;
              occupyNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 88: {
              bitField0_ |= 0x00000400;
              occupySpeed_ = input.readInt32();
              break;
            }
            case 96: {
              bitField0_ |= 0x00000800;
              occupyClanId_ = input.readInt64();
              break;
            }
            case 104: {
              bitField0_ |= 0x00001000;
              showColor_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00002000;
              fisrtOwnTsMs_ = input.readInt64();
              break;
            }
            case 120: {
              bitField0_ |= 0x00004000;
              rebuildNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 128: {
              bitField0_ |= 0x00008000;
              rebuildSpeed_ = input.readInt32();
              break;
            }
            case 136: {
              bitField0_ |= 0x00010000;
              rebuildNum_ = input.readInt32();
              break;
            }
            case 144: {
              bitField0_ |= 0x00020000;
              fileNumCalcTsMs_ = input.readInt64();
              break;
            }
            case 152: {
              bitField0_ |= 0x00040000;
              alreadyFireNum_ = input.readInt32();
              break;
            }
            case 160: {
              bitField0_ |= 0x00080000;
              wouldOverBurn_ = input.readBool();
              break;
            }
            case 168: {
              bitField0_ |= 0x00100000;
              rebuildTotalWork_ = input.readInt32();
              break;
            }
            case 176: {
              bitField0_ |= 0x00200000;
              mustBurnEndTsMs_ = input.readInt64();
              break;
            }
            case 184: {
              bitField0_ |= 0x00400000;
              flagColor_ = input.readInt32();
              break;
            }
            case 192: {
              bitField0_ |= 0x00800000;
              flagShading_ = input.readInt32();
              break;
            }
            case 200: {
              bitField0_ |= 0x01000000;
              flagSign_ = input.readInt32();
              break;
            }
            case 210: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x02000000;
              lastHitPlayerName_ = bs;
              break;
            }
            case 216: {
              bitField0_ |= 0x04000000;
              fireSpeed_ = input.readInt32();
              break;
            }
            case 224: {
              bitField0_ |= 0x08000000;
              triggerFireClanId_ = input.readInt64();
              break;
            }
            case 232: {
              bitField0_ |= 0x10000000;
              nationFlagId_ = input.readInt32();
              break;
            }
            case 240: {
              bitField0_ |= 0x20000000;
              zoneId_ = input.readInt32();
              break;
            }
            case 248: {
              bitField0_ |= 0x40000000;
              zoneColor_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_OccupyInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_OccupyInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuilding.OccupyInfo.class, com.yorha.proto.MapBuilding.OccupyInfo.Builder.class);
    }

    private int bitField0_;
    public static final int STATE_FIELD_NUMBER = 1;
    private int state_;
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 占领状态
     * </pre>
     *
     * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.OccupyState getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.OccupyState result = com.yorha.proto.CommonEnum.OccupyState.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.OccupyState.TOS_NEUTRAL : result;
    }

    public static final int STATESTARTTSMS_FIELD_NUMBER = 2;
    private long stateStartTsMs_;
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return Whether the stateStartTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateStartTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 状态开始时间戳
     * </pre>
     *
     * <code>optional int64 stateStartTsMs = 2;</code>
     * @return The stateStartTsMs.
     */
    @java.lang.Override
    public long getStateStartTsMs() {
      return stateStartTsMs_;
    }

    public static final int STATEENDTSMS_FIELD_NUMBER = 3;
    private long stateEndTsMs_;
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return Whether the stateEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasStateEndTsMs() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 进入下一阶段时间戳
     * </pre>
     *
     * <code>optional int64 stateEndTsMs = 3;</code>
     * @return The stateEndTsMs.
     */
    @java.lang.Override
    public long getStateEndTsMs() {
      return stateEndTsMs_;
    }

    public static final int OWNERCLANID_FIELD_NUMBER = 4;
    private long ownerClanId_;
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return Whether the ownerClanId field is set.
     */
    @java.lang.Override
    public boolean hasOwnerClanId() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 所属联盟id
     * </pre>
     *
     * <code>optional int64 ownerClanId = 4;</code>
     * @return The ownerClanId.
     */
    @java.lang.Override
    public long getOwnerClanId() {
      return ownerClanId_;
    }

    public static final int SHOWCLANSIMPLENAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object showClanSimpleName_;
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanSimpleName() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The showClanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getShowClanSimpleName() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
     * </pre>
     *
     * <code>optional string showClanSimpleName = 5;</code>
     * @return The bytes for showClanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanSimpleNameBytes() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHOWCLANNAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object showClanName_;
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return Whether the showClanName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The showClanName.
     */
    @java.lang.Override
    public java.lang.String getShowClanName() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示联盟名字
     * </pre>
     *
     * <code>optional string showClanName = 6;</code>
     * @return The bytes for showClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanNameBytes() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int OWNEROCCUPYTSMS_FIELD_NUMBER = 7;
    private long ownerOccupyTsMs_;
    /**
     * <pre>
     * 所属联盟占领成功时间
     * </pre>
     *
     * <code>optional int64 ownerOccupyTsMs = 7;</code>
     * @return Whether the ownerOccupyTsMs field is set.
     */
    @java.lang.Override
    public boolean hasOwnerOccupyTsMs() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 所属联盟占领成功时间
     * </pre>
     *
     * <code>optional int64 ownerOccupyTsMs = 7;</code>
     * @return The ownerOccupyTsMs.
     */
    @java.lang.Override
    public long getOwnerOccupyTsMs() {
      return ownerOccupyTsMs_;
    }

    public static final int OCCUPYNUM_FIELD_NUMBER = 8;
    private int occupyNum_;
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return Whether the occupyNum field is set.
     */
    @java.lang.Override
    public boolean hasOccupyNum() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 当前占领值
     * </pre>
     *
     * <code>optional int32 occupyNum = 8;</code>
     * @return The occupyNum.
     */
    @java.lang.Override
    public int getOccupyNum() {
      return occupyNum_;
    }

    public static final int OCCUPYTSMS_FIELD_NUMBER = 9;
    private long occupyTsMs_;
    /**
     * <pre>
     * 当前正在占领的开始时间
     * </pre>
     *
     * <code>optional int64 occupyTsMs = 9;</code>
     * @return Whether the occupyTsMs field is set.
     */
    @java.lang.Override
    public boolean hasOccupyTsMs() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 当前正在占领的开始时间
     * </pre>
     *
     * <code>optional int64 occupyTsMs = 9;</code>
     * @return The occupyTsMs.
     */
    @java.lang.Override
    public long getOccupyTsMs() {
      return occupyTsMs_;
    }

    public static final int OCCUPYNUMCALCTSMS_FIELD_NUMBER = 10;
    private long occupyNumCalcTsMs_;
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return Whether the occupyNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasOccupyNumCalcTsMs() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 占领值上次结算时间戳
     * </pre>
     *
     * <code>optional int64 occupyNumCalcTsMs = 10;</code>
     * @return The occupyNumCalcTsMs.
     */
    @java.lang.Override
    public long getOccupyNumCalcTsMs() {
      return occupyNumCalcTsMs_;
    }

    public static final int OCCUPYSPEED_FIELD_NUMBER = 11;
    private int occupySpeed_;
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return Whether the occupySpeed field is set.
     */
    @java.lang.Override
    public boolean hasOccupySpeed() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 占领速度
     * </pre>
     *
     * <code>optional int32 occupySpeed = 11;</code>
     * @return The occupySpeed.
     */
    @java.lang.Override
    public int getOccupySpeed() {
      return occupySpeed_;
    }

    public static final int OCCUPYCLANID_FIELD_NUMBER = 12;
    private long occupyClanId_;
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return Whether the occupyClanId field is set.
     */
    @java.lang.Override
    public boolean hasOccupyClanId() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 当前占领中联盟id
     * </pre>
     *
     * <code>optional int64 occupyClanId = 12;</code>
     * @return The occupyClanId.
     */
    @java.lang.Override
    public long getOccupyClanId() {
      return occupyClanId_;
    }

    public static final int SHOWCOLOR_FIELD_NUMBER = 13;
    private int showColor_;
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return Whether the showColor field is set.
     */
    @java.lang.Override
    public boolean hasShowColor() {
      return ((bitField0_ & 0x00001000) != 0);
    }
    /**
     * <pre>
     * 显示的联盟领土颜色
     * </pre>
     *
     * <code>optional int32 showColor = 13;</code>
     * @return The showColor.
     */
    @java.lang.Override
    public int getShowColor() {
      return showColor_;
    }

    public static final int FISRTOWNTSMS_FIELD_NUMBER = 14;
    private long fisrtOwnTsMs_;
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return Whether the fisrtOwnTsMs field is set.
     */
    @java.lang.Override
    public boolean hasFisrtOwnTsMs() {
      return ((bitField0_ & 0x00002000) != 0);
    }
    /**
     * <pre>
     * 首次占领成功时间戳
     * </pre>
     *
     * <code>optional int64 fisrtOwnTsMs = 14;</code>
     * @return The fisrtOwnTsMs.
     */
    @java.lang.Override
    public long getFisrtOwnTsMs() {
      return fisrtOwnTsMs_;
    }

    public static final int REBUILDNUMCALCTSMS_FIELD_NUMBER = 15;
    private long rebuildNumCalcTsMs_;
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return Whether the rebuildNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasRebuildNumCalcTsMs() {
      return ((bitField0_ & 0x00004000) != 0);
    }
    /**
     * <pre>
     * 重建上次结算时间戳
     * </pre>
     *
     * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
     * @return The rebuildNumCalcTsMs.
     */
    @java.lang.Override
    public long getRebuildNumCalcTsMs() {
      return rebuildNumCalcTsMs_;
    }

    public static final int REBUILDSPEED_FIELD_NUMBER = 16;
    private int rebuildSpeed_;
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return Whether the rebuildSpeed field is set.
     */
    @java.lang.Override
    public boolean hasRebuildSpeed() {
      return ((bitField0_ & 0x00008000) != 0);
    }
    /**
     * <pre>
     * 重建速度
     * </pre>
     *
     * <code>optional int32 rebuildSpeed = 16;</code>
     * @return The rebuildSpeed.
     */
    @java.lang.Override
    public int getRebuildSpeed() {
      return rebuildSpeed_;
    }

    public static final int REBUILDNUM_FIELD_NUMBER = 17;
    private int rebuildNum_;
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return Whether the rebuildNum field is set.
     */
    @java.lang.Override
    public boolean hasRebuildNum() {
      return ((bitField0_ & 0x00010000) != 0);
    }
    /**
     * <pre>
     * 当前重建值
     * </pre>
     *
     * <code>optional int32 rebuildNum = 17;</code>
     * @return The rebuildNum.
     */
    @java.lang.Override
    public int getRebuildNum() {
      return rebuildNum_;
    }

    public static final int FILENUMCALCTSMS_FIELD_NUMBER = 18;
    private long fileNumCalcTsMs_;
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return Whether the fileNumCalcTsMs field is set.
     */
    @java.lang.Override
    public boolean hasFileNumCalcTsMs() {
      return ((bitField0_ & 0x00020000) != 0);
    }
    /**
     * <pre>
     * 着火上次结算时间戳
     * </pre>
     *
     * <code>optional int64 fileNumCalcTsMs = 18;</code>
     * @return The fileNumCalcTsMs.
     */
    @java.lang.Override
    public long getFileNumCalcTsMs() {
      return fileNumCalcTsMs_;
    }

    public static final int ALREADYFIRENUM_FIELD_NUMBER = 19;
    private int alreadyFireNum_;
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return Whether the alreadyFireNum field is set.
     */
    @java.lang.Override
    public boolean hasAlreadyFireNum() {
      return ((bitField0_ & 0x00040000) != 0);
    }
    /**
     * <pre>
     * 已经着火烧毁的部分
     * </pre>
     *
     * <code>optional int32 alreadyFireNum = 19;</code>
     * @return The alreadyFireNum.
     */
    @java.lang.Override
    public int getAlreadyFireNum() {
      return alreadyFireNum_;
    }

    public static final int WOULDOVERBURN_FIELD_NUMBER = 20;
    private boolean wouldOverBurn_;
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return Whether the wouldOverBurn field is set.
     */
    @java.lang.Override
    public boolean hasWouldOverBurn() {
      return ((bitField0_ & 0x00080000) != 0);
    }
    /**
     * <pre>
     * 不灭火情况下是否会被烧毁
     * </pre>
     *
     * <code>optional bool wouldOverBurn = 20;</code>
     * @return The wouldOverBurn.
     */
    @java.lang.Override
    public boolean getWouldOverBurn() {
      return wouldOverBurn_;
    }

    public static final int REBUILDTOTALWORK_FIELD_NUMBER = 21;
    private int rebuildTotalWork_;
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return Whether the rebuildTotalWork field is set.
     */
    @java.lang.Override
    public boolean hasRebuildTotalWork() {
      return ((bitField0_ & 0x00100000) != 0);
    }
    /**
     * <pre>
     * 重建总工程量（用于计算百分比）
     * </pre>
     *
     * <code>optional int32 rebuildTotalWork = 21;</code>
     * @return The rebuildTotalWork.
     */
    @java.lang.Override
    public int getRebuildTotalWork() {
      return rebuildTotalWork_;
    }

    public static final int MUSTBURNENDTSMS_FIELD_NUMBER = 22;
    private long mustBurnEndTsMs_;
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return Whether the mustBurnEndTsMs field is set.
     */
    @java.lang.Override
    public boolean hasMustBurnEndTsMs() {
      return ((bitField0_ & 0x00200000) != 0);
    }
    /**
     * <pre>
     * 必须燃烧的结束时间戳
     * </pre>
     *
     * <code>optional int64 mustBurnEndTsMs = 22;</code>
     * @return The mustBurnEndTsMs.
     */
    @java.lang.Override
    public long getMustBurnEndTsMs() {
      return mustBurnEndTsMs_;
    }

    public static final int FLAGCOLOR_FIELD_NUMBER = 23;
    private int flagColor_;
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return Whether the flagColor field is set.
     */
    @java.lang.Override
    public boolean hasFlagColor() {
      return ((bitField0_ & 0x00400000) != 0);
    }
    /**
     * <pre>
     * 旗帜颜色
     * </pre>
     *
     * <code>optional int32 flagColor = 23;</code>
     * @return The flagColor.
     */
    @java.lang.Override
    public int getFlagColor() {
      return flagColor_;
    }

    public static final int FLAGSHADING_FIELD_NUMBER = 24;
    private int flagShading_;
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return Whether the flagShading field is set.
     */
    @java.lang.Override
    public boolean hasFlagShading() {
      return ((bitField0_ & 0x00800000) != 0);
    }
    /**
     * <pre>
     * 旗帜底纹
     * </pre>
     *
     * <code>optional int32 flagShading = 24;</code>
     * @return The flagShading.
     */
    @java.lang.Override
    public int getFlagShading() {
      return flagShading_;
    }

    public static final int FLAGSIGN_FIELD_NUMBER = 25;
    private int flagSign_;
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return Whether the flagSign field is set.
     */
    @java.lang.Override
    public boolean hasFlagSign() {
      return ((bitField0_ & 0x01000000) != 0);
    }
    /**
     * <pre>
     * 旗帜标志
     * </pre>
     *
     * <code>optional int32 flagSign = 25;</code>
     * @return The flagSign.
     */
    @java.lang.Override
    public int getFlagSign() {
      return flagSign_;
    }

    public static final int LASTHITPLAYERNAME_FIELD_NUMBER = 26;
    private volatile java.lang.Object lastHitPlayerName_;
    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return Whether the lastHitPlayerName field is set.
     */
    @java.lang.Override
    public boolean hasLastHitPlayerName() {
      return ((bitField0_ & 0x02000000) != 0);
    }
    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return The lastHitPlayerName.
     */
    @java.lang.Override
    public java.lang.String getLastHitPlayerName() {
      java.lang.Object ref = lastHitPlayerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          lastHitPlayerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 最后一击玩家名字
     * </pre>
     *
     * <code>optional string lastHitPlayerName = 26;</code>
     * @return The bytes for lastHitPlayerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getLastHitPlayerNameBytes() {
      java.lang.Object ref = lastHitPlayerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        lastHitPlayerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int FIRESPEED_FIELD_NUMBER = 27;
    private int fireSpeed_;
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return Whether the fireSpeed field is set.
     */
    @java.lang.Override
    public boolean hasFireSpeed() {
      return ((bitField0_ & 0x04000000) != 0);
    }
    /**
     * <pre>
     * 燃烧速度
     * </pre>
     *
     * <code>optional int32 fireSpeed = 27;</code>
     * @return The fireSpeed.
     */
    @java.lang.Override
    public int getFireSpeed() {
      return fireSpeed_;
    }

    public static final int TRIGGERFIRECLANID_FIELD_NUMBER = 28;
    private long triggerFireClanId_;
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return Whether the triggerFireClanId field is set.
     */
    @java.lang.Override
    public boolean hasTriggerFireClanId() {
      return ((bitField0_ & 0x08000000) != 0);
    }
    /**
     * <pre>
     * 触发燃烧的军团id
     * </pre>
     *
     * <code>optional int64 triggerFireClanId = 28;</code>
     * @return The triggerFireClanId.
     */
    @java.lang.Override
    public long getTriggerFireClanId() {
      return triggerFireClanId_;
    }

    public static final int NATIONFLAGID_FIELD_NUMBER = 29;
    private int nationFlagId_;
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return Whether the nationFlagId field is set.
     */
    @java.lang.Override
    public boolean hasNationFlagId() {
      return ((bitField0_ & 0x10000000) != 0);
    }
    /**
     * <pre>
     * 国家旗帜id
     * </pre>
     *
     * <code>optional int32 nationFlagId = 29;</code>
     * @return The nationFlagId.
     */
    @java.lang.Override
    public int getNationFlagId() {
      return nationFlagId_;
    }

    public static final int ZONEID_FIELD_NUMBER = 30;
    private int zoneId_;
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x20000000) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 30;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    public static final int ZONECOLOR_FIELD_NUMBER = 31;
    private int zoneColor_;
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return Whether the zoneColor field is set.
     */
    @java.lang.Override
    public boolean hasZoneColor() {
      return ((bitField0_ & 0x40000000) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器的领土颜色设置
     * </pre>
     *
     * <code>optional int32 zoneColor = 31;</code>
     * @return The zoneColor.
     */
    @java.lang.Override
    public int getZoneColor() {
      return zoneColor_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, state_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt64(3, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeInt64(4, ownerClanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, showClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt64(7, ownerOccupyTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(8, occupyNum_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt64(9, occupyTsMs_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt64(10, occupyNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeInt32(11, occupySpeed_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt64(12, occupyClanId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        output.writeInt32(13, showColor_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        output.writeInt64(14, fisrtOwnTsMs_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        output.writeInt64(15, rebuildNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        output.writeInt32(16, rebuildSpeed_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        output.writeInt32(17, rebuildNum_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        output.writeInt64(18, fileNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        output.writeInt32(19, alreadyFireNum_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        output.writeBool(20, wouldOverBurn_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        output.writeInt32(21, rebuildTotalWork_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        output.writeInt64(22, mustBurnEndTsMs_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        output.writeInt32(23, flagColor_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        output.writeInt32(24, flagShading_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        output.writeInt32(25, flagSign_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 26, lastHitPlayerName_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        output.writeInt32(27, fireSpeed_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        output.writeInt64(28, triggerFireClanId_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        output.writeInt32(29, nationFlagId_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        output.writeInt32(30, zoneId_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        output.writeInt32(31, zoneColor_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, state_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, stateStartTsMs_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(3, stateEndTsMs_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(4, ownerClanId_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, showClanName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(7, ownerOccupyTsMs_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, occupyNum_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(9, occupyTsMs_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(10, occupyNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(11, occupySpeed_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(12, occupyClanId_);
      }
      if (((bitField0_ & 0x00001000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(13, showColor_);
      }
      if (((bitField0_ & 0x00002000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(14, fisrtOwnTsMs_);
      }
      if (((bitField0_ & 0x00004000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(15, rebuildNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00008000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(16, rebuildSpeed_);
      }
      if (((bitField0_ & 0x00010000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(17, rebuildNum_);
      }
      if (((bitField0_ & 0x00020000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(18, fileNumCalcTsMs_);
      }
      if (((bitField0_ & 0x00040000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(19, alreadyFireNum_);
      }
      if (((bitField0_ & 0x00080000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(20, wouldOverBurn_);
      }
      if (((bitField0_ & 0x00100000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(21, rebuildTotalWork_);
      }
      if (((bitField0_ & 0x00200000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(22, mustBurnEndTsMs_);
      }
      if (((bitField0_ & 0x00400000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(23, flagColor_);
      }
      if (((bitField0_ & 0x00800000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(24, flagShading_);
      }
      if (((bitField0_ & 0x01000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(25, flagSign_);
      }
      if (((bitField0_ & 0x02000000) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(26, lastHitPlayerName_);
      }
      if (((bitField0_ & 0x04000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(27, fireSpeed_);
      }
      if (((bitField0_ & 0x08000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(28, triggerFireClanId_);
      }
      if (((bitField0_ & 0x10000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(29, nationFlagId_);
      }
      if (((bitField0_ & 0x20000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(30, zoneId_);
      }
      if (((bitField0_ & 0x40000000) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(31, zoneColor_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuilding.OccupyInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuilding.OccupyInfo other = (com.yorha.proto.MapBuilding.OccupyInfo) obj;

      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasStateStartTsMs() != other.hasStateStartTsMs()) return false;
      if (hasStateStartTsMs()) {
        if (getStateStartTsMs()
            != other.getStateStartTsMs()) return false;
      }
      if (hasStateEndTsMs() != other.hasStateEndTsMs()) return false;
      if (hasStateEndTsMs()) {
        if (getStateEndTsMs()
            != other.getStateEndTsMs()) return false;
      }
      if (hasOwnerClanId() != other.hasOwnerClanId()) return false;
      if (hasOwnerClanId()) {
        if (getOwnerClanId()
            != other.getOwnerClanId()) return false;
      }
      if (hasShowClanSimpleName() != other.hasShowClanSimpleName()) return false;
      if (hasShowClanSimpleName()) {
        if (!getShowClanSimpleName()
            .equals(other.getShowClanSimpleName())) return false;
      }
      if (hasShowClanName() != other.hasShowClanName()) return false;
      if (hasShowClanName()) {
        if (!getShowClanName()
            .equals(other.getShowClanName())) return false;
      }
      if (hasOwnerOccupyTsMs() != other.hasOwnerOccupyTsMs()) return false;
      if (hasOwnerOccupyTsMs()) {
        if (getOwnerOccupyTsMs()
            != other.getOwnerOccupyTsMs()) return false;
      }
      if (hasOccupyNum() != other.hasOccupyNum()) return false;
      if (hasOccupyNum()) {
        if (getOccupyNum()
            != other.getOccupyNum()) return false;
      }
      if (hasOccupyTsMs() != other.hasOccupyTsMs()) return false;
      if (hasOccupyTsMs()) {
        if (getOccupyTsMs()
            != other.getOccupyTsMs()) return false;
      }
      if (hasOccupyNumCalcTsMs() != other.hasOccupyNumCalcTsMs()) return false;
      if (hasOccupyNumCalcTsMs()) {
        if (getOccupyNumCalcTsMs()
            != other.getOccupyNumCalcTsMs()) return false;
      }
      if (hasOccupySpeed() != other.hasOccupySpeed()) return false;
      if (hasOccupySpeed()) {
        if (getOccupySpeed()
            != other.getOccupySpeed()) return false;
      }
      if (hasOccupyClanId() != other.hasOccupyClanId()) return false;
      if (hasOccupyClanId()) {
        if (getOccupyClanId()
            != other.getOccupyClanId()) return false;
      }
      if (hasShowColor() != other.hasShowColor()) return false;
      if (hasShowColor()) {
        if (getShowColor()
            != other.getShowColor()) return false;
      }
      if (hasFisrtOwnTsMs() != other.hasFisrtOwnTsMs()) return false;
      if (hasFisrtOwnTsMs()) {
        if (getFisrtOwnTsMs()
            != other.getFisrtOwnTsMs()) return false;
      }
      if (hasRebuildNumCalcTsMs() != other.hasRebuildNumCalcTsMs()) return false;
      if (hasRebuildNumCalcTsMs()) {
        if (getRebuildNumCalcTsMs()
            != other.getRebuildNumCalcTsMs()) return false;
      }
      if (hasRebuildSpeed() != other.hasRebuildSpeed()) return false;
      if (hasRebuildSpeed()) {
        if (getRebuildSpeed()
            != other.getRebuildSpeed()) return false;
      }
      if (hasRebuildNum() != other.hasRebuildNum()) return false;
      if (hasRebuildNum()) {
        if (getRebuildNum()
            != other.getRebuildNum()) return false;
      }
      if (hasFileNumCalcTsMs() != other.hasFileNumCalcTsMs()) return false;
      if (hasFileNumCalcTsMs()) {
        if (getFileNumCalcTsMs()
            != other.getFileNumCalcTsMs()) return false;
      }
      if (hasAlreadyFireNum() != other.hasAlreadyFireNum()) return false;
      if (hasAlreadyFireNum()) {
        if (getAlreadyFireNum()
            != other.getAlreadyFireNum()) return false;
      }
      if (hasWouldOverBurn() != other.hasWouldOverBurn()) return false;
      if (hasWouldOverBurn()) {
        if (getWouldOverBurn()
            != other.getWouldOverBurn()) return false;
      }
      if (hasRebuildTotalWork() != other.hasRebuildTotalWork()) return false;
      if (hasRebuildTotalWork()) {
        if (getRebuildTotalWork()
            != other.getRebuildTotalWork()) return false;
      }
      if (hasMustBurnEndTsMs() != other.hasMustBurnEndTsMs()) return false;
      if (hasMustBurnEndTsMs()) {
        if (getMustBurnEndTsMs()
            != other.getMustBurnEndTsMs()) return false;
      }
      if (hasFlagColor() != other.hasFlagColor()) return false;
      if (hasFlagColor()) {
        if (getFlagColor()
            != other.getFlagColor()) return false;
      }
      if (hasFlagShading() != other.hasFlagShading()) return false;
      if (hasFlagShading()) {
        if (getFlagShading()
            != other.getFlagShading()) return false;
      }
      if (hasFlagSign() != other.hasFlagSign()) return false;
      if (hasFlagSign()) {
        if (getFlagSign()
            != other.getFlagSign()) return false;
      }
      if (hasLastHitPlayerName() != other.hasLastHitPlayerName()) return false;
      if (hasLastHitPlayerName()) {
        if (!getLastHitPlayerName()
            .equals(other.getLastHitPlayerName())) return false;
      }
      if (hasFireSpeed() != other.hasFireSpeed()) return false;
      if (hasFireSpeed()) {
        if (getFireSpeed()
            != other.getFireSpeed()) return false;
      }
      if (hasTriggerFireClanId() != other.hasTriggerFireClanId()) return false;
      if (hasTriggerFireClanId()) {
        if (getTriggerFireClanId()
            != other.getTriggerFireClanId()) return false;
      }
      if (hasNationFlagId() != other.hasNationFlagId()) return false;
      if (hasNationFlagId()) {
        if (getNationFlagId()
            != other.getNationFlagId()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (hasZoneColor() != other.hasZoneColor()) return false;
      if (hasZoneColor()) {
        if (getZoneColor()
            != other.getZoneColor()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasStateStartTsMs()) {
        hash = (37 * hash) + STATESTARTTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateStartTsMs());
      }
      if (hasStateEndTsMs()) {
        hash = (37 * hash) + STATEENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getStateEndTsMs());
      }
      if (hasOwnerClanId()) {
        hash = (37 * hash) + OWNERCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerClanId());
      }
      if (hasShowClanSimpleName()) {
        hash = (37 * hash) + SHOWCLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanSimpleName().hashCode();
      }
      if (hasShowClanName()) {
        hash = (37 * hash) + SHOWCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanName().hashCode();
      }
      if (hasOwnerOccupyTsMs()) {
        hash = (37 * hash) + OWNEROCCUPYTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOwnerOccupyTsMs());
      }
      if (hasOccupyNum()) {
        hash = (37 * hash) + OCCUPYNUM_FIELD_NUMBER;
        hash = (53 * hash) + getOccupyNum();
      }
      if (hasOccupyTsMs()) {
        hash = (37 * hash) + OCCUPYTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOccupyTsMs());
      }
      if (hasOccupyNumCalcTsMs()) {
        hash = (37 * hash) + OCCUPYNUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOccupyNumCalcTsMs());
      }
      if (hasOccupySpeed()) {
        hash = (37 * hash) + OCCUPYSPEED_FIELD_NUMBER;
        hash = (53 * hash) + getOccupySpeed();
      }
      if (hasOccupyClanId()) {
        hash = (37 * hash) + OCCUPYCLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getOccupyClanId());
      }
      if (hasShowColor()) {
        hash = (37 * hash) + SHOWCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getShowColor();
      }
      if (hasFisrtOwnTsMs()) {
        hash = (37 * hash) + FISRTOWNTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFisrtOwnTsMs());
      }
      if (hasRebuildNumCalcTsMs()) {
        hash = (37 * hash) + REBUILDNUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getRebuildNumCalcTsMs());
      }
      if (hasRebuildSpeed()) {
        hash = (37 * hash) + REBUILDSPEED_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildSpeed();
      }
      if (hasRebuildNum()) {
        hash = (37 * hash) + REBUILDNUM_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildNum();
      }
      if (hasFileNumCalcTsMs()) {
        hash = (37 * hash) + FILENUMCALCTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getFileNumCalcTsMs());
      }
      if (hasAlreadyFireNum()) {
        hash = (37 * hash) + ALREADYFIRENUM_FIELD_NUMBER;
        hash = (53 * hash) + getAlreadyFireNum();
      }
      if (hasWouldOverBurn()) {
        hash = (37 * hash) + WOULDOVERBURN_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getWouldOverBurn());
      }
      if (hasRebuildTotalWork()) {
        hash = (37 * hash) + REBUILDTOTALWORK_FIELD_NUMBER;
        hash = (53 * hash) + getRebuildTotalWork();
      }
      if (hasMustBurnEndTsMs()) {
        hash = (37 * hash) + MUSTBURNENDTSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMustBurnEndTsMs());
      }
      if (hasFlagColor()) {
        hash = (37 * hash) + FLAGCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getFlagColor();
      }
      if (hasFlagShading()) {
        hash = (37 * hash) + FLAGSHADING_FIELD_NUMBER;
        hash = (53 * hash) + getFlagShading();
      }
      if (hasFlagSign()) {
        hash = (37 * hash) + FLAGSIGN_FIELD_NUMBER;
        hash = (53 * hash) + getFlagSign();
      }
      if (hasLastHitPlayerName()) {
        hash = (37 * hash) + LASTHITPLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getLastHitPlayerName().hashCode();
      }
      if (hasFireSpeed()) {
        hash = (37 * hash) + FIRESPEED_FIELD_NUMBER;
        hash = (53 * hash) + getFireSpeed();
      }
      if (hasTriggerFireClanId()) {
        hash = (37 * hash) + TRIGGERFIRECLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getTriggerFireClanId());
      }
      if (hasNationFlagId()) {
        hash = (37 * hash) + NATIONFLAGID_FIELD_NUMBER;
        hash = (53 * hash) + getNationFlagId();
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      if (hasZoneColor()) {
        hash = (37 * hash) + ZONECOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getZoneColor();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.OccupyInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuilding.OccupyInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * <pre>
     * 占领信息
     * </pre>
     *
     * Protobuf type {@code com.yorha.proto.OccupyInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.OccupyInfo)
        com.yorha.proto.MapBuilding.OccupyInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_OccupyInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_OccupyInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuilding.OccupyInfo.class, com.yorha.proto.MapBuilding.OccupyInfo.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuilding.OccupyInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        stateStartTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        stateEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000004);
        ownerClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000008);
        showClanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000010);
        showClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        ownerOccupyTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000040);
        occupyNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        occupyTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000100);
        occupyNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000200);
        occupySpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x00000400);
        occupyClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000800);
        showColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00001000);
        fisrtOwnTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00002000);
        rebuildNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00004000);
        rebuildSpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x00008000);
        rebuildNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00010000);
        fileNumCalcTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00020000);
        alreadyFireNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00040000);
        wouldOverBurn_ = false;
        bitField0_ = (bitField0_ & ~0x00080000);
        rebuildTotalWork_ = 0;
        bitField0_ = (bitField0_ & ~0x00100000);
        mustBurnEndTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00200000);
        flagColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00400000);
        flagShading_ = 0;
        bitField0_ = (bitField0_ & ~0x00800000);
        flagSign_ = 0;
        bitField0_ = (bitField0_ & ~0x01000000);
        lastHitPlayerName_ = "";
        bitField0_ = (bitField0_ & ~0x02000000);
        fireSpeed_ = 0;
        bitField0_ = (bitField0_ & ~0x04000000);
        triggerFireClanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x08000000);
        nationFlagId_ = 0;
        bitField0_ = (bitField0_ & ~0x10000000);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x20000000);
        zoneColor_ = 0;
        bitField0_ = (bitField0_ & ~0x40000000);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_OccupyInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.OccupyInfo getDefaultInstanceForType() {
        return com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.OccupyInfo build() {
        com.yorha.proto.MapBuilding.OccupyInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.OccupyInfo buildPartial() {
        com.yorha.proto.MapBuilding.OccupyInfo result = new com.yorha.proto.MapBuilding.OccupyInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.stateStartTsMs_ = stateStartTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.stateEndTsMs_ = stateEndTsMs_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.ownerClanId_ = ownerClanId_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          to_bitField0_ |= 0x00000010;
        }
        result.showClanSimpleName_ = showClanSimpleName_;
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.showClanName_ = showClanName_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.ownerOccupyTsMs_ = ownerOccupyTsMs_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.occupyNum_ = occupyNum_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.occupyTsMs_ = occupyTsMs_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.occupyNumCalcTsMs_ = occupyNumCalcTsMs_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.occupySpeed_ = occupySpeed_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.occupyClanId_ = occupyClanId_;
          to_bitField0_ |= 0x00000800;
        }
        if (((from_bitField0_ & 0x00001000) != 0)) {
          result.showColor_ = showColor_;
          to_bitField0_ |= 0x00001000;
        }
        if (((from_bitField0_ & 0x00002000) != 0)) {
          result.fisrtOwnTsMs_ = fisrtOwnTsMs_;
          to_bitField0_ |= 0x00002000;
        }
        if (((from_bitField0_ & 0x00004000) != 0)) {
          result.rebuildNumCalcTsMs_ = rebuildNumCalcTsMs_;
          to_bitField0_ |= 0x00004000;
        }
        if (((from_bitField0_ & 0x00008000) != 0)) {
          result.rebuildSpeed_ = rebuildSpeed_;
          to_bitField0_ |= 0x00008000;
        }
        if (((from_bitField0_ & 0x00010000) != 0)) {
          result.rebuildNum_ = rebuildNum_;
          to_bitField0_ |= 0x00010000;
        }
        if (((from_bitField0_ & 0x00020000) != 0)) {
          result.fileNumCalcTsMs_ = fileNumCalcTsMs_;
          to_bitField0_ |= 0x00020000;
        }
        if (((from_bitField0_ & 0x00040000) != 0)) {
          result.alreadyFireNum_ = alreadyFireNum_;
          to_bitField0_ |= 0x00040000;
        }
        if (((from_bitField0_ & 0x00080000) != 0)) {
          result.wouldOverBurn_ = wouldOverBurn_;
          to_bitField0_ |= 0x00080000;
        }
        if (((from_bitField0_ & 0x00100000) != 0)) {
          result.rebuildTotalWork_ = rebuildTotalWork_;
          to_bitField0_ |= 0x00100000;
        }
        if (((from_bitField0_ & 0x00200000) != 0)) {
          result.mustBurnEndTsMs_ = mustBurnEndTsMs_;
          to_bitField0_ |= 0x00200000;
        }
        if (((from_bitField0_ & 0x00400000) != 0)) {
          result.flagColor_ = flagColor_;
          to_bitField0_ |= 0x00400000;
        }
        if (((from_bitField0_ & 0x00800000) != 0)) {
          result.flagShading_ = flagShading_;
          to_bitField0_ |= 0x00800000;
        }
        if (((from_bitField0_ & 0x01000000) != 0)) {
          result.flagSign_ = flagSign_;
          to_bitField0_ |= 0x01000000;
        }
        if (((from_bitField0_ & 0x02000000) != 0)) {
          to_bitField0_ |= 0x02000000;
        }
        result.lastHitPlayerName_ = lastHitPlayerName_;
        if (((from_bitField0_ & 0x04000000) != 0)) {
          result.fireSpeed_ = fireSpeed_;
          to_bitField0_ |= 0x04000000;
        }
        if (((from_bitField0_ & 0x08000000) != 0)) {
          result.triggerFireClanId_ = triggerFireClanId_;
          to_bitField0_ |= 0x08000000;
        }
        if (((from_bitField0_ & 0x10000000) != 0)) {
          result.nationFlagId_ = nationFlagId_;
          to_bitField0_ |= 0x10000000;
        }
        if (((from_bitField0_ & 0x20000000) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x20000000;
        }
        if (((from_bitField0_ & 0x40000000) != 0)) {
          result.zoneColor_ = zoneColor_;
          to_bitField0_ |= 0x40000000;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuilding.OccupyInfo) {
          return mergeFrom((com.yorha.proto.MapBuilding.OccupyInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuilding.OccupyInfo other) {
        if (other == com.yorha.proto.MapBuilding.OccupyInfo.getDefaultInstance()) return this;
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasStateStartTsMs()) {
          setStateStartTsMs(other.getStateStartTsMs());
        }
        if (other.hasStateEndTsMs()) {
          setStateEndTsMs(other.getStateEndTsMs());
        }
        if (other.hasOwnerClanId()) {
          setOwnerClanId(other.getOwnerClanId());
        }
        if (other.hasShowClanSimpleName()) {
          bitField0_ |= 0x00000010;
          showClanSimpleName_ = other.showClanSimpleName_;
          onChanged();
        }
        if (other.hasShowClanName()) {
          bitField0_ |= 0x00000020;
          showClanName_ = other.showClanName_;
          onChanged();
        }
        if (other.hasOwnerOccupyTsMs()) {
          setOwnerOccupyTsMs(other.getOwnerOccupyTsMs());
        }
        if (other.hasOccupyNum()) {
          setOccupyNum(other.getOccupyNum());
        }
        if (other.hasOccupyTsMs()) {
          setOccupyTsMs(other.getOccupyTsMs());
        }
        if (other.hasOccupyNumCalcTsMs()) {
          setOccupyNumCalcTsMs(other.getOccupyNumCalcTsMs());
        }
        if (other.hasOccupySpeed()) {
          setOccupySpeed(other.getOccupySpeed());
        }
        if (other.hasOccupyClanId()) {
          setOccupyClanId(other.getOccupyClanId());
        }
        if (other.hasShowColor()) {
          setShowColor(other.getShowColor());
        }
        if (other.hasFisrtOwnTsMs()) {
          setFisrtOwnTsMs(other.getFisrtOwnTsMs());
        }
        if (other.hasRebuildNumCalcTsMs()) {
          setRebuildNumCalcTsMs(other.getRebuildNumCalcTsMs());
        }
        if (other.hasRebuildSpeed()) {
          setRebuildSpeed(other.getRebuildSpeed());
        }
        if (other.hasRebuildNum()) {
          setRebuildNum(other.getRebuildNum());
        }
        if (other.hasFileNumCalcTsMs()) {
          setFileNumCalcTsMs(other.getFileNumCalcTsMs());
        }
        if (other.hasAlreadyFireNum()) {
          setAlreadyFireNum(other.getAlreadyFireNum());
        }
        if (other.hasWouldOverBurn()) {
          setWouldOverBurn(other.getWouldOverBurn());
        }
        if (other.hasRebuildTotalWork()) {
          setRebuildTotalWork(other.getRebuildTotalWork());
        }
        if (other.hasMustBurnEndTsMs()) {
          setMustBurnEndTsMs(other.getMustBurnEndTsMs());
        }
        if (other.hasFlagColor()) {
          setFlagColor(other.getFlagColor());
        }
        if (other.hasFlagShading()) {
          setFlagShading(other.getFlagShading());
        }
        if (other.hasFlagSign()) {
          setFlagSign(other.getFlagSign());
        }
        if (other.hasLastHitPlayerName()) {
          bitField0_ |= 0x02000000;
          lastHitPlayerName_ = other.lastHitPlayerName_;
          onChanged();
        }
        if (other.hasFireSpeed()) {
          setFireSpeed(other.getFireSpeed());
        }
        if (other.hasTriggerFireClanId()) {
          setTriggerFireClanId(other.getTriggerFireClanId());
        }
        if (other.hasNationFlagId()) {
          setNationFlagId(other.getNationFlagId());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        if (other.hasZoneColor()) {
          setZoneColor(other.getZoneColor());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuilding.OccupyInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuilding.OccupyInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int state_ = 0;
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.OccupyState getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.OccupyState result = com.yorha.proto.CommonEnum.OccupyState.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.OccupyState.TOS_NEUTRAL : result;
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.OccupyState value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领状态
       * </pre>
       *
       * <code>optional .com.yorha.proto.OccupyState state = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000001);
        state_ = 0;
        onChanged();
        return this;
      }

      private long stateStartTsMs_ ;
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return Whether the stateStartTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateStartTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return The stateStartTsMs.
       */
      @java.lang.Override
      public long getStateStartTsMs() {
        return stateStartTsMs_;
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @param value The stateStartTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateStartTsMs(long value) {
        bitField0_ |= 0x00000002;
        stateStartTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态开始时间戳
       * </pre>
       *
       * <code>optional int64 stateStartTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateStartTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        stateStartTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long stateEndTsMs_ ;
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return Whether the stateEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasStateEndTsMs() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return The stateEndTsMs.
       */
      @java.lang.Override
      public long getStateEndTsMs() {
        return stateEndTsMs_;
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @param value The stateEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setStateEndTsMs(long value) {
        bitField0_ |= 0x00000004;
        stateEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 进入下一阶段时间戳
       * </pre>
       *
       * <code>optional int64 stateEndTsMs = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearStateEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00000004);
        stateEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long ownerClanId_ ;
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return Whether the ownerClanId field is set.
       */
      @java.lang.Override
      public boolean hasOwnerClanId() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return The ownerClanId.
       */
      @java.lang.Override
      public long getOwnerClanId() {
        return ownerClanId_;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @param value The ownerClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerClanId(long value) {
        bitField0_ |= 0x00000008;
        ownerClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟id
       * </pre>
       *
       * <code>optional int64 ownerClanId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerClanId() {
        bitField0_ = (bitField0_ & ~0x00000008);
        ownerClanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object showClanSimpleName_ = "";
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return Whether the showClanSimpleName field is set.
       */
      public boolean hasShowClanSimpleName() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return The showClanSimpleName.
       */
      public java.lang.String getShowClanSimpleName() {
        java.lang.Object ref = showClanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return The bytes for showClanSimpleName.
       */
      public com.google.protobuf.ByteString
          getShowClanSimpleNameBytes() {
        java.lang.Object ref = showClanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @param value The showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000010);
        showClanSimpleName_ = getDefaultInstance().getShowClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟简称  （显示的是占领中的，无人占领中就是拥有者的)
       * </pre>
       *
       * <code>optional string showClanSimpleName = 5;</code>
       * @param value The bytes for showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000010;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object showClanName_ = "";
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return Whether the showClanName field is set.
       */
      public boolean hasShowClanName() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return The showClanName.
       */
      public java.lang.String getShowClanName() {
        java.lang.Object ref = showClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return The bytes for showClanName.
       */
      public com.google.protobuf.ByteString
          getShowClanNameBytes() {
        java.lang.Object ref = showClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @param value The showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        showClanName_ = getDefaultInstance().getShowClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示联盟名字
       * </pre>
       *
       * <code>optional string showClanName = 6;</code>
       * @param value The bytes for showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanName_ = value;
        onChanged();
        return this;
      }

      private long ownerOccupyTsMs_ ;
      /**
       * <pre>
       * 所属联盟占领成功时间
       * </pre>
       *
       * <code>optional int64 ownerOccupyTsMs = 7;</code>
       * @return Whether the ownerOccupyTsMs field is set.
       */
      @java.lang.Override
      public boolean hasOwnerOccupyTsMs() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 所属联盟占领成功时间
       * </pre>
       *
       * <code>optional int64 ownerOccupyTsMs = 7;</code>
       * @return The ownerOccupyTsMs.
       */
      @java.lang.Override
      public long getOwnerOccupyTsMs() {
        return ownerOccupyTsMs_;
      }
      /**
       * <pre>
       * 所属联盟占领成功时间
       * </pre>
       *
       * <code>optional int64 ownerOccupyTsMs = 7;</code>
       * @param value The ownerOccupyTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setOwnerOccupyTsMs(long value) {
        bitField0_ |= 0x00000040;
        ownerOccupyTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟占领成功时间
       * </pre>
       *
       * <code>optional int64 ownerOccupyTsMs = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearOwnerOccupyTsMs() {
        bitField0_ = (bitField0_ & ~0x00000040);
        ownerOccupyTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int occupyNum_ ;
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return Whether the occupyNum field is set.
       */
      @java.lang.Override
      public boolean hasOccupyNum() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return The occupyNum.
       */
      @java.lang.Override
      public int getOccupyNum() {
        return occupyNum_;
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @param value The occupyNum to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyNum(int value) {
        bitField0_ |= 0x00000080;
        occupyNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前占领值
       * </pre>
       *
       * <code>optional int32 occupyNum = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyNum() {
        bitField0_ = (bitField0_ & ~0x00000080);
        occupyNum_ = 0;
        onChanged();
        return this;
      }

      private long occupyTsMs_ ;
      /**
       * <pre>
       * 当前正在占领的开始时间
       * </pre>
       *
       * <code>optional int64 occupyTsMs = 9;</code>
       * @return Whether the occupyTsMs field is set.
       */
      @java.lang.Override
      public boolean hasOccupyTsMs() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 当前正在占领的开始时间
       * </pre>
       *
       * <code>optional int64 occupyTsMs = 9;</code>
       * @return The occupyTsMs.
       */
      @java.lang.Override
      public long getOccupyTsMs() {
        return occupyTsMs_;
      }
      /**
       * <pre>
       * 当前正在占领的开始时间
       * </pre>
       *
       * <code>optional int64 occupyTsMs = 9;</code>
       * @param value The occupyTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyTsMs(long value) {
        bitField0_ |= 0x00000100;
        occupyTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前正在占领的开始时间
       * </pre>
       *
       * <code>optional int64 occupyTsMs = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyTsMs() {
        bitField0_ = (bitField0_ & ~0x00000100);
        occupyTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long occupyNumCalcTsMs_ ;
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return Whether the occupyNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasOccupyNumCalcTsMs() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return The occupyNumCalcTsMs.
       */
      @java.lang.Override
      public long getOccupyNumCalcTsMs() {
        return occupyNumCalcTsMs_;
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @param value The occupyNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyNumCalcTsMs(long value) {
        bitField0_ |= 0x00000200;
        occupyNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领值上次结算时间戳
       * </pre>
       *
       * <code>optional int64 occupyNumCalcTsMs = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00000200);
        occupyNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int occupySpeed_ ;
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return Whether the occupySpeed field is set.
       */
      @java.lang.Override
      public boolean hasOccupySpeed() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return The occupySpeed.
       */
      @java.lang.Override
      public int getOccupySpeed() {
        return occupySpeed_;
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @param value The occupySpeed to set.
       * @return This builder for chaining.
       */
      public Builder setOccupySpeed(int value) {
        bitField0_ |= 0x00000400;
        occupySpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 占领速度
       * </pre>
       *
       * <code>optional int32 occupySpeed = 11;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupySpeed() {
        bitField0_ = (bitField0_ & ~0x00000400);
        occupySpeed_ = 0;
        onChanged();
        return this;
      }

      private long occupyClanId_ ;
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return Whether the occupyClanId field is set.
       */
      @java.lang.Override
      public boolean hasOccupyClanId() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return The occupyClanId.
       */
      @java.lang.Override
      public long getOccupyClanId() {
        return occupyClanId_;
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @param value The occupyClanId to set.
       * @return This builder for chaining.
       */
      public Builder setOccupyClanId(long value) {
        bitField0_ |= 0x00000800;
        occupyClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前占领中联盟id
       * </pre>
       *
       * <code>optional int64 occupyClanId = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearOccupyClanId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        occupyClanId_ = 0L;
        onChanged();
        return this;
      }

      private int showColor_ ;
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return Whether the showColor field is set.
       */
      @java.lang.Override
      public boolean hasShowColor() {
        return ((bitField0_ & 0x00001000) != 0);
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return The showColor.
       */
      @java.lang.Override
      public int getShowColor() {
        return showColor_;
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @param value The showColor to set.
       * @return This builder for chaining.
       */
      public Builder setShowColor(int value) {
        bitField0_ |= 0x00001000;
        showColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示的联盟领土颜色
       * </pre>
       *
       * <code>optional int32 showColor = 13;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowColor() {
        bitField0_ = (bitField0_ & ~0x00001000);
        showColor_ = 0;
        onChanged();
        return this;
      }

      private long fisrtOwnTsMs_ ;
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return Whether the fisrtOwnTsMs field is set.
       */
      @java.lang.Override
      public boolean hasFisrtOwnTsMs() {
        return ((bitField0_ & 0x00002000) != 0);
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return The fisrtOwnTsMs.
       */
      @java.lang.Override
      public long getFisrtOwnTsMs() {
        return fisrtOwnTsMs_;
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @param value The fisrtOwnTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setFisrtOwnTsMs(long value) {
        bitField0_ |= 0x00002000;
        fisrtOwnTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 首次占领成功时间戳
       * </pre>
       *
       * <code>optional int64 fisrtOwnTsMs = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearFisrtOwnTsMs() {
        bitField0_ = (bitField0_ & ~0x00002000);
        fisrtOwnTsMs_ = 0L;
        onChanged();
        return this;
      }

      private long rebuildNumCalcTsMs_ ;
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return Whether the rebuildNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasRebuildNumCalcTsMs() {
        return ((bitField0_ & 0x00004000) != 0);
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return The rebuildNumCalcTsMs.
       */
      @java.lang.Override
      public long getRebuildNumCalcTsMs() {
        return rebuildNumCalcTsMs_;
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @param value The rebuildNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildNumCalcTsMs(long value) {
        bitField0_ |= 0x00004000;
        rebuildNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建上次结算时间戳
       * </pre>
       *
       * <code>optional int64 rebuildNumCalcTsMs = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00004000);
        rebuildNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int rebuildSpeed_ ;
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return Whether the rebuildSpeed field is set.
       */
      @java.lang.Override
      public boolean hasRebuildSpeed() {
        return ((bitField0_ & 0x00008000) != 0);
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return The rebuildSpeed.
       */
      @java.lang.Override
      public int getRebuildSpeed() {
        return rebuildSpeed_;
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @param value The rebuildSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildSpeed(int value) {
        bitField0_ |= 0x00008000;
        rebuildSpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建速度
       * </pre>
       *
       * <code>optional int32 rebuildSpeed = 16;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildSpeed() {
        bitField0_ = (bitField0_ & ~0x00008000);
        rebuildSpeed_ = 0;
        onChanged();
        return this;
      }

      private int rebuildNum_ ;
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return Whether the rebuildNum field is set.
       */
      @java.lang.Override
      public boolean hasRebuildNum() {
        return ((bitField0_ & 0x00010000) != 0);
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return The rebuildNum.
       */
      @java.lang.Override
      public int getRebuildNum() {
        return rebuildNum_;
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @param value The rebuildNum to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildNum(int value) {
        bitField0_ |= 0x00010000;
        rebuildNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前重建值
       * </pre>
       *
       * <code>optional int32 rebuildNum = 17;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildNum() {
        bitField0_ = (bitField0_ & ~0x00010000);
        rebuildNum_ = 0;
        onChanged();
        return this;
      }

      private long fileNumCalcTsMs_ ;
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return Whether the fileNumCalcTsMs field is set.
       */
      @java.lang.Override
      public boolean hasFileNumCalcTsMs() {
        return ((bitField0_ & 0x00020000) != 0);
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return The fileNumCalcTsMs.
       */
      @java.lang.Override
      public long getFileNumCalcTsMs() {
        return fileNumCalcTsMs_;
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @param value The fileNumCalcTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setFileNumCalcTsMs(long value) {
        bitField0_ |= 0x00020000;
        fileNumCalcTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 着火上次结算时间戳
       * </pre>
       *
       * <code>optional int64 fileNumCalcTsMs = 18;</code>
       * @return This builder for chaining.
       */
      public Builder clearFileNumCalcTsMs() {
        bitField0_ = (bitField0_ & ~0x00020000);
        fileNumCalcTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int alreadyFireNum_ ;
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return Whether the alreadyFireNum field is set.
       */
      @java.lang.Override
      public boolean hasAlreadyFireNum() {
        return ((bitField0_ & 0x00040000) != 0);
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return The alreadyFireNum.
       */
      @java.lang.Override
      public int getAlreadyFireNum() {
        return alreadyFireNum_;
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @param value The alreadyFireNum to set.
       * @return This builder for chaining.
       */
      public Builder setAlreadyFireNum(int value) {
        bitField0_ |= 0x00040000;
        alreadyFireNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 已经着火烧毁的部分
       * </pre>
       *
       * <code>optional int32 alreadyFireNum = 19;</code>
       * @return This builder for chaining.
       */
      public Builder clearAlreadyFireNum() {
        bitField0_ = (bitField0_ & ~0x00040000);
        alreadyFireNum_ = 0;
        onChanged();
        return this;
      }

      private boolean wouldOverBurn_ ;
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return Whether the wouldOverBurn field is set.
       */
      @java.lang.Override
      public boolean hasWouldOverBurn() {
        return ((bitField0_ & 0x00080000) != 0);
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return The wouldOverBurn.
       */
      @java.lang.Override
      public boolean getWouldOverBurn() {
        return wouldOverBurn_;
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @param value The wouldOverBurn to set.
       * @return This builder for chaining.
       */
      public Builder setWouldOverBurn(boolean value) {
        bitField0_ |= 0x00080000;
        wouldOverBurn_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 不灭火情况下是否会被烧毁
       * </pre>
       *
       * <code>optional bool wouldOverBurn = 20;</code>
       * @return This builder for chaining.
       */
      public Builder clearWouldOverBurn() {
        bitField0_ = (bitField0_ & ~0x00080000);
        wouldOverBurn_ = false;
        onChanged();
        return this;
      }

      private int rebuildTotalWork_ ;
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return Whether the rebuildTotalWork field is set.
       */
      @java.lang.Override
      public boolean hasRebuildTotalWork() {
        return ((bitField0_ & 0x00100000) != 0);
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return The rebuildTotalWork.
       */
      @java.lang.Override
      public int getRebuildTotalWork() {
        return rebuildTotalWork_;
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @param value The rebuildTotalWork to set.
       * @return This builder for chaining.
       */
      public Builder setRebuildTotalWork(int value) {
        bitField0_ |= 0x00100000;
        rebuildTotalWork_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 重建总工程量（用于计算百分比）
       * </pre>
       *
       * <code>optional int32 rebuildTotalWork = 21;</code>
       * @return This builder for chaining.
       */
      public Builder clearRebuildTotalWork() {
        bitField0_ = (bitField0_ & ~0x00100000);
        rebuildTotalWork_ = 0;
        onChanged();
        return this;
      }

      private long mustBurnEndTsMs_ ;
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return Whether the mustBurnEndTsMs field is set.
       */
      @java.lang.Override
      public boolean hasMustBurnEndTsMs() {
        return ((bitField0_ & 0x00200000) != 0);
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return The mustBurnEndTsMs.
       */
      @java.lang.Override
      public long getMustBurnEndTsMs() {
        return mustBurnEndTsMs_;
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @param value The mustBurnEndTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setMustBurnEndTsMs(long value) {
        bitField0_ |= 0x00200000;
        mustBurnEndTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 必须燃烧的结束时间戳
       * </pre>
       *
       * <code>optional int64 mustBurnEndTsMs = 22;</code>
       * @return This builder for chaining.
       */
      public Builder clearMustBurnEndTsMs() {
        bitField0_ = (bitField0_ & ~0x00200000);
        mustBurnEndTsMs_ = 0L;
        onChanged();
        return this;
      }

      private int flagColor_ ;
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return Whether the flagColor field is set.
       */
      @java.lang.Override
      public boolean hasFlagColor() {
        return ((bitField0_ & 0x00400000) != 0);
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return The flagColor.
       */
      @java.lang.Override
      public int getFlagColor() {
        return flagColor_;
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @param value The flagColor to set.
       * @return This builder for chaining.
       */
      public Builder setFlagColor(int value) {
        bitField0_ |= 0x00400000;
        flagColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜颜色
       * </pre>
       *
       * <code>optional int32 flagColor = 23;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagColor() {
        bitField0_ = (bitField0_ & ~0x00400000);
        flagColor_ = 0;
        onChanged();
        return this;
      }

      private int flagShading_ ;
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return Whether the flagShading field is set.
       */
      @java.lang.Override
      public boolean hasFlagShading() {
        return ((bitField0_ & 0x00800000) != 0);
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return The flagShading.
       */
      @java.lang.Override
      public int getFlagShading() {
        return flagShading_;
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @param value The flagShading to set.
       * @return This builder for chaining.
       */
      public Builder setFlagShading(int value) {
        bitField0_ |= 0x00800000;
        flagShading_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜底纹
       * </pre>
       *
       * <code>optional int32 flagShading = 24;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagShading() {
        bitField0_ = (bitField0_ & ~0x00800000);
        flagShading_ = 0;
        onChanged();
        return this;
      }

      private int flagSign_ ;
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return Whether the flagSign field is set.
       */
      @java.lang.Override
      public boolean hasFlagSign() {
        return ((bitField0_ & 0x01000000) != 0);
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return The flagSign.
       */
      @java.lang.Override
      public int getFlagSign() {
        return flagSign_;
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @param value The flagSign to set.
       * @return This builder for chaining.
       */
      public Builder setFlagSign(int value) {
        bitField0_ |= 0x01000000;
        flagSign_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 旗帜标志
       * </pre>
       *
       * <code>optional int32 flagSign = 25;</code>
       * @return This builder for chaining.
       */
      public Builder clearFlagSign() {
        bitField0_ = (bitField0_ & ~0x01000000);
        flagSign_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object lastHitPlayerName_ = "";
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @return Whether the lastHitPlayerName field is set.
       */
      public boolean hasLastHitPlayerName() {
        return ((bitField0_ & 0x02000000) != 0);
      }
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @return The lastHitPlayerName.
       */
      public java.lang.String getLastHitPlayerName() {
        java.lang.Object ref = lastHitPlayerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            lastHitPlayerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @return The bytes for lastHitPlayerName.
       */
      public com.google.protobuf.ByteString
          getLastHitPlayerNameBytes() {
        java.lang.Object ref = lastHitPlayerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          lastHitPlayerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @param value The lastHitPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setLastHitPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        lastHitPlayerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @return This builder for chaining.
       */
      public Builder clearLastHitPlayerName() {
        bitField0_ = (bitField0_ & ~0x02000000);
        lastHitPlayerName_ = getDefaultInstance().getLastHitPlayerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最后一击玩家名字
       * </pre>
       *
       * <code>optional string lastHitPlayerName = 26;</code>
       * @param value The bytes for lastHitPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setLastHitPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x02000000;
        lastHitPlayerName_ = value;
        onChanged();
        return this;
      }

      private int fireSpeed_ ;
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return Whether the fireSpeed field is set.
       */
      @java.lang.Override
      public boolean hasFireSpeed() {
        return ((bitField0_ & 0x04000000) != 0);
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return The fireSpeed.
       */
      @java.lang.Override
      public int getFireSpeed() {
        return fireSpeed_;
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @param value The fireSpeed to set.
       * @return This builder for chaining.
       */
      public Builder setFireSpeed(int value) {
        bitField0_ |= 0x04000000;
        fireSpeed_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 燃烧速度
       * </pre>
       *
       * <code>optional int32 fireSpeed = 27;</code>
       * @return This builder for chaining.
       */
      public Builder clearFireSpeed() {
        bitField0_ = (bitField0_ & ~0x04000000);
        fireSpeed_ = 0;
        onChanged();
        return this;
      }

      private long triggerFireClanId_ ;
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return Whether the triggerFireClanId field is set.
       */
      @java.lang.Override
      public boolean hasTriggerFireClanId() {
        return ((bitField0_ & 0x08000000) != 0);
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return The triggerFireClanId.
       */
      @java.lang.Override
      public long getTriggerFireClanId() {
        return triggerFireClanId_;
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @param value The triggerFireClanId to set.
       * @return This builder for chaining.
       */
      public Builder setTriggerFireClanId(long value) {
        bitField0_ |= 0x08000000;
        triggerFireClanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 触发燃烧的军团id
       * </pre>
       *
       * <code>optional int64 triggerFireClanId = 28;</code>
       * @return This builder for chaining.
       */
      public Builder clearTriggerFireClanId() {
        bitField0_ = (bitField0_ & ~0x08000000);
        triggerFireClanId_ = 0L;
        onChanged();
        return this;
      }

      private int nationFlagId_ ;
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return Whether the nationFlagId field is set.
       */
      @java.lang.Override
      public boolean hasNationFlagId() {
        return ((bitField0_ & 0x10000000) != 0);
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return The nationFlagId.
       */
      @java.lang.Override
      public int getNationFlagId() {
        return nationFlagId_;
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @param value The nationFlagId to set.
       * @return This builder for chaining.
       */
      public Builder setNationFlagId(int value) {
        bitField0_ |= 0x10000000;
        nationFlagId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 国家旗帜id
       * </pre>
       *
       * <code>optional int32 nationFlagId = 29;</code>
       * @return This builder for chaining.
       */
      public Builder clearNationFlagId() {
        bitField0_ = (bitField0_ & ~0x10000000);
        nationFlagId_ = 0;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x20000000) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x20000000;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 30;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x20000000);
        zoneId_ = 0;
        onChanged();
        return this;
      }

      private int zoneColor_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return Whether the zoneColor field is set.
       */
      @java.lang.Override
      public boolean hasZoneColor() {
        return ((bitField0_ & 0x40000000) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return The zoneColor.
       */
      @java.lang.Override
      public int getZoneColor() {
        return zoneColor_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @param value The zoneColor to set.
       * @return This builder for chaining.
       */
      public Builder setZoneColor(int value) {
        bitField0_ |= 0x40000000;
        zoneColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器的领土颜色设置
       * </pre>
       *
       * <code>optional int32 zoneColor = 31;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneColor() {
        bitField0_ = (bitField0_ & ~0x40000000);
        zoneColor_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.OccupyInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.OccupyInfo)
    private static final com.yorha.proto.MapBuilding.OccupyInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuilding.OccupyInfo();
    }

    public static com.yorha.proto.MapBuilding.OccupyInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<OccupyInfo>
        PARSER = new com.google.protobuf.AbstractParser<OccupyInfo>() {
      @java.lang.Override
      public OccupyInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new OccupyInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<OccupyInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<OccupyInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuilding.OccupyInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ConstructInfoOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ConstructInfo)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    boolean hasType();
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    com.yorha.proto.CommonEnum.MapBuildingType getType();

    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return Whether the currentDurability field is set.
     */
    boolean hasCurrentDurability();
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return The currentDurability.
     */
    int getCurrentDurability();

    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return Whether the maxDurability field is set.
     */
    boolean hasMaxDurability();
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return The maxDurability.
     */
    int getMaxDurability();

    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return Whether the isOnFire field is set.
     */
    boolean hasIsOnFire();
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return The isOnFire.
     */
    boolean getIsOnFire();

    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return Whether the isConnectedToCommandNet field is set.
     */
    boolean hasIsConnectedToCommandNet();
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return The isConnectedToCommandNet.
     */
    boolean getIsConnectedToCommandNet();

    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return Whether the beforeRebuildTemplateId field is set.
     */
    boolean hasBeforeRebuildTemplateId();
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return The beforeRebuildTemplateId.
     */
    int getBeforeRebuildTemplateId();

    /**
     * <pre>
     * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
     * </pre>
     *
     * <code>optional int32 affectedByWhichMainBase = 7;</code>
     * @return Whether the affectedByWhichMainBase field is set.
     */
    boolean hasAffectedByWhichMainBase();
    /**
     * <pre>
     * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
     * </pre>
     *
     * <code>optional int32 affectedByWhichMainBase = 7;</code>
     * @return The affectedByWhichMainBase.
     */
    int getAffectedByWhichMainBase();

    /**
     * <pre>
     * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
     * </pre>
     *
     * <code>optional int32 noMainBase = 8;</code>
     * @return Whether the noMainBase field is set.
     */
    boolean hasNoMainBase();
    /**
     * <pre>
     * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
     * </pre>
     *
     * <code>optional int32 noMainBase = 8;</code>
     * @return The noMainBase.
     */
    int getNoMainBase();

    /**
     * <pre>
     * 建设联盟建筑的player对应的staff id
     * </pre>
     *
     * <code>optional int32 startStaffId = 9;</code>
     * @return Whether the startStaffId field is set.
     */
    boolean hasStartStaffId();
    /**
     * <pre>
     * 建设联盟建筑的player对应的staff id
     * </pre>
     *
     * <code>optional int32 startStaffId = 9;</code>
     * @return The startStaffId.
     */
    int getStartStaffId();

    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return Whether the startPlayerName field is set.
     */
    boolean hasStartPlayerName();
    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return The startPlayerName.
     */
    java.lang.String getStartPlayerName();
    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return The bytes for startPlayerName.
     */
    com.google.protobuf.ByteString
        getStartPlayerNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ConstructInfo}
   */
  public static final class ConstructInfo extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ConstructInfo)
      ConstructInfoOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ConstructInfo.newBuilder() to construct.
    private ConstructInfo(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ConstructInfo() {
      type_ = 0;
      startPlayerName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ConstructInfo();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ConstructInfo(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.MapBuildingType value = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                type_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              currentDurability_ = input.readInt32();
              break;
            }
            case 24: {
              bitField0_ |= 0x00000004;
              maxDurability_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000008;
              isOnFire_ = input.readBool();
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              isConnectedToCommandNet_ = input.readBool();
              break;
            }
            case 48: {
              bitField0_ |= 0x00000020;
              beforeRebuildTemplateId_ = input.readInt32();
              break;
            }
            case 56: {
              bitField0_ |= 0x00000040;
              affectedByWhichMainBase_ = input.readInt32();
              break;
            }
            case 64: {
              bitField0_ |= 0x00000080;
              noMainBase_ = input.readInt32();
              break;
            }
            case 72: {
              bitField0_ |= 0x00000100;
              startStaffId_ = input.readInt32();
              break;
            }
            case 82: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000200;
              startPlayerName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_ConstructInfo_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_ConstructInfo_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuilding.ConstructInfo.class, com.yorha.proto.MapBuilding.ConstructInfo.Builder.class);
    }

    private int bitField0_;
    public static final int TYPE_FIELD_NUMBER = 1;
    private int type_;
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return Whether the type field is set.
     */
    @java.lang.Override public boolean hasType() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 建筑type
     * </pre>
     *
     * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
     * @return The type.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.MapBuildingType getType() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
      return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
    }

    public static final int CURRENTDURABILITY_FIELD_NUMBER = 2;
    private int currentDurability_;
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return Whether the currentDurability field is set.
     */
    @java.lang.Override
    public boolean hasCurrentDurability() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 当前耐久度
     * </pre>
     *
     * <code>optional int32 currentDurability = 2;</code>
     * @return The currentDurability.
     */
    @java.lang.Override
    public int getCurrentDurability() {
      return currentDurability_;
    }

    public static final int MAXDURABILITY_FIELD_NUMBER = 3;
    private int maxDurability_;
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return Whether the maxDurability field is set.
     */
    @java.lang.Override
    public boolean hasMaxDurability() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 最大耐久度
     * </pre>
     *
     * <code>optional int32 maxDurability = 3;</code>
     * @return The maxDurability.
     */
    @java.lang.Override
    public int getMaxDurability() {
      return maxDurability_;
    }

    public static final int ISONFIRE_FIELD_NUMBER = 4;
    private boolean isOnFire_;
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return Whether the isOnFire field is set.
     */
    @java.lang.Override
    public boolean hasIsOnFire() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 是否处在着火状态
     * </pre>
     *
     * <code>optional bool isOnFire = 4;</code>
     * @return The isOnFire.
     */
    @java.lang.Override
    public boolean getIsOnFire() {
      return isOnFire_;
    }

    public static final int ISCONNECTEDTOCOMMANDNET_FIELD_NUMBER = 5;
    private boolean isConnectedToCommandNet_;
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return Whether the isConnectedToCommandNet field is set.
     */
    @java.lang.Override
    public boolean hasIsConnectedToCommandNet() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 是否连接到指挥网
     * </pre>
     *
     * <code>optional bool isConnectedToCommandNet = 5;</code>
     * @return The isConnectedToCommandNet.
     */
    @java.lang.Override
    public boolean getIsConnectedToCommandNet() {
      return isConnectedToCommandNet_;
    }

    public static final int BEFOREREBUILDTEMPLATEID_FIELD_NUMBER = 6;
    private int beforeRebuildTemplateId_;
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return Whether the beforeRebuildTemplateId field is set.
     */
    @java.lang.Override
    public boolean hasBeforeRebuildTemplateId() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 改建前建筑配置id
     * </pre>
     *
     * <code>optional int32 beforeRebuildTemplateId = 6;</code>
     * @return The beforeRebuildTemplateId.
     */
    @java.lang.Override
    public int getBeforeRebuildTemplateId() {
      return beforeRebuildTemplateId_;
    }

    public static final int AFFECTEDBYWHICHMAINBASE_FIELD_NUMBER = 7;
    private int affectedByWhichMainBase_;
    /**
     * <pre>
     * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
     * </pre>
     *
     * <code>optional int32 affectedByWhichMainBase = 7;</code>
     * @return Whether the affectedByWhichMainBase field is set.
     */
    @java.lang.Override
    public boolean hasAffectedByWhichMainBase() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
     * </pre>
     *
     * <code>optional int32 affectedByWhichMainBase = 7;</code>
     * @return The affectedByWhichMainBase.
     */
    @java.lang.Override
    public int getAffectedByWhichMainBase() {
      return affectedByWhichMainBase_;
    }

    public static final int NOMAINBASE_FIELD_NUMBER = 8;
    private int noMainBase_;
    /**
     * <pre>
     * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
     * </pre>
     *
     * <code>optional int32 noMainBase = 8;</code>
     * @return Whether the noMainBase field is set.
     */
    @java.lang.Override
    public boolean hasNoMainBase() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
     * </pre>
     *
     * <code>optional int32 noMainBase = 8;</code>
     * @return The noMainBase.
     */
    @java.lang.Override
    public int getNoMainBase() {
      return noMainBase_;
    }

    public static final int STARTSTAFFID_FIELD_NUMBER = 9;
    private int startStaffId_;
    /**
     * <pre>
     * 建设联盟建筑的player对应的staff id
     * </pre>
     *
     * <code>optional int32 startStaffId = 9;</code>
     * @return Whether the startStaffId field is set.
     */
    @java.lang.Override
    public boolean hasStartStaffId() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 建设联盟建筑的player对应的staff id
     * </pre>
     *
     * <code>optional int32 startStaffId = 9;</code>
     * @return The startStaffId.
     */
    @java.lang.Override
    public int getStartStaffId() {
      return startStaffId_;
    }

    public static final int STARTPLAYERNAME_FIELD_NUMBER = 10;
    private volatile java.lang.Object startPlayerName_;
    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return Whether the startPlayerName field is set.
     */
    @java.lang.Override
    public boolean hasStartPlayerName() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return The startPlayerName.
     */
    @java.lang.Override
    public java.lang.String getStartPlayerName() {
      java.lang.Object ref = startPlayerName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          startPlayerName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 建设联盟建筑的player名字
     * </pre>
     *
     * <code>optional string startPlayerName = 10;</code>
     * @return The bytes for startPlayerName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getStartPlayerNameBytes() {
      java.lang.Object ref = startPlayerName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        startPlayerName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, currentDurability_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(3, maxDurability_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeBool(4, isOnFire_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeBool(5, isConnectedToCommandNet_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        output.writeInt32(6, beforeRebuildTemplateId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        output.writeInt32(7, affectedByWhichMainBase_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeInt32(8, noMainBase_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeInt32(9, startStaffId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 10, startPlayerName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, type_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, currentDurability_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, maxDurability_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(4, isOnFire_);
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(5, isConnectedToCommandNet_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(6, beforeRebuildTemplateId_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(7, affectedByWhichMainBase_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(8, noMainBase_);
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(9, startStaffId_);
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(10, startPlayerName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuilding.ConstructInfo)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuilding.ConstructInfo other = (com.yorha.proto.MapBuilding.ConstructInfo) obj;

      if (hasType() != other.hasType()) return false;
      if (hasType()) {
        if (type_ != other.type_) return false;
      }
      if (hasCurrentDurability() != other.hasCurrentDurability()) return false;
      if (hasCurrentDurability()) {
        if (getCurrentDurability()
            != other.getCurrentDurability()) return false;
      }
      if (hasMaxDurability() != other.hasMaxDurability()) return false;
      if (hasMaxDurability()) {
        if (getMaxDurability()
            != other.getMaxDurability()) return false;
      }
      if (hasIsOnFire() != other.hasIsOnFire()) return false;
      if (hasIsOnFire()) {
        if (getIsOnFire()
            != other.getIsOnFire()) return false;
      }
      if (hasIsConnectedToCommandNet() != other.hasIsConnectedToCommandNet()) return false;
      if (hasIsConnectedToCommandNet()) {
        if (getIsConnectedToCommandNet()
            != other.getIsConnectedToCommandNet()) return false;
      }
      if (hasBeforeRebuildTemplateId() != other.hasBeforeRebuildTemplateId()) return false;
      if (hasBeforeRebuildTemplateId()) {
        if (getBeforeRebuildTemplateId()
            != other.getBeforeRebuildTemplateId()) return false;
      }
      if (hasAffectedByWhichMainBase() != other.hasAffectedByWhichMainBase()) return false;
      if (hasAffectedByWhichMainBase()) {
        if (getAffectedByWhichMainBase()
            != other.getAffectedByWhichMainBase()) return false;
      }
      if (hasNoMainBase() != other.hasNoMainBase()) return false;
      if (hasNoMainBase()) {
        if (getNoMainBase()
            != other.getNoMainBase()) return false;
      }
      if (hasStartStaffId() != other.hasStartStaffId()) return false;
      if (hasStartStaffId()) {
        if (getStartStaffId()
            != other.getStartStaffId()) return false;
      }
      if (hasStartPlayerName() != other.hasStartPlayerName()) return false;
      if (hasStartPlayerName()) {
        if (!getStartPlayerName()
            .equals(other.getStartPlayerName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasType()) {
        hash = (37 * hash) + TYPE_FIELD_NUMBER;
        hash = (53 * hash) + type_;
      }
      if (hasCurrentDurability()) {
        hash = (37 * hash) + CURRENTDURABILITY_FIELD_NUMBER;
        hash = (53 * hash) + getCurrentDurability();
      }
      if (hasMaxDurability()) {
        hash = (37 * hash) + MAXDURABILITY_FIELD_NUMBER;
        hash = (53 * hash) + getMaxDurability();
      }
      if (hasIsOnFire()) {
        hash = (37 * hash) + ISONFIRE_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsOnFire());
      }
      if (hasIsConnectedToCommandNet()) {
        hash = (37 * hash) + ISCONNECTEDTOCOMMANDNET_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsConnectedToCommandNet());
      }
      if (hasBeforeRebuildTemplateId()) {
        hash = (37 * hash) + BEFOREREBUILDTEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getBeforeRebuildTemplateId();
      }
      if (hasAffectedByWhichMainBase()) {
        hash = (37 * hash) + AFFECTEDBYWHICHMAINBASE_FIELD_NUMBER;
        hash = (53 * hash) + getAffectedByWhichMainBase();
      }
      if (hasNoMainBase()) {
        hash = (37 * hash) + NOMAINBASE_FIELD_NUMBER;
        hash = (53 * hash) + getNoMainBase();
      }
      if (hasStartStaffId()) {
        hash = (37 * hash) + STARTSTAFFID_FIELD_NUMBER;
        hash = (53 * hash) + getStartStaffId();
      }
      if (hasStartPlayerName()) {
        hash = (37 * hash) + STARTPLAYERNAME_FIELD_NUMBER;
        hash = (53 * hash) + getStartPlayerName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.ConstructInfo parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuilding.ConstructInfo prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ConstructInfo}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ConstructInfo)
        com.yorha.proto.MapBuilding.ConstructInfoOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_ConstructInfo_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_ConstructInfo_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuilding.ConstructInfo.class, com.yorha.proto.MapBuilding.ConstructInfo.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuilding.ConstructInfo.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        type_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        currentDurability_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        maxDurability_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        isOnFire_ = false;
        bitField0_ = (bitField0_ & ~0x00000008);
        isConnectedToCommandNet_ = false;
        bitField0_ = (bitField0_ & ~0x00000010);
        beforeRebuildTemplateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000020);
        affectedByWhichMainBase_ = 0;
        bitField0_ = (bitField0_ & ~0x00000040);
        noMainBase_ = 0;
        bitField0_ = (bitField0_ & ~0x00000080);
        startStaffId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000100);
        startPlayerName_ = "";
        bitField0_ = (bitField0_ & ~0x00000200);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_ConstructInfo_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.ConstructInfo getDefaultInstanceForType() {
        return com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.ConstructInfo build() {
        com.yorha.proto.MapBuilding.ConstructInfo result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.ConstructInfo buildPartial() {
        com.yorha.proto.MapBuilding.ConstructInfo result = new com.yorha.proto.MapBuilding.ConstructInfo(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.type_ = type_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.currentDurability_ = currentDurability_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.maxDurability_ = maxDurability_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          result.isOnFire_ = isOnFire_;
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.isConnectedToCommandNet_ = isConnectedToCommandNet_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          result.beforeRebuildTemplateId_ = beforeRebuildTemplateId_;
          to_bitField0_ |= 0x00000020;
        }
        if (((from_bitField0_ & 0x00000040) != 0)) {
          result.affectedByWhichMainBase_ = affectedByWhichMainBase_;
          to_bitField0_ |= 0x00000040;
        }
        if (((from_bitField0_ & 0x00000080) != 0)) {
          result.noMainBase_ = noMainBase_;
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          result.startStaffId_ = startStaffId_;
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          to_bitField0_ |= 0x00000200;
        }
        result.startPlayerName_ = startPlayerName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuilding.ConstructInfo) {
          return mergeFrom((com.yorha.proto.MapBuilding.ConstructInfo)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuilding.ConstructInfo other) {
        if (other == com.yorha.proto.MapBuilding.ConstructInfo.getDefaultInstance()) return this;
        if (other.hasType()) {
          setType(other.getType());
        }
        if (other.hasCurrentDurability()) {
          setCurrentDurability(other.getCurrentDurability());
        }
        if (other.hasMaxDurability()) {
          setMaxDurability(other.getMaxDurability());
        }
        if (other.hasIsOnFire()) {
          setIsOnFire(other.getIsOnFire());
        }
        if (other.hasIsConnectedToCommandNet()) {
          setIsConnectedToCommandNet(other.getIsConnectedToCommandNet());
        }
        if (other.hasBeforeRebuildTemplateId()) {
          setBeforeRebuildTemplateId(other.getBeforeRebuildTemplateId());
        }
        if (other.hasAffectedByWhichMainBase()) {
          setAffectedByWhichMainBase(other.getAffectedByWhichMainBase());
        }
        if (other.hasNoMainBase()) {
          setNoMainBase(other.getNoMainBase());
        }
        if (other.hasStartStaffId()) {
          setStartStaffId(other.getStartStaffId());
        }
        if (other.hasStartPlayerName()) {
          bitField0_ |= 0x00000200;
          startPlayerName_ = other.startPlayerName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuilding.ConstructInfo parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuilding.ConstructInfo) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int type_ = 0;
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return Whether the type field is set.
       */
      @java.lang.Override public boolean hasType() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return The type.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.MapBuildingType getType() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.MapBuildingType result = com.yorha.proto.CommonEnum.MapBuildingType.valueOf(type_);
        return result == null ? com.yorha.proto.CommonEnum.MapBuildingType.MBT_NONE : result;
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @param value The type to set.
       * @return This builder for chaining.
       */
      public Builder setType(com.yorha.proto.CommonEnum.MapBuildingType value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        type_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建筑type
       * </pre>
       *
       * <code>optional .com.yorha.proto.MapBuildingType type = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearType() {
        bitField0_ = (bitField0_ & ~0x00000001);
        type_ = 0;
        onChanged();
        return this;
      }

      private int currentDurability_ ;
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return Whether the currentDurability field is set.
       */
      @java.lang.Override
      public boolean hasCurrentDurability() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return The currentDurability.
       */
      @java.lang.Override
      public int getCurrentDurability() {
        return currentDurability_;
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @param value The currentDurability to set.
       * @return This builder for chaining.
       */
      public Builder setCurrentDurability(int value) {
        bitField0_ |= 0x00000002;
        currentDurability_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前耐久度
       * </pre>
       *
       * <code>optional int32 currentDurability = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrentDurability() {
        bitField0_ = (bitField0_ & ~0x00000002);
        currentDurability_ = 0;
        onChanged();
        return this;
      }

      private int maxDurability_ ;
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return Whether the maxDurability field is set.
       */
      @java.lang.Override
      public boolean hasMaxDurability() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return The maxDurability.
       */
      @java.lang.Override
      public int getMaxDurability() {
        return maxDurability_;
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @param value The maxDurability to set.
       * @return This builder for chaining.
       */
      public Builder setMaxDurability(int value) {
        bitField0_ |= 0x00000004;
        maxDurability_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 最大耐久度
       * </pre>
       *
       * <code>optional int32 maxDurability = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearMaxDurability() {
        bitField0_ = (bitField0_ & ~0x00000004);
        maxDurability_ = 0;
        onChanged();
        return this;
      }

      private boolean isOnFire_ ;
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return Whether the isOnFire field is set.
       */
      @java.lang.Override
      public boolean hasIsOnFire() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return The isOnFire.
       */
      @java.lang.Override
      public boolean getIsOnFire() {
        return isOnFire_;
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @param value The isOnFire to set.
       * @return This builder for chaining.
       */
      public Builder setIsOnFire(boolean value) {
        bitField0_ |= 0x00000008;
        isOnFire_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否处在着火状态
       * </pre>
       *
       * <code>optional bool isOnFire = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsOnFire() {
        bitField0_ = (bitField0_ & ~0x00000008);
        isOnFire_ = false;
        onChanged();
        return this;
      }

      private boolean isConnectedToCommandNet_ ;
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return Whether the isConnectedToCommandNet field is set.
       */
      @java.lang.Override
      public boolean hasIsConnectedToCommandNet() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return The isConnectedToCommandNet.
       */
      @java.lang.Override
      public boolean getIsConnectedToCommandNet() {
        return isConnectedToCommandNet_;
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @param value The isConnectedToCommandNet to set.
       * @return This builder for chaining.
       */
      public Builder setIsConnectedToCommandNet(boolean value) {
        bitField0_ |= 0x00000010;
        isConnectedToCommandNet_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否连接到指挥网
       * </pre>
       *
       * <code>optional bool isConnectedToCommandNet = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsConnectedToCommandNet() {
        bitField0_ = (bitField0_ & ~0x00000010);
        isConnectedToCommandNet_ = false;
        onChanged();
        return this;
      }

      private int beforeRebuildTemplateId_ ;
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return Whether the beforeRebuildTemplateId field is set.
       */
      @java.lang.Override
      public boolean hasBeforeRebuildTemplateId() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return The beforeRebuildTemplateId.
       */
      @java.lang.Override
      public int getBeforeRebuildTemplateId() {
        return beforeRebuildTemplateId_;
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @param value The beforeRebuildTemplateId to set.
       * @return This builder for chaining.
       */
      public Builder setBeforeRebuildTemplateId(int value) {
        bitField0_ |= 0x00000020;
        beforeRebuildTemplateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 改建前建筑配置id
       * </pre>
       *
       * <code>optional int32 beforeRebuildTemplateId = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearBeforeRebuildTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000020);
        beforeRebuildTemplateId_ = 0;
        onChanged();
        return this;
      }

      private int affectedByWhichMainBase_ ;
      /**
       * <pre>
       * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
       * </pre>
       *
       * <code>optional int32 affectedByWhichMainBase = 7;</code>
       * @return Whether the affectedByWhichMainBase field is set.
       */
      @java.lang.Override
      public boolean hasAffectedByWhichMainBase() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
       * </pre>
       *
       * <code>optional int32 affectedByWhichMainBase = 7;</code>
       * @return The affectedByWhichMainBase.
       */
      @java.lang.Override
      public int getAffectedByWhichMainBase() {
        return affectedByWhichMainBase_;
      }
      /**
       * <pre>
       * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
       * </pre>
       *
       * <code>optional int32 affectedByWhichMainBase = 7;</code>
       * @param value The affectedByWhichMainBase to set.
       * @return This builder for chaining.
       */
      public Builder setAffectedByWhichMainBase(int value) {
        bitField0_ |= 0x00000040;
        affectedByWhichMainBase_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 在哪个主基地的指挥网范围内[位表示受到哪个指挥中心影响]
       * </pre>
       *
       * <code>optional int32 affectedByWhichMainBase = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearAffectedByWhichMainBase() {
        bitField0_ = (bitField0_ & ~0x00000040);
        affectedByWhichMainBase_ = 0;
        onChanged();
        return this;
      }

      private int noMainBase_ ;
      /**
       * <pre>
       * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
       * </pre>
       *
       * <code>optional int32 noMainBase = 8;</code>
       * @return Whether the noMainBase field is set.
       */
      @java.lang.Override
      public boolean hasNoMainBase() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
       * </pre>
       *
       * <code>optional int32 noMainBase = 8;</code>
       * @return The noMainBase.
       */
      @java.lang.Override
      public int getNoMainBase() {
        return noMainBase_;
      }
      /**
       * <pre>
       * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
       * </pre>
       *
       * <code>optional int32 noMainBase = 8;</code>
       * @param value The noMainBase to set.
       * @return This builder for chaining.
       */
      public Builder setNoMainBase(int value) {
        bitField0_ |= 0x00000080;
        noMainBase_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 仅当建筑是主基地时使用，代表是第几个主基地，用于标记指挥网建筑
       * </pre>
       *
       * <code>optional int32 noMainBase = 8;</code>
       * @return This builder for chaining.
       */
      public Builder clearNoMainBase() {
        bitField0_ = (bitField0_ & ~0x00000080);
        noMainBase_ = 0;
        onChanged();
        return this;
      }

      private int startStaffId_ ;
      /**
       * <pre>
       * 建设联盟建筑的player对应的staff id
       * </pre>
       *
       * <code>optional int32 startStaffId = 9;</code>
       * @return Whether the startStaffId field is set.
       */
      @java.lang.Override
      public boolean hasStartStaffId() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 建设联盟建筑的player对应的staff id
       * </pre>
       *
       * <code>optional int32 startStaffId = 9;</code>
       * @return The startStaffId.
       */
      @java.lang.Override
      public int getStartStaffId() {
        return startStaffId_;
      }
      /**
       * <pre>
       * 建设联盟建筑的player对应的staff id
       * </pre>
       *
       * <code>optional int32 startStaffId = 9;</code>
       * @param value The startStaffId to set.
       * @return This builder for chaining.
       */
      public Builder setStartStaffId(int value) {
        bitField0_ |= 0x00000100;
        startStaffId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建设联盟建筑的player对应的staff id
       * </pre>
       *
       * <code>optional int32 startStaffId = 9;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartStaffId() {
        bitField0_ = (bitField0_ & ~0x00000100);
        startStaffId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object startPlayerName_ = "";
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @return Whether the startPlayerName field is set.
       */
      public boolean hasStartPlayerName() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @return The startPlayerName.
       */
      public java.lang.String getStartPlayerName() {
        java.lang.Object ref = startPlayerName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            startPlayerName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @return The bytes for startPlayerName.
       */
      public com.google.protobuf.ByteString
          getStartPlayerNameBytes() {
        java.lang.Object ref = startPlayerName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          startPlayerName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @param value The startPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setStartPlayerName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        startPlayerName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @return This builder for chaining.
       */
      public Builder clearStartPlayerName() {
        bitField0_ = (bitField0_ & ~0x00000200);
        startPlayerName_ = getDefaultInstance().getStartPlayerName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 建设联盟建筑的player名字
       * </pre>
       *
       * <code>optional string startPlayerName = 10;</code>
       * @param value The bytes for startPlayerName to set.
       * @return This builder for chaining.
       */
      public Builder setStartPlayerNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000200;
        startPlayerName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ConstructInfo)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ConstructInfo)
    private static final com.yorha.proto.MapBuilding.ConstructInfo DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuilding.ConstructInfo();
    }

    public static com.yorha.proto.MapBuilding.ConstructInfo getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ConstructInfo>
        PARSER = new com.google.protobuf.AbstractParser<ConstructInfo>() {
      @java.lang.Override
      public ConstructInfo parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ConstructInfo(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ConstructInfo> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ConstructInfo> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuilding.ConstructInfo getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface MapBuildingKingdomModelOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.MapBuildingKingdomModel)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     * @return Whether the kingCardHead field is set.
     */
    boolean hasKingCardHead();
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     * @return The kingCardHead.
     */
    com.yorha.proto.Struct.PlayerCardHead getKingCardHead();
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     */
    com.yorha.proto.Struct.PlayerCardHeadOrBuilder getKingCardHeadOrBuilder();

    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return Whether the selectedColor field is set.
     */
    boolean hasSelectedColor();
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return The selectedColor.
     */
    int getSelectedColor();

    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return Whether the destroyZoneId field is set.
     */
    boolean hasDestroyZoneId();
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return The destroyZoneId.
     */
    int getDestroyZoneId();

    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return Whether the destroyClanName field is set.
     */
    boolean hasDestroyClanName();
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The destroyClanName.
     */
    java.lang.String getDestroyClanName();
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The bytes for destroyClanName.
     */
    com.google.protobuf.ByteString
        getDestroyClanNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.MapBuildingKingdomModel}
   */
  public static final class MapBuildingKingdomModel extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.MapBuildingKingdomModel)
      MapBuildingKingdomModelOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use MapBuildingKingdomModel.newBuilder() to construct.
    private MapBuildingKingdomModel(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private MapBuildingKingdomModel() {
      destroyClanName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new MapBuildingKingdomModel();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private MapBuildingKingdomModel(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.Struct.PlayerCardHead.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = kingCardHead_.toBuilder();
              }
              kingCardHead_ = input.readMessage(com.yorha.proto.Struct.PlayerCardHead.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(kingCardHead_);
                kingCardHead_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 24: {
              bitField0_ |= 0x00000002;
              selectedColor_ = input.readInt32();
              break;
            }
            case 32: {
              bitField0_ |= 0x00000004;
              destroyZoneId_ = input.readInt32();
              break;
            }
            case 42: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000008;
              destroyClanName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingKingdomModel_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.MapBuilding.MapBuildingKingdomModel.class, com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder.class);
    }

    private int bitField0_;
    public static final int KINGCARDHEAD_FIELD_NUMBER = 1;
    private com.yorha.proto.Struct.PlayerCardHead kingCardHead_;
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     * @return Whether the kingCardHead field is set.
     */
    @java.lang.Override
    public boolean hasKingCardHead() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     * @return The kingCardHead.
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHead getKingCardHead() {
      return kingCardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : kingCardHead_;
    }
    /**
     * <pre>
     * 国王的铭牌
     * </pre>
     *
     * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getKingCardHeadOrBuilder() {
      return kingCardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : kingCardHead_;
    }

    public static final int SELECTEDCOLOR_FIELD_NUMBER = 3;
    private int selectedColor_;
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return Whether the selectedColor field is set.
     */
    @java.lang.Override
    public boolean hasSelectedColor() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 被选择的颜色
     * </pre>
     *
     * <code>optional int32 selectedColor = 3;</code>
     * @return The selectedColor.
     */
    @java.lang.Override
    public int getSelectedColor() {
      return selectedColor_;
    }

    public static final int DESTROYZONEID_FIELD_NUMBER = 4;
    private int destroyZoneId_;
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return Whether the destroyZoneId field is set.
     */
    @java.lang.Override
    public boolean hasDestroyZoneId() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 摧毁的zoneId
     * </pre>
     *
     * <code>optional int32 destroyZoneId = 4;</code>
     * @return The destroyZoneId.
     */
    @java.lang.Override
    public int getDestroyZoneId() {
      return destroyZoneId_;
    }

    public static final int DESTROYCLANNAME_FIELD_NUMBER = 5;
    private volatile java.lang.Object destroyClanName_;
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return Whether the destroyClanName field is set.
     */
    @java.lang.Override
    public boolean hasDestroyClanName() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The destroyClanName.
     */
    @java.lang.Override
    public java.lang.String getDestroyClanName() {
      java.lang.Object ref = destroyClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          destroyClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 摧毁的联盟名字
     * </pre>
     *
     * <code>optional string destroyClanName = 5;</code>
     * @return The bytes for destroyClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getDestroyClanNameBytes() {
      java.lang.Object ref = destroyClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        destroyClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getKingCardHead());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(3, selectedColor_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeInt32(4, destroyZoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 5, destroyClanName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getKingCardHead());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(3, selectedColor_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(4, destroyZoneId_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(5, destroyClanName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.MapBuilding.MapBuildingKingdomModel)) {
        return super.equals(obj);
      }
      com.yorha.proto.MapBuilding.MapBuildingKingdomModel other = (com.yorha.proto.MapBuilding.MapBuildingKingdomModel) obj;

      if (hasKingCardHead() != other.hasKingCardHead()) return false;
      if (hasKingCardHead()) {
        if (!getKingCardHead()
            .equals(other.getKingCardHead())) return false;
      }
      if (hasSelectedColor() != other.hasSelectedColor()) return false;
      if (hasSelectedColor()) {
        if (getSelectedColor()
            != other.getSelectedColor()) return false;
      }
      if (hasDestroyZoneId() != other.hasDestroyZoneId()) return false;
      if (hasDestroyZoneId()) {
        if (getDestroyZoneId()
            != other.getDestroyZoneId()) return false;
      }
      if (hasDestroyClanName() != other.hasDestroyClanName()) return false;
      if (hasDestroyClanName()) {
        if (!getDestroyClanName()
            .equals(other.getDestroyClanName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasKingCardHead()) {
        hash = (37 * hash) + KINGCARDHEAD_FIELD_NUMBER;
        hash = (53 * hash) + getKingCardHead().hashCode();
      }
      if (hasSelectedColor()) {
        hash = (37 * hash) + SELECTEDCOLOR_FIELD_NUMBER;
        hash = (53 * hash) + getSelectedColor();
      }
      if (hasDestroyZoneId()) {
        hash = (37 * hash) + DESTROYZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getDestroyZoneId();
      }
      if (hasDestroyClanName()) {
        hash = (37 * hash) + DESTROYCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getDestroyClanName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.MapBuilding.MapBuildingKingdomModel prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.MapBuildingKingdomModel}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.MapBuildingKingdomModel)
        com.yorha.proto.MapBuilding.MapBuildingKingdomModelOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingKingdomModel_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.MapBuilding.MapBuildingKingdomModel.class, com.yorha.proto.MapBuilding.MapBuildingKingdomModel.Builder.class);
      }

      // Construct using com.yorha.proto.MapBuilding.MapBuildingKingdomModel.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getKingCardHeadFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = null;
        } else {
          kingCardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        selectedColor_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        destroyZoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        destroyClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.MapBuilding.internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModel getDefaultInstanceForType() {
        return com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModel build() {
        com.yorha.proto.MapBuilding.MapBuildingKingdomModel result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.MapBuilding.MapBuildingKingdomModel buildPartial() {
        com.yorha.proto.MapBuilding.MapBuildingKingdomModel result = new com.yorha.proto.MapBuilding.MapBuildingKingdomModel(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (kingCardHeadBuilder_ == null) {
            result.kingCardHead_ = kingCardHead_;
          } else {
            result.kingCardHead_ = kingCardHeadBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.selectedColor_ = selectedColor_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          result.destroyZoneId_ = destroyZoneId_;
          to_bitField0_ |= 0x00000004;
        }
        if (((from_bitField0_ & 0x00000008) != 0)) {
          to_bitField0_ |= 0x00000008;
        }
        result.destroyClanName_ = destroyClanName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.MapBuilding.MapBuildingKingdomModel) {
          return mergeFrom((com.yorha.proto.MapBuilding.MapBuildingKingdomModel)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.MapBuilding.MapBuildingKingdomModel other) {
        if (other == com.yorha.proto.MapBuilding.MapBuildingKingdomModel.getDefaultInstance()) return this;
        if (other.hasKingCardHead()) {
          mergeKingCardHead(other.getKingCardHead());
        }
        if (other.hasSelectedColor()) {
          setSelectedColor(other.getSelectedColor());
        }
        if (other.hasDestroyZoneId()) {
          setDestroyZoneId(other.getDestroyZoneId());
        }
        if (other.hasDestroyClanName()) {
          bitField0_ |= 0x00000008;
          destroyClanName_ = other.destroyClanName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.MapBuilding.MapBuildingKingdomModel parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.MapBuilding.MapBuildingKingdomModel) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.Struct.PlayerCardHead kingCardHead_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> kingCardHeadBuilder_;
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       * @return Whether the kingCardHead field is set.
       */
      public boolean hasKingCardHead() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       * @return The kingCardHead.
       */
      public com.yorha.proto.Struct.PlayerCardHead getKingCardHead() {
        if (kingCardHeadBuilder_ == null) {
          return kingCardHead_ == null ? com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : kingCardHead_;
        } else {
          return kingCardHeadBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public Builder setKingCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (kingCardHeadBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          kingCardHead_ = value;
          onChanged();
        } else {
          kingCardHeadBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public Builder setKingCardHead(
          com.yorha.proto.Struct.PlayerCardHead.Builder builderForValue) {
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = builderForValue.build();
          onChanged();
        } else {
          kingCardHeadBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public Builder mergeKingCardHead(com.yorha.proto.Struct.PlayerCardHead value) {
        if (kingCardHeadBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              kingCardHead_ != null &&
              kingCardHead_ != com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance()) {
            kingCardHead_ =
              com.yorha.proto.Struct.PlayerCardHead.newBuilder(kingCardHead_).mergeFrom(value).buildPartial();
          } else {
            kingCardHead_ = value;
          }
          onChanged();
        } else {
          kingCardHeadBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public Builder clearKingCardHead() {
        if (kingCardHeadBuilder_ == null) {
          kingCardHead_ = null;
          onChanged();
        } else {
          kingCardHeadBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHead.Builder getKingCardHeadBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getKingCardHeadFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      public com.yorha.proto.Struct.PlayerCardHeadOrBuilder getKingCardHeadOrBuilder() {
        if (kingCardHeadBuilder_ != null) {
          return kingCardHeadBuilder_.getMessageOrBuilder();
        } else {
          return kingCardHead_ == null ?
              com.yorha.proto.Struct.PlayerCardHead.getDefaultInstance() : kingCardHead_;
        }
      }
      /**
       * <pre>
       * 国王的铭牌
       * </pre>
       *
       * <code>optional .com.yorha.proto.PlayerCardHead kingCardHead = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder> 
          getKingCardHeadFieldBuilder() {
        if (kingCardHeadBuilder_ == null) {
          kingCardHeadBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.Struct.PlayerCardHead, com.yorha.proto.Struct.PlayerCardHead.Builder, com.yorha.proto.Struct.PlayerCardHeadOrBuilder>(
                  getKingCardHead(),
                  getParentForChildren(),
                  isClean());
          kingCardHead_ = null;
        }
        return kingCardHeadBuilder_;
      }

      private int selectedColor_ ;
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return Whether the selectedColor field is set.
       */
      @java.lang.Override
      public boolean hasSelectedColor() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return The selectedColor.
       */
      @java.lang.Override
      public int getSelectedColor() {
        return selectedColor_;
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @param value The selectedColor to set.
       * @return This builder for chaining.
       */
      public Builder setSelectedColor(int value) {
        bitField0_ |= 0x00000002;
        selectedColor_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 被选择的颜色
       * </pre>
       *
       * <code>optional int32 selectedColor = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearSelectedColor() {
        bitField0_ = (bitField0_ & ~0x00000002);
        selectedColor_ = 0;
        onChanged();
        return this;
      }

      private int destroyZoneId_ ;
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return Whether the destroyZoneId field is set.
       */
      @java.lang.Override
      public boolean hasDestroyZoneId() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return The destroyZoneId.
       */
      @java.lang.Override
      public int getDestroyZoneId() {
        return destroyZoneId_;
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @param value The destroyZoneId to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyZoneId(int value) {
        bitField0_ |= 0x00000004;
        destroyZoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的zoneId
       * </pre>
       *
       * <code>optional int32 destroyZoneId = 4;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestroyZoneId() {
        bitField0_ = (bitField0_ & ~0x00000004);
        destroyZoneId_ = 0;
        onChanged();
        return this;
      }

      private java.lang.Object destroyClanName_ = "";
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return Whether the destroyClanName field is set.
       */
      public boolean hasDestroyClanName() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return The destroyClanName.
       */
      public java.lang.String getDestroyClanName() {
        java.lang.Object ref = destroyClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            destroyClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return The bytes for destroyClanName.
       */
      public com.google.protobuf.ByteString
          getDestroyClanNameBytes() {
        java.lang.Object ref = destroyClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          destroyClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @param value The destroyClanName to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        destroyClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearDestroyClanName() {
        bitField0_ = (bitField0_ & ~0x00000008);
        destroyClanName_ = getDefaultInstance().getDestroyClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 摧毁的联盟名字
       * </pre>
       *
       * <code>optional string destroyClanName = 5;</code>
       * @param value The bytes for destroyClanName to set.
       * @return This builder for chaining.
       */
      public Builder setDestroyClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000008;
        destroyClanName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.MapBuildingKingdomModel)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.MapBuildingKingdomModel)
    private static final com.yorha.proto.MapBuilding.MapBuildingKingdomModel DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.MapBuilding.MapBuildingKingdomModel();
    }

    public static com.yorha.proto.MapBuilding.MapBuildingKingdomModel getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<MapBuildingKingdomModel>
        PARSER = new com.google.protobuf.AbstractParser<MapBuildingKingdomModel>() {
      @java.lang.Override
      public MapBuildingKingdomModel parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new MapBuildingKingdomModel(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<MapBuildingKingdomModel> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<MapBuildingKingdomModel> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.MapBuilding.MapBuildingKingdomModel getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MapBuildingEntity_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MapBuildingEntity_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_OccupyInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_OccupyInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ConstructInfo_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ConstructInfo_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_MapBuildingKingdomModel_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n+ss_proto/gen/mapBuilding/map_building." +
      "proto\022\017com.yorha.proto\032\034ss_proto/gen/cnc" +
      "/basic.proto\032%ss_proto/gen/common/common" +
      "_enum.proto\032 ss_proto/gen/common/struct." +
      "proto\032\'ss_proto/gen/common/struct_battle" +
      ".proto\032\'ss_proto/gen/common/struct_playe" +
      "r.proto\"\336\005\n\021MapBuildingEntity\022%\n\005point\030\001" +
      " \001(\0132\026.com.yorha.proto.Point\022\016\n\006partId\030\002" +
      " \001(\005\022\022\n\ntemplateId\030\003 \001(\005\0221\n\tinnerArmy\030\004 " +
      "\001(\0132\036.com.yorha.proto.CityInnerArmy\022/\n\no" +
      "ccupyinfo\030\005 \001(\0132\033.com.yorha.proto.Occupy" +
      "Info\022%\n\005troop\030\006 \001(\0132\026.com.yorha.proto.Tr" +
      "oop\022\'\n\006battle\030\007 \001(\0132\027.com.yorha.proto.Ba" +
      "ttle\022)\n\007buffSys\030\010 \001(\0132\030.com.yorha.proto." +
      "BuffSys\0225\n\rconstructInfo\030\t \001(\0132\036.com.yor" +
      "ha.proto.ConstructInfo\0224\n\ndevBuffSys\030\013 \001" +
      "(\0132 .com.yorha.proto.SceneDevBuffSys\0224\n\t" +
      "safeGuard\030\014 \001(\0132!.com.yorha.proto.Specia" +
      "lSafeGuard\022<\n\030recommendSoldierTypeList\030\r" +
      " \001(\0132\032.com.yorha.proto.Int32List\022\026\n\016pass" +
      "ingArmyNum\030\016 \001(\005\022/\n\nexpression\030\017 \001(\0132\033.c" +
      "om.yorha.proto.Expression\022>\n\014kingdomMode" +
      "l\030\020 \001(\0132(.com.yorha.proto.MapBuildingKin" +
      "gdomModel\0225\n\005arrow\030\027 \001(\0132&.com.yorha.pro" +
      "to.Int64ArmyArrowItemMap\"\332\005\n\nOccupyInfo\022" +
      "+\n\005state\030\001 \001(\0162\034.com.yorha.proto.OccupyS" +
      "tate\022\026\n\016stateStartTsMs\030\002 \001(\003\022\024\n\014stateEnd" +
      "TsMs\030\003 \001(\003\022\023\n\013ownerClanId\030\004 \001(\003\022\032\n\022showC" +
      "lanSimpleName\030\005 \001(\t\022\024\n\014showClanName\030\006 \001(" +
      "\t\022\027\n\017ownerOccupyTsMs\030\007 \001(\003\022\021\n\toccupyNum\030" +
      "\010 \001(\005\022\022\n\noccupyTsMs\030\t \001(\003\022\031\n\021occupyNumCa" +
      "lcTsMs\030\n \001(\003\022\023\n\013occupySpeed\030\013 \001(\005\022\024\n\014occ" +
      "upyClanId\030\014 \001(\003\022\021\n\tshowColor\030\r \001(\005\022\024\n\014fi" +
      "srtOwnTsMs\030\016 \001(\003\022\032\n\022rebuildNumCalcTsMs\030\017" +
      " \001(\003\022\024\n\014rebuildSpeed\030\020 \001(\005\022\022\n\nrebuildNum" +
      "\030\021 \001(\005\022\027\n\017fileNumCalcTsMs\030\022 \001(\003\022\026\n\016alrea" +
      "dyFireNum\030\023 \001(\005\022\025\n\rwouldOverBurn\030\024 \001(\010\022\030" +
      "\n\020rebuildTotalWork\030\025 \001(\005\022\027\n\017mustBurnEndT" +
      "sMs\030\026 \001(\003\022\021\n\tflagColor\030\027 \001(\005\022\023\n\013flagShad" +
      "ing\030\030 \001(\005\022\020\n\010flagSign\030\031 \001(\005\022\031\n\021lastHitPl" +
      "ayerName\030\032 \001(\t\022\021\n\tfireSpeed\030\033 \001(\005\022\031\n\021tri" +
      "ggerFireClanId\030\034 \001(\003\022\024\n\014nationFlagId\030\035 \001" +
      "(\005\022\016\n\006zoneId\030\036 \001(\005\022\021\n\tzoneColor\030\037 \001(\005\"\251\002" +
      "\n\rConstructInfo\022.\n\004type\030\001 \001(\0162 .com.yorh" +
      "a.proto.MapBuildingType\022\031\n\021currentDurabi" +
      "lity\030\002 \001(\005\022\025\n\rmaxDurability\030\003 \001(\005\022\020\n\010isO" +
      "nFire\030\004 \001(\010\022\037\n\027isConnectedToCommandNet\030\005" +
      " \001(\010\022\037\n\027beforeRebuildTemplateId\030\006 \001(\005\022\037\n" +
      "\027affectedByWhichMainBase\030\007 \001(\005\022\022\n\nnoMain" +
      "Base\030\010 \001(\005\022\024\n\014startStaffId\030\t \001(\005\022\027\n\017star" +
      "tPlayerName\030\n \001(\t\"\227\001\n\027MapBuildingKingdom" +
      "Model\0225\n\014kingCardHead\030\001 \001(\0132\037.com.yorha." +
      "proto.PlayerCardHead\022\025\n\rselectedColor\030\003 " +
      "\001(\005\022\025\n\rdestroyZoneId\030\004 \001(\005\022\027\n\017destroyCla" +
      "nName\030\005 \001(\tB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.Basic.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
          com.yorha.proto.Struct.getDescriptor(),
          com.yorha.proto.StructBattle.getDescriptor(),
          com.yorha.proto.StructPlayer.getDescriptor(),
        });
    internal_static_com_yorha_proto_MapBuildingEntity_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_MapBuildingEntity_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MapBuildingEntity_descriptor,
        new java.lang.String[] { "Point", "PartId", "TemplateId", "InnerArmy", "Occupyinfo", "Troop", "Battle", "BuffSys", "ConstructInfo", "DevBuffSys", "SafeGuard", "RecommendSoldierTypeList", "PassingArmyNum", "Expression", "KingdomModel", "Arrow", });
    internal_static_com_yorha_proto_OccupyInfo_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_OccupyInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_OccupyInfo_descriptor,
        new java.lang.String[] { "State", "StateStartTsMs", "StateEndTsMs", "OwnerClanId", "ShowClanSimpleName", "ShowClanName", "OwnerOccupyTsMs", "OccupyNum", "OccupyTsMs", "OccupyNumCalcTsMs", "OccupySpeed", "OccupyClanId", "ShowColor", "FisrtOwnTsMs", "RebuildNumCalcTsMs", "RebuildSpeed", "RebuildNum", "FileNumCalcTsMs", "AlreadyFireNum", "WouldOverBurn", "RebuildTotalWork", "MustBurnEndTsMs", "FlagColor", "FlagShading", "FlagSign", "LastHitPlayerName", "FireSpeed", "TriggerFireClanId", "NationFlagId", "ZoneId", "ZoneColor", });
    internal_static_com_yorha_proto_ConstructInfo_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_ConstructInfo_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ConstructInfo_descriptor,
        new java.lang.String[] { "Type", "CurrentDurability", "MaxDurability", "IsOnFire", "IsConnectedToCommandNet", "BeforeRebuildTemplateId", "AffectedByWhichMainBase", "NoMainBase", "StartStaffId", "StartPlayerName", });
    internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_MapBuildingKingdomModel_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_MapBuildingKingdomModel_descriptor,
        new java.lang.String[] { "KingCardHead", "SelectedColor", "DestroyZoneId", "DestroyClanName", });
    com.yorha.proto.Basic.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
    com.yorha.proto.Struct.getDescriptor();
    com.yorha.proto.StructBattle.getDescriptor();
    com.yorha.proto.StructPlayer.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

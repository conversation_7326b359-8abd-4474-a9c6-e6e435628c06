// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_activity.proto

package com.yorha.proto;

public final class SsSceneActivity {
  private SsSceneActivity() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface EnableActivityEffectAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EnableActivityEffectAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 活动效果
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
     * @return Whether the activityEffect field is set.
     */
    boolean hasActivityEffect();
    /**
     * <pre>
     * 活动效果
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
     * @return The activityEffect.
     */
    com.yorha.proto.CommonEnum.ZoneActivityEffect getActivityEffect();

    /**
     * <pre>
     * 过期时间
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return Whether the expireTsMs field is set.
     */
    boolean hasExpireTsMs();
    /**
     * <pre>
     * 过期时间
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return The expireTsMs.
     */
    long getExpireTsMs();
  }
  /**
   * Protobuf type {@code com.yorha.proto.EnableActivityEffectAsk}
   */
  public static final class EnableActivityEffectAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EnableActivityEffectAsk)
      EnableActivityEffectAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EnableActivityEffectAsk.newBuilder() to construct.
    private EnableActivityEffectAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EnableActivityEffectAsk() {
      activityEffect_ = 0;
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EnableActivityEffectAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EnableActivityEffectAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ZoneActivityEffect value = com.yorha.proto.CommonEnum.ZoneActivityEffect.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(1, rawValue);
              } else {
                bitField0_ |= 0x00000001;
                activityEffect_ = rawValue;
              }
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              expireTsMs_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.class, com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ACTIVITYEFFECT_FIELD_NUMBER = 1;
    private int activityEffect_;
    /**
     * <pre>
     * 活动效果
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
     * @return Whether the activityEffect field is set.
     */
    @java.lang.Override public boolean hasActivityEffect() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 活动效果
     * </pre>
     *
     * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
     * @return The activityEffect.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ZoneActivityEffect getActivityEffect() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ZoneActivityEffect result = com.yorha.proto.CommonEnum.ZoneActivityEffect.valueOf(activityEffect_);
      return result == null ? com.yorha.proto.CommonEnum.ZoneActivityEffect.ZAE_NONE : result;
    }

    public static final int EXPIRETSMS_FIELD_NUMBER = 2;
    private long expireTsMs_;
    /**
     * <pre>
     * 过期时间
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return Whether the expireTsMs field is set.
     */
    @java.lang.Override
    public boolean hasExpireTsMs() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 过期时间
     * </pre>
     *
     * <code>optional int64 expireTsMs = 2;</code>
     * @return The expireTsMs.
     */
    @java.lang.Override
    public long getExpireTsMs() {
      return expireTsMs_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeEnum(1, activityEffect_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, expireTsMs_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(1, activityEffect_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, expireTsMs_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk other = (com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk) obj;

      if (hasActivityEffect() != other.hasActivityEffect()) return false;
      if (hasActivityEffect()) {
        if (activityEffect_ != other.activityEffect_) return false;
      }
      if (hasExpireTsMs() != other.hasExpireTsMs()) return false;
      if (hasExpireTsMs()) {
        if (getExpireTsMs()
            != other.getExpireTsMs()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasActivityEffect()) {
        hash = (37 * hash) + ACTIVITYEFFECT_FIELD_NUMBER;
        hash = (53 * hash) + activityEffect_;
      }
      if (hasExpireTsMs()) {
        hash = (37 * hash) + EXPIRETSMS_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getExpireTsMs());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EnableActivityEffectAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EnableActivityEffectAsk)
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.class, com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        activityEffect_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        expireTsMs_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk build() {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk buildPartial() {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk result = new com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.activityEffect_ = activityEffect_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.expireTsMs_ = expireTsMs_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk) {
          return mergeFrom((com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk other) {
        if (other == com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk.getDefaultInstance()) return this;
        if (other.hasActivityEffect()) {
          setActivityEffect(other.getActivityEffect());
        }
        if (other.hasExpireTsMs()) {
          setExpireTsMs(other.getExpireTsMs());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int activityEffect_ = 0;
      /**
       * <pre>
       * 活动效果
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
       * @return Whether the activityEffect field is set.
       */
      @java.lang.Override public boolean hasActivityEffect() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 活动效果
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
       * @return The activityEffect.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ZoneActivityEffect getActivityEffect() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ZoneActivityEffect result = com.yorha.proto.CommonEnum.ZoneActivityEffect.valueOf(activityEffect_);
        return result == null ? com.yorha.proto.CommonEnum.ZoneActivityEffect.ZAE_NONE : result;
      }
      /**
       * <pre>
       * 活动效果
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
       * @param value The activityEffect to set.
       * @return This builder for chaining.
       */
      public Builder setActivityEffect(com.yorha.proto.CommonEnum.ZoneActivityEffect value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000001;
        activityEffect_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 活动效果
       * </pre>
       *
       * <code>optional .com.yorha.proto.ZoneActivityEffect activityEffect = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearActivityEffect() {
        bitField0_ = (bitField0_ & ~0x00000001);
        activityEffect_ = 0;
        onChanged();
        return this;
      }

      private long expireTsMs_ ;
      /**
       * <pre>
       * 过期时间
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return Whether the expireTsMs field is set.
       */
      @java.lang.Override
      public boolean hasExpireTsMs() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 过期时间
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return The expireTsMs.
       */
      @java.lang.Override
      public long getExpireTsMs() {
        return expireTsMs_;
      }
      /**
       * <pre>
       * 过期时间
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @param value The expireTsMs to set.
       * @return This builder for chaining.
       */
      public Builder setExpireTsMs(long value) {
        bitField0_ |= 0x00000002;
        expireTsMs_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 过期时间
       * </pre>
       *
       * <code>optional int64 expireTsMs = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearExpireTsMs() {
        bitField0_ = (bitField0_ & ~0x00000002);
        expireTsMs_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EnableActivityEffectAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EnableActivityEffectAsk)
    private static final com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk();
    }

    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EnableActivityEffectAsk>
        PARSER = new com.google.protobuf.AbstractParser<EnableActivityEffectAsk>() {
      @java.lang.Override
      public EnableActivityEffectAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EnableActivityEffectAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EnableActivityEffectAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EnableActivityEffectAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivity.EnableActivityEffectAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface EnableActivityEffectAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.EnableActivityEffectAns)
      com.google.protobuf.MessageOrBuilder {
  }
  /**
   * Protobuf type {@code com.yorha.proto.EnableActivityEffectAns}
   */
  public static final class EnableActivityEffectAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.EnableActivityEffectAns)
      EnableActivityEffectAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use EnableActivityEffectAns.newBuilder() to construct.
    private EnableActivityEffectAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private EnableActivityEffectAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new EnableActivityEffectAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private EnableActivityEffectAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.class, com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.Builder.class);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneActivity.EnableActivityEffectAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneActivity.EnableActivityEffectAns other = (com.yorha.proto.SsSceneActivity.EnableActivityEffectAns) obj;

      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneActivity.EnableActivityEffectAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.EnableActivityEffectAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.EnableActivityEffectAns)
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.class, com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneActivity.internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAns build() {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneActivity.EnableActivityEffectAns buildPartial() {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAns result = new com.yorha.proto.SsSceneActivity.EnableActivityEffectAns(this);
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneActivity.EnableActivityEffectAns) {
          return mergeFrom((com.yorha.proto.SsSceneActivity.EnableActivityEffectAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneActivity.EnableActivityEffectAns other) {
        if (other == com.yorha.proto.SsSceneActivity.EnableActivityEffectAns.getDefaultInstance()) return this;
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneActivity.EnableActivityEffectAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneActivity.EnableActivityEffectAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.EnableActivityEffectAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.EnableActivityEffectAns)
    private static final com.yorha.proto.SsSceneActivity.EnableActivityEffectAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneActivity.EnableActivityEffectAns();
    }

    public static com.yorha.proto.SsSceneActivity.EnableActivityEffectAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<EnableActivityEffectAns>
        PARSER = new com.google.protobuf.AbstractParser<EnableActivityEffectAns>() {
      @java.lang.Override
      public EnableActivityEffectAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new EnableActivityEffectAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<EnableActivityEffectAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<EnableActivityEffectAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneActivity.EnableActivityEffectAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EnableActivityEffectAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_EnableActivityEffectAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n*ss_proto/gen/scene/ss_scene_activity.p" +
      "roto\022\017com.yorha.proto\032%ss_proto/gen/comm" +
      "on/common_enum.proto\"j\n\027EnableActivityEf" +
      "fectAsk\022;\n\016activityEffect\030\001 \001(\0162#.com.yo" +
      "rha.proto.ZoneActivityEffect\022\022\n\nexpireTs" +
      "Ms\030\002 \001(\003\"\031\n\027EnableActivityEffectAnsB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_EnableActivityEffectAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EnableActivityEffectAsk_descriptor,
        new java.lang.String[] { "ActivityEffect", "ExpireTsMs", });
    internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_EnableActivityEffectAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_EnableActivityEffectAns_descriptor,
        new java.lang.String[] { });
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/option.proto

package com.yorha.proto;

public final class Option {
  private Option() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
    registry.add(com.yorha.proto.Option.tcaplusPrimarykey);
    registry.add(com.yorha.proto.Option.tcaplusIndex);
    registry.add(com.yorha.proto.Option.tcaplusSplitkey);
    registry.add(com.yorha.proto.Option.tcaplusSharedataUuid);
    registry.add(com.yorha.proto.Option.tcaplusSharedataSubscribers);
    registry.add(com.yorha.proto.Option.tcaplusCustomattr2);
    registry.add(com.yorha.proto.Option.documentId);
    registry.add(com.yorha.proto.Option.documentIndex);
    registry.add(com.yorha.proto.Option.tcaplusFieldVersion);
    registry.add(com.yorha.proto.Option.columnSplit);
    registry.add(com.yorha.proto.Option.stringSize);
    registry.add(com.yorha.proto.Option.name);
    registry.add(com.yorha.proto.Option.addNeedSync);
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public static final int TCAPLUS_PRIMARYKEY_FIELD_NUMBER = 50002;
  /**
   * <pre>
   *uid,platid
   * </pre>
   *
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusPrimarykey = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_INDEX_FIELD_NUMBER = 50003;
  /**
   * <pre>
   *index1:a|index2:b
   * </pre>
   *
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusIndex = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_SPLITKEY_FIELD_NUMBER = 50004;
  /**
   * <pre>
   *uid
   * </pre>
   *
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusSplitkey = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_SHAREDATA_UUID_FIELD_NUMBER = 50005;
  /**
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusSharedataUuid = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_SHAREDATA_SUBSCRIBERS_FIELD_NUMBER = 50006;
  /**
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusSharedataSubscribers = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_CUSTOMATTR2_FIELD_NUMBER = 50007;
  /**
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> tcaplusCustomattr2 = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int DOCUMENT_ID_FIELD_NUMBER = 50008;
  /**
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> documentId = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int DOCUMENT_INDEX_FIELD_NUMBER = 50009;
  /**
   * <code>extend .google.protobuf.MessageOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.MessageOptions,
      java.lang.String> documentIndex = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int TCAPLUS_FIELD_VERSION_FIELD_NUMBER = 50002;
  /**
   * <code>extend .google.protobuf.FieldOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.FieldOptions,
      java.lang.Integer> tcaplusFieldVersion = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.Integer.class,
        null);
  public static final int COLUMN_SPLIT_FIELD_NUMBER = 50003;
  /**
   * <pre>
   * tcaplus max 256K per col 1M per line  "1:17,2:17" = xxx_1 version 17 xxx_2 version 17
   * </pre>
   *
   * <code>extend .google.protobuf.FieldOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.FieldOptions,
      java.lang.String> columnSplit = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int STRING_SIZE_FIELD_NUMBER = 50004;
  /**
   * <code>extend .google.protobuf.FieldOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.FieldOptions,
      java.lang.Integer> stringSize = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.Integer.class,
        null);
  public static final int NAME_FIELD_NUMBER = 50002;
  /**
   * <code>extend .google.protobuf.EnumValueOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.EnumValueOptions,
      java.lang.String> name = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.String.class,
        null);
  public static final int ADDNEEDSYNC_FIELD_NUMBER = 50003;
  /**
   * <code>extend .google.protobuf.EnumValueOptions { ... }</code>
   */
  public static final
    com.google.protobuf.GeneratedMessage.GeneratedExtension<
      com.google.protobuf.DescriptorProtos.EnumValueOptions,
      java.lang.Boolean> addNeedSync = com.google.protobuf.GeneratedMessage
          .newFileScopedGeneratedExtension(
        java.lang.Boolean.class,
        null);

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\031ss_proto/gen/option.proto\022\017com.yorha.p" +
      "roto\032 google/protobuf/descriptor.proto:?" +
      "\n\022tcaplus_primarykey\022\037.google.protobuf.M" +
      "essageOptions\030\322\206\003 \001(\t:\000::\n\rtcaplus_index" +
      "\022\037.google.protobuf.MessageOptions\030\323\206\003 \001(" +
      "\t:\000:;\n\020tcaplus_splitkey\022\037.google.protobu" +
      "f.MessageOptions\030\324\206\003 \001(\t:C\n\026tcaplus_shar" +
      "edata_uuid\022\037.google.protobuf.MessageOpti" +
      "ons\030\325\206\003 \001(\t:\000:J\n\035tcaplus_sharedata_subsc" +
      "ribers\022\037.google.protobuf.MessageOptions\030" +
      "\326\206\003 \001(\t:\000:Q\n\023tcaplus_customattr2\022\037.googl" +
      "e.protobuf.MessageOptions\030\327\206\003 \001(\t:\021Table" +
      "Type=GENERIC:6\n\013document_id\022\037.google.pro" +
      "tobuf.MessageOptions\030\330\206\003 \001(\t:;\n\016document" +
      "_index\022\037.google.protobuf.MessageOptions\030" +
      "\331\206\003 \001(\t:\000:A\n\025tcaplus_field_version\022\035.goo" +
      "gle.protobuf.FieldOptions\030\322\206\003 \001(\005:\0011:7\n\014" +
      "column_split\022\035.google.protobuf.FieldOpti" +
      "ons\030\323\206\003 \001(\t:\000::\n\013string_size\022\035.google.pr" +
      "otobuf.FieldOptions\030\324\206\003 \001(\005:\0041024:1\n\004nam" +
      "e\022!.google.protobuf.EnumValueOptions\030\322\206\003" +
      " \001(\t:>\n\013addNeedSync\022!.google.protobuf.En" +
      "umValueOptions\030\323\206\003 \001(\010:\004trueB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.google.protobuf.DescriptorProtos.getDescriptor(),
        });
    tcaplusPrimarykey.internalInit(descriptor.getExtensions().get(0));
    tcaplusIndex.internalInit(descriptor.getExtensions().get(1));
    tcaplusSplitkey.internalInit(descriptor.getExtensions().get(2));
    tcaplusSharedataUuid.internalInit(descriptor.getExtensions().get(3));
    tcaplusSharedataSubscribers.internalInit(descriptor.getExtensions().get(4));
    tcaplusCustomattr2.internalInit(descriptor.getExtensions().get(5));
    documentId.internalInit(descriptor.getExtensions().get(6));
    documentIndex.internalInit(descriptor.getExtensions().get(7));
    tcaplusFieldVersion.internalInit(descriptor.getExtensions().get(8));
    columnSplit.internalInit(descriptor.getExtensions().get(9));
    stringSize.internalInit(descriptor.getExtensions().get(10));
    name.internalInit(descriptor.getExtensions().get(11));
    addNeedSync.internalInit(descriptor.getExtensions().get(12));
    com.google.protobuf.DescriptorProtos.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

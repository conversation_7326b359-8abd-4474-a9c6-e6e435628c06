// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/clanCard/ss_clanCard.proto

package com.yorha.proto;

public final class SsClanCard {
  private SsClanCard() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface QueryClanNameAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanNameAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanNameAsk}
   */
  public static final class QueryClanNameAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanNameAsk)
      QueryClanNameAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanNameAsk.newBuilder() to construct.
    private QueryClanNameAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanNameAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanNameAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanNameAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanNameAsk.class, com.yorha.proto.SsClanCard.QueryClanNameAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanNameAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanNameAsk other = (com.yorha.proto.SsClanCard.QueryClanNameAsk) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanNameAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanNameAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanNameAsk)
        com.yorha.proto.SsClanCard.QueryClanNameAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanNameAsk.class, com.yorha.proto.SsClanCard.QueryClanNameAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanNameAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanNameAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAsk build() {
        com.yorha.proto.SsClanCard.QueryClanNameAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAsk buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanNameAsk result = new com.yorha.proto.SsClanCard.QueryClanNameAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanNameAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanNameAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanNameAsk other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanNameAsk.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanNameAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanNameAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanNameAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanNameAsk)
    private static final com.yorha.proto.SsClanCard.QueryClanNameAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanNameAsk();
    }

    public static com.yorha.proto.SsClanCard.QueryClanNameAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanNameAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanNameAsk>() {
      @java.lang.Override
      public QueryClanNameAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanNameAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanNameAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanNameAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanNameAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryClanNameAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanNameAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <code>optional string clanName = 2;</code>
     * @return Whether the clanName field is set.
     */
    boolean hasClanName();
    /**
     * <code>optional string clanName = 2;</code>
     * @return The clanName.
     */
    java.lang.String getClanName();
    /**
     * <code>optional string clanName = 2;</code>
     * @return The bytes for clanName.
     */
    com.google.protobuf.ByteString
        getClanNameBytes();

    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanNameAns}
   */
  public static final class QueryClanNameAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanNameAns)
      QueryClanNameAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanNameAns.newBuilder() to construct.
    private QueryClanNameAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanNameAns() {
      clanName_ = "";
      clanSimpleName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanNameAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanNameAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              clanName_ = bs;
              break;
            }
            case 26: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000004;
              clanSimpleName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanNameAns.class, com.yorha.proto.SsClanCard.QueryClanNameAns.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int CLANNAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object clanName_;
    /**
     * <code>optional string clanName = 2;</code>
     * @return Whether the clanName field is set.
     */
    @java.lang.Override
    public boolean hasClanName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string clanName = 2;</code>
     * @return The clanName.
     */
    @java.lang.Override
    public java.lang.String getClanName() {
      java.lang.Object ref = clanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clanName = 2;</code>
     * @return The bytes for clanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanNameBytes() {
      java.lang.Object ref = clanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 3;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clanSimpleName = 3;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, clanName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 3, clanSimpleName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, clanName_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(3, clanSimpleName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanNameAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanNameAns other = (com.yorha.proto.SsClanCard.QueryClanNameAns) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasClanName() != other.hasClanName()) return false;
      if (hasClanName()) {
        if (!getClanName()
            .equals(other.getClanName())) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasClanName()) {
        hash = (37 * hash) + CLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanName().hashCode();
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanNameAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanNameAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanNameAns)
        com.yorha.proto.SsClanCard.QueryClanNameAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanNameAns.class, com.yorha.proto.SsClanCard.QueryClanNameAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanNameAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        clanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000004);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanNameAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanNameAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAns build() {
        com.yorha.proto.SsClanCard.QueryClanNameAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanNameAns buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanNameAns result = new com.yorha.proto.SsClanCard.QueryClanNameAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.clanName_ = clanName_;
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.clanSimpleName_ = clanSimpleName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanNameAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanNameAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanNameAns other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanNameAns.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasClanName()) {
          bitField0_ |= 0x00000002;
          clanName_ = other.clanName_;
          onChanged();
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000004;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanNameAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanNameAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object clanName_ = "";
      /**
       * <code>optional string clanName = 2;</code>
       * @return Whether the clanName field is set.
       */
      public boolean hasClanName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string clanName = 2;</code>
       * @return The clanName.
       */
      public java.lang.String getClanName() {
        java.lang.Object ref = clanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clanName = 2;</code>
       * @return The bytes for clanName.
       */
      public com.google.protobuf.ByteString
          getClanNameBytes() {
        java.lang.Object ref = clanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clanName = 2;</code>
       * @param value The clanName to set.
       * @return This builder for chaining.
       */
      public Builder setClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        clanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        clanName_ = getDefaultInstance().getClanName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanName = 2;</code>
       * @param value The bytes for clanName to set.
       * @return This builder for chaining.
       */
      public Builder setClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        clanName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000004);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSimpleName = 3;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000004;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanNameAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanNameAns)
    private static final com.yorha.proto.SsClanCard.QueryClanNameAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanNameAns();
    }

    public static com.yorha.proto.SsClanCard.QueryClanNameAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanNameAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanNameAns>() {
      @java.lang.Override
      public QueryClanNameAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanNameAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanNameAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanNameAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanNameAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryClanSimpleAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanSimpleAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanSimpleAsk}
   */
  public static final class QueryClanSimpleAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanSimpleAsk)
      QueryClanSimpleAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanSimpleAsk.newBuilder() to construct.
    private QueryClanSimpleAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanSimpleAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanSimpleAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanSimpleAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanSimpleAsk.class, com.yorha.proto.SsClanCard.QueryClanSimpleAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanSimpleAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanSimpleAsk other = (com.yorha.proto.SsClanCard.QueryClanSimpleAsk) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanSimpleAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanSimpleAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanSimpleAsk)
        com.yorha.proto.SsClanCard.QueryClanSimpleAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanSimpleAsk.class, com.yorha.proto.SsClanCard.QueryClanSimpleAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanSimpleAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanSimpleAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAsk build() {
        com.yorha.proto.SsClanCard.QueryClanSimpleAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAsk buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanSimpleAsk result = new com.yorha.proto.SsClanCard.QueryClanSimpleAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanSimpleAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanSimpleAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanSimpleAsk other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanSimpleAsk.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanSimpleAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanSimpleAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanSimpleAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanSimpleAsk)
    private static final com.yorha.proto.SsClanCard.QueryClanSimpleAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanSimpleAsk();
    }

    public static com.yorha.proto.SsClanCard.QueryClanSimpleAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanSimpleAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanSimpleAsk>() {
      @java.lang.Override
      public QueryClanSimpleAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanSimpleAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanSimpleAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanSimpleAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanSimpleAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryClanSimpleAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanSimpleAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     * @return The info.
     */
    com.yorha.proto.CommonMsg.ClanSimpleInfo getInfo();
    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     */
    com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanSimpleAns}
   */
  public static final class QueryClanSimpleAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanSimpleAns)
      QueryClanSimpleAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanSimpleAns.newBuilder() to construct.
    private QueryClanSimpleAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanSimpleAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanSimpleAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanSimpleAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.CommonMsg.ClanSimpleInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanSimpleAns.class, com.yorha.proto.SsClanCard.QueryClanSimpleAns.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ClanSimpleInfo info_;
    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfo() {
      return info_ == null ? com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanSimpleAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanSimpleAns other = (com.yorha.proto.SsClanCard.QueryClanSimpleAns) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanSimpleAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanSimpleAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanSimpleAns)
        com.yorha.proto.SsClanCard.QueryClanSimpleAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanSimpleAns.class, com.yorha.proto.SsClanCard.QueryClanSimpleAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanSimpleAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanSimpleAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAns build() {
        com.yorha.proto.SsClanCard.QueryClanSimpleAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanSimpleAns buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanSimpleAns result = new com.yorha.proto.SsClanCard.QueryClanSimpleAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanSimpleAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanSimpleAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanSimpleAns other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanSimpleAns.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanSimpleAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanSimpleAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ClanSimpleInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanSimpleInfo, com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder, com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder> infoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       * @return The info.
       */
      public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public Builder setInfo(com.yorha.proto.CommonMsg.ClanSimpleInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public Builder setInfo(
          com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public Builder mergeInfo(com.yorha.proto.CommonMsg.ClanSimpleInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.CommonMsg.ClanSimpleInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanSimpleInfo info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanSimpleInfo, com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder, com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ClanSimpleInfo, com.yorha.proto.CommonMsg.ClanSimpleInfo.Builder, com.yorha.proto.CommonMsg.ClanSimpleInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanSimpleAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanSimpleAns)
    private static final com.yorha.proto.SsClanCard.QueryClanSimpleAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanSimpleAns();
    }

    public static com.yorha.proto.SsClanCard.QueryClanSimpleAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanSimpleAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanSimpleAns>() {
      @java.lang.Override
      public QueryClanSimpleAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanSimpleAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanSimpleAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanSimpleAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanSimpleAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryClanCardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanCardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    long getClanId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanCardAsk}
   */
  public static final class QueryClanCardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanCardAsk)
      QueryClanCardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanCardAsk.newBuilder() to construct.
    private QueryClanCardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanCardAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanCardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanCardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              clanId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanCardAsk.class, com.yorha.proto.SsClanCard.QueryClanCardAsk.Builder.class);
    }

    private int bitField0_;
    public static final int CLANID_FIELD_NUMBER = 1;
    private long clanId_;
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional int64 clanId = 1;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, clanId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, clanId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanCardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanCardAsk other = (com.yorha.proto.SsClanCard.QueryClanCardAsk) obj;

      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanCardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanCardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanCardAsk)
        com.yorha.proto.SsClanCard.QueryClanCardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanCardAsk.class, com.yorha.proto.SsClanCard.QueryClanCardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanCardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanCardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAsk build() {
        com.yorha.proto.SsClanCard.QueryClanCardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAsk buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanCardAsk result = new com.yorha.proto.SsClanCard.QueryClanCardAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanCardAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanCardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanCardAsk other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanCardAsk.getDefaultInstance()) return this;
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanCardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanCardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long clanId_ ;
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000001;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 clanId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanCardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanCardAsk)
    private static final com.yorha.proto.SsClanCard.QueryClanCardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanCardAsk();
    }

    public static com.yorha.proto.SsClanCard.QueryClanCardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanCardAsk>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanCardAsk>() {
      @java.lang.Override
      public QueryClanCardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanCardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanCardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanCardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanCardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface QueryClanCardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.QueryClanCardAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    boolean hasInfo();
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     * @return The info.
     */
    com.yorha.proto.CommonMsg.ClanCardInfo getInfo();
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     */
    com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.QueryClanCardAns}
   */
  public static final class QueryClanCardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.QueryClanCardAns)
      QueryClanCardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use QueryClanCardAns.newBuilder() to construct.
    private QueryClanCardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private QueryClanCardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new QueryClanCardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private QueryClanCardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ClanCardInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = info_.toBuilder();
              }
              info_ = input.readMessage(com.yorha.proto.CommonMsg.ClanCardInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(info_);
                info_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.QueryClanCardAns.class, com.yorha.proto.SsClanCard.QueryClanCardAns.Builder.class);
    }

    private int bitField0_;
    public static final int INFO_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ClanCardInfo info_;
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     * @return Whether the info field is set.
     */
    @java.lang.Override
    public boolean hasInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     * @return The info.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanCardInfo getInfo() {
      return info_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : info_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getInfoOrBuilder() {
      return info_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : info_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.QueryClanCardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.QueryClanCardAns other = (com.yorha.proto.SsClanCard.QueryClanCardAns) obj;

      if (hasInfo() != other.hasInfo()) return false;
      if (hasInfo()) {
        if (!getInfo()
            .equals(other.getInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasInfo()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + getInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.QueryClanCardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.QueryClanCardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.QueryClanCardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.QueryClanCardAns)
        com.yorha.proto.SsClanCard.QueryClanCardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.QueryClanCardAns.class, com.yorha.proto.SsClanCard.QueryClanCardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.QueryClanCardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (infoBuilder_ == null) {
          info_ = null;
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_QueryClanCardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.QueryClanCardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAns build() {
        com.yorha.proto.SsClanCard.QueryClanCardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.QueryClanCardAns buildPartial() {
        com.yorha.proto.SsClanCard.QueryClanCardAns result = new com.yorha.proto.SsClanCard.QueryClanCardAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (infoBuilder_ == null) {
            result.info_ = info_;
          } else {
            result.info_ = infoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.QueryClanCardAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.QueryClanCardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.QueryClanCardAns other) {
        if (other == com.yorha.proto.SsClanCard.QueryClanCardAns.getDefaultInstance()) return this;
        if (other.hasInfo()) {
          mergeInfo(other.getInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.QueryClanCardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.QueryClanCardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ClanCardInfo info_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder> infoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       * @return Whether the info field is set.
       */
      public boolean hasInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       * @return The info.
       */
      public com.yorha.proto.CommonMsg.ClanCardInfo getInfo() {
        if (infoBuilder_ == null) {
          return info_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : info_;
        } else {
          return infoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public Builder setInfo(com.yorha.proto.CommonMsg.ClanCardInfo value) {
        if (infoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          info_ = value;
          onChanged();
        } else {
          infoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public Builder setInfo(
          com.yorha.proto.CommonMsg.ClanCardInfo.Builder builderForValue) {
        if (infoBuilder_ == null) {
          info_ = builderForValue.build();
          onChanged();
        } else {
          infoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public Builder mergeInfo(com.yorha.proto.CommonMsg.ClanCardInfo value) {
        if (infoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              info_ != null &&
              info_ != com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance()) {
            info_ =
              com.yorha.proto.CommonMsg.ClanCardInfo.newBuilder(info_).mergeFrom(value).buildPartial();
          } else {
            info_ = value;
          }
          onChanged();
        } else {
          infoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public Builder clearInfo() {
        if (infoBuilder_ == null) {
          info_ = null;
          onChanged();
        } else {
          infoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanCardInfo.Builder getInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getInfoOrBuilder() {
        if (infoBuilder_ != null) {
          return infoBuilder_.getMessageOrBuilder();
        } else {
          return info_ == null ?
              com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : info_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo info = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder> 
          getInfoFieldBuilder() {
        if (infoBuilder_ == null) {
          infoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder>(
                  getInfo(),
                  getParentForChildren(),
                  isClean());
          info_ = null;
        }
        return infoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.QueryClanCardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.QueryClanCardAns)
    private static final com.yorha.proto.SsClanCard.QueryClanCardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.QueryClanCardAns();
    }

    public static com.yorha.proto.SsClanCard.QueryClanCardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<QueryClanCardAns>
        PARSER = new com.google.protobuf.AbstractParser<QueryClanCardAns>() {
      @java.lang.Override
      public QueryClanCardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new QueryClanCardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<QueryClanCardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<QueryClanCardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.QueryClanCardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanNameAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanNameAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    java.util.List<java.lang.Long> getClansList();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    int getClansCount();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    long getClans(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanNameAsk}
   */
  public static final class BatchQueryClanNameAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanNameAsk)
      BatchQueryClanNameAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanNameAsk.newBuilder() to construct.
    private BatchQueryClanNameAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanNameAsk() {
      clans_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanNameAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanNameAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              clans_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                clans_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.Builder.class);
    }

    public static final int CLANS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList clans_;
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getClansList() {
      return clans_;
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    public int getClansCount() {
      return clans_.size();
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    public long getClans(int index) {
      return clans_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < clans_.size(); i++) {
        output.writeInt64(1, clans_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < clans_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(clans_.getLong(i));
        }
        size += dataSize;
        size += 1 * getClansList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanNameAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanNameAsk other = (com.yorha.proto.SsClanCard.BatchQueryClanNameAsk) obj;

      if (!getClansList()
          .equals(other.getClansList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getClansCount() > 0) {
        hash = (37 * hash) + CLANS_FIELD_NUMBER;
        hash = (53 * hash) + getClansList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanNameAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanNameAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanNameAsk)
        com.yorha.proto.SsClanCard.BatchQueryClanNameAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAsk build() {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAsk buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAsk result = new com.yorha.proto.SsClanCard.BatchQueryClanNameAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.clans_ = clans_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanNameAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanNameAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanNameAsk other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanNameAsk.getDefaultInstance()) return this;
        if (!other.clans_.isEmpty()) {
          if (clans_.isEmpty()) {
            clans_ = other.clans_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureClansIsMutable();
            clans_.addAll(other.clans_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanNameAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList clans_ = emptyLongList();
      private void ensureClansIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          clans_ = mutableCopy(clans_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return A list containing the clans.
       */
      public java.util.List<java.lang.Long>
          getClansList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(clans_) : clans_;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return The count of clans.
       */
      public int getClansCount() {
        return clans_.size();
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index of the element to return.
       * @return The clans at the given index.
       */
      public long getClans(int index) {
        return clans_.getLong(index);
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index to set the value at.
       * @param value The clans to set.
       * @return This builder for chaining.
       */
      public Builder setClans(
          int index, long value) {
        ensureClansIsMutable();
        clans_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param value The clans to add.
       * @return This builder for chaining.
       */
      public Builder addClans(long value) {
        ensureClansIsMutable();
        clans_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param values The clans to add.
       * @return This builder for chaining.
       */
      public Builder addAllClans(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureClansIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clans_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClans() {
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanNameAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanNameAsk)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanNameAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanNameAsk();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanNameAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanNameAsk>() {
      @java.lang.Override
      public BatchQueryClanNameAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanNameAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanNameAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanNameAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanNameAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface ClanAllNameOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanAllName)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional string clanName = 1;</code>
     * @return Whether the clanName field is set.
     */
    boolean hasClanName();
    /**
     * <code>optional string clanName = 1;</code>
     * @return The clanName.
     */
    java.lang.String getClanName();
    /**
     * <code>optional string clanName = 1;</code>
     * @return The bytes for clanName.
     */
    com.google.protobuf.ByteString
        getClanNameBytes();

    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return Whether the clanSimpleName field is set.
     */
    boolean hasClanSimpleName();
    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return The clanSimpleName.
     */
    java.lang.String getClanSimpleName();
    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return The bytes for clanSimpleName.
     */
    com.google.protobuf.ByteString
        getClanSimpleNameBytes();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanAllName}
   */
  public static final class ClanAllName extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanAllName)
      ClanAllNameOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanAllName.newBuilder() to construct.
    private ClanAllName(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanAllName() {
      clanName_ = "";
      clanSimpleName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanAllName();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanAllName(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000001;
              clanName_ = bs;
              break;
            }
            case 18: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000002;
              clanSimpleName_ = bs;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_ClanAllName_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_ClanAllName_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.ClanAllName.class, com.yorha.proto.SsClanCard.ClanAllName.Builder.class);
    }

    private int bitField0_;
    public static final int CLANNAME_FIELD_NUMBER = 1;
    private volatile java.lang.Object clanName_;
    /**
     * <code>optional string clanName = 1;</code>
     * @return Whether the clanName field is set.
     */
    @java.lang.Override
    public boolean hasClanName() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional string clanName = 1;</code>
     * @return The clanName.
     */
    @java.lang.Override
    public java.lang.String getClanName() {
      java.lang.Object ref = clanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clanName = 1;</code>
     * @return The bytes for clanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanNameBytes() {
      java.lang.Object ref = clanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int CLANSIMPLENAME_FIELD_NUMBER = 2;
    private volatile java.lang.Object clanSimpleName_;
    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return Whether the clanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasClanSimpleName() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return The clanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getClanSimpleName() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          clanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <code>optional string clanSimpleName = 2;</code>
     * @return The bytes for clanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getClanSimpleNameBytes() {
      java.lang.Object ref = clanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        clanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 1, clanName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 2, clanSimpleName_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(1, clanName_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(2, clanSimpleName_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.ClanAllName)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.ClanAllName other = (com.yorha.proto.SsClanCard.ClanAllName) obj;

      if (hasClanName() != other.hasClanName()) return false;
      if (hasClanName()) {
        if (!getClanName()
            .equals(other.getClanName())) return false;
      }
      if (hasClanSimpleName() != other.hasClanSimpleName()) return false;
      if (hasClanSimpleName()) {
        if (!getClanSimpleName()
            .equals(other.getClanSimpleName())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasClanName()) {
        hash = (37 * hash) + CLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanName().hashCode();
      }
      if (hasClanSimpleName()) {
        hash = (37 * hash) + CLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getClanSimpleName().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.ClanAllName parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.ClanAllName prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanAllName}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanAllName)
        com.yorha.proto.SsClanCard.ClanAllNameOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_ClanAllName_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_ClanAllName_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.ClanAllName.class, com.yorha.proto.SsClanCard.ClanAllName.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.ClanAllName.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000001);
        clanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_ClanAllName_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.ClanAllName getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.ClanAllName.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.ClanAllName build() {
        com.yorha.proto.SsClanCard.ClanAllName result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.ClanAllName buildPartial() {
        com.yorha.proto.SsClanCard.ClanAllName result = new com.yorha.proto.SsClanCard.ClanAllName(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          to_bitField0_ |= 0x00000001;
        }
        result.clanName_ = clanName_;
        if (((from_bitField0_ & 0x00000002) != 0)) {
          to_bitField0_ |= 0x00000002;
        }
        result.clanSimpleName_ = clanSimpleName_;
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.ClanAllName) {
          return mergeFrom((com.yorha.proto.SsClanCard.ClanAllName)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.ClanAllName other) {
        if (other == com.yorha.proto.SsClanCard.ClanAllName.getDefaultInstance()) return this;
        if (other.hasClanName()) {
          bitField0_ |= 0x00000001;
          clanName_ = other.clanName_;
          onChanged();
        }
        if (other.hasClanSimpleName()) {
          bitField0_ |= 0x00000002;
          clanSimpleName_ = other.clanSimpleName_;
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.ClanAllName parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.ClanAllName) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private java.lang.Object clanName_ = "";
      /**
       * <code>optional string clanName = 1;</code>
       * @return Whether the clanName field is set.
       */
      public boolean hasClanName() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional string clanName = 1;</code>
       * @return The clanName.
       */
      public java.lang.String getClanName() {
        java.lang.Object ref = clanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clanName = 1;</code>
       * @return The bytes for clanName.
       */
      public com.google.protobuf.ByteString
          getClanNameBytes() {
        java.lang.Object ref = clanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clanName = 1;</code>
       * @param value The clanName to set.
       * @return This builder for chaining.
       */
      public Builder setClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        clanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanName = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanName() {
        bitField0_ = (bitField0_ & ~0x00000001);
        clanName_ = getDefaultInstance().getClanName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanName = 1;</code>
       * @param value The bytes for clanName to set.
       * @return This builder for chaining.
       */
      public Builder setClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000001;
        clanName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object clanSimpleName_ = "";
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @return Whether the clanSimpleName field is set.
       */
      public boolean hasClanSimpleName() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @return The clanSimpleName.
       */
      public java.lang.String getClanSimpleName() {
        java.lang.Object ref = clanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            clanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @return The bytes for clanSimpleName.
       */
      public com.google.protobuf.ByteString
          getClanSimpleNameBytes() {
        java.lang.Object ref = clanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          clanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @param value The clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000002);
        clanSimpleName_ = getDefaultInstance().getClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <code>optional string clanSimpleName = 2;</code>
       * @param value The bytes for clanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000002;
        clanSimpleName_ = value;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanAllName)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanAllName)
    private static final com.yorha.proto.SsClanCard.ClanAllName DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.ClanAllName();
    }

    public static com.yorha.proto.SsClanCard.ClanAllName getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanAllName>
        PARSER = new com.google.protobuf.AbstractParser<ClanAllName>() {
      @java.lang.Override
      public ClanAllName parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanAllName(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanAllName> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanAllName> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.ClanAllName getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanNameAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanNameAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    boolean containsInfo(
        long key);
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
    getInfo();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
    getInfoMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */

    com.yorha.proto.SsClanCard.ClanAllName getInfoOrDefault(
        long key,
        com.yorha.proto.SsClanCard.ClanAllName defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */

    com.yorha.proto.SsClanCard.ClanAllName getInfoOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanNameAns}
   */
  public static final class BatchQueryClanNameAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanNameAns)
      BatchQueryClanNameAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanNameAns.newBuilder() to construct.
    private BatchQueryClanNameAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanNameAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanNameAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanNameAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = com.google.protobuf.MapField.newMapField(
                    InfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
              info__ = input.readMessage(
                  InfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              info_.getMutableMap().put(
                  info__.getKey(), info__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanNameAns.class, com.yorha.proto.SsClanCard.BatchQueryClanNameAns.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private static final class InfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>newDefaultInstance(
                  com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.SsClanCard.ClanAllName.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> info_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
    internalGetInfo() {
      if (info_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            InfoDefaultEntryHolder.defaultEntry);
      }
      return info_;
    }

    public int getInfoCount() {
      return internalGetInfo().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */

    @java.lang.Override
    public boolean containsInfo(
        long key) {
      
      return internalGetInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> getInfo() {
      return getInfoMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> getInfoMap() {
      return internalGetInfo().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.SsClanCard.ClanAllName getInfoOrDefault(
        long key,
        com.yorha.proto.SsClanCard.ClanAllName defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> map =
          internalGetInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.SsClanCard.ClanAllName getInfoOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> map =
          internalGetInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetInfo(),
          InfoDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> entry
           : internalGetInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
        info__ = InfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, info__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanNameAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanNameAns other = (com.yorha.proto.SsClanCard.BatchQueryClanNameAns) obj;

      if (!internalGetInfo().equals(
          other.internalGetInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetInfo().getMap().isEmpty()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanNameAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanNameAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanNameAns)
        com.yorha.proto.SsClanCard.BatchQueryClanNameAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanNameAns.class, com.yorha.proto.SsClanCard.BatchQueryClanNameAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanNameAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanNameAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAns build() {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanNameAns buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAns result = new com.yorha.proto.SsClanCard.BatchQueryClanNameAns(this);
        int from_bitField0_ = bitField0_;
        result.info_ = internalGetInfo();
        result.info_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanNameAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanNameAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanNameAns other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanNameAns.getDefaultInstance()) return this;
        internalGetMutableInfo().mergeFrom(
            other.internalGetInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanNameAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanNameAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> info_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
      internalGetInfo() {
        if (info_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        return info_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
      internalGetMutableInfo() {
        onChanged();;
        if (info_ == null) {
          info_ = com.google.protobuf.MapField.newMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        if (!info_.isMutable()) {
          info_ = info_.copy();
        }
        return info_;
      }

      public int getInfoCount() {
        return internalGetInfo().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */

      @java.lang.Override
      public boolean containsInfo(
          long key) {
        
        return internalGetInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> getInfo() {
        return getInfoMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> getInfoMap() {
        return internalGetInfo().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.SsClanCard.ClanAllName getInfoOrDefault(
          long key,
          com.yorha.proto.SsClanCard.ClanAllName defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> map =
            internalGetInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.SsClanCard.ClanAllName getInfoOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> map =
            internalGetInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearInfo() {
        internalGetMutableInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */

      public Builder removeInfo(
          long key) {
        
        internalGetMutableInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName>
      getMutableInfo() {
        return internalGetMutableInfo().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */
      public Builder putInfo(
          long key,
          com.yorha.proto.SsClanCard.ClanAllName value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanAllName&gt; info = 1;</code>
       */

      public Builder putAllInfo(
          java.util.Map<java.lang.Long, com.yorha.proto.SsClanCard.ClanAllName> values) {
        internalGetMutableInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanNameAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanNameAns)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanNameAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanNameAns();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanNameAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanNameAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanNameAns>() {
      @java.lang.Override
      public BatchQueryClanNameAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanNameAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanNameAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanNameAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanNameAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanSimpleAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanSimpleAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    java.util.List<java.lang.Long> getClansList();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    int getClansCount();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    long getClans(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanSimpleAsk}
   */
  public static final class BatchQueryClanSimpleAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanSimpleAsk)
      BatchQueryClanSimpleAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanSimpleAsk.newBuilder() to construct.
    private BatchQueryClanSimpleAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanSimpleAsk() {
      clans_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanSimpleAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanSimpleAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              clans_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                clans_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.Builder.class);
    }

    public static final int CLANS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList clans_;
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getClansList() {
      return clans_;
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    public int getClansCount() {
      return clans_.size();
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    public long getClans(int index) {
      return clans_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < clans_.size(); i++) {
        output.writeInt64(1, clans_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < clans_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(clans_.getLong(i));
        }
        size += dataSize;
        size += 1 * getClansList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk other = (com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk) obj;

      if (!getClansList()
          .equals(other.getClansList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getClansCount() > 0) {
        hash = (37 * hash) + CLANS_FIELD_NUMBER;
        hash = (53 * hash) + getClansList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanSimpleAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanSimpleAsk)
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk build() {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk result = new com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.clans_ = clans_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk.getDefaultInstance()) return this;
        if (!other.clans_.isEmpty()) {
          if (clans_.isEmpty()) {
            clans_ = other.clans_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureClansIsMutable();
            clans_.addAll(other.clans_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList clans_ = emptyLongList();
      private void ensureClansIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          clans_ = mutableCopy(clans_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return A list containing the clans.
       */
      public java.util.List<java.lang.Long>
          getClansList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(clans_) : clans_;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return The count of clans.
       */
      public int getClansCount() {
        return clans_.size();
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index of the element to return.
       * @return The clans at the given index.
       */
      public long getClans(int index) {
        return clans_.getLong(index);
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index to set the value at.
       * @param value The clans to set.
       * @return This builder for chaining.
       */
      public Builder setClans(
          int index, long value) {
        ensureClansIsMutable();
        clans_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param value The clans to add.
       * @return This builder for chaining.
       */
      public Builder addClans(long value) {
        ensureClansIsMutable();
        clans_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param values The clans to add.
       * @return This builder for chaining.
       */
      public Builder addAllClans(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureClansIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clans_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClans() {
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanSimpleAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanSimpleAsk)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanSimpleAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanSimpleAsk>() {
      @java.lang.Override
      public BatchQueryClanSimpleAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanSimpleAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanSimpleAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanSimpleAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanSimpleAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanSimpleAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    boolean containsInfo(
        long key);
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
    getInfo();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
    getInfoMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */

    com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrDefault(
        long key,
        com.yorha.proto.CommonMsg.ClanSimpleInfo defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */

    com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanSimpleAns}
   */
  public static final class BatchQueryClanSimpleAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanSimpleAns)
      BatchQueryClanSimpleAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanSimpleAns.newBuilder() to construct.
    private BatchQueryClanSimpleAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanSimpleAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanSimpleAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanSimpleAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = com.google.protobuf.MapField.newMapField(
                    InfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
              info__ = input.readMessage(
                  InfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              info_.getMutableMap().put(
                  info__.getKey(), info__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.class, com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private static final class InfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>newDefaultInstance(
                  com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.CommonMsg.ClanSimpleInfo.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> info_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
    internalGetInfo() {
      if (info_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            InfoDefaultEntryHolder.defaultEntry);
      }
      return info_;
    }

    public int getInfoCount() {
      return internalGetInfo().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */

    @java.lang.Override
    public boolean containsInfo(
        long key) {
      
      return internalGetInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> getInfo() {
      return getInfoMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> getInfoMap() {
      return internalGetInfo().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrDefault(
        long key,
        com.yorha.proto.CommonMsg.ClanSimpleInfo defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> map =
          internalGetInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> map =
          internalGetInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetInfo(),
          InfoDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> entry
           : internalGetInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
        info__ = InfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, info__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns other = (com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns) obj;

      if (!internalGetInfo().equals(
          other.internalGetInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetInfo().getMap().isEmpty()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanSimpleAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanSimpleAns)
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.class, com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns build() {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns result = new com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns(this);
        int from_bitField0_ = bitField0_;
        result.info_ = internalGetInfo();
        result.info_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns.getDefaultInstance()) return this;
        internalGetMutableInfo().mergeFrom(
            other.internalGetInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> info_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
      internalGetInfo() {
        if (info_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        return info_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
      internalGetMutableInfo() {
        onChanged();;
        if (info_ == null) {
          info_ = com.google.protobuf.MapField.newMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        if (!info_.isMutable()) {
          info_ = info_.copy();
        }
        return info_;
      }

      public int getInfoCount() {
        return internalGetInfo().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */

      @java.lang.Override
      public boolean containsInfo(
          long key) {
        
        return internalGetInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> getInfo() {
        return getInfoMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> getInfoMap() {
        return internalGetInfo().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrDefault(
          long key,
          com.yorha.proto.CommonMsg.ClanSimpleInfo defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> map =
            internalGetInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.ClanSimpleInfo getInfoOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> map =
            internalGetInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearInfo() {
        internalGetMutableInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */

      public Builder removeInfo(
          long key) {
        
        internalGetMutableInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo>
      getMutableInfo() {
        return internalGetMutableInfo().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */
      public Builder putInfo(
          long key,
          com.yorha.proto.CommonMsg.ClanSimpleInfo value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanSimpleInfo&gt; info = 1;</code>
       */

      public Builder putAllInfo(
          java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanSimpleInfo> values) {
        internalGetMutableInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanSimpleAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanSimpleAns)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanSimpleAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanSimpleAns>() {
      @java.lang.Override
      public BatchQueryClanSimpleAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanSimpleAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanSimpleAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanSimpleAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanSimpleAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanCardAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanCardAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    java.util.List<java.lang.Long> getClansList();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    int getClansCount();
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    long getClans(int index);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanCardAsk}
   */
  public static final class BatchQueryClanCardAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanCardAsk)
      BatchQueryClanCardAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanCardAsk.newBuilder() to construct.
    private BatchQueryClanCardAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanCardAsk() {
      clans_ = emptyLongList();
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanCardAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanCardAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              clans_.addLong(input.readInt64());
              break;
            }
            case 10: {
              int length = input.readRawVarint32();
              int limit = input.pushLimit(length);
              if (!((mutable_bitField0_ & 0x00000001) != 0) && input.getBytesUntilLimit() > 0) {
                clans_ = newLongList();
                mutable_bitField0_ |= 0x00000001;
              }
              while (input.getBytesUntilLimit() > 0) {
                clans_.addLong(input.readInt64());
              }
              input.popLimit(limit);
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        if (((mutable_bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable(); // C
        }
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.Builder.class);
    }

    public static final int CLANS_FIELD_NUMBER = 1;
    private com.google.protobuf.Internal.LongList clans_;
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return A list containing the clans.
     */
    @java.lang.Override
    public java.util.List<java.lang.Long>
        getClansList() {
      return clans_;
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @return The count of clans.
     */
    public int getClansCount() {
      return clans_.size();
    }
    /**
     * <code>repeated int64 clans = 1;</code>
     * @param index The index of the element to return.
     * @return The clans at the given index.
     */
    public long getClans(int index) {
      return clans_.getLong(index);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      for (int i = 0; i < clans_.size(); i++) {
        output.writeInt64(1, clans_.getLong(i));
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      {
        int dataSize = 0;
        for (int i = 0; i < clans_.size(); i++) {
          dataSize += com.google.protobuf.CodedOutputStream
            .computeInt64SizeNoTag(clans_.getLong(i));
        }
        size += dataSize;
        size += 1 * getClansList().size();
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanCardAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanCardAsk other = (com.yorha.proto.SsClanCard.BatchQueryClanCardAsk) obj;

      if (!getClansList()
          .equals(other.getClansList())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (getClansCount() > 0) {
        hash = (37 * hash) + CLANS_FIELD_NUMBER;
        hash = (53 * hash) + getClansList().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanCardAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanCardAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanCardAsk)
        com.yorha.proto.SsClanCard.BatchQueryClanCardAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.class, com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAsk build() {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAsk buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAsk result = new com.yorha.proto.SsClanCard.BatchQueryClanCardAsk(this);
        int from_bitField0_ = bitField0_;
        if (((bitField0_ & 0x00000001) != 0)) {
          clans_.makeImmutable();
          bitField0_ = (bitField0_ & ~0x00000001);
        }
        result.clans_ = clans_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanCardAsk) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanCardAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanCardAsk other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanCardAsk.getDefaultInstance()) return this;
        if (!other.clans_.isEmpty()) {
          if (clans_.isEmpty()) {
            clans_ = other.clans_;
            bitField0_ = (bitField0_ & ~0x00000001);
          } else {
            ensureClansIsMutable();
            clans_.addAll(other.clans_);
          }
          onChanged();
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanCardAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.Internal.LongList clans_ = emptyLongList();
      private void ensureClansIsMutable() {
        if (!((bitField0_ & 0x00000001) != 0)) {
          clans_ = mutableCopy(clans_);
          bitField0_ |= 0x00000001;
         }
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return A list containing the clans.
       */
      public java.util.List<java.lang.Long>
          getClansList() {
        return ((bitField0_ & 0x00000001) != 0) ?
                 java.util.Collections.unmodifiableList(clans_) : clans_;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return The count of clans.
       */
      public int getClansCount() {
        return clans_.size();
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index of the element to return.
       * @return The clans at the given index.
       */
      public long getClans(int index) {
        return clans_.getLong(index);
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param index The index to set the value at.
       * @param value The clans to set.
       * @return This builder for chaining.
       */
      public Builder setClans(
          int index, long value) {
        ensureClansIsMutable();
        clans_.setLong(index, value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param value The clans to add.
       * @return This builder for chaining.
       */
      public Builder addClans(long value) {
        ensureClansIsMutable();
        clans_.addLong(value);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @param values The clans to add.
       * @return This builder for chaining.
       */
      public Builder addAllClans(
          java.lang.Iterable<? extends java.lang.Long> values) {
        ensureClansIsMutable();
        com.google.protobuf.AbstractMessageLite.Builder.addAll(
            values, clans_);
        onChanged();
        return this;
      }
      /**
       * <code>repeated int64 clans = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearClans() {
        clans_ = emptyLongList();
        bitField0_ = (bitField0_ & ~0x00000001);
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanCardAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanCardAsk)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanCardAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanCardAsk();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanCardAsk>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanCardAsk>() {
      @java.lang.Override
      public BatchQueryClanCardAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanCardAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanCardAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanCardAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanCardAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface BatchQueryClanCardAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.BatchQueryClanCardAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    int getInfoCount();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    boolean containsInfo(
        long key);
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Deprecated
    java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
    getInfo();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
    getInfoMap();
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */

    com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrDefault(
        long key,
        com.yorha.proto.CommonMsg.ClanCardInfo defaultValue);
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */

    com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrThrow(
        long key);
  }
  /**
   * Protobuf type {@code com.yorha.proto.BatchQueryClanCardAns}
   */
  public static final class BatchQueryClanCardAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.BatchQueryClanCardAns)
      BatchQueryClanCardAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use BatchQueryClanCardAns.newBuilder() to construct.
    private BatchQueryClanCardAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private BatchQueryClanCardAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new BatchQueryClanCardAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private BatchQueryClanCardAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              if (!((mutable_bitField0_ & 0x00000001) != 0)) {
                info_ = com.google.protobuf.MapField.newMapField(
                    InfoDefaultEntryHolder.defaultEntry);
                mutable_bitField0_ |= 0x00000001;
              }
              com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
              info__ = input.readMessage(
                  InfoDefaultEntryHolder.defaultEntry.getParserForType(), extensionRegistry);
              info_.getMutableMap().put(
                  info__.getKey(), info__.getValue());
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor;
    }

    @SuppressWarnings({"rawtypes"})
    @java.lang.Override
    protected com.google.protobuf.MapField internalGetMapField(
        int number) {
      switch (number) {
        case 1:
          return internalGetInfo();
        default:
          throw new RuntimeException(
              "Invalid map field number: " + number);
      }
    }
    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.BatchQueryClanCardAns.class, com.yorha.proto.SsClanCard.BatchQueryClanCardAns.Builder.class);
    }

    public static final int INFO_FIELD_NUMBER = 1;
    private static final class InfoDefaultEntryHolder {
      static final com.google.protobuf.MapEntry<
          java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> defaultEntry =
              com.google.protobuf.MapEntry
              .<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>newDefaultInstance(
                  com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_descriptor, 
                  com.google.protobuf.WireFormat.FieldType.INT64,
                  0L,
                  com.google.protobuf.WireFormat.FieldType.MESSAGE,
                  com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance());
    }
    private com.google.protobuf.MapField<
        java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> info_;
    private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
    internalGetInfo() {
      if (info_ == null) {
        return com.google.protobuf.MapField.emptyMapField(
            InfoDefaultEntryHolder.defaultEntry);
      }
      return info_;
    }

    public int getInfoCount() {
      return internalGetInfo().getMap().size();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */

    @java.lang.Override
    public boolean containsInfo(
        long key) {
      
      return internalGetInfo().getMap().containsKey(key);
    }
    /**
     * Use {@link #getInfoMap()} instead.
     */
    @java.lang.Override
    @java.lang.Deprecated
    public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> getInfo() {
      return getInfoMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> getInfoMap() {
      return internalGetInfo().getMap();
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrDefault(
        long key,
        com.yorha.proto.CommonMsg.ClanCardInfo defaultValue) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> map =
          internalGetInfo().getMap();
      return map.containsKey(key) ? map.get(key) : defaultValue;
    }
    /**
     * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
     */
    @java.lang.Override

    public com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrThrow(
        long key) {
      
      java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> map =
          internalGetInfo().getMap();
      if (!map.containsKey(key)) {
        throw new java.lang.IllegalArgumentException();
      }
      return map.get(key);
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      com.google.protobuf.GeneratedMessageV3
        .serializeLongMapTo(
          output,
          internalGetInfo(),
          InfoDefaultEntryHolder.defaultEntry,
          1);
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      for (java.util.Map.Entry<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> entry
           : internalGetInfo().getMap().entrySet()) {
        com.google.protobuf.MapEntry<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
        info__ = InfoDefaultEntryHolder.defaultEntry.newBuilderForType()
            .setKey(entry.getKey())
            .setValue(entry.getValue())
            .build();
        size += com.google.protobuf.CodedOutputStream
            .computeMessageSize(1, info__);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.BatchQueryClanCardAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.BatchQueryClanCardAns other = (com.yorha.proto.SsClanCard.BatchQueryClanCardAns) obj;

      if (!internalGetInfo().equals(
          other.internalGetInfo())) return false;
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (!internalGetInfo().getMap().isEmpty()) {
        hash = (37 * hash) + INFO_FIELD_NUMBER;
        hash = (53 * hash) + internalGetInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.BatchQueryClanCardAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.BatchQueryClanCardAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.BatchQueryClanCardAns)
        com.yorha.proto.SsClanCard.BatchQueryClanCardAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor;
      }

      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @SuppressWarnings({"rawtypes"})
      protected com.google.protobuf.MapField internalGetMutableMapField(
          int number) {
        switch (number) {
          case 1:
            return internalGetMutableInfo();
          default:
            throw new RuntimeException(
                "Invalid map field number: " + number);
        }
      }
      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.BatchQueryClanCardAns.class, com.yorha.proto.SsClanCard.BatchQueryClanCardAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.BatchQueryClanCardAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        internalGetMutableInfo().clear();
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAns getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.BatchQueryClanCardAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAns build() {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.BatchQueryClanCardAns buildPartial() {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAns result = new com.yorha.proto.SsClanCard.BatchQueryClanCardAns(this);
        int from_bitField0_ = bitField0_;
        result.info_ = internalGetInfo();
        result.info_.makeImmutable();
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.BatchQueryClanCardAns) {
          return mergeFrom((com.yorha.proto.SsClanCard.BatchQueryClanCardAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.BatchQueryClanCardAns other) {
        if (other == com.yorha.proto.SsClanCard.BatchQueryClanCardAns.getDefaultInstance()) return this;
        internalGetMutableInfo().mergeFrom(
            other.internalGetInfo());
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.BatchQueryClanCardAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.BatchQueryClanCardAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.google.protobuf.MapField<
          java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> info_;
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
      internalGetInfo() {
        if (info_ == null) {
          return com.google.protobuf.MapField.emptyMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        return info_;
      }
      private com.google.protobuf.MapField<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
      internalGetMutableInfo() {
        onChanged();;
        if (info_ == null) {
          info_ = com.google.protobuf.MapField.newMapField(
              InfoDefaultEntryHolder.defaultEntry);
        }
        if (!info_.isMutable()) {
          info_ = info_.copy();
        }
        return info_;
      }

      public int getInfoCount() {
        return internalGetInfo().getMap().size();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */

      @java.lang.Override
      public boolean containsInfo(
          long key) {
        
        return internalGetInfo().getMap().containsKey(key);
      }
      /**
       * Use {@link #getInfoMap()} instead.
       */
      @java.lang.Override
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> getInfo() {
        return getInfoMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> getInfoMap() {
        return internalGetInfo().getMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrDefault(
          long key,
          com.yorha.proto.CommonMsg.ClanCardInfo defaultValue) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> map =
            internalGetInfo().getMap();
        return map.containsKey(key) ? map.get(key) : defaultValue;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */
      @java.lang.Override

      public com.yorha.proto.CommonMsg.ClanCardInfo getInfoOrThrow(
          long key) {
        
        java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> map =
            internalGetInfo().getMap();
        if (!map.containsKey(key)) {
          throw new java.lang.IllegalArgumentException();
        }
        return map.get(key);
      }

      public Builder clearInfo() {
        internalGetMutableInfo().getMutableMap()
            .clear();
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */

      public Builder removeInfo(
          long key) {
        
        internalGetMutableInfo().getMutableMap()
            .remove(key);
        return this;
      }
      /**
       * Use alternate mutation accessors instead.
       */
      @java.lang.Deprecated
      public java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo>
      getMutableInfo() {
        return internalGetMutableInfo().getMutableMap();
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */
      public Builder putInfo(
          long key,
          com.yorha.proto.CommonMsg.ClanCardInfo value) {
        
        if (value == null) { throw new java.lang.NullPointerException(); }
        internalGetMutableInfo().getMutableMap()
            .put(key, value);
        return this;
      }
      /**
       * <code>map&lt;int64, .com.yorha.proto.ClanCardInfo&gt; info = 1;</code>
       */

      public Builder putAllInfo(
          java.util.Map<java.lang.Long, com.yorha.proto.CommonMsg.ClanCardInfo> values) {
        internalGetMutableInfo().getMutableMap()
            .putAll(values);
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.BatchQueryClanCardAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.BatchQueryClanCardAns)
    private static final com.yorha.proto.SsClanCard.BatchQueryClanCardAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.BatchQueryClanCardAns();
    }

    public static com.yorha.proto.SsClanCard.BatchQueryClanCardAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<BatchQueryClanCardAns>
        PARSER = new com.google.protobuf.AbstractParser<BatchQueryClanCardAns>() {
      @java.lang.Override
      public BatchQueryClanCardAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new BatchQueryClanCardAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<BatchQueryClanCardAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<BatchQueryClanCardAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.BatchQueryClanCardAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface UpdateClanCardCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.UpdateClanCardCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    boolean hasCardInfo();
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     * @return The cardInfo.
     */
    com.yorha.proto.CommonMsg.ClanCardInfo getCardInfo();
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     */
    com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getCardInfoOrBuilder();
  }
  /**
   * Protobuf type {@code com.yorha.proto.UpdateClanCardCmd}
   */
  public static final class UpdateClanCardCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.UpdateClanCardCmd)
      UpdateClanCardCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use UpdateClanCardCmd.newBuilder() to construct.
    private UpdateClanCardCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private UpdateClanCardCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new UpdateClanCardCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private UpdateClanCardCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.CommonMsg.ClanCardInfo.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = cardInfo_.toBuilder();
              }
              cardInfo_ = input.readMessage(com.yorha.proto.CommonMsg.ClanCardInfo.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(cardInfo_);
                cardInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_UpdateClanCardCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsClanCard.UpdateClanCardCmd.class, com.yorha.proto.SsClanCard.UpdateClanCardCmd.Builder.class);
    }

    private int bitField0_;
    public static final int CARDINFO_FIELD_NUMBER = 1;
    private com.yorha.proto.CommonMsg.ClanCardInfo cardInfo_;
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     * @return Whether the cardInfo field is set.
     */
    @java.lang.Override
    public boolean hasCardInfo() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     * @return The cardInfo.
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanCardInfo getCardInfo() {
      return cardInfo_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : cardInfo_;
    }
    /**
     * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getCardInfoOrBuilder() {
      return cardInfo_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : cardInfo_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getCardInfo());
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getCardInfo());
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsClanCard.UpdateClanCardCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsClanCard.UpdateClanCardCmd other = (com.yorha.proto.SsClanCard.UpdateClanCardCmd) obj;

      if (hasCardInfo() != other.hasCardInfo()) return false;
      if (hasCardInfo()) {
        if (!getCardInfo()
            .equals(other.getCardInfo())) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasCardInfo()) {
        hash = (37 * hash) + CARDINFO_FIELD_NUMBER;
        hash = (53 * hash) + getCardInfo().hashCode();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsClanCard.UpdateClanCardCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.UpdateClanCardCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.UpdateClanCardCmd)
        com.yorha.proto.SsClanCard.UpdateClanCardCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_UpdateClanCardCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsClanCard.UpdateClanCardCmd.class, com.yorha.proto.SsClanCard.UpdateClanCardCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsClanCard.UpdateClanCardCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getCardInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsClanCard.internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.UpdateClanCardCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsClanCard.UpdateClanCardCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.UpdateClanCardCmd build() {
        com.yorha.proto.SsClanCard.UpdateClanCardCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsClanCard.UpdateClanCardCmd buildPartial() {
        com.yorha.proto.SsClanCard.UpdateClanCardCmd result = new com.yorha.proto.SsClanCard.UpdateClanCardCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (cardInfoBuilder_ == null) {
            result.cardInfo_ = cardInfo_;
          } else {
            result.cardInfo_ = cardInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsClanCard.UpdateClanCardCmd) {
          return mergeFrom((com.yorha.proto.SsClanCard.UpdateClanCardCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsClanCard.UpdateClanCardCmd other) {
        if (other == com.yorha.proto.SsClanCard.UpdateClanCardCmd.getDefaultInstance()) return this;
        if (other.hasCardInfo()) {
          mergeCardInfo(other.getCardInfo());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsClanCard.UpdateClanCardCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsClanCard.UpdateClanCardCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.CommonMsg.ClanCardInfo cardInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder> cardInfoBuilder_;
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       * @return Whether the cardInfo field is set.
       */
      public boolean hasCardInfo() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       * @return The cardInfo.
       */
      public com.yorha.proto.CommonMsg.ClanCardInfo getCardInfo() {
        if (cardInfoBuilder_ == null) {
          return cardInfo_ == null ? com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : cardInfo_;
        } else {
          return cardInfoBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public Builder setCardInfo(com.yorha.proto.CommonMsg.ClanCardInfo value) {
        if (cardInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          cardInfo_ = value;
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public Builder setCardInfo(
          com.yorha.proto.CommonMsg.ClanCardInfo.Builder builderForValue) {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = builderForValue.build();
          onChanged();
        } else {
          cardInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public Builder mergeCardInfo(com.yorha.proto.CommonMsg.ClanCardInfo value) {
        if (cardInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              cardInfo_ != null &&
              cardInfo_ != com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance()) {
            cardInfo_ =
              com.yorha.proto.CommonMsg.ClanCardInfo.newBuilder(cardInfo_).mergeFrom(value).buildPartial();
          } else {
            cardInfo_ = value;
          }
          onChanged();
        } else {
          cardInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public Builder clearCardInfo() {
        if (cardInfoBuilder_ == null) {
          cardInfo_ = null;
          onChanged();
        } else {
          cardInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanCardInfo.Builder getCardInfoBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getCardInfoFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      public com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder getCardInfoOrBuilder() {
        if (cardInfoBuilder_ != null) {
          return cardInfoBuilder_.getMessageOrBuilder();
        } else {
          return cardInfo_ == null ?
              com.yorha.proto.CommonMsg.ClanCardInfo.getDefaultInstance() : cardInfo_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.ClanCardInfo cardInfo = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder> 
          getCardInfoFieldBuilder() {
        if (cardInfoBuilder_ == null) {
          cardInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.CommonMsg.ClanCardInfo, com.yorha.proto.CommonMsg.ClanCardInfo.Builder, com.yorha.proto.CommonMsg.ClanCardInfoOrBuilder>(
                  getCardInfo(),
                  getParentForChildren(),
                  isClean());
          cardInfo_ = null;
        }
        return cardInfoBuilder_;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.UpdateClanCardCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.UpdateClanCardCmd)
    private static final com.yorha.proto.SsClanCard.UpdateClanCardCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsClanCard.UpdateClanCardCmd();
    }

    public static com.yorha.proto.SsClanCard.UpdateClanCardCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<UpdateClanCardCmd>
        PARSER = new com.google.protobuf.AbstractParser<UpdateClanCardCmd>() {
      @java.lang.Override
      public UpdateClanCardCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new UpdateClanCardCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<UpdateClanCardCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<UpdateClanCardCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsClanCard.UpdateClanCardCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanNameAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanNameAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanNameAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanNameAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanSimpleAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanSimpleAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanCardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanCardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_QueryClanCardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_QueryClanCardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanNameAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanAllName_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanAllName_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanNameAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanSimpleAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanCardAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanCardAns_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_UpdateClanCardCmd_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n\'ss_proto/gen/clanCard/ss_clanCard.prot" +
      "o\022\017com.yorha.proto\032$ss_proto/gen/common/" +
      "common_msg.proto\"\"\n\020QueryClanNameAsk\022\016\n\006" +
      "clanId\030\001 \001(\003\"L\n\020QueryClanNameAns\022\016\n\006clan" +
      "Id\030\001 \001(\003\022\020\n\010clanName\030\002 \001(\t\022\026\n\016clanSimple" +
      "Name\030\003 \001(\t\"$\n\022QueryClanSimpleAsk\022\016\n\006clan" +
      "Id\030\001 \001(\003\"C\n\022QueryClanSimpleAns\022-\n\004info\030\001" +
      " \001(\0132\037.com.yorha.proto.ClanSimpleInfo\"\"\n" +
      "\020QueryClanCardAsk\022\016\n\006clanId\030\001 \001(\003\"?\n\020Que" +
      "ryClanCardAns\022+\n\004info\030\001 \001(\0132\035.com.yorha." +
      "proto.ClanCardInfo\"&\n\025BatchQueryClanName" +
      "Ask\022\r\n\005clans\030\001 \003(\003\"7\n\013ClanAllName\022\020\n\010cla" +
      "nName\030\001 \001(\t\022\026\n\016clanSimpleName\030\002 \001(\t\"\242\001\n\025" +
      "BatchQueryClanNameAns\022>\n\004info\030\001 \003(\01320.co" +
      "m.yorha.proto.BatchQueryClanNameAns.Info" +
      "Entry\032I\n\tInfoEntry\022\013\n\003key\030\001 \001(\003\022+\n\005value" +
      "\030\002 \001(\0132\034.com.yorha.proto.ClanAllName:\0028\001" +
      "\"(\n\027BatchQueryClanSimpleAsk\022\r\n\005clans\030\001 \003" +
      "(\003\"\251\001\n\027BatchQueryClanSimpleAns\022@\n\004info\030\001" +
      " \003(\01322.com.yorha.proto.BatchQueryClanSim" +
      "pleAns.InfoEntry\032L\n\tInfoEntry\022\013\n\003key\030\001 \001" +
      "(\003\022.\n\005value\030\002 \001(\0132\037.com.yorha.proto.Clan" +
      "SimpleInfo:\0028\001\"&\n\025BatchQueryClanCardAsk\022" +
      "\r\n\005clans\030\001 \003(\003\"\243\001\n\025BatchQueryClanCardAns" +
      "\022>\n\004info\030\001 \003(\01320.com.yorha.proto.BatchQu" +
      "eryClanCardAns.InfoEntry\032J\n\tInfoEntry\022\013\n" +
      "\003key\030\001 \001(\003\022,\n\005value\030\002 \001(\0132\035.com.yorha.pr" +
      "oto.ClanCardInfo:\0028\001\"D\n\021UpdateClanCardCm" +
      "d\022/\n\010cardInfo\030\001 \001(\0132\035.com.yorha.proto.Cl" +
      "anCardInfoB\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.CommonMsg.getDescriptor(),
        });
    internal_static_com_yorha_proto_QueryClanNameAsk_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_QueryClanNameAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanNameAsk_descriptor,
        new java.lang.String[] { "ClanId", });
    internal_static_com_yorha_proto_QueryClanNameAns_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_QueryClanNameAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanNameAns_descriptor,
        new java.lang.String[] { "ClanId", "ClanName", "ClanSimpleName", });
    internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_QueryClanSimpleAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanSimpleAsk_descriptor,
        new java.lang.String[] { "ClanId", });
    internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor =
      getDescriptor().getMessageTypes().get(3);
    internal_static_com_yorha_proto_QueryClanSimpleAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanSimpleAns_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_QueryClanCardAsk_descriptor =
      getDescriptor().getMessageTypes().get(4);
    internal_static_com_yorha_proto_QueryClanCardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanCardAsk_descriptor,
        new java.lang.String[] { "ClanId", });
    internal_static_com_yorha_proto_QueryClanCardAns_descriptor =
      getDescriptor().getMessageTypes().get(5);
    internal_static_com_yorha_proto_QueryClanCardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_QueryClanCardAns_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor =
      getDescriptor().getMessageTypes().get(6);
    internal_static_com_yorha_proto_BatchQueryClanNameAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanNameAsk_descriptor,
        new java.lang.String[] { "Clans", });
    internal_static_com_yorha_proto_ClanAllName_descriptor =
      getDescriptor().getMessageTypes().get(7);
    internal_static_com_yorha_proto_ClanAllName_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanAllName_descriptor,
        new java.lang.String[] { "ClanName", "ClanSimpleName", });
    internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor =
      getDescriptor().getMessageTypes().get(8);
    internal_static_com_yorha_proto_BatchQueryClanNameAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryClanNameAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanNameAns_InfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor =
      getDescriptor().getMessageTypes().get(9);
    internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanSimpleAsk_descriptor,
        new java.lang.String[] { "Clans", });
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor =
      getDescriptor().getMessageTypes().get(10);
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryClanSimpleAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanSimpleAns_InfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor =
      getDescriptor().getMessageTypes().get(11);
    internal_static_com_yorha_proto_BatchQueryClanCardAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanCardAsk_descriptor,
        new java.lang.String[] { "Clans", });
    internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor =
      getDescriptor().getMessageTypes().get(12);
    internal_static_com_yorha_proto_BatchQueryClanCardAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor,
        new java.lang.String[] { "Info", });
    internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_descriptor =
      internal_static_com_yorha_proto_BatchQueryClanCardAns_descriptor.getNestedTypes().get(0);
    internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_BatchQueryClanCardAns_InfoEntry_descriptor,
        new java.lang.String[] { "Key", "Value", });
    internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor =
      getDescriptor().getMessageTypes().get(13);
    internal_static_com_yorha_proto_UpdateClanCardCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_UpdateClanCardCmd_descriptor,
        new java.lang.String[] { "CardInfo", });
    com.yorha.proto.CommonMsg.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

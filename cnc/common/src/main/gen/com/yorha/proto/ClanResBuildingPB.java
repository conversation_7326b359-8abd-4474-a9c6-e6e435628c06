// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: cs_proto/gen/clanResBuilding/clanResBuildingPB.proto

package com.yorha.proto;

public final class ClanResBuildingPB {
  private ClanResBuildingPB() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface ClanResBuildingEntityPBOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.ClanResBuildingEntityPB)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    boolean hasEntityId();
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    long getEntityId();

    /**
     * <pre>
     * 模板id，地图建筑配置表的第一列
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    boolean hasTemplateId();
    /**
     * <pre>
     * 模板id，地图建筑配置表的第一列
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    int getTemplateId();

    /**
     * <pre>
     * 状态：建造中，可采集
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
     * @return Whether the state field is set.
     */
    boolean hasState();
    /**
     * <pre>
     * 状态：建造中，可采集
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
     * @return The state.
     */
    com.yorha.proto.CommonEnum.ClanResBuildingStage getState();

    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     * @return Whether the point field is set.
     */
    boolean hasPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     * @return The point.
     */
    com.yorha.proto.StructPB.PointPB getPoint();
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     */
    com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder();

    /**
     * <pre>
     * 所属军团id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return Whether the clanId field is set.
     */
    boolean hasClanId();
    /**
     * <pre>
     * 所属军团id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return The clanId.
     */
    long getClanId();

    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    boolean hasShowClanSimpleName();
    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return The showClanSimpleName.
     */
    java.lang.String getShowClanSimpleName();
    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return The bytes for showClanSimpleName.
     */
    com.google.protobuf.ByteString
        getShowClanSimpleNameBytes();

    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return Whether the showClanName field is set.
     */
    boolean hasShowClanName();
    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return The showClanName.
     */
    java.lang.String getShowClanName();
    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return The bytes for showClanName.
     */
    com.google.protobuf.ByteString
        getShowClanNameBytes();

    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     * @return Whether the progressInfo field is set.
     */
    boolean hasProgressInfo();
    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     * @return The progressInfo.
     */
    com.yorha.proto.StructCommonPB.ProgressInfoPB getProgressInfo();
    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     */
    com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getProgressInfoOrBuilder();

    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     * @return Whether the flagInfo field is set.
     */
    boolean hasFlagInfo();
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     * @return The flagInfo.
     */
    com.yorha.proto.StructClanPB.ClanFlagInfoPB getFlagInfo();
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     */
    com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getFlagInfoOrBuilder();

    /**
     * <pre>
     * 当前采集人数，停留在界面需要实时变动
     * </pre>
     *
     * <code>optional int32 currentCollectNum = 12;</code>
     * @return Whether the currentCollectNum field is set.
     */
    boolean hasCurrentCollectNum();
    /**
     * <pre>
     * 当前采集人数，停留在界面需要实时变动
     * </pre>
     *
     * <code>optional int32 currentCollectNum = 12;</code>
     * @return The currentCollectNum.
     */
    int getCurrentCollectNum();

    /**
     * <pre>
     * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
     * </pre>
     *
     * <code>optional bool isCollecting = 14;</code>
     * @return Whether the isCollecting field is set.
     */
    boolean hasIsCollecting();
    /**
     * <pre>
     * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
     * </pre>
     *
     * <code>optional bool isCollecting = 14;</code>
     * @return The isCollecting.
     */
    boolean getIsCollecting();

    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 15;</code>
     * @return Whether the zoneId field is set.
     */
    boolean hasZoneId();
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 15;</code>
     * @return The zoneId.
     */
    int getZoneId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.ClanResBuildingEntityPB}
   */
  public static final class ClanResBuildingEntityPB extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.ClanResBuildingEntityPB)
      ClanResBuildingEntityPBOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use ClanResBuildingEntityPB.newBuilder() to construct.
    private ClanResBuildingEntityPB(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private ClanResBuildingEntityPB() {
      state_ = 0;
      showClanSimpleName_ = "";
      showClanName_ = "";
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new ClanResBuildingEntityPB();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private ClanResBuildingEntityPB(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              entityId_ = input.readInt64();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              templateId_ = input.readInt32();
              break;
            }
            case 24: {
              int rawValue = input.readEnum();
                @SuppressWarnings("deprecation")
              com.yorha.proto.CommonEnum.ClanResBuildingStage value = com.yorha.proto.CommonEnum.ClanResBuildingStage.valueOf(rawValue);
              if (value == null) {
                unknownFields.mergeVarintField(3, rawValue);
              } else {
                bitField0_ |= 0x00000004;
                state_ = rawValue;
              }
              break;
            }
            case 34: {
              com.yorha.proto.StructPB.PointPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000008) != 0)) {
                subBuilder = point_.toBuilder();
              }
              point_ = input.readMessage(com.yorha.proto.StructPB.PointPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(point_);
                point_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000008;
              break;
            }
            case 40: {
              bitField0_ |= 0x00000010;
              clanId_ = input.readInt64();
              break;
            }
            case 50: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000020;
              showClanSimpleName_ = bs;
              break;
            }
            case 58: {
              com.google.protobuf.ByteString bs = input.readBytes();
              bitField0_ |= 0x00000040;
              showClanName_ = bs;
              break;
            }
            case 82: {
              com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000080) != 0)) {
                subBuilder = progressInfo_.toBuilder();
              }
              progressInfo_ = input.readMessage(com.yorha.proto.StructCommonPB.ProgressInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(progressInfo_);
                progressInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000080;
              break;
            }
            case 90: {
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder subBuilder = null;
              if (((bitField0_ & 0x00000100) != 0)) {
                subBuilder = flagInfo_.toBuilder();
              }
              flagInfo_ = input.readMessage(com.yorha.proto.StructClanPB.ClanFlagInfoPB.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(flagInfo_);
                flagInfo_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000100;
              break;
            }
            case 96: {
              bitField0_ |= 0x00000200;
              currentCollectNum_ = input.readInt32();
              break;
            }
            case 112: {
              bitField0_ |= 0x00000400;
              isCollecting_ = input.readBool();
              break;
            }
            case 120: {
              bitField0_ |= 0x00000800;
              zoneId_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.ClanResBuildingPB.internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.ClanResBuildingPB.internal_static_com_yorha_proto_ClanResBuildingEntityPB_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.class, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder.class);
    }

    private int bitField0_;
    public static final int ENTITYID_FIELD_NUMBER = 1;
    private long entityId_;
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 1;</code>
     * @return Whether the entityId field is set.
     */
    @java.lang.Override
    public boolean hasEntityId() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 唯一id
     * </pre>
     *
     * <code>optional int64 entityId = 1;</code>
     * @return The entityId.
     */
    @java.lang.Override
    public long getEntityId() {
      return entityId_;
    }

    public static final int TEMPLATEID_FIELD_NUMBER = 2;
    private int templateId_;
    /**
     * <pre>
     * 模板id，地图建筑配置表的第一列
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return Whether the templateId field is set.
     */
    @java.lang.Override
    public boolean hasTemplateId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 模板id，地图建筑配置表的第一列
     * </pre>
     *
     * <code>optional int32 templateId = 2;</code>
     * @return The templateId.
     */
    @java.lang.Override
    public int getTemplateId() {
      return templateId_;
    }

    public static final int STATE_FIELD_NUMBER = 3;
    private int state_;
    /**
     * <pre>
     * 状态：建造中，可采集
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
     * @return Whether the state field is set.
     */
    @java.lang.Override public boolean hasState() {
      return ((bitField0_ & 0x00000004) != 0);
    }
    /**
     * <pre>
     * 状态：建造中，可采集
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
     * @return The state.
     */
    @java.lang.Override public com.yorha.proto.CommonEnum.ClanResBuildingStage getState() {
      @SuppressWarnings("deprecation")
      com.yorha.proto.CommonEnum.ClanResBuildingStage result = com.yorha.proto.CommonEnum.ClanResBuildingStage.valueOf(state_);
      return result == null ? com.yorha.proto.CommonEnum.ClanResBuildingStage.CRBS_NONE : result;
    }

    public static final int POINT_FIELD_NUMBER = 4;
    private com.yorha.proto.StructPB.PointPB point_;
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     * @return Whether the point field is set.
     */
    @java.lang.Override
    public boolean hasPoint() {
      return ((bitField0_ & 0x00000008) != 0);
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     * @return The point.
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPB getPoint() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }
    /**
     * <pre>
     * 坐标点
     * </pre>
     *
     * <code>optional .com.yorha.proto.PointPB point = 4;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
      return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
    }

    public static final int CLANID_FIELD_NUMBER = 5;
    private long clanId_;
    /**
     * <pre>
     * 所属军团id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return Whether the clanId field is set.
     */
    @java.lang.Override
    public boolean hasClanId() {
      return ((bitField0_ & 0x00000010) != 0);
    }
    /**
     * <pre>
     * 所属军团id
     * </pre>
     *
     * <code>optional int64 clanId = 5;</code>
     * @return The clanId.
     */
    @java.lang.Override
    public long getClanId() {
      return clanId_;
    }

    public static final int SHOWCLANSIMPLENAME_FIELD_NUMBER = 6;
    private volatile java.lang.Object showClanSimpleName_;
    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return Whether the showClanSimpleName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanSimpleName() {
      return ((bitField0_ & 0x00000020) != 0);
    }
    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return The showClanSimpleName.
     */
    @java.lang.Override
    public java.lang.String getShowClanSimpleName() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanSimpleName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示军团简称
     * </pre>
     *
     * <code>optional string showClanSimpleName = 6;</code>
     * @return The bytes for showClanSimpleName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanSimpleNameBytes() {
      java.lang.Object ref = showClanSimpleName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanSimpleName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int SHOWCLANNAME_FIELD_NUMBER = 7;
    private volatile java.lang.Object showClanName_;
    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return Whether the showClanName field is set.
     */
    @java.lang.Override
    public boolean hasShowClanName() {
      return ((bitField0_ & 0x00000040) != 0);
    }
    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return The showClanName.
     */
    @java.lang.Override
    public java.lang.String getShowClanName() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        return (java.lang.String) ref;
      } else {
        com.google.protobuf.ByteString bs = 
            (com.google.protobuf.ByteString) ref;
        java.lang.String s = bs.toStringUtf8();
        if (bs.isValidUtf8()) {
          showClanName_ = s;
        }
        return s;
      }
    }
    /**
     * <pre>
     * 显示军团名字
     * </pre>
     *
     * <code>optional string showClanName = 7;</code>
     * @return The bytes for showClanName.
     */
    @java.lang.Override
    public com.google.protobuf.ByteString
        getShowClanNameBytes() {
      java.lang.Object ref = showClanName_;
      if (ref instanceof java.lang.String) {
        com.google.protobuf.ByteString b = 
            com.google.protobuf.ByteString.copyFromUtf8(
                (java.lang.String) ref);
        showClanName_ = b;
        return b;
      } else {
        return (com.google.protobuf.ByteString) ref;
      }
    }

    public static final int PROGRESSINFO_FIELD_NUMBER = 10;
    private com.yorha.proto.StructCommonPB.ProgressInfoPB progressInfo_;
    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     * @return Whether the progressInfo field is set.
     */
    @java.lang.Override
    public boolean hasProgressInfo() {
      return ((bitField0_ & 0x00000080) != 0);
    }
    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     * @return The progressInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.ProgressInfoPB getProgressInfo() {
      return progressInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : progressInfo_;
    }
    /**
     * <pre>
     * 整个资源中心建设、采集的进度条数据
     * </pre>
     *
     * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getProgressInfoOrBuilder() {
      return progressInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : progressInfo_;
    }

    public static final int FLAGINFO_FIELD_NUMBER = 11;
    private com.yorha.proto.StructClanPB.ClanFlagInfoPB flagInfo_;
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     * @return Whether the flagInfo field is set.
     */
    @java.lang.Override
    public boolean hasFlagInfo() {
      return ((bitField0_ & 0x00000100) != 0);
    }
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     * @return The flagInfo.
     */
    @java.lang.Override
    public com.yorha.proto.StructClanPB.ClanFlagInfoPB getFlagInfo() {
      return flagInfo_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : flagInfo_;
    }
    /**
     * <pre>
     * 军团旗帜信息
     * </pre>
     *
     * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getFlagInfoOrBuilder() {
      return flagInfo_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : flagInfo_;
    }

    public static final int CURRENTCOLLECTNUM_FIELD_NUMBER = 12;
    private int currentCollectNum_;
    /**
     * <pre>
     * 当前采集人数，停留在界面需要实时变动
     * </pre>
     *
     * <code>optional int32 currentCollectNum = 12;</code>
     * @return Whether the currentCollectNum field is set.
     */
    @java.lang.Override
    public boolean hasCurrentCollectNum() {
      return ((bitField0_ & 0x00000200) != 0);
    }
    /**
     * <pre>
     * 当前采集人数，停留在界面需要实时变动
     * </pre>
     *
     * <code>optional int32 currentCollectNum = 12;</code>
     * @return The currentCollectNum.
     */
    @java.lang.Override
    public int getCurrentCollectNum() {
      return currentCollectNum_;
    }

    public static final int ISCOLLECTING_FIELD_NUMBER = 14;
    private boolean isCollecting_;
    /**
     * <pre>
     * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
     * </pre>
     *
     * <code>optional bool isCollecting = 14;</code>
     * @return Whether the isCollecting field is set.
     */
    @java.lang.Override
    public boolean hasIsCollecting() {
      return ((bitField0_ & 0x00000400) != 0);
    }
    /**
     * <pre>
     * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
     * </pre>
     *
     * <code>optional bool isCollecting = 14;</code>
     * @return The isCollecting.
     */
    @java.lang.Override
    public boolean getIsCollecting() {
      return isCollecting_;
    }

    public static final int ZONEID_FIELD_NUMBER = 15;
    private int zoneId_;
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 15;</code>
     * @return Whether the zoneId field is set.
     */
    @java.lang.Override
    public boolean hasZoneId() {
      return ((bitField0_ & 0x00000800) != 0);
    }
    /**
     * <pre>
     * 所属联盟的所属服务器id
     * </pre>
     *
     * <code>optional int32 zoneId = 15;</code>
     * @return The zoneId.
     */
    @java.lang.Override
    public int getZoneId() {
      return zoneId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt64(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        output.writeEnum(3, state_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        output.writeMessage(4, getPoint());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        output.writeInt64(5, clanId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 6, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        com.google.protobuf.GeneratedMessageV3.writeString(output, 7, showClanName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        output.writeMessage(10, getProgressInfo());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        output.writeMessage(11, getFlagInfo());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        output.writeInt32(12, currentCollectNum_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        output.writeBool(14, isCollecting_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        output.writeInt32(15, zoneId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(1, entityId_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, templateId_);
      }
      if (((bitField0_ & 0x00000004) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeEnumSize(3, state_);
      }
      if (((bitField0_ & 0x00000008) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(4, getPoint());
      }
      if (((bitField0_ & 0x00000010) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(5, clanId_);
      }
      if (((bitField0_ & 0x00000020) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(6, showClanSimpleName_);
      }
      if (((bitField0_ & 0x00000040) != 0)) {
        size += com.google.protobuf.GeneratedMessageV3.computeStringSize(7, showClanName_);
      }
      if (((bitField0_ & 0x00000080) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(10, getProgressInfo());
      }
      if (((bitField0_ & 0x00000100) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(11, getFlagInfo());
      }
      if (((bitField0_ & 0x00000200) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(12, currentCollectNum_);
      }
      if (((bitField0_ & 0x00000400) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeBoolSize(14, isCollecting_);
      }
      if (((bitField0_ & 0x00000800) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(15, zoneId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB)) {
        return super.equals(obj);
      }
      com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB other = (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) obj;

      if (hasEntityId() != other.hasEntityId()) return false;
      if (hasEntityId()) {
        if (getEntityId()
            != other.getEntityId()) return false;
      }
      if (hasTemplateId() != other.hasTemplateId()) return false;
      if (hasTemplateId()) {
        if (getTemplateId()
            != other.getTemplateId()) return false;
      }
      if (hasState() != other.hasState()) return false;
      if (hasState()) {
        if (state_ != other.state_) return false;
      }
      if (hasPoint() != other.hasPoint()) return false;
      if (hasPoint()) {
        if (!getPoint()
            .equals(other.getPoint())) return false;
      }
      if (hasClanId() != other.hasClanId()) return false;
      if (hasClanId()) {
        if (getClanId()
            != other.getClanId()) return false;
      }
      if (hasShowClanSimpleName() != other.hasShowClanSimpleName()) return false;
      if (hasShowClanSimpleName()) {
        if (!getShowClanSimpleName()
            .equals(other.getShowClanSimpleName())) return false;
      }
      if (hasShowClanName() != other.hasShowClanName()) return false;
      if (hasShowClanName()) {
        if (!getShowClanName()
            .equals(other.getShowClanName())) return false;
      }
      if (hasProgressInfo() != other.hasProgressInfo()) return false;
      if (hasProgressInfo()) {
        if (!getProgressInfo()
            .equals(other.getProgressInfo())) return false;
      }
      if (hasFlagInfo() != other.hasFlagInfo()) return false;
      if (hasFlagInfo()) {
        if (!getFlagInfo()
            .equals(other.getFlagInfo())) return false;
      }
      if (hasCurrentCollectNum() != other.hasCurrentCollectNum()) return false;
      if (hasCurrentCollectNum()) {
        if (getCurrentCollectNum()
            != other.getCurrentCollectNum()) return false;
      }
      if (hasIsCollecting() != other.hasIsCollecting()) return false;
      if (hasIsCollecting()) {
        if (getIsCollecting()
            != other.getIsCollecting()) return false;
      }
      if (hasZoneId() != other.hasZoneId()) return false;
      if (hasZoneId()) {
        if (getZoneId()
            != other.getZoneId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasEntityId()) {
        hash = (37 * hash) + ENTITYID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getEntityId());
      }
      if (hasTemplateId()) {
        hash = (37 * hash) + TEMPLATEID_FIELD_NUMBER;
        hash = (53 * hash) + getTemplateId();
      }
      if (hasState()) {
        hash = (37 * hash) + STATE_FIELD_NUMBER;
        hash = (53 * hash) + state_;
      }
      if (hasPoint()) {
        hash = (37 * hash) + POINT_FIELD_NUMBER;
        hash = (53 * hash) + getPoint().hashCode();
      }
      if (hasClanId()) {
        hash = (37 * hash) + CLANID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getClanId());
      }
      if (hasShowClanSimpleName()) {
        hash = (37 * hash) + SHOWCLANSIMPLENAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanSimpleName().hashCode();
      }
      if (hasShowClanName()) {
        hash = (37 * hash) + SHOWCLANNAME_FIELD_NUMBER;
        hash = (53 * hash) + getShowClanName().hashCode();
      }
      if (hasProgressInfo()) {
        hash = (37 * hash) + PROGRESSINFO_FIELD_NUMBER;
        hash = (53 * hash) + getProgressInfo().hashCode();
      }
      if (hasFlagInfo()) {
        hash = (37 * hash) + FLAGINFO_FIELD_NUMBER;
        hash = (53 * hash) + getFlagInfo().hashCode();
      }
      if (hasCurrentCollectNum()) {
        hash = (37 * hash) + CURRENTCOLLECTNUM_FIELD_NUMBER;
        hash = (53 * hash) + getCurrentCollectNum();
      }
      if (hasIsCollecting()) {
        hash = (37 * hash) + ISCOLLECTING_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashBoolean(
            getIsCollecting());
      }
      if (hasZoneId()) {
        hash = (37 * hash) + ZONEID_FIELD_NUMBER;
        hash = (53 * hash) + getZoneId();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.ClanResBuildingEntityPB}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.ClanResBuildingEntityPB)
        com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPBOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.ClanResBuildingPB.internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.ClanResBuildingPB.internal_static_com_yorha_proto_ClanResBuildingEntityPB_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.class, com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.Builder.class);
      }

      // Construct using com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getPointFieldBuilder();
          getProgressInfoFieldBuilder();
          getFlagInfoFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        entityId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000001);
        templateId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        state_ = 0;
        bitField0_ = (bitField0_ & ~0x00000004);
        if (pointBuilder_ == null) {
          point_ = null;
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        clanId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000010);
        showClanSimpleName_ = "";
        bitField0_ = (bitField0_ & ~0x00000020);
        showClanName_ = "";
        bitField0_ = (bitField0_ & ~0x00000040);
        if (progressInfoBuilder_ == null) {
          progressInfo_ = null;
        } else {
          progressInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        if (flagInfoBuilder_ == null) {
          flagInfo_ = null;
        } else {
          flagInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        currentCollectNum_ = 0;
        bitField0_ = (bitField0_ & ~0x00000200);
        isCollecting_ = false;
        bitField0_ = (bitField0_ & ~0x00000400);
        zoneId_ = 0;
        bitField0_ = (bitField0_ & ~0x00000800);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.ClanResBuildingPB.internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getDefaultInstanceForType() {
        return com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB build() {
        com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB buildPartial() {
        com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB result = new com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.entityId_ = entityId_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.templateId_ = templateId_;
          to_bitField0_ |= 0x00000002;
        }
        if (((from_bitField0_ & 0x00000004) != 0)) {
          to_bitField0_ |= 0x00000004;
        }
        result.state_ = state_;
        if (((from_bitField0_ & 0x00000008) != 0)) {
          if (pointBuilder_ == null) {
            result.point_ = point_;
          } else {
            result.point_ = pointBuilder_.build();
          }
          to_bitField0_ |= 0x00000008;
        }
        if (((from_bitField0_ & 0x00000010) != 0)) {
          result.clanId_ = clanId_;
          to_bitField0_ |= 0x00000010;
        }
        if (((from_bitField0_ & 0x00000020) != 0)) {
          to_bitField0_ |= 0x00000020;
        }
        result.showClanSimpleName_ = showClanSimpleName_;
        if (((from_bitField0_ & 0x00000040) != 0)) {
          to_bitField0_ |= 0x00000040;
        }
        result.showClanName_ = showClanName_;
        if (((from_bitField0_ & 0x00000080) != 0)) {
          if (progressInfoBuilder_ == null) {
            result.progressInfo_ = progressInfo_;
          } else {
            result.progressInfo_ = progressInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000080;
        }
        if (((from_bitField0_ & 0x00000100) != 0)) {
          if (flagInfoBuilder_ == null) {
            result.flagInfo_ = flagInfo_;
          } else {
            result.flagInfo_ = flagInfoBuilder_.build();
          }
          to_bitField0_ |= 0x00000100;
        }
        if (((from_bitField0_ & 0x00000200) != 0)) {
          result.currentCollectNum_ = currentCollectNum_;
          to_bitField0_ |= 0x00000200;
        }
        if (((from_bitField0_ & 0x00000400) != 0)) {
          result.isCollecting_ = isCollecting_;
          to_bitField0_ |= 0x00000400;
        }
        if (((from_bitField0_ & 0x00000800) != 0)) {
          result.zoneId_ = zoneId_;
          to_bitField0_ |= 0x00000800;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) {
          return mergeFrom((com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB other) {
        if (other == com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB.getDefaultInstance()) return this;
        if (other.hasEntityId()) {
          setEntityId(other.getEntityId());
        }
        if (other.hasTemplateId()) {
          setTemplateId(other.getTemplateId());
        }
        if (other.hasState()) {
          setState(other.getState());
        }
        if (other.hasPoint()) {
          mergePoint(other.getPoint());
        }
        if (other.hasClanId()) {
          setClanId(other.getClanId());
        }
        if (other.hasShowClanSimpleName()) {
          bitField0_ |= 0x00000020;
          showClanSimpleName_ = other.showClanSimpleName_;
          onChanged();
        }
        if (other.hasShowClanName()) {
          bitField0_ |= 0x00000040;
          showClanName_ = other.showClanName_;
          onChanged();
        }
        if (other.hasProgressInfo()) {
          mergeProgressInfo(other.getProgressInfo());
        }
        if (other.hasFlagInfo()) {
          mergeFlagInfo(other.getFlagInfo());
        }
        if (other.hasCurrentCollectNum()) {
          setCurrentCollectNum(other.getCurrentCollectNum());
        }
        if (other.hasIsCollecting()) {
          setIsCollecting(other.getIsCollecting());
        }
        if (other.hasZoneId()) {
          setZoneId(other.getZoneId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private long entityId_ ;
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 1;</code>
       * @return Whether the entityId field is set.
       */
      @java.lang.Override
      public boolean hasEntityId() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 1;</code>
       * @return The entityId.
       */
      @java.lang.Override
      public long getEntityId() {
        return entityId_;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 1;</code>
       * @param value The entityId to set.
       * @return This builder for chaining.
       */
      public Builder setEntityId(long value) {
        bitField0_ |= 0x00000001;
        entityId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 唯一id
       * </pre>
       *
       * <code>optional int64 entityId = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearEntityId() {
        bitField0_ = (bitField0_ & ~0x00000001);
        entityId_ = 0L;
        onChanged();
        return this;
      }

      private int templateId_ ;
      /**
       * <pre>
       * 模板id，地图建筑配置表的第一列
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return Whether the templateId field is set.
       */
      @java.lang.Override
      public boolean hasTemplateId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 模板id，地图建筑配置表的第一列
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return The templateId.
       */
      @java.lang.Override
      public int getTemplateId() {
        return templateId_;
      }
      /**
       * <pre>
       * 模板id，地图建筑配置表的第一列
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @param value The templateId to set.
       * @return This builder for chaining.
       */
      public Builder setTemplateId(int value) {
        bitField0_ |= 0x00000002;
        templateId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 模板id，地图建筑配置表的第一列
       * </pre>
       *
       * <code>optional int32 templateId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearTemplateId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        templateId_ = 0;
        onChanged();
        return this;
      }

      private int state_ = 0;
      /**
       * <pre>
       * 状态：建造中，可采集
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
       * @return Whether the state field is set.
       */
      @java.lang.Override public boolean hasState() {
        return ((bitField0_ & 0x00000004) != 0);
      }
      /**
       * <pre>
       * 状态：建造中，可采集
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
       * @return The state.
       */
      @java.lang.Override
      public com.yorha.proto.CommonEnum.ClanResBuildingStage getState() {
        @SuppressWarnings("deprecation")
        com.yorha.proto.CommonEnum.ClanResBuildingStage result = com.yorha.proto.CommonEnum.ClanResBuildingStage.valueOf(state_);
        return result == null ? com.yorha.proto.CommonEnum.ClanResBuildingStage.CRBS_NONE : result;
      }
      /**
       * <pre>
       * 状态：建造中，可采集
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
       * @param value The state to set.
       * @return This builder for chaining.
       */
      public Builder setState(com.yorha.proto.CommonEnum.ClanResBuildingStage value) {
        if (value == null) {
          throw new NullPointerException();
        }
        bitField0_ |= 0x00000004;
        state_ = value.getNumber();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 状态：建造中，可采集
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanResBuildingStage state = 3;</code>
       * @return This builder for chaining.
       */
      public Builder clearState() {
        bitField0_ = (bitField0_ & ~0x00000004);
        state_ = 0;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructPB.PointPB point_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> pointBuilder_;
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       * @return Whether the point field is set.
       */
      public boolean hasPoint() {
        return ((bitField0_ & 0x00000008) != 0);
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       * @return The point.
       */
      public com.yorha.proto.StructPB.PointPB getPoint() {
        if (pointBuilder_ == null) {
          return point_ == null ? com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        } else {
          return pointBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public Builder setPoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          point_ = value;
          onChanged();
        } else {
          pointBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public Builder setPoint(
          com.yorha.proto.StructPB.PointPB.Builder builderForValue) {
        if (pointBuilder_ == null) {
          point_ = builderForValue.build();
          onChanged();
        } else {
          pointBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public Builder mergePoint(com.yorha.proto.StructPB.PointPB value) {
        if (pointBuilder_ == null) {
          if (((bitField0_ & 0x00000008) != 0) &&
              point_ != null &&
              point_ != com.yorha.proto.StructPB.PointPB.getDefaultInstance()) {
            point_ =
              com.yorha.proto.StructPB.PointPB.newBuilder(point_).mergeFrom(value).buildPartial();
          } else {
            point_ = value;
          }
          onChanged();
        } else {
          pointBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000008;
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public Builder clearPoint() {
        if (pointBuilder_ == null) {
          point_ = null;
          onChanged();
        } else {
          pointBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000008);
        return this;
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public com.yorha.proto.StructPB.PointPB.Builder getPointBuilder() {
        bitField0_ |= 0x00000008;
        onChanged();
        return getPointFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      public com.yorha.proto.StructPB.PointPBOrBuilder getPointOrBuilder() {
        if (pointBuilder_ != null) {
          return pointBuilder_.getMessageOrBuilder();
        } else {
          return point_ == null ?
              com.yorha.proto.StructPB.PointPB.getDefaultInstance() : point_;
        }
      }
      /**
       * <pre>
       * 坐标点
       * </pre>
       *
       * <code>optional .com.yorha.proto.PointPB point = 4;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder> 
          getPointFieldBuilder() {
        if (pointBuilder_ == null) {
          pointBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructPB.PointPB, com.yorha.proto.StructPB.PointPB.Builder, com.yorha.proto.StructPB.PointPBOrBuilder>(
                  getPoint(),
                  getParentForChildren(),
                  isClean());
          point_ = null;
        }
        return pointBuilder_;
      }

      private long clanId_ ;
      /**
       * <pre>
       * 所属军团id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return Whether the clanId field is set.
       */
      @java.lang.Override
      public boolean hasClanId() {
        return ((bitField0_ & 0x00000010) != 0);
      }
      /**
       * <pre>
       * 所属军团id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return The clanId.
       */
      @java.lang.Override
      public long getClanId() {
        return clanId_;
      }
      /**
       * <pre>
       * 所属军团id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @param value The clanId to set.
       * @return This builder for chaining.
       */
      public Builder setClanId(long value) {
        bitField0_ |= 0x00000010;
        clanId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属军团id
       * </pre>
       *
       * <code>optional int64 clanId = 5;</code>
       * @return This builder for chaining.
       */
      public Builder clearClanId() {
        bitField0_ = (bitField0_ & ~0x00000010);
        clanId_ = 0L;
        onChanged();
        return this;
      }

      private java.lang.Object showClanSimpleName_ = "";
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @return Whether the showClanSimpleName field is set.
       */
      public boolean hasShowClanSimpleName() {
        return ((bitField0_ & 0x00000020) != 0);
      }
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @return The showClanSimpleName.
       */
      public java.lang.String getShowClanSimpleName() {
        java.lang.Object ref = showClanSimpleName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanSimpleName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @return The bytes for showClanSimpleName.
       */
      public com.google.protobuf.ByteString
          getShowClanSimpleNameBytes() {
        java.lang.Object ref = showClanSimpleName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanSimpleName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @param value The showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanSimpleName() {
        bitField0_ = (bitField0_ & ~0x00000020);
        showClanSimpleName_ = getDefaultInstance().getShowClanSimpleName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示军团简称
       * </pre>
       *
       * <code>optional string showClanSimpleName = 6;</code>
       * @param value The bytes for showClanSimpleName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanSimpleNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000020;
        showClanSimpleName_ = value;
        onChanged();
        return this;
      }

      private java.lang.Object showClanName_ = "";
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @return Whether the showClanName field is set.
       */
      public boolean hasShowClanName() {
        return ((bitField0_ & 0x00000040) != 0);
      }
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @return The showClanName.
       */
      public java.lang.String getShowClanName() {
        java.lang.Object ref = showClanName_;
        if (!(ref instanceof java.lang.String)) {
          com.google.protobuf.ByteString bs =
              (com.google.protobuf.ByteString) ref;
          java.lang.String s = bs.toStringUtf8();
          if (bs.isValidUtf8()) {
            showClanName_ = s;
          }
          return s;
        } else {
          return (java.lang.String) ref;
        }
      }
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @return The bytes for showClanName.
       */
      public com.google.protobuf.ByteString
          getShowClanNameBytes() {
        java.lang.Object ref = showClanName_;
        if (ref instanceof String) {
          com.google.protobuf.ByteString b = 
              com.google.protobuf.ByteString.copyFromUtf8(
                  (java.lang.String) ref);
          showClanName_ = b;
          return b;
        } else {
          return (com.google.protobuf.ByteString) ref;
        }
      }
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @param value The showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanName(
          java.lang.String value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        showClanName_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @return This builder for chaining.
       */
      public Builder clearShowClanName() {
        bitField0_ = (bitField0_ & ~0x00000040);
        showClanName_ = getDefaultInstance().getShowClanName();
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 显示军团名字
       * </pre>
       *
       * <code>optional string showClanName = 7;</code>
       * @param value The bytes for showClanName to set.
       * @return This builder for chaining.
       */
      public Builder setShowClanNameBytes(
          com.google.protobuf.ByteString value) {
        if (value == null) {
    throw new NullPointerException();
  }
  bitField0_ |= 0x00000040;
        showClanName_ = value;
        onChanged();
        return this;
      }

      private com.yorha.proto.StructCommonPB.ProgressInfoPB progressInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder> progressInfoBuilder_;
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       * @return Whether the progressInfo field is set.
       */
      public boolean hasProgressInfo() {
        return ((bitField0_ & 0x00000080) != 0);
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       * @return The progressInfo.
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPB getProgressInfo() {
        if (progressInfoBuilder_ == null) {
          return progressInfo_ == null ? com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : progressInfo_;
        } else {
          return progressInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public Builder setProgressInfo(com.yorha.proto.StructCommonPB.ProgressInfoPB value) {
        if (progressInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          progressInfo_ = value;
          onChanged();
        } else {
          progressInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public Builder setProgressInfo(
          com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder builderForValue) {
        if (progressInfoBuilder_ == null) {
          progressInfo_ = builderForValue.build();
          onChanged();
        } else {
          progressInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public Builder mergeProgressInfo(com.yorha.proto.StructCommonPB.ProgressInfoPB value) {
        if (progressInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000080) != 0) &&
              progressInfo_ != null &&
              progressInfo_ != com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance()) {
            progressInfo_ =
              com.yorha.proto.StructCommonPB.ProgressInfoPB.newBuilder(progressInfo_).mergeFrom(value).buildPartial();
          } else {
            progressInfo_ = value;
          }
          onChanged();
        } else {
          progressInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000080;
        return this;
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public Builder clearProgressInfo() {
        if (progressInfoBuilder_ == null) {
          progressInfo_ = null;
          onChanged();
        } else {
          progressInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000080);
        return this;
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder getProgressInfoBuilder() {
        bitField0_ |= 0x00000080;
        onChanged();
        return getProgressInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      public com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder getProgressInfoOrBuilder() {
        if (progressInfoBuilder_ != null) {
          return progressInfoBuilder_.getMessageOrBuilder();
        } else {
          return progressInfo_ == null ?
              com.yorha.proto.StructCommonPB.ProgressInfoPB.getDefaultInstance() : progressInfo_;
        }
      }
      /**
       * <pre>
       * 整个资源中心建设、采集的进度条数据
       * </pre>
       *
       * <code>optional .com.yorha.proto.ProgressInfoPB progressInfo = 10;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder> 
          getProgressInfoFieldBuilder() {
        if (progressInfoBuilder_ == null) {
          progressInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructCommonPB.ProgressInfoPB, com.yorha.proto.StructCommonPB.ProgressInfoPB.Builder, com.yorha.proto.StructCommonPB.ProgressInfoPBOrBuilder>(
                  getProgressInfo(),
                  getParentForChildren(),
                  isClean());
          progressInfo_ = null;
        }
        return progressInfoBuilder_;
      }

      private com.yorha.proto.StructClanPB.ClanFlagInfoPB flagInfo_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder> flagInfoBuilder_;
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       * @return Whether the flagInfo field is set.
       */
      public boolean hasFlagInfo() {
        return ((bitField0_ & 0x00000100) != 0);
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       * @return The flagInfo.
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPB getFlagInfo() {
        if (flagInfoBuilder_ == null) {
          return flagInfo_ == null ? com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : flagInfo_;
        } else {
          return flagInfoBuilder_.getMessage();
        }
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public Builder setFlagInfo(com.yorha.proto.StructClanPB.ClanFlagInfoPB value) {
        if (flagInfoBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          flagInfo_ = value;
          onChanged();
        } else {
          flagInfoBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public Builder setFlagInfo(
          com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder builderForValue) {
        if (flagInfoBuilder_ == null) {
          flagInfo_ = builderForValue.build();
          onChanged();
        } else {
          flagInfoBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public Builder mergeFlagInfo(com.yorha.proto.StructClanPB.ClanFlagInfoPB value) {
        if (flagInfoBuilder_ == null) {
          if (((bitField0_ & 0x00000100) != 0) &&
              flagInfo_ != null &&
              flagInfo_ != com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance()) {
            flagInfo_ =
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.newBuilder(flagInfo_).mergeFrom(value).buildPartial();
          } else {
            flagInfo_ = value;
          }
          onChanged();
        } else {
          flagInfoBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000100;
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public Builder clearFlagInfo() {
        if (flagInfoBuilder_ == null) {
          flagInfo_ = null;
          onChanged();
        } else {
          flagInfoBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000100);
        return this;
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder getFlagInfoBuilder() {
        bitField0_ |= 0x00000100;
        onChanged();
        return getFlagInfoFieldBuilder().getBuilder();
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      public com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder getFlagInfoOrBuilder() {
        if (flagInfoBuilder_ != null) {
          return flagInfoBuilder_.getMessageOrBuilder();
        } else {
          return flagInfo_ == null ?
              com.yorha.proto.StructClanPB.ClanFlagInfoPB.getDefaultInstance() : flagInfo_;
        }
      }
      /**
       * <pre>
       * 军团旗帜信息
       * </pre>
       *
       * <code>optional .com.yorha.proto.ClanFlagInfoPB flagInfo = 11;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder> 
          getFlagInfoFieldBuilder() {
        if (flagInfoBuilder_ == null) {
          flagInfoBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructClanPB.ClanFlagInfoPB, com.yorha.proto.StructClanPB.ClanFlagInfoPB.Builder, com.yorha.proto.StructClanPB.ClanFlagInfoPBOrBuilder>(
                  getFlagInfo(),
                  getParentForChildren(),
                  isClean());
          flagInfo_ = null;
        }
        return flagInfoBuilder_;
      }

      private int currentCollectNum_ ;
      /**
       * <pre>
       * 当前采集人数，停留在界面需要实时变动
       * </pre>
       *
       * <code>optional int32 currentCollectNum = 12;</code>
       * @return Whether the currentCollectNum field is set.
       */
      @java.lang.Override
      public boolean hasCurrentCollectNum() {
        return ((bitField0_ & 0x00000200) != 0);
      }
      /**
       * <pre>
       * 当前采集人数，停留在界面需要实时变动
       * </pre>
       *
       * <code>optional int32 currentCollectNum = 12;</code>
       * @return The currentCollectNum.
       */
      @java.lang.Override
      public int getCurrentCollectNum() {
        return currentCollectNum_;
      }
      /**
       * <pre>
       * 当前采集人数，停留在界面需要实时变动
       * </pre>
       *
       * <code>optional int32 currentCollectNum = 12;</code>
       * @param value The currentCollectNum to set.
       * @return This builder for chaining.
       */
      public Builder setCurrentCollectNum(int value) {
        bitField0_ |= 0x00000200;
        currentCollectNum_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 当前采集人数，停留在界面需要实时变动
       * </pre>
       *
       * <code>optional int32 currentCollectNum = 12;</code>
       * @return This builder for chaining.
       */
      public Builder clearCurrentCollectNum() {
        bitField0_ = (bitField0_ & ~0x00000200);
        currentCollectNum_ = 0;
        onChanged();
        return this;
      }

      private boolean isCollecting_ ;
      /**
       * <pre>
       * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
       * </pre>
       *
       * <code>optional bool isCollecting = 14;</code>
       * @return Whether the isCollecting field is set.
       */
      @java.lang.Override
      public boolean hasIsCollecting() {
        return ((bitField0_ & 0x00000400) != 0);
      }
      /**
       * <pre>
       * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
       * </pre>
       *
       * <code>optional bool isCollecting = 14;</code>
       * @return The isCollecting.
       */
      @java.lang.Override
      public boolean getIsCollecting() {
        return isCollecting_;
      }
      /**
       * <pre>
       * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
       * </pre>
       *
       * <code>optional bool isCollecting = 14;</code>
       * @param value The isCollecting to set.
       * @return This builder for chaining.
       */
      public Builder setIsCollecting(boolean value) {
        bitField0_ |= 0x00000400;
        isCollecting_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 是否有人正在采集，仅当建筑是可采集状态时发送，true代表有人在采集
       * </pre>
       *
       * <code>optional bool isCollecting = 14;</code>
       * @return This builder for chaining.
       */
      public Builder clearIsCollecting() {
        bitField0_ = (bitField0_ & ~0x00000400);
        isCollecting_ = false;
        onChanged();
        return this;
      }

      private int zoneId_ ;
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 15;</code>
       * @return Whether the zoneId field is set.
       */
      @java.lang.Override
      public boolean hasZoneId() {
        return ((bitField0_ & 0x00000800) != 0);
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 15;</code>
       * @return The zoneId.
       */
      @java.lang.Override
      public int getZoneId() {
        return zoneId_;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 15;</code>
       * @param value The zoneId to set.
       * @return This builder for chaining.
       */
      public Builder setZoneId(int value) {
        bitField0_ |= 0x00000800;
        zoneId_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 所属联盟的所属服务器id
       * </pre>
       *
       * <code>optional int32 zoneId = 15;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneId() {
        bitField0_ = (bitField0_ & ~0x00000800);
        zoneId_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.ClanResBuildingEntityPB)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.ClanResBuildingEntityPB)
    private static final com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB();
    }

    public static com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<ClanResBuildingEntityPB>
        PARSER = new com.google.protobuf.AbstractParser<ClanResBuildingEntityPB>() {
      @java.lang.Override
      public ClanResBuildingEntityPB parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new ClanResBuildingEntityPB(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<ClanResBuildingEntityPB> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<ClanResBuildingEntityPB> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.ClanResBuildingPB.ClanResBuildingEntityPB getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_ClanResBuildingEntityPB_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n4cs_proto/gen/clanResBuilding/clanResBu" +
      "ildingPB.proto\022\017com.yorha.proto\032\"cs_prot" +
      "o/gen/common/structPB.proto\032\'cs_proto/ge" +
      "n/common/struct_clanPB.proto\032)cs_proto/g" +
      "en/common/struct_commonPB.proto\032%ss_prot" +
      "o/gen/common/common_enum.proto\"\213\003\n\027ClanR" +
      "esBuildingEntityPB\022\020\n\010entityId\030\001 \001(\003\022\022\n\n" +
      "templateId\030\002 \001(\005\0224\n\005state\030\003 \001(\0162%.com.yo" +
      "rha.proto.ClanResBuildingStage\022\'\n\005point\030" +
      "\004 \001(\0132\030.com.yorha.proto.PointPB\022\016\n\006clanI" +
      "d\030\005 \001(\003\022\032\n\022showClanSimpleName\030\006 \001(\t\022\024\n\014s" +
      "howClanName\030\007 \001(\t\0225\n\014progressInfo\030\n \001(\0132" +
      "\037.com.yorha.proto.ProgressInfoPB\0221\n\010flag" +
      "Info\030\013 \001(\0132\037.com.yorha.proto.ClanFlagInf" +
      "oPB\022\031\n\021currentCollectNum\030\014 \001(\005\022\024\n\014isColl" +
      "ecting\030\016 \001(\010\022\016\n\006zoneId\030\017 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructPB.getDescriptor(),
          com.yorha.proto.StructClanPB.getDescriptor(),
          com.yorha.proto.StructCommonPB.getDescriptor(),
          com.yorha.proto.CommonEnum.getDescriptor(),
        });
    internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_ClanResBuildingEntityPB_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_ClanResBuildingEntityPB_descriptor,
        new java.lang.String[] { "EntityId", "TemplateId", "State", "Point", "ClanId", "ShowClanSimpleName", "ShowClanName", "ProgressInfo", "FlagInfo", "CurrentCollectNum", "IsCollecting", "ZoneId", });
    com.yorha.proto.StructPB.getDescriptor();
    com.yorha.proto.StructClanPB.getDescriptor();
    com.yorha.proto.StructCommonPB.getDescriptor();
    com.yorha.proto.CommonEnum.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

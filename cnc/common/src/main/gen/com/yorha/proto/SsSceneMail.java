// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: ss_proto/gen/scene/ss_scene_mail.proto

package com.yorha.proto;

public final class SsSceneMail {
  private SsSceneMail() {}
  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistryLite registry) {
  }

  public static void registerAllExtensions(
      com.google.protobuf.ExtensionRegistry registry) {
    registerAllExtensions(
        (com.google.protobuf.ExtensionRegistryLite) registry);
  }
  public interface SendZoneMailCmdOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SendZoneMailCmd)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     * @return Whether the mailSendParams field is set.
     */
    boolean hasMailSendParams();
    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     * @return The mailSendParams.
     */
    com.yorha.proto.StructMail.MailSendParams getMailSendParams();
    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     */
    com.yorha.proto.StructMail.MailSendParamsOrBuilder getMailSendParamsOrBuilder();

    /**
     * <code>optional int64 mailId = 2;</code>
     * @return Whether the mailId field is set.
     */
    boolean hasMailId();
    /**
     * <code>optional int64 mailId = 2;</code>
     * @return The mailId.
     */
    long getMailId();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SendZoneMailCmd}
   */
  public static final class SendZoneMailCmd extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SendZoneMailCmd)
      SendZoneMailCmdOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SendZoneMailCmd.newBuilder() to construct.
    private SendZoneMailCmd(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SendZoneMailCmd() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SendZoneMailCmd();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SendZoneMailCmd(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructMail.MailSendParams.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = mailSendParams_.toBuilder();
              }
              mailSendParams_ = input.readMessage(com.yorha.proto.StructMail.MailSendParams.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(mailSendParams_);
                mailSendParams_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              mailId_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SendZoneMailCmd_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SendZoneMailCmd_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMail.SendZoneMailCmd.class, com.yorha.proto.SsSceneMail.SendZoneMailCmd.Builder.class);
    }

    private int bitField0_;
    public static final int MAILSENDPARAMS_FIELD_NUMBER = 1;
    private com.yorha.proto.StructMail.MailSendParams mailSendParams_;
    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     * @return Whether the mailSendParams field is set.
     */
    @java.lang.Override
    public boolean hasMailSendParams() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     * @return The mailSendParams.
     */
    @java.lang.Override
    public com.yorha.proto.StructMail.MailSendParams getMailSendParams() {
      return mailSendParams_ == null ? com.yorha.proto.StructMail.MailSendParams.getDefaultInstance() : mailSendParams_;
    }
    /**
     * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMail.MailSendParamsOrBuilder getMailSendParamsOrBuilder() {
      return mailSendParams_ == null ? com.yorha.proto.StructMail.MailSendParams.getDefaultInstance() : mailSendParams_;
    }

    public static final int MAILID_FIELD_NUMBER = 2;
    private long mailId_;
    /**
     * <code>optional int64 mailId = 2;</code>
     * @return Whether the mailId field is set.
     */
    @java.lang.Override
    public boolean hasMailId() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int64 mailId = 2;</code>
     * @return The mailId.
     */
    @java.lang.Override
    public long getMailId() {
      return mailId_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getMailSendParams());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, mailId_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getMailSendParams());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, mailId_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMail.SendZoneMailCmd)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMail.SendZoneMailCmd other = (com.yorha.proto.SsSceneMail.SendZoneMailCmd) obj;

      if (hasMailSendParams() != other.hasMailSendParams()) return false;
      if (hasMailSendParams()) {
        if (!getMailSendParams()
            .equals(other.getMailSendParams())) return false;
      }
      if (hasMailId() != other.hasMailId()) return false;
      if (hasMailId()) {
        if (getMailId()
            != other.getMailId()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasMailSendParams()) {
        hash = (37 * hash) + MAILSENDPARAMS_FIELD_NUMBER;
        hash = (53 * hash) + getMailSendParams().hashCode();
      }
      if (hasMailId()) {
        hash = (37 * hash) + MAILID_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getMailId());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMail.SendZoneMailCmd prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SendZoneMailCmd}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SendZoneMailCmd)
        com.yorha.proto.SsSceneMail.SendZoneMailCmdOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SendZoneMailCmd_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SendZoneMailCmd_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMail.SendZoneMailCmd.class, com.yorha.proto.SsSceneMail.SendZoneMailCmd.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMail.SendZoneMailCmd.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getMailSendParamsFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (mailSendParamsBuilder_ == null) {
          mailSendParams_ = null;
        } else {
          mailSendParamsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        mailId_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SendZoneMailCmd_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SendZoneMailCmd getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMail.SendZoneMailCmd.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SendZoneMailCmd build() {
        com.yorha.proto.SsSceneMail.SendZoneMailCmd result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SendZoneMailCmd buildPartial() {
        com.yorha.proto.SsSceneMail.SendZoneMailCmd result = new com.yorha.proto.SsSceneMail.SendZoneMailCmd(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (mailSendParamsBuilder_ == null) {
            result.mailSendParams_ = mailSendParams_;
          } else {
            result.mailSendParams_ = mailSendParamsBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.mailId_ = mailId_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMail.SendZoneMailCmd) {
          return mergeFrom((com.yorha.proto.SsSceneMail.SendZoneMailCmd)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMail.SendZoneMailCmd other) {
        if (other == com.yorha.proto.SsSceneMail.SendZoneMailCmd.getDefaultInstance()) return this;
        if (other.hasMailSendParams()) {
          mergeMailSendParams(other.getMailSendParams());
        }
        if (other.hasMailId()) {
          setMailId(other.getMailId());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMail.SendZoneMailCmd parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMail.SendZoneMailCmd) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMail.MailSendParams mailSendParams_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMail.MailSendParams, com.yorha.proto.StructMail.MailSendParams.Builder, com.yorha.proto.StructMail.MailSendParamsOrBuilder> mailSendParamsBuilder_;
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       * @return Whether the mailSendParams field is set.
       */
      public boolean hasMailSendParams() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       * @return The mailSendParams.
       */
      public com.yorha.proto.StructMail.MailSendParams getMailSendParams() {
        if (mailSendParamsBuilder_ == null) {
          return mailSendParams_ == null ? com.yorha.proto.StructMail.MailSendParams.getDefaultInstance() : mailSendParams_;
        } else {
          return mailSendParamsBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public Builder setMailSendParams(com.yorha.proto.StructMail.MailSendParams value) {
        if (mailSendParamsBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          mailSendParams_ = value;
          onChanged();
        } else {
          mailSendParamsBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public Builder setMailSendParams(
          com.yorha.proto.StructMail.MailSendParams.Builder builderForValue) {
        if (mailSendParamsBuilder_ == null) {
          mailSendParams_ = builderForValue.build();
          onChanged();
        } else {
          mailSendParamsBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public Builder mergeMailSendParams(com.yorha.proto.StructMail.MailSendParams value) {
        if (mailSendParamsBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              mailSendParams_ != null &&
              mailSendParams_ != com.yorha.proto.StructMail.MailSendParams.getDefaultInstance()) {
            mailSendParams_ =
              com.yorha.proto.StructMail.MailSendParams.newBuilder(mailSendParams_).mergeFrom(value).buildPartial();
          } else {
            mailSendParams_ = value;
          }
          onChanged();
        } else {
          mailSendParamsBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public Builder clearMailSendParams() {
        if (mailSendParamsBuilder_ == null) {
          mailSendParams_ = null;
          onChanged();
        } else {
          mailSendParamsBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public com.yorha.proto.StructMail.MailSendParams.Builder getMailSendParamsBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getMailSendParamsFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      public com.yorha.proto.StructMail.MailSendParamsOrBuilder getMailSendParamsOrBuilder() {
        if (mailSendParamsBuilder_ != null) {
          return mailSendParamsBuilder_.getMessageOrBuilder();
        } else {
          return mailSendParams_ == null ?
              com.yorha.proto.StructMail.MailSendParams.getDefaultInstance() : mailSendParams_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.MailSendParams mailSendParams = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMail.MailSendParams, com.yorha.proto.StructMail.MailSendParams.Builder, com.yorha.proto.StructMail.MailSendParamsOrBuilder> 
          getMailSendParamsFieldBuilder() {
        if (mailSendParamsBuilder_ == null) {
          mailSendParamsBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMail.MailSendParams, com.yorha.proto.StructMail.MailSendParams.Builder, com.yorha.proto.StructMail.MailSendParamsOrBuilder>(
                  getMailSendParams(),
                  getParentForChildren(),
                  isClean());
          mailSendParams_ = null;
        }
        return mailSendParamsBuilder_;
      }

      private long mailId_ ;
      /**
       * <code>optional int64 mailId = 2;</code>
       * @return Whether the mailId field is set.
       */
      @java.lang.Override
      public boolean hasMailId() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int64 mailId = 2;</code>
       * @return The mailId.
       */
      @java.lang.Override
      public long getMailId() {
        return mailId_;
      }
      /**
       * <code>optional int64 mailId = 2;</code>
       * @param value The mailId to set.
       * @return This builder for chaining.
       */
      public Builder setMailId(long value) {
        bitField0_ |= 0x00000002;
        mailId_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int64 mailId = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearMailId() {
        bitField0_ = (bitField0_ & ~0x00000002);
        mailId_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SendZoneMailCmd)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SendZoneMailCmd)
    private static final com.yorha.proto.SsSceneMail.SendZoneMailCmd DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMail.SendZoneMailCmd();
    }

    public static com.yorha.proto.SsSceneMail.SendZoneMailCmd getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SendZoneMailCmd>
        PARSER = new com.google.protobuf.AbstractParser<SendZoneMailCmd>() {
      @java.lang.Override
      public SendZoneMailCmd parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SendZoneMailCmd(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SendZoneMailCmd> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SendZoneMailCmd> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMail.SendZoneMailCmd getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SyncPlayerOfflineMailsAskOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncPlayerOfflineMailsAsk)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <pre>
     * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
     * </pre>
     *
     * <code>optional int32 zoneMailIndex = 1;</code>
     * @return Whether the zoneMailIndex field is set.
     */
    boolean hasZoneMailIndex();
    /**
     * <pre>
     * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
     * </pre>
     *
     * <code>optional int32 zoneMailIndex = 1;</code>
     * @return The zoneMailIndex.
     */
    int getZoneMailIndex();

    /**
     * <pre>
     * 创角时间
     * </pre>
     *
     * <code>optional int64 createTime = 2;</code>
     * @return Whether the createTime field is set.
     */
    boolean hasCreateTime();
    /**
     * <pre>
     * 创角时间
     * </pre>
     *
     * <code>optional int64 createTime = 2;</code>
     * @return The createTime.
     */
    long getCreateTime();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncPlayerOfflineMailsAsk}
   */
  public static final class SyncPlayerOfflineMailsAsk extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncPlayerOfflineMailsAsk)
      SyncPlayerOfflineMailsAskOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncPlayerOfflineMailsAsk.newBuilder() to construct.
    private SyncPlayerOfflineMailsAsk(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncPlayerOfflineMailsAsk() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncPlayerOfflineMailsAsk();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncPlayerOfflineMailsAsk(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 8: {
              bitField0_ |= 0x00000001;
              zoneMailIndex_ = input.readInt32();
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              createTime_ = input.readInt64();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.class, com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.Builder.class);
    }

    private int bitField0_;
    public static final int ZONEMAILINDEX_FIELD_NUMBER = 1;
    private int zoneMailIndex_;
    /**
     * <pre>
     * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
     * </pre>
     *
     * <code>optional int32 zoneMailIndex = 1;</code>
     * @return Whether the zoneMailIndex field is set.
     */
    @java.lang.Override
    public boolean hasZoneMailIndex() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <pre>
     * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
     * </pre>
     *
     * <code>optional int32 zoneMailIndex = 1;</code>
     * @return The zoneMailIndex.
     */
    @java.lang.Override
    public int getZoneMailIndex() {
      return zoneMailIndex_;
    }

    public static final int CREATETIME_FIELD_NUMBER = 2;
    private long createTime_;
    /**
     * <pre>
     * 创角时间
     * </pre>
     *
     * <code>optional int64 createTime = 2;</code>
     * @return Whether the createTime field is set.
     */
    @java.lang.Override
    public boolean hasCreateTime() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <pre>
     * 创角时间
     * </pre>
     *
     * <code>optional int64 createTime = 2;</code>
     * @return The createTime.
     */
    @java.lang.Override
    public long getCreateTime() {
      return createTime_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeInt32(1, zoneMailIndex_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt64(2, createTime_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(1, zoneMailIndex_);
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt64Size(2, createTime_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk other = (com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk) obj;

      if (hasZoneMailIndex() != other.hasZoneMailIndex()) return false;
      if (hasZoneMailIndex()) {
        if (getZoneMailIndex()
            != other.getZoneMailIndex()) return false;
      }
      if (hasCreateTime() != other.hasCreateTime()) return false;
      if (hasCreateTime()) {
        if (getCreateTime()
            != other.getCreateTime()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasZoneMailIndex()) {
        hash = (37 * hash) + ZONEMAILINDEX_FIELD_NUMBER;
        hash = (53 * hash) + getZoneMailIndex();
      }
      if (hasCreateTime()) {
        hash = (37 * hash) + CREATETIME_FIELD_NUMBER;
        hash = (53 * hash) + com.google.protobuf.Internal.hashLong(
            getCreateTime());
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncPlayerOfflineMailsAsk}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncPlayerOfflineMailsAsk)
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAskOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.class, com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        zoneMailIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000001);
        createTime_ = 0L;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk build() {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk buildPartial() {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk result = new com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          result.zoneMailIndex_ = zoneMailIndex_;
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.createTime_ = createTime_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk) {
          return mergeFrom((com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk other) {
        if (other == com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk.getDefaultInstance()) return this;
        if (other.hasZoneMailIndex()) {
          setZoneMailIndex(other.getZoneMailIndex());
        }
        if (other.hasCreateTime()) {
          setCreateTime(other.getCreateTime());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private int zoneMailIndex_ ;
      /**
       * <pre>
       * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
       * </pre>
       *
       * <code>optional int32 zoneMailIndex = 1;</code>
       * @return Whether the zoneMailIndex field is set.
       */
      @java.lang.Override
      public boolean hasZoneMailIndex() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <pre>
       * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
       * </pre>
       *
       * <code>optional int32 zoneMailIndex = 1;</code>
       * @return The zoneMailIndex.
       */
      @java.lang.Override
      public int getZoneMailIndex() {
        return zoneMailIndex_;
      }
      /**
       * <pre>
       * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
       * </pre>
       *
       * <code>optional int32 zoneMailIndex = 1;</code>
       * @param value The zoneMailIndex to set.
       * @return This builder for chaining.
       */
      public Builder setZoneMailIndex(int value) {
        bitField0_ |= 0x00000001;
        zoneMailIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 玩家已经收取到的全服邮件下标, &lt; 0 由邮件Actor决定下发哪些邮件。
       * </pre>
       *
       * <code>optional int32 zoneMailIndex = 1;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneMailIndex() {
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneMailIndex_ = 0;
        onChanged();
        return this;
      }

      private long createTime_ ;
      /**
       * <pre>
       * 创角时间
       * </pre>
       *
       * <code>optional int64 createTime = 2;</code>
       * @return Whether the createTime field is set.
       */
      @java.lang.Override
      public boolean hasCreateTime() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <pre>
       * 创角时间
       * </pre>
       *
       * <code>optional int64 createTime = 2;</code>
       * @return The createTime.
       */
      @java.lang.Override
      public long getCreateTime() {
        return createTime_;
      }
      /**
       * <pre>
       * 创角时间
       * </pre>
       *
       * <code>optional int64 createTime = 2;</code>
       * @param value The createTime to set.
       * @return This builder for chaining.
       */
      public Builder setCreateTime(long value) {
        bitField0_ |= 0x00000002;
        createTime_ = value;
        onChanged();
        return this;
      }
      /**
       * <pre>
       * 创角时间
       * </pre>
       *
       * <code>optional int64 createTime = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearCreateTime() {
        bitField0_ = (bitField0_ & ~0x00000002);
        createTime_ = 0L;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncPlayerOfflineMailsAsk)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncPlayerOfflineMailsAsk)
    private static final com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk();
    }

    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncPlayerOfflineMailsAsk>
        PARSER = new com.google.protobuf.AbstractParser<SyncPlayerOfflineMailsAsk>() {
      @java.lang.Override
      public SyncPlayerOfflineMailsAsk parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncPlayerOfflineMailsAsk(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncPlayerOfflineMailsAsk> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncPlayerOfflineMailsAsk> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAsk getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  public interface SyncPlayerOfflineMailsAnsOrBuilder extends
      // @@protoc_insertion_point(interface_extends:com.yorha.proto.SyncPlayerOfflineMailsAns)
      com.google.protobuf.MessageOrBuilder {

    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     * @return Whether the newMailCacheList field is set.
     */
    boolean hasNewMailCacheList();
    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     * @return The newMailCacheList.
     */
    com.yorha.proto.StructMail.NewMailCacheList getNewMailCacheList();
    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     */
    com.yorha.proto.StructMail.NewMailCacheListOrBuilder getNewMailCacheListOrBuilder();

    /**
     * <code>optional int32 zoneMailIndex = 2;</code>
     * @return Whether the zoneMailIndex field is set.
     */
    boolean hasZoneMailIndex();
    /**
     * <code>optional int32 zoneMailIndex = 2;</code>
     * @return The zoneMailIndex.
     */
    int getZoneMailIndex();
  }
  /**
   * Protobuf type {@code com.yorha.proto.SyncPlayerOfflineMailsAns}
   */
  public static final class SyncPlayerOfflineMailsAns extends
      com.google.protobuf.GeneratedMessageV3 implements
      // @@protoc_insertion_point(message_implements:com.yorha.proto.SyncPlayerOfflineMailsAns)
      SyncPlayerOfflineMailsAnsOrBuilder {
  private static final long serialVersionUID = 0L;
    // Use SyncPlayerOfflineMailsAns.newBuilder() to construct.
    private SyncPlayerOfflineMailsAns(com.google.protobuf.GeneratedMessageV3.Builder<?> builder) {
      super(builder);
    }
    private SyncPlayerOfflineMailsAns() {
    }

    @java.lang.Override
    @SuppressWarnings({"unused"})
    protected java.lang.Object newInstance(
        UnusedPrivateParameter unused) {
      return new SyncPlayerOfflineMailsAns();
    }

    @java.lang.Override
    public final com.google.protobuf.UnknownFieldSet
    getUnknownFields() {
      return this.unknownFields;
    }
    private SyncPlayerOfflineMailsAns(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      this();
      if (extensionRegistry == null) {
        throw new java.lang.NullPointerException();
      }
      int mutable_bitField0_ = 0;
      com.google.protobuf.UnknownFieldSet.Builder unknownFields =
          com.google.protobuf.UnknownFieldSet.newBuilder();
      try {
        boolean done = false;
        while (!done) {
          int tag = input.readTag();
          switch (tag) {
            case 0:
              done = true;
              break;
            case 10: {
              com.yorha.proto.StructMail.NewMailCacheList.Builder subBuilder = null;
              if (((bitField0_ & 0x00000001) != 0)) {
                subBuilder = newMailCacheList_.toBuilder();
              }
              newMailCacheList_ = input.readMessage(com.yorha.proto.StructMail.NewMailCacheList.PARSER, extensionRegistry);
              if (subBuilder != null) {
                subBuilder.mergeFrom(newMailCacheList_);
                newMailCacheList_ = subBuilder.buildPartial();
              }
              bitField0_ |= 0x00000001;
              break;
            }
            case 16: {
              bitField0_ |= 0x00000002;
              zoneMailIndex_ = input.readInt32();
              break;
            }
            default: {
              if (!parseUnknownField(
                  input, unknownFields, extensionRegistry, tag)) {
                done = true;
              }
              break;
            }
          }
        }
      } catch (com.google.protobuf.InvalidProtocolBufferException e) {
        throw e.setUnfinishedMessage(this);
      } catch (java.io.IOException e) {
        throw new com.google.protobuf.InvalidProtocolBufferException(
            e).setUnfinishedMessage(this);
      } finally {
        this.unknownFields = unknownFields.build();
        makeExtensionsImmutable();
      }
    }
    public static final com.google.protobuf.Descriptors.Descriptor
        getDescriptor() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor;
    }

    @java.lang.Override
    protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
        internalGetFieldAccessorTable() {
      return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_fieldAccessorTable
          .ensureFieldAccessorsInitialized(
              com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.class, com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.Builder.class);
    }

    private int bitField0_;
    public static final int NEWMAILCACHELIST_FIELD_NUMBER = 1;
    private com.yorha.proto.StructMail.NewMailCacheList newMailCacheList_;
    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     * @return Whether the newMailCacheList field is set.
     */
    @java.lang.Override
    public boolean hasNewMailCacheList() {
      return ((bitField0_ & 0x00000001) != 0);
    }
    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     * @return The newMailCacheList.
     */
    @java.lang.Override
    public com.yorha.proto.StructMail.NewMailCacheList getNewMailCacheList() {
      return newMailCacheList_ == null ? com.yorha.proto.StructMail.NewMailCacheList.getDefaultInstance() : newMailCacheList_;
    }
    /**
     * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
     */
    @java.lang.Override
    public com.yorha.proto.StructMail.NewMailCacheListOrBuilder getNewMailCacheListOrBuilder() {
      return newMailCacheList_ == null ? com.yorha.proto.StructMail.NewMailCacheList.getDefaultInstance() : newMailCacheList_;
    }

    public static final int ZONEMAILINDEX_FIELD_NUMBER = 2;
    private int zoneMailIndex_;
    /**
     * <code>optional int32 zoneMailIndex = 2;</code>
     * @return Whether the zoneMailIndex field is set.
     */
    @java.lang.Override
    public boolean hasZoneMailIndex() {
      return ((bitField0_ & 0x00000002) != 0);
    }
    /**
     * <code>optional int32 zoneMailIndex = 2;</code>
     * @return The zoneMailIndex.
     */
    @java.lang.Override
    public int getZoneMailIndex() {
      return zoneMailIndex_;
    }

    private byte memoizedIsInitialized = -1;
    @java.lang.Override
    public final boolean isInitialized() {
      byte isInitialized = memoizedIsInitialized;
      if (isInitialized == 1) return true;
      if (isInitialized == 0) return false;

      memoizedIsInitialized = 1;
      return true;
    }

    @java.lang.Override
    public void writeTo(com.google.protobuf.CodedOutputStream output)
                        throws java.io.IOException {
      if (((bitField0_ & 0x00000001) != 0)) {
        output.writeMessage(1, getNewMailCacheList());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        output.writeInt32(2, zoneMailIndex_);
      }
      unknownFields.writeTo(output);
    }

    @java.lang.Override
    public int getSerializedSize() {
      int size = memoizedSize;
      if (size != -1) return size;

      size = 0;
      if (((bitField0_ & 0x00000001) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeMessageSize(1, getNewMailCacheList());
      }
      if (((bitField0_ & 0x00000002) != 0)) {
        size += com.google.protobuf.CodedOutputStream
          .computeInt32Size(2, zoneMailIndex_);
      }
      size += unknownFields.getSerializedSize();
      memoizedSize = size;
      return size;
    }

    @java.lang.Override
    public boolean equals(final java.lang.Object obj) {
      if (obj == this) {
       return true;
      }
      if (!(obj instanceof com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns)) {
        return super.equals(obj);
      }
      com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns other = (com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns) obj;

      if (hasNewMailCacheList() != other.hasNewMailCacheList()) return false;
      if (hasNewMailCacheList()) {
        if (!getNewMailCacheList()
            .equals(other.getNewMailCacheList())) return false;
      }
      if (hasZoneMailIndex() != other.hasZoneMailIndex()) return false;
      if (hasZoneMailIndex()) {
        if (getZoneMailIndex()
            != other.getZoneMailIndex()) return false;
      }
      if (!unknownFields.equals(other.unknownFields)) return false;
      return true;
    }

    @java.lang.Override
    public int hashCode() {
      if (memoizedHashCode != 0) {
        return memoizedHashCode;
      }
      int hash = 41;
      hash = (19 * hash) + getDescriptor().hashCode();
      if (hasNewMailCacheList()) {
        hash = (37 * hash) + NEWMAILCACHELIST_FIELD_NUMBER;
        hash = (53 * hash) + getNewMailCacheList().hashCode();
      }
      if (hasZoneMailIndex()) {
        hash = (37 * hash) + ZONEMAILINDEX_FIELD_NUMBER;
        hash = (53 * hash) + getZoneMailIndex();
      }
      hash = (29 * hash) + unknownFields.hashCode();
      memoizedHashCode = hash;
      return hash;
    }

    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        java.nio.ByteBuffer data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        java.nio.ByteBuffer data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        com.google.protobuf.ByteString data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        com.google.protobuf.ByteString data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(byte[] data)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        byte[] data,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws com.google.protobuf.InvalidProtocolBufferException {
      return PARSER.parseFrom(data, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseDelimitedFrom(java.io.InputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseDelimitedFrom(
        java.io.InputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseDelimitedWithIOException(PARSER, input, extensionRegistry);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        com.google.protobuf.CodedInputStream input)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input);
    }
    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parseFrom(
        com.google.protobuf.CodedInputStream input,
        com.google.protobuf.ExtensionRegistryLite extensionRegistry)
        throws java.io.IOException {
      return com.google.protobuf.GeneratedMessageV3
          .parseWithIOException(PARSER, input, extensionRegistry);
    }

    @java.lang.Override
    public Builder newBuilderForType() { return newBuilder(); }
    public static Builder newBuilder() {
      return DEFAULT_INSTANCE.toBuilder();
    }
    public static Builder newBuilder(com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns prototype) {
      return DEFAULT_INSTANCE.toBuilder().mergeFrom(prototype);
    }
    @java.lang.Override
    public Builder toBuilder() {
      return this == DEFAULT_INSTANCE
          ? new Builder() : new Builder().mergeFrom(this);
    }

    @java.lang.Override
    protected Builder newBuilderForType(
        com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
      Builder builder = new Builder(parent);
      return builder;
    }
    /**
     * Protobuf type {@code com.yorha.proto.SyncPlayerOfflineMailsAns}
     */
    public static final class Builder extends
        com.google.protobuf.GeneratedMessageV3.Builder<Builder> implements
        // @@protoc_insertion_point(builder_implements:com.yorha.proto.SyncPlayerOfflineMailsAns)
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAnsOrBuilder {
      public static final com.google.protobuf.Descriptors.Descriptor
          getDescriptor() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor;
      }

      @java.lang.Override
      protected com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
          internalGetFieldAccessorTable() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_fieldAccessorTable
            .ensureFieldAccessorsInitialized(
                com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.class, com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.Builder.class);
      }

      // Construct using com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.newBuilder()
      private Builder() {
        maybeForceBuilderInitialization();
      }

      private Builder(
          com.google.protobuf.GeneratedMessageV3.BuilderParent parent) {
        super(parent);
        maybeForceBuilderInitialization();
      }
      private void maybeForceBuilderInitialization() {
        if (com.google.protobuf.GeneratedMessageV3
                .alwaysUseFieldBuilders) {
          getNewMailCacheListFieldBuilder();
        }
      }
      @java.lang.Override
      public Builder clear() {
        super.clear();
        if (newMailCacheListBuilder_ == null) {
          newMailCacheList_ = null;
        } else {
          newMailCacheListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        zoneMailIndex_ = 0;
        bitField0_ = (bitField0_ & ~0x00000002);
        return this;
      }

      @java.lang.Override
      public com.google.protobuf.Descriptors.Descriptor
          getDescriptorForType() {
        return com.yorha.proto.SsSceneMail.internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns getDefaultInstanceForType() {
        return com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.getDefaultInstance();
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns build() {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns result = buildPartial();
        if (!result.isInitialized()) {
          throw newUninitializedMessageException(result);
        }
        return result;
      }

      @java.lang.Override
      public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns buildPartial() {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns result = new com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns(this);
        int from_bitField0_ = bitField0_;
        int to_bitField0_ = 0;
        if (((from_bitField0_ & 0x00000001) != 0)) {
          if (newMailCacheListBuilder_ == null) {
            result.newMailCacheList_ = newMailCacheList_;
          } else {
            result.newMailCacheList_ = newMailCacheListBuilder_.build();
          }
          to_bitField0_ |= 0x00000001;
        }
        if (((from_bitField0_ & 0x00000002) != 0)) {
          result.zoneMailIndex_ = zoneMailIndex_;
          to_bitField0_ |= 0x00000002;
        }
        result.bitField0_ = to_bitField0_;
        onBuilt();
        return result;
      }

      @java.lang.Override
      public Builder clone() {
        return super.clone();
      }
      @java.lang.Override
      public Builder setField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.setField(field, value);
      }
      @java.lang.Override
      public Builder clearField(
          com.google.protobuf.Descriptors.FieldDescriptor field) {
        return super.clearField(field);
      }
      @java.lang.Override
      public Builder clearOneof(
          com.google.protobuf.Descriptors.OneofDescriptor oneof) {
        return super.clearOneof(oneof);
      }
      @java.lang.Override
      public Builder setRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          int index, java.lang.Object value) {
        return super.setRepeatedField(field, index, value);
      }
      @java.lang.Override
      public Builder addRepeatedField(
          com.google.protobuf.Descriptors.FieldDescriptor field,
          java.lang.Object value) {
        return super.addRepeatedField(field, value);
      }
      @java.lang.Override
      public Builder mergeFrom(com.google.protobuf.Message other) {
        if (other instanceof com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns) {
          return mergeFrom((com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns)other);
        } else {
          super.mergeFrom(other);
          return this;
        }
      }

      public Builder mergeFrom(com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns other) {
        if (other == com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns.getDefaultInstance()) return this;
        if (other.hasNewMailCacheList()) {
          mergeNewMailCacheList(other.getNewMailCacheList());
        }
        if (other.hasZoneMailIndex()) {
          setZoneMailIndex(other.getZoneMailIndex());
        }
        this.mergeUnknownFields(other.unknownFields);
        onChanged();
        return this;
      }

      @java.lang.Override
      public final boolean isInitialized() {
        return true;
      }

      @java.lang.Override
      public Builder mergeFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws java.io.IOException {
        com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns parsedMessage = null;
        try {
          parsedMessage = PARSER.parsePartialFrom(input, extensionRegistry);
        } catch (com.google.protobuf.InvalidProtocolBufferException e) {
          parsedMessage = (com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns) e.getUnfinishedMessage();
          throw e.unwrapIOException();
        } finally {
          if (parsedMessage != null) {
            mergeFrom(parsedMessage);
          }
        }
        return this;
      }
      private int bitField0_;

      private com.yorha.proto.StructMail.NewMailCacheList newMailCacheList_;
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMail.NewMailCacheList, com.yorha.proto.StructMail.NewMailCacheList.Builder, com.yorha.proto.StructMail.NewMailCacheListOrBuilder> newMailCacheListBuilder_;
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       * @return Whether the newMailCacheList field is set.
       */
      public boolean hasNewMailCacheList() {
        return ((bitField0_ & 0x00000001) != 0);
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       * @return The newMailCacheList.
       */
      public com.yorha.proto.StructMail.NewMailCacheList getNewMailCacheList() {
        if (newMailCacheListBuilder_ == null) {
          return newMailCacheList_ == null ? com.yorha.proto.StructMail.NewMailCacheList.getDefaultInstance() : newMailCacheList_;
        } else {
          return newMailCacheListBuilder_.getMessage();
        }
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public Builder setNewMailCacheList(com.yorha.proto.StructMail.NewMailCacheList value) {
        if (newMailCacheListBuilder_ == null) {
          if (value == null) {
            throw new NullPointerException();
          }
          newMailCacheList_ = value;
          onChanged();
        } else {
          newMailCacheListBuilder_.setMessage(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public Builder setNewMailCacheList(
          com.yorha.proto.StructMail.NewMailCacheList.Builder builderForValue) {
        if (newMailCacheListBuilder_ == null) {
          newMailCacheList_ = builderForValue.build();
          onChanged();
        } else {
          newMailCacheListBuilder_.setMessage(builderForValue.build());
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public Builder mergeNewMailCacheList(com.yorha.proto.StructMail.NewMailCacheList value) {
        if (newMailCacheListBuilder_ == null) {
          if (((bitField0_ & 0x00000001) != 0) &&
              newMailCacheList_ != null &&
              newMailCacheList_ != com.yorha.proto.StructMail.NewMailCacheList.getDefaultInstance()) {
            newMailCacheList_ =
              com.yorha.proto.StructMail.NewMailCacheList.newBuilder(newMailCacheList_).mergeFrom(value).buildPartial();
          } else {
            newMailCacheList_ = value;
          }
          onChanged();
        } else {
          newMailCacheListBuilder_.mergeFrom(value);
        }
        bitField0_ |= 0x00000001;
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public Builder clearNewMailCacheList() {
        if (newMailCacheListBuilder_ == null) {
          newMailCacheList_ = null;
          onChanged();
        } else {
          newMailCacheListBuilder_.clear();
        }
        bitField0_ = (bitField0_ & ~0x00000001);
        return this;
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public com.yorha.proto.StructMail.NewMailCacheList.Builder getNewMailCacheListBuilder() {
        bitField0_ |= 0x00000001;
        onChanged();
        return getNewMailCacheListFieldBuilder().getBuilder();
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      public com.yorha.proto.StructMail.NewMailCacheListOrBuilder getNewMailCacheListOrBuilder() {
        if (newMailCacheListBuilder_ != null) {
          return newMailCacheListBuilder_.getMessageOrBuilder();
        } else {
          return newMailCacheList_ == null ?
              com.yorha.proto.StructMail.NewMailCacheList.getDefaultInstance() : newMailCacheList_;
        }
      }
      /**
       * <code>optional .com.yorha.proto.NewMailCacheList newMailCacheList = 1;</code>
       */
      private com.google.protobuf.SingleFieldBuilderV3<
          com.yorha.proto.StructMail.NewMailCacheList, com.yorha.proto.StructMail.NewMailCacheList.Builder, com.yorha.proto.StructMail.NewMailCacheListOrBuilder> 
          getNewMailCacheListFieldBuilder() {
        if (newMailCacheListBuilder_ == null) {
          newMailCacheListBuilder_ = new com.google.protobuf.SingleFieldBuilderV3<
              com.yorha.proto.StructMail.NewMailCacheList, com.yorha.proto.StructMail.NewMailCacheList.Builder, com.yorha.proto.StructMail.NewMailCacheListOrBuilder>(
                  getNewMailCacheList(),
                  getParentForChildren(),
                  isClean());
          newMailCacheList_ = null;
        }
        return newMailCacheListBuilder_;
      }

      private int zoneMailIndex_ ;
      /**
       * <code>optional int32 zoneMailIndex = 2;</code>
       * @return Whether the zoneMailIndex field is set.
       */
      @java.lang.Override
      public boolean hasZoneMailIndex() {
        return ((bitField0_ & 0x00000002) != 0);
      }
      /**
       * <code>optional int32 zoneMailIndex = 2;</code>
       * @return The zoneMailIndex.
       */
      @java.lang.Override
      public int getZoneMailIndex() {
        return zoneMailIndex_;
      }
      /**
       * <code>optional int32 zoneMailIndex = 2;</code>
       * @param value The zoneMailIndex to set.
       * @return This builder for chaining.
       */
      public Builder setZoneMailIndex(int value) {
        bitField0_ |= 0x00000002;
        zoneMailIndex_ = value;
        onChanged();
        return this;
      }
      /**
       * <code>optional int32 zoneMailIndex = 2;</code>
       * @return This builder for chaining.
       */
      public Builder clearZoneMailIndex() {
        bitField0_ = (bitField0_ & ~0x00000002);
        zoneMailIndex_ = 0;
        onChanged();
        return this;
      }
      @java.lang.Override
      public final Builder setUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.setUnknownFields(unknownFields);
      }

      @java.lang.Override
      public final Builder mergeUnknownFields(
          final com.google.protobuf.UnknownFieldSet unknownFields) {
        return super.mergeUnknownFields(unknownFields);
      }


      // @@protoc_insertion_point(builder_scope:com.yorha.proto.SyncPlayerOfflineMailsAns)
    }

    // @@protoc_insertion_point(class_scope:com.yorha.proto.SyncPlayerOfflineMailsAns)
    private static final com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns DEFAULT_INSTANCE;
    static {
      DEFAULT_INSTANCE = new com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns();
    }

    public static com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns getDefaultInstance() {
      return DEFAULT_INSTANCE;
    }

    @java.lang.Deprecated public static final com.google.protobuf.Parser<SyncPlayerOfflineMailsAns>
        PARSER = new com.google.protobuf.AbstractParser<SyncPlayerOfflineMailsAns>() {
      @java.lang.Override
      public SyncPlayerOfflineMailsAns parsePartialFrom(
          com.google.protobuf.CodedInputStream input,
          com.google.protobuf.ExtensionRegistryLite extensionRegistry)
          throws com.google.protobuf.InvalidProtocolBufferException {
        return new SyncPlayerOfflineMailsAns(input, extensionRegistry);
      }
    };

    public static com.google.protobuf.Parser<SyncPlayerOfflineMailsAns> parser() {
      return PARSER;
    }

    @java.lang.Override
    public com.google.protobuf.Parser<SyncPlayerOfflineMailsAns> getParserForType() {
      return PARSER;
    }

    @java.lang.Override
    public com.yorha.proto.SsSceneMail.SyncPlayerOfflineMailsAns getDefaultInstanceForType() {
      return DEFAULT_INSTANCE;
    }

  }

  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SendZoneMailCmd_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SendZoneMailCmd_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_fieldAccessorTable;
  private static final com.google.protobuf.Descriptors.Descriptor
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor;
  private static final 
    com.google.protobuf.GeneratedMessageV3.FieldAccessorTable
      internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_fieldAccessorTable;

  public static com.google.protobuf.Descriptors.FileDescriptor
      getDescriptor() {
    return descriptor;
  }
  private static  com.google.protobuf.Descriptors.FileDescriptor
      descriptor;
  static {
    java.lang.String[] descriptorData = {
      "\n&ss_proto/gen/scene/ss_scene_mail.proto" +
      "\022\017com.yorha.proto\032%ss_proto/gen/common/s" +
      "truct_mail.proto\"Z\n\017SendZoneMailCmd\0227\n\016m" +
      "ailSendParams\030\001 \001(\0132\037.com.yorha.proto.Ma" +
      "ilSendParams\022\016\n\006mailId\030\002 \001(\003\"F\n\031SyncPlay" +
      "erOfflineMailsAsk\022\025\n\rzoneMailIndex\030\001 \001(\005" +
      "\022\022\n\ncreateTime\030\002 \001(\003\"o\n\031SyncPlayerOfflin" +
      "eMailsAns\022;\n\020newMailCacheList\030\001 \001(\0132!.co" +
      "m.yorha.proto.NewMailCacheList\022\025\n\rzoneMa" +
      "ilIndex\030\002 \001(\005B\002H\001"
    };
    descriptor = com.google.protobuf.Descriptors.FileDescriptor
      .internalBuildGeneratedFileFrom(descriptorData,
        new com.google.protobuf.Descriptors.FileDescriptor[] {
          com.yorha.proto.StructMail.getDescriptor(),
        });
    internal_static_com_yorha_proto_SendZoneMailCmd_descriptor =
      getDescriptor().getMessageTypes().get(0);
    internal_static_com_yorha_proto_SendZoneMailCmd_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SendZoneMailCmd_descriptor,
        new java.lang.String[] { "MailSendParams", "MailId", });
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor =
      getDescriptor().getMessageTypes().get(1);
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncPlayerOfflineMailsAsk_descriptor,
        new java.lang.String[] { "ZoneMailIndex", "CreateTime", });
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor =
      getDescriptor().getMessageTypes().get(2);
    internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_fieldAccessorTable = new
      com.google.protobuf.GeneratedMessageV3.FieldAccessorTable(
        internal_static_com_yorha_proto_SyncPlayerOfflineMailsAns_descriptor,
        new java.lang.String[] { "NewMailCacheList", "ZoneMailIndex", });
    com.yorha.proto.StructMail.getDescriptor();
  }

  // @@protoc_insertion_point(outer_class_scope)
}

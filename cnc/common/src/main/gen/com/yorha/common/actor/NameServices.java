package com.yorha.common.actor;

import com.yorha.proto.SsName.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface NameServices {
    Logger LOGGER = LogManager.getLogger(NameServices.class);

    NameService getNameService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.OCCUPYBATCHNAMECMD:
                getNameService().handleOccupyBatchNameCmd((OccupyBatchNameCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RELEASENAMECMD:
                getNameService().handleReleaseNameCmd((ReleaseNameCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SEARCHCLANNAMEASK:
                getNameService().handleSearchClanNameAsk((SearchClanNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SEARCHPLAYERNAMEASK:
                getNameService().handleSearchPlayerNameAsk((SearchPlayerNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SEARCHNAMEMATCHEDASK:
                getNameService().handleSearchNameMatchedAsk((SearchNameMatchedAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsRank.*;

public interface RankService {

    void handleOpenRankAsk(OpenRankAsk ask); // 开放排行榜，rankId -> 配置，仅用于非常驻排行榜，如果数据库有数据会加载而不会新建

    void handleGetTopRankInfoAsk(GetTopRankInfoAsk ask); // 获取排行榜首

    void handleGetRankPageInfoAsk(GetRankPageInfoAsk ask); // 获取排行页面

    void handleRankInfoByRanksAsk(RankInfoByRanksAsk ask); // 查询指定多个排名的玩家

    void handleRankInfoByPlayersAsk(RankInfoByPlayersAsk ask); // 查询指定多个玩家

    void handleGetRankInfoByLimitAsk(GetRankInfoByLimitAsk ask); // 查询指定范围内的排行榜数据

    void handleUpdateRankingCmd(UpdateRankingCmd ask); // 更新个人排行

    void handleDeleteRankingCmd(DeleteRankingCmd ask); // 删除个人排行

    void handleUpdateBatchRankingAsk(UpdateBatchRankingAsk ask); // 批量更新个人排行

    void handleClearRankCmd(ClearRankCmd ask); // 清理排行榜 仅清理 不是删除

    void handleGetRangeAsk(GetRangeAsk ask); // 返回指定Range的玩家数据,仅用于非常驻排行榜

    void handleDeleteRankAndGetTopAsk(DeleteRankAndGetTopAsk ask); // 删除排行榜并返回指定Range的玩家数据，仅用于非常驻排行榜，会清空内存和数据库中的数据

    void handleDeleteAllRankAboutMeCmd(DeleteAllRankAboutMeCmd ask); // 删除所有排行榜中我的信息

    void handleGetAllTargetRankAsk(GetAllTargetRankAsk ask); // 获取所有目标服务新的排名

    void handleGetRankInfoByPlayerAsk(GetRankInfoByPlayerAsk ask); // 查询个人排行

}
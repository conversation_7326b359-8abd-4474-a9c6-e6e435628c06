package com.yorha.common.actor;

import com.yorha.proto.SsActivity.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ActivityServices {
    Logger LOGGER = LogManager.getLogger(ActivityServices.class);

    ActivityService getActivityService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.TAKEACTIVITYREWARDASK:
                getActivityService().handleTakeActivityRewardAsk((TakeActivityRewardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETALLACTIVITYSTATUSASK:
                getActivityService().handleGetAllActivityStatusAsk((GetAllActivityStatusAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.INITACTIVITYACTORASK:
                getActivityService().handleInitActivityActorAsk((InitActivityActorAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
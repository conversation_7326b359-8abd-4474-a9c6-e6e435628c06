package com.yorha.common.actor;

import com.yorha.proto.SsPlayerClan.*;

public interface PlayerClanService {

    void handleClanApplyResultAsk(ClanApplyResultAsk ask);

    void handleOnNtfClanKickOffResultCmd(OnNtfClanKickOffResultCmd ask);

    void handleOnClanAdditionUpdateCmd(OnClanAdditionUpdateCmd ask);

    void handleOnClanDevBuffUpdateCmd(OnClanDevBuffUpdateCmd ask);

    void handleOnAddClanScoreCmd(OnAddClanScoreCmd ask);

    void handleOnAddClanPowerResourceCmd(OnAddClanPowerResourceCmd ask);

    void handleOnClanHelpHappenAsk(OnClanHelpHappenAsk ask);

    void handleOnPlayerNeedClanInfoChangeCmd(OnPlayerNeedClanInfoChangeCmd ask);

    void handleOnClanTerritoryLvChangeCmd(OnClanTerritoryLvChangeCmd ask); // 势力值等级变更，通知所有玩家

}
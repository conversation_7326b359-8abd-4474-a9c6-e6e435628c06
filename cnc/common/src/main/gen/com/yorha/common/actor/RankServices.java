package com.yorha.common.actor;

import com.yorha.proto.SsRank.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface RankServices {
    Logger LOGGER = LogManager.getLogger(RankServices.class);

    RankService getRankService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.OPENRANKASK:
                getRankService().handleOpenRankAsk((OpenRankAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETTOPRANKINFOASK:
                getRankService().handleGetTopRankInfoAsk((GetTopRankInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETRANKPAGEINFOASK:
                getRankService().handleGetRankPageInfoAsk((GetRankPageInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.RANKINFOBYRANKSASK:
                getRankService().handleRankInfoByRanksAsk((RankInfoByRanksAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.RANKINFOBYPLAYERSASK:
                getRankService().handleRankInfoByPlayersAsk((RankInfoByPlayersAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETRANKINFOBYLIMITASK:
                getRankService().handleGetRankInfoByLimitAsk((GetRankInfoByLimitAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATERANKINGCMD:
                getRankService().handleUpdateRankingCmd((UpdateRankingCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.DELETERANKINGCMD:
                getRankService().handleDeleteRankingCmd((DeleteRankingCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEBATCHRANKINGASK:
                getRankService().handleUpdateBatchRankingAsk((UpdateBatchRankingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLEARRANKCMD:
                getRankService().handleClearRankCmd((ClearRankCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETRANGEASK:
                getRankService().handleGetRangeAsk((GetRangeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DELETERANKANDGETTOPASK:
                getRankService().handleDeleteRankAndGetTopAsk((DeleteRankAndGetTopAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DELETEALLRANKABOUTMECMD:
                getRankService().handleDeleteAllRankAboutMeCmd((DeleteAllRankAboutMeCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETALLTARGETRANKASK:
                getRankService().handleGetAllTargetRankAsk((GetAllTargetRankAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETRANKINFOBYPLAYERASK:
                getRankService().handleGetRankInfoByPlayerAsk((GetRankInfoByPlayerAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsMonitor.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface MonitorServices {
    Logger LOGGER = LogManager.getLogger(MonitorServices.class);

    MonitorService getMonitorService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.MONITORDAILYPLAYERNUMCMD:
                getMonitorService().handleMonitorDailyPlayerNumCmd((MonitorDailyPlayerNumCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsSceneCityArmy.*;

public interface SceneCityArmyService {

    void handleRepairCityWallAsk(RepairCityWallAsk ask); // 城墙维修

    void handleOutFireCityWallAsk(OutFireCityWallAsk ask); // 城墙灭火

    void handleSetCityFallAsk(SetCityFallAsk ask); // 城池落地

    void handleMoveCityFixedAsk(MoveCityFixedAsk ask); // 定点迁城

    void handleMoveCityRandomAsk(MoveCityRandomAsk ask); // 随机迁城

    void handleMoveCityVerifyAsk(MoveCityVerifyAsk ask); // 迁城点验证

    void handleCreateArmyCheckAsk(CreateArmyCheckAsk ask); // 创建行军先行检查

    void handleCreatePlayerArmyAsk(CreatePlayerArmyAsk ask); // 创建行军

    void handleChangeArmyActionCheckAsk(ChangeArmyActionCheckAsk ask); // 操控行军先行检查

    void handleChangePlayerArmyActionAsk(ChangePlayerArmyActionAsk ask); // 操控行军

    void handleForcedDefeatArmyAsk(ForcedDefeatArmyAsk ask); // 强制溃败行军

}
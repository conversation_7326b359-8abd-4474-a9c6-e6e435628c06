package com.yorha.common.actor;

import com.yorha.proto.SsClanCard.*;

public interface ClanCardService {

    void handleQueryClanNameAsk(QueryClanNameAsk ask); // 查询联盟名字

    void handleQueryClanSimpleAsk(QueryClanSimpleAsk ask); // 查询联盟简单数据   就是名字+旗帜+盟主名字

    void handleQueryClanCardAsk(QueryClanCardAsk ask); // 查询联盟名片数据

    void handleBatchQueryClanNameAsk(BatchQueryClanNameAsk ask); // 批量查询联盟名字

    void handleBatchQueryClanSimpleAsk(BatchQueryClanSimpleAsk ask); // 批量查询联盟简单数据   就是名字+旗帜+盟主名字

    void handleBatchQueryClanCardAsk(BatchQueryClanCardAsk ask); // 批量查询联盟名片数据

    void handleUpdateClanCardCmd(UpdateClanCardCmd ask); // 更新联盟数据

}
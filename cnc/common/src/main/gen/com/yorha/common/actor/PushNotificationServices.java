package com.yorha.common.actor;

import com.yorha.proto.SsPushNotification.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface PushNotificationServices {
    Logger LOGGER = LogManager.getLogger(PushNotificationServices.class);

    PushNotificationService getPushNotificationService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.PUSHSINGLENOTIFICATIONCMD:
                getPushNotificationService().handlePushSingleNotificationCmd((PushSingleNotificationCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PUSHMULTIPLENOTIFICATIONCMD:
                getPushNotificationService().handlePushMultipleNotificationCmd((PushMultipleNotificationCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PUSHTOPICNOTIFICATIONCMD:
                getPushNotificationService().handlePushTopicNotificationCmd((PushTopicNotificationCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
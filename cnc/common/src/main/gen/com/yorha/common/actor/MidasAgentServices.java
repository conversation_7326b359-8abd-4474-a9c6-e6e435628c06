package com.yorha.common.actor;

import com.yorha.proto.SsMidasAgent.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface MidasAgentServices {
    Logger LOGGER = LogManager.getLogger(MidasAgentServices.class);

    MidasAgentService getMidasAgentService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.MIDASQUERYASK:
                getMidasAgentService().handleMidasQueryAsk((MidasQueryAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MIDASCONSUMEASK:
                getMidasAgentService().handleMidasConsumeAsk((MidasConsumeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MIDASROLLBACKCONSUMEASK:
                getMidasAgentService().handleMidasRollbackConsumeAsk((MidasRollbackConsumeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MIDASPRESENTASK:
                getMidasAgentService().handleMidasPresentAsk((MidasPresentAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MIDASSWITCHSTRESSTESTINGMODECMD:
                getMidasAgentService().handleMidasSwitchStressTestingModeCmd((MidasSwitchStressTestingModeCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
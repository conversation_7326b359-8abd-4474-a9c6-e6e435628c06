package com.yorha.common.actor;

import com.yorha.proto.SsClanActivity.*;
import com.yorha.proto.SsClanAttr.*;
import com.yorha.proto.SsClanBase.*;
import com.yorha.proto.SsClanChat.*;
import com.yorha.proto.SsClanGift.*;
import com.yorha.proto.SsClanHelp.*;
import com.yorha.proto.SsClanMember.*;
import com.yorha.proto.SsClanRank.*;
import com.yorha.proto.SsClanStore.*;
import com.yorha.proto.SsClanTech.*;
import com.yorha.proto.SsClanTerritory.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ClanServices {
    Logger LOGGER = LogManager.getLogger(ClanServices.class);

    ClanActivityService getClanActivityService();
    ClanAttrService getClanAttrService();
    ClanBaseService getClanBaseService();
    ClanChatService getClanChatService();
    ClanGiftService getClanGiftService();
    ClanHelpService getClanHelpService();
    ClanMemberMgrService getClanMemberMgrService();
    ClanRankMgrService getClanRankMgrService();
    ClanStoreService getClanStoreService();
    ClanTechService getClanTechService();
    ClanTerritoryService getClanTerritoryService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.ADDCLANACTSCORECMD:
                getClanActivityService().handleAddClanActScoreCmd((AddClanActScoreCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHACTIVITYDATAASK:
                getClanActivityService().handleFetchActivityDataAsk((FetchActivityDataAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANPRIVILEGEASK:
                getClanAttrService().handleModifyClanPrivilegeAsk((ModifyClanPrivilegeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKMODIFYNAMEQUIETTIMEASK:
                getClanAttrService().handleCheckModifyNameQuietTimeAsk((CheckModifyNameQuietTimeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANNAMEASK:
                getClanAttrService().handleModifyClanNameAsk((ModifyClanNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANSIMPLENAMEASK:
                getClanAttrService().handleModifyClanSimpleNameAsk((ModifyClanSimpleNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKCLANFLAGANDTERRITORYCOLORASK:
                getClanAttrService().handleCheckClanFlagAndTerritoryColorAsk((CheckClanFlagAndTerritoryColorAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANFLAGANDTERRITORYCOLORASK:
                getClanAttrService().handleModifyClanFlagAndTerritoryColorAsk((ModifyClanFlagAndTerritoryColorAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANWELCOMELETTERASK:
                getClanAttrService().handleModifyClanWelcomeLetterAsk((ModifyClanWelcomeLetterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYCLANSETTINGMISCASK:
                getClanAttrService().handleModifyClanSettingMiscAsk((ModifyClanSettingMiscAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANMARKPOSITIONCMD:
                getClanAttrService().handleClanMarkPositionCmd((ClanMarkPositionCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCREDDOTTOCLANCMD:
                getClanAttrService().handleSyncRedDotToClanCmd((SyncRedDotToClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHRESOURCESSNAPSHOTASK:
                getClanAttrService().handleFetchResourcesSnapshotAsk((FetchResourcesSnapshotAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANWAREHOUSEINFOASK:
                getClanAttrService().handleFetchClanWareHouseInfoAsk((FetchClanWareHouseInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONADDCLANSCOREFORCLANCMD:
                getClanAttrService().handleOnAddClanScoreForClanCmd((OnAddClanScoreForClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATECLANLOGCMD:
                getClanAttrService().handleCreateClanLogCmd((CreateClanLogCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANLOGASK:
                getClanAttrService().handleFetchClanLogAsk((FetchClanLogAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHDISSOLVINGINFOASK:
                getClanAttrService().handleFetchDissolvingInfoAsk((FetchDissolvingInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPQUERYCLANINFOASK:
                getClanAttrService().handleIDIPQueryClanInfoAsk((IDIPQueryClanInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPRESETCLANINFOASK:
                getClanAttrService().handleIDIPResetClanInfoAsk((IDIPResetClanInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKMODIFYSNAMEQUIETTIMEASK:
                getClanAttrService().handleCheckModifySNameQuietTimeAsk((CheckModifySNameQuietTimeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANPOSITIONMARKASK:
                getClanAttrService().handleFetchClanPositionMarkAsk((FetchClanPositionMarkAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANNUMTIPASK:
                getClanAttrService().handleFetchClanNumTipAsk((FetchClanNumTipAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATECLANENTITYASK:
                getClanBaseService().handleCreateClanEntityAsk((CreateClanEntityAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANCARDASK:
                getClanBaseService().handleFetchClanCardAsk((FetchClanCardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANRANKSIMPLEINFOASK:
                getClanBaseService().handleFetchClanRankSimpleInfoAsk((FetchClanRankSimpleInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANMEMBERINFOASK:
                getClanBaseService().handleFetchClanMemberInfoAsk((FetchClanMemberInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANAPPLYMEMBERSASK:
                getClanBaseService().handleFetchClanApplyMembersAsk((FetchClanApplyMembersAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANWELCOMELETTERASK:
                getClanBaseService().handleFetchClanWelcomeLetterAsk((FetchClanWelcomeLetterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETCLANBASEINFOASK:
                getClanBaseService().handleGetClanBaseInfoAsk((GetClanBaseInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDCLANMAILANDSAVEASK:
                getClanBaseService().handleSendClanMailAndSaveAsk((SendClanMailAndSaveAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.EXECUTECLANGMASK:
                getClanBaseService().handleExecuteClanGmAsk((ExecuteClanGmAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERDISSOLVECLANASK:
                getClanBaseService().handlePlayerDissolveClanAsk((PlayerDissolveClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERCANCELDISSOLVECLANASK:
                getClanBaseService().handlePlayerCancelDissolveClanAsk((PlayerCancelDissolveClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.RECORDCLANSNAPSHOTCMD:
                getClanBaseService().handleRecordClanSnapshotCmd((RecordClanSnapshotCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTCLANONLINEMEMBERCSCMD:
                getClanBaseService().handleBroadcastClanOnlineMemberCsCmd((BroadcastClanOnlineMemberCsCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDCLANRESOURCESCMD:
                getClanBaseService().handleAddClanResourcesCmd((AddClanResourcesCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.INVITEOTHERTOCLANASK:
                getClanBaseService().handleInviteOtherToClanAsk((InviteOtherToClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKINVITEEXISTASK:
                getClanBaseService().handleCheckInviteExistAsk((CheckInviteExistAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REFUSECLANINVITECMD:
                getClanBaseService().handleRefuseClanInviteCmd((RefuseClanInviteCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.REFRESHCLANTHINGSASK:
                getClanBaseService().handleRefreshClanThingsAsk((RefreshClanThingsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PUSHNOTIFYCMD:
                getClanBaseService().handlePushNotifyCmd((PushNotifyCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANCHATASK:
                getClanChatService().handleFetchClanChatAsk((FetchClanChatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDCLANCHATASK:
                getClanChatService().handleSendClanChatAsk((SendClanChatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDGIFTPOINTANDKEYASK:
                getClanGiftService().handleAddGiftPointAndKeyAsk((AddGiftPointAndKeyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDCLANGIFTCMD:
                getClanGiftService().handleAddClanGiftCmd((AddClanGiftCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERQUEUETASKCMD:
                getClanHelpService().handleSyncPlayerQueueTaskCmd((SyncPlayerQueueTaskCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANHELPSASK:
                getClanHelpService().handleFetchClanHelpsAsk((FetchClanHelpsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FINISHALLCLANHELPSASK:
                getClanHelpService().handleFinishAllClanHelpsAsk((FinishAllClanHelpsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONQUEUETASKFINISHEDCMD:
                getClanHelpService().handleOnQueueTaskFinishedCmd((OnQueueTaskFinishedCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.NEEDCLANHELPITEMIDSNTFCMD:
                getClanHelpService().handleNeedClanHelpItemIdsNtfCmd((NeedClanHelpItemIdsNtfCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERAPPLYCLANASK:
                getClanMemberMgrService().handlePlayerApplyClanAsk((PlayerApplyClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PROCESSCLANAPPLYASK:
                getClanMemberMgrService().handleProcessClanApplyAsk((ProcessClanApplyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERQUITCLANASK:
                getClanMemberMgrService().handlePlayerQuitClanAsk((PlayerQuitClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KICKOUTCLANMEMBERASK:
                getClanMemberMgrService().handleKickOutClanMemberAsk((KickOutClanMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKINCLANASK:
                getClanMemberMgrService().handleCheckInClanAsk((CheckInClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKOUTCLANASK:
                getClanMemberMgrService().handleCheckOutClanAsk((CheckOutClanAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GRANTCLANSTAFFASK:
                getClanMemberMgrService().handleGrantClanStaffAsk((GrantClanStaffAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANPOWERREWARDASK:
                getClanMemberMgrService().handleFetchClanPowerRewardAsk((FetchClanPowerRewardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATECLANMEMBERINFOCMD:
                getClanMemberMgrService().handleUpdateClanMemberInfoCmd((UpdateClanMemberInfoCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERCANCELAPPLYCMD:
                getClanMemberMgrService().handlePlayerCancelApplyCmd((PlayerCancelApplyCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.APPLYOWNERDISSOLVINGASK:
                getClanMemberMgrService().handleApplyOwnerDissolvingAsk((ApplyOwnerDissolvingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CANCELAPPLYOWNERDISSOLVINGASK:
                getClanMemberMgrService().handleCancelApplyOwnerDissolvingAsk((CancelApplyOwnerDissolvingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERTOPNHEROCMD:
                getClanMemberMgrService().handleSyncPlayerTopNHeroCmd((SyncPlayerTopNHeroCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETCLANRANKPAGEINFOASK:
                getClanRankMgrService().handleGetClanRankPageInfoAsk((GetClanRankPageInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETTOPCLANRANKINFOASK:
                getClanRankMgrService().handleGetTopClanRankInfoAsk((GetTopClanRankInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATECLANRANKINGCMD:
                getClanRankMgrService().handleUpdateClanRankingCmd((UpdateClanRankingCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANSTOREASK:
                getClanStoreService().handleFetchClanStoreAsk((FetchClanStoreAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANSTORERECORDASK:
                getClanStoreService().handleFetchClanStoreRecordAsk((FetchClanStoreRecordAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.OPERATECLANSTOREITEMASK:
                getClanStoreService().handleOperateClanStoreItemAsk((OperateClanStoreItemAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHFETCHINFOASK:
                getClanTechService().handleClanTechFetchInfoAsk((ClanTechFetchInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHDONATECHECKASK:
                getClanTechService().handleClanTechDonateCheckAsk((ClanTechDonateCheckAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHDONATEASK:
                getClanTechService().handleClanTechDonateAsk((ClanTechDonateAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHRESEARCHASK:
                getClanTechService().handleClanTechResearchAsk((ClanTechResearchAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHRECOMMENDASK:
                getClanTechService().handleClanTechRecommendAsk((ClanTechRecommendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANTECHDETAILASK:
                getClanTechService().handleClanTechDetailAsk((ClanTechDetailAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETCLANPOWERREWARDASK:
                getClanTerritoryService().handleGetClanPowerRewardAsk((GetClanPowerRewardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKREBUILDCLANBUILDINGASK:
                getClanTerritoryService().handleCheckRebuildClanBuildingAsk((CheckRebuildClanBuildingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLACECLANRESBUILDASK:
                getClanTerritoryService().handlePlaceClanResBuildAsk((PlaceClanResBuildAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKEXTINGUISHCLANBUILDINGASK:
                getClanTerritoryService().handleCheckExtinguishClanBuildingAsk((CheckExtinguishClanBuildingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANBUILDINGINFOASK:
                getClanTerritoryService().handleFetchClanBuildingInfoAsk((FetchClanBuildingInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHTERRITORYPAGEALLASK:
                getClanTerritoryService().handleFetchTerritoryPageAllAsk((FetchTerritoryPageAllAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCTERRITORYBUFFSNAPSHOTCMD:
                getClanTerritoryService().handleSyncTerritoryBuffSnapshotCmd((SyncTerritoryBuffSnapshotCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCTERRITORYINFOCMD:
                getClanTerritoryService().handleSyncTerritoryInfoCmd((SyncTerritoryInfoCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCCLANBUILDSTATUSCMD:
                getClanTerritoryService().handleSyncClanBuildStatusCmd((SyncClanBuildStatusCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
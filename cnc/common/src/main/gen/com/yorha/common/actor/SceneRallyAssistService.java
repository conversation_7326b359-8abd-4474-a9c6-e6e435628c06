package com.yorha.common.actor;

import com.yorha.proto.SsSceneRallyAssist.*;

public interface SceneRallyAssistService {

    void handleFetchRallyListAsk(FetchRallyListAsk ask); // 查询集结列表信息

    void handleFetchOneRallyAsk(FetchOneRallyAsk ask); // 查询指定id的集结信息

    void handlePlayerCancelRallyAsk(PlayerCancelRallyAsk ask); // 取消集结

    void handleRepatriateRallyMemberAsk(RepatriateRallyMemberAsk ask); // 遣返集结成员

    void handleSetRallyRecommendSoldierAsk(SetRallyRecommendSoldierAsk ask); // 设置推荐兵种

    void handleFetchWarningAsk(FetchWarningAsk ask); // 拉取预警列表

    void handleSetWarningItemTagAsk(SetWarningItemTagAsk ask); // 忽略/取消忽略预警

    void handleIgnoreAllWarningAsk(IgnoreAllWarningAsk ask); // 忽略所有预警

    void handleFetchInnerArmyAsk(FetchInnerArmyAsk ask); // 拉取城内军队

    void handleRepatriateAssistMemberAsk(RepatriateAssistMemberAsk ask); // 遣返援助

    void handleChangeAssistLeaderAsk(ChangeAssistLeaderAsk ask); // 更换联盟建筑援助防守队长

}
package com.yorha.common.actor;

import com.yorha.proto.SsGate.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface GateServices {
    Logger LOGGER = LogManager.getLogger(GateServices.class);

    GateService getGateService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.SHUTDOWNCMD:
                getGateService().handleShutdownCmd((ShutdownCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTALLCMD:
                getGateService().handleBroadcastAllCmd((BroadcastAllCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTONLINEPLAYERSSCMD:
                getGateService().handleBroadcastOnlinePlayerSsCmd((BroadcastOnlinePlayerSsCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
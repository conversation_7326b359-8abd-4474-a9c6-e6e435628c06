package com.yorha.common.actor;

import com.yorha.proto.SsPlayerFriend.*;

public interface PlayerFriendService {

    void handleAddNewFriendAsk(AddNewFriendAsk ask); // 申请添加好友

    void handleAgreeAddFriendAsk(AgreeAddFriendAsk ask); // 玩家同意加好友

    void handleRefuseAddFriendAsk(RefuseAddFriendAsk ask); // 玩家拒绝加好友

    void handleDelFriendAsk(DelFriendAsk ask); // 玩家删除好友

    void handleJudgeBeShieldAsk(JudgeBeShieldAsk ask); // 判断对方是否将自己屏蔽

}
package com.yorha.common.actor;

import com.yorha.proto.SsSceneClan.*;

public interface SceneClanMgrService {

    void handleFetchTerritoryPageAsk(FetchTerritoryPageAsk ask); // 拉取联盟领地页面数据

    void handleAbandonClanMapBuildingAsk(AbandonClanMapBuildingAsk ask); // 放弃联盟据点

    void handleOnClanCreatedCmd(OnClanCreatedCmd ask); // 联盟新建成功

    void handleOnClanDissolutionCmd(OnClanDissolutionCmd ask); // 联盟解散

    void handleSyncSceneClanCmd(SyncSceneClanCmd ask); // 联盟数据变更同步场景

    void handleSyncClanActorStatusCmd(SyncClanActorStatusCmd ask); // 联盟Actor状态同步

    void handleConstructClanBuildingAsk(ConstructClanBuildingAsk ask); // 建设联盟建筑

    void handleDestroyClanBuildingAsk(DestroyClanBuildingAsk ask); // 拆除联盟建筑

    void handleExtinguishBuildingFireAsk(ExtinguishBuildingFireAsk ask); // 熄灭联盟建筑

    void handleFetchClanBuildingHpAsk(FetchClanBuildingHpAsk ask); // 获取联盟建筑耐久度

    void handleCheckCanFreeRebuildAsk(CheckCanFreeRebuildAsk ask); // 检查是否可以免费放置建筑，实际是检查军团是否已执行过放置军团建筑的逻辑

    void handleVerifyCanRebuildAsk(VerifyCanRebuildAsk ask); // 预先检查是否可以放置建筑

    void handleVerifyCanExtinguishAsk(VerifyCanExtinguishAsk ask); // 预先检查是否可以熄灭建筑

    void handleSyncClanAdditionCmd(SyncClanAdditionCmd ask); // 同步联盟加成

    void handleAskForClanRecommendAsk(AskForClanRecommendAsk ask); // 请求军团推荐

    void handleFetchDefaultClanListAsk(FetchDefaultClanListAsk ask); // 拉取默认军团列表，即不输入名字搜索情况下，玩家点开军团列表看到的

    void handleVerifyCanPlaceClanResAsk(VerifyCanPlaceClanResAsk ask); // 放置军团资源中心检查

    void handlePlaceClanResBuildInSceneAsk(PlaceClanResBuildInSceneAsk ask); // 放置军团资源中心

    void handleFetchClanResBuildSimpleInfoAsk(FetchClanResBuildSimpleInfoAsk ask); // 拉取军团资源中心简要信息

    void handleCheckPlayerCityInTerritoryAsk(CheckPlayerCityInTerritoryAsk ask); // 检查玩家是否在联盟领土上

    void handleAddDevBuffFromClanCmd(AddDevBuffFromClanCmd ask);

    void handleRemoveDevBuffFromClanCmd(RemoveDevBuffFromClanCmd ask);

    void handleUpdateAdditionFromClanCmd(UpdateAdditionFromClanCmd ask);

    void handleGetClanCommandCenterNumAsk(GetClanCommandCenterNumAsk ask); // 获取联盟指挥中心数量

}
package com.yorha.common.actor;

import com.yorha.proto.SsPlayerCard.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface PlayerCardServices {
    Logger LOGGER = LogManager.getLogger(PlayerCardServices.class);

    PlayerCardService getPlayerCardService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.QUERYPLAYERCARDASK:
                getPlayerCardService().handleQueryPlayerCardAsk((QueryPlayerCardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYPLAYERCARDASK:
                getPlayerCardService().handleBatchQueryPlayerCardAsk((BatchQueryPlayerCardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYPLAYERCARDHEADASK:
                getPlayerCardService().handleQueryPlayerCardHeadAsk((QueryPlayerCardHeadAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYPLAYERCARDHEADASK:
                getPlayerCardService().handleBatchQueryPlayerCardHeadAsk((BatchQueryPlayerCardHeadAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEPLAYERCARDCMD:
                getPlayerCardService().handleUpdatePlayerCardCmd((UpdatePlayerCardCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYPLAYERZONEIDASK:
                getPlayerCardService().handleBatchQueryPlayerZoneIdAsk((BatchQueryPlayerZoneIdAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsClanMember.*;

public interface ClanMemberMgrService {

    void handlePlayerApplyClanAsk(PlayerApplyClanAsk ask); // 申请加入联盟

    void handleProcessClanApplyAsk(ProcessClanApplyAsk ask); // 处理申请信息

    void handlePlayerQuitClanAsk(PlayerQuitClanAsk ask); // 退出联盟

    void handleKickOutClanMemberAsk(KickOutClanMemberAsk ask); // 踢除联盟玩家

    void handleCheckInClanAsk(CheckInClanAsk ask); // 向联盟登记玩家

    void handleCheckOutClanAsk(CheckOutClanAsk ask); // 向联盟登出玩家

    void handleGrantClanStaffAsk(GrantClanStaffAsk ask); // 授予官职

    void handleFetchClanPowerRewardAsk(FetchClanPowerRewardAsk ask); // 拉取势力宝箱资源奖励

    void handleUpdateClanMemberInfoCmd(UpdateClanMemberInfoCmd ask); // 更新联盟成员信息

    void handlePlayerCancelApplyCmd(PlayerCancelApplyCmd ask); // 取消申请

    void handleApplyOwnerDissolvingAsk(ApplyOwnerDissolvingAsk ask); // 解散状态下，申请成为军团长

    void handleCancelApplyOwnerDissolvingAsk(CancelApplyOwnerDissolvingAsk ask); // 解散状态下，取消申请成为军团长

    void handleSyncPlayerTopNHeroCmd(SyncPlayerTopNHeroCmd ask); // 同步战力topN英雄变化

}
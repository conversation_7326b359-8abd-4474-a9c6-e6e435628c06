package com.yorha.common.actor;

import com.yorha.proto.SsPathFinding.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface PathFindingServices {
    Logger LOGGER = LogManager.getLogger(PathFindingServices.class);

    PathFindingService getPathFindingService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.SEARCHPATHASYNCASK:
                getPathFindingService().handleSearchPathAsyncAsk((SearchPathAsyncAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
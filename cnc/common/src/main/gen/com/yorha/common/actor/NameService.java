package com.yorha.common.actor;

import com.yorha.proto.SsName.*;

public interface NameService {

    void handleOccupyBatchNameCmd(OccupyBatchNameCmd ask); // 批量占用名称

    void handleReleaseNameCmd(ReleaseNameCmd ask); // 批量占用名称

    void handleSearchClanNameAsk(SearchClanNameAsk ask); // 搜索联盟列表

    void handleSearchPlayerNameAsk(SearchPlayerNameAsk ask); // 搜索玩家列表

    void handleSearchNameMatchedAsk(SearchNameMatchedAsk ask); // IdIp搜索名称

}
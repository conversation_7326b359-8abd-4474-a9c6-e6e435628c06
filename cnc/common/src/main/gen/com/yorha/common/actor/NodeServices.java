package com.yorha.common.actor;

import com.yorha.proto.SsNode.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface NodeServices {
    Logger LOGGER = LogManager.getLogger(NodeServices.class);

    NodeService getNodeService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.NODETASKCMD:
                getNodeService().handleNodeTaskCmd((NodeTaskCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
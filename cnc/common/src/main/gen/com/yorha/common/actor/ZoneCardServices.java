package com.yorha.common.actor;

import com.yorha.proto.SsZoneCard.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ZoneCardServices {
    Logger LOGGER = LogManager.getLogger(ZoneCardServices.class);

    ZoneCardService getZoneCardService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.GETALLZONEINFOASK:
                getZoneCardService().handleGetAllZoneInfoAsk((GetAllZoneInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETMULTIZONESTATUSASK:
                getZoneCardService().handleGetMultiZoneStatusAsk((GetMultiZoneStatusAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETZONESUNDERSEASONASK:
                getZoneCardService().handleGetZonesUnderSeasonAsk((GetZonesUnderSeasonAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETZONEMILESTONEASK:
                getZoneCardService().handleGetZoneMileStoneAsk((GetZoneMileStoneAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
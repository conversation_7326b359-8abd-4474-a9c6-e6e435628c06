package com.yorha.common.actor;

import com.yorha.proto.SsSceneDungeon.*;

public interface SceneDungeonService {

    void handleCreateDungeonAsk(CreateDungeonAsk ask); // 创建副本

    void handleEnterDungeonAsk(EnterDungeonAsk ask); // 进入副本

    void handleLeaveDungeonAsk(LeaveDungeonAsk ask); // 离开副本

    void handlePlayerLoginAsk(PlayerLoginAsk ask); // 通知场景玩家登入

    void handlePlayerLogoutAsk(PlayerLogoutAsk ask); // 通知场景玩家登出

    void handlePerformActionAsk(PerformActionAsk ask); // 执行远征动作

}
package com.yorha.common.actor;

import com.yorha.proto.SsZoneCard.*;

public interface ZoneCardService {

    void handleGetAllZoneInfoAsk(GetAllZoneInfoAsk ask); // 获取各服信息

    void handleGetMultiZoneStatusAsk(GetMultiZoneStatusAsk ask); // 获取多服服务器状态

    void handleGetZonesUnderSeasonAsk(GetZonesUnderSeasonAsk ask); // 获取指定赛季下服务器列表

    void handleGetZoneMileStoneAsk(GetZoneMileStoneAsk ask); // 获取指定服的里程碑id

}

package com.yorha.common.io;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.proto.*;

import java.util.HashMap;
import java.util.Map;

public abstract class MsgType {
	public static final int	DIRGETSERVERLIST_C2S_MSG = 1;
	public static final int	DIRGETSERVERLIST_S2C_MSG = 10001;
	public static final int	DIRGETZONE_C2S_MSG = 2;
	public static final int	DIRGETZONE_S2C_MSG = 10002;
	public static final int	DIRDELACCOUNT_C2S_MSG = 3;
	public static final int	DIRDELACCOUNT_S2C_MSG = 10003;
	public static final int	DEVENVRESINFONTF = 4;
	public static final int	DIRREGISTERACCOUNT_C2S_MSG = 5;
	public static final int	DIRREGISTERACCOUNT_S2C_MSG = 10005;
	public static final int	KICKOFFNTF = 6;
	public static final int	DIRACCOUNTLOGIN_C2S_MSG = 7;
	public static final int	DIRACCOUNTLOGIN_S2C_MSG = 10007;
	public static final int	DIRACCOUNTBIND_C2S_MSG = 8;
	public static final int	DIRACCOUNTBIND_S2C_MSG = 10008;
	public static final int	DIRACCOUNTUNBIND_C2S_MSG = 9;
	public static final int	DIRACCOUNTUNBIND_S2C_MSG = 10009;
	public static final int	DIRACCOUNTBINDINFO_C2S_MSG = 10;
	public static final int	DIRACCOUNTBINDINFO_S2C_MSG = 10010;
	public static final int	DIRPING_C2S_MSG = 11;
	public static final int	DIRPING_S2C_MSG = 10011;
	public static final int	LOGIN_C2S_MSG = 101;
	public static final int	LOGIN_S2C_MSG = 10101;
	public static final int	GETROLELIST_C2S_MSG = 102;
	public static final int	GETROLELIST_S2C_MSG = 10102;
	public static final int	ACCOUNTLOGIN_C2S_MSG = 103;
	public static final int	ACCOUNTLOGIN_S2C_MSG = 10103;
	public static final int	ACCOUNTBIND_C2S_MSG = 104;
	public static final int	ACCOUNTBIND_S2C_MSG = 10104;
	public static final int	ACCOUNTUNBIND_C2S_MSG = 105;
	public static final int	ACCOUNTUNBIND_S2C_MSG = 10105;
	public static final int	ACCOUNTBINDINFO_C2S_MSG = 106;
	public static final int	ACCOUNTBINDINFO_S2C_MSG = 10106;
	public static final int	RALLYNTFMSG = 107;
	public static final int	COMMANDINVALID_S2C_MSG = 108;
	public static final int	HOPECOMMANDNTFMSG = 109;
	public static final int	REPORTEXECUTE_C2S_MSG = 110;
	public static final int	REPORTEXECUTE_S2C_MSG = 10110;
	public static final int	DISCONNECT = 201;
	public static final int	WARN = 202;
	public static final int	LOGLOADING_C2S_MSG = 203;
	public static final int	LOGLOADING_S2C_MSG = 10203;
	public static final int	KEEPALIVE_C2S_MSG = 204;
	public static final int	KEEPALIVE_S2C_MSG = 10204;
	public static final int	ENTITYNTFMSG = 301;
	public static final int	SCENEOBJBRIEFNTFMSG = 302;
	public static final int	SCENEOBJBRIEFATTR = 303;
	public static final int	PLAYER_FETCHACHIEVEMENT_C2S = 401;
	public static final int	PLAYER_FETCHACHIEVEMENT_S2C = 10401;
	public static final int	PLAYER_TAKEACHIEVEMENTREWARD_C2S = 402;
	public static final int	PLAYER_TAKEACHIEVEMENTREWARD_S2C = 10402;
	public static final int	PLAYER_ACHIEVEMENTCOMPLETENTF_NTF = 403;
	public static final int	PLAYER_SHAREACHIEVEMENT_C2S = 404;
	public static final int	PLAYER_SHAREACHIEVEMENT_S2C = 10404;
	public static final int	PLAYER_ACTIVITYTAKEREWARD_C2S = 501;
	public static final int	PLAYER_ACTIVITYTAKEREWARD_S2C = 10501;
	public static final int	PLAYER_ACTIVITYBUYGOOD_C2S = 502;
	public static final int	PLAYER_ACTIVITYBUYGOOD_S2C = 10502;
	public static final int	PLAYER_ACTIVITYBUYSECONDQUEUE_C2S = 503;
	public static final int	PLAYER_ACTIVITYBUYSECONDQUEUE_S2C = 10503;
	public static final int	PLAYER_SENDACTIVITYINVITEMSG_C2S = 504;
	public static final int	PLAYER_SENDACTIVITYINVITEMSG_S2C = 10504;
	public static final int	PLAYER_BESTCOMMANDERFETCH_C2S = 505;
	public static final int	PLAYER_BESTCOMMANDERFETCH_S2C = 10505;
	public static final int	PLAYER_BESTCOMMANDERHISTORYRANK_C2S = 506;
	public static final int	PLAYER_BESTCOMMANDERHISTORYRANK_S2C = 10506;
	public static final int	PLAYER_OPENSTORERATINGWINDOW_NTF = 507;
	public static final int	PLAYER_REPORTSTORERATINGRESULT_C2S = 508;
	public static final int	PLAYER_REPORTSTORERATINGRESULT_S2C = 10508;
	public static final int	PLAYER_CHOOSESPLEVELREWARD_C2S = 511;
	public static final int	PLAYER_CHOOSESPLEVELREWARD_S2C = 10511;
	public static final int	PLAYER_BPTICKETEXCHANGE_C2S = 513;
	public static final int	PLAYER_BPTICKETEXCHANGE_S2C = 10513;
	public static final int	PLAYER_BUYBP_C2S = 514;
	public static final int	PLAYER_BUYBP_S2C = 10514;
	public static final int	PLAYER_FETCHACTIVITYCALENDAR_C2S = 515;
	public static final int	PLAYER_FETCHACTIVITYCALENDAR_S2C = 10515;
	public static final int	PLAYER_CHOOSEBESTCOMMANDERITEM_C2S = 516;
	public static final int	PLAYER_CHOOSEBESTCOMMANDERITEM_S2C = 10516;
	public static final int	PLAYER_FETCHACTIVITYCLANDATA_C2S = 517;
	public static final int	PLAYER_FETCHACTIVITYCLANDATA_S2C = 10517;
	public static final int	PLAYER_BUYLOTTERYVOUCHER_C2S = 518;
	public static final int	PLAYER_BUYLOTTERYVOUCHER_S2C = 10518;
	public static final int	PLAYER_LOTTERYNATASHADRAW_C2S = 519;
	public static final int	PLAYER_LOTTERYNATASHADRAW_S2C = 10519;
	public static final int	PLAYER_SETTLELOTTERYHERO_C2S = 520;
	public static final int	PLAYER_SETTLELOTTERYHERO_S2C = 10520;
	public static final int	PLAYER_LOTTERYNATASHASHOPEXCHANGE_C2S = 521;
	public static final int	PLAYER_LOTTERYNATASHASHOPEXCHANGE_S2C = 10521;
	public static final int	PLAYER_CREATESPYPLANE_C2S = 601;
	public static final int	PLAYER_CREATESPYPLANE_S2C = 10601;
	public static final int	PLAYER_CHANGEACTIONSPYPLANE_C2S = 602;
	public static final int	PLAYER_CHANGEACTIONSPYPLANE_S2C = 10602;
	public static final int	PLAYER_CHECKSPY_C2S = 603;
	public static final int	PLAYER_CHECKSPY_S2C = 10603;
	public static final int	PLAYER_CREATELOGISTICSPLANE_C2S = 604;
	public static final int	PLAYER_CREATELOGISTICSPLANE_S2C = 10604;
	public static final int	PLAYER_CHANGELOGISTICSPLANE_C2S = 605;
	public static final int	PLAYER_CHANGELOGISTICSPLANE_S2C = 10605;
	public static final int	PLAYER_OBTAINBATTLEPASSTASKEXP_C2S = 701;
	public static final int	PLAYER_OBTAINBATTLEPASSTASKEXP_S2C = 10701;
	public static final int	PLAYER_OBTAINDALIYEXPBOX_C2S = 702;
	public static final int	PLAYER_OBTAINDALIYEXPBOX_S2C = 10702;
	public static final int	PLAYER_OBTAINBATTLEPASSLEVELREWARD_C2S = 703;
	public static final int	PLAYER_OBTAINBATTLEPASSLEVELREWARD_S2C = 10703;
	public static final int	PLAYER_PURCHASEBATTLEPASSLEVEL_C2S = 704;
	public static final int	PLAYER_PURCHASEBATTLEPASSLEVEL_S2C = 10704;
	public static final int	PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_C2S = 705;
	public static final int	PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_S2C = 10705;
	public static final int	PLAYER_OBTAINSEASONDALIYEXPBOX_C2S = 706;
	public static final int	PLAYER_OBTAINSEASONDALIYEXPBOX_S2C = 10706;
	public static final int	PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_C2S = 707;
	public static final int	PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_S2C = 10707;
	public static final int	PLAYER_PURCHASESEASONBATTLEPASSLEVEL_C2S = 708;
	public static final int	PLAYER_PURCHASESEASONBATTLEPASSLEVEL_S2C = 10708;
	public static final int	PLAYER_STARTCAMPAIGN_C2S = 801;
	public static final int	PLAYER_STARTCAMPAIGN_S2C = 10801;
	public static final int	PLAYER_ABORTCAMPAIGN_C2S = 802;
	public static final int	PLAYER_ABORTCAMPAIGN_S2C = 10802;
	public static final int	PLAYER_STARTMISSION_C2S = 803;
	public static final int	PLAYER_STARTMISSION_S2C = 10803;
	public static final int	PLAYER_FINISHMISSION_C2S = 804;
	public static final int	PLAYER_FINISHMISSION_S2C = 10804;
	public static final int	PLAYER_MERGECAMPAIGNITEM_C2S = 805;
	public static final int	PLAYER_MERGECAMPAIGNITEM_S2C = 10805;
	public static final int	PLAYER_FINALCAMPAIGNRESULT_NTF = 806;
	public static final int	PLAYER_CAMPAIGNEVENTREST_C2S = 807;
	public static final int	PLAYER_CAMPAIGNEVENTREST_S2C = 10807;
	public static final int	PLAYER_CAMPAIGNEVENTBAR_C2S = 808;
	public static final int	PLAYER_CAMPAIGNEVENTBAR_S2C = 10808;
	public static final int	PLAYER_CAMPAIGNEVENTTRAIN_C2S = 809;
	public static final int	PLAYER_CAMPAIGNEVENTTRAIN_S2C = 10809;
	public static final int	PLAYER_CAMPAIGNEVENTRESTORE_C2S = 810;
	public static final int	PLAYER_CAMPAIGNEVENTRESTORE_S2C = 10810;
	public static final int	PLAYER_CAMPAIGNEVENTREPAIR_C2S = 811;
	public static final int	PLAYER_CAMPAIGNEVENTREPAIR_S2C = 10811;
	public static final int	PLAYER_CAMPAIGNEVENTEXIT_C2S = 812;
	public static final int	PLAYER_CAMPAIGNEVENTEXIT_S2C = 10812;
	public static final int	PLAYER_CAMPAIGNMISSIONSTART_C2S = 813;
	public static final int	PLAYER_CAMPAIGNMISSIONSTART_S2C = 10813;
	public static final int	PLAYER_CAMPAIGNMISSIONFINISH_C2S = 814;
	public static final int	PLAYER_CAMPAIGNMISSIONFINISH_S2C = 10814;
	public static final int	PLAYER_TOUCHMISSIONCHEST_C2S = 815;
	public static final int	PLAYER_TOUCHMISSIONCHEST_S2C = 10815;
	public static final int	PLAYER_GETCHARACTERLIST_C2S = 901;
	public static final int	PLAYER_GETCHARACTERLIST_S2C = 10901;
	public static final int	PLAYER_GETZONESERVERLIST_C2S = 902;
	public static final int	PLAYER_GETZONESERVERLIST_S2C = 10902;
	public static final int	PLAYER_MANAGESTARFORCHARACTER_C2S = 903;
	public static final int	PLAYER_MANAGESTARFORCHARACTER_S2C = 10903;
	public static final int	PLAYER_CHECKREGISTERNEWCHARACTER_C2S = 904;
	public static final int	PLAYER_CHECKREGISTERNEWCHARACTER_S2C = 10904;
	public static final int	PLAYER_GETZONEIPPORT_C2S = 905;
	public static final int	PLAYER_GETZONEIPPORT_S2C = 10905;
	public static final int	PLAYER_GETCHATMESSAGES_C2S = 1001;
	public static final int	PLAYER_GETCHATMESSAGES_S2C = 11001;
	public static final int	PLAYER_CHATREQUEST_C2S = 1002;
	public static final int	PLAYER_CHATREQUEST_S2C = 11002;
	public static final int	PLAYER_READCHATMESSAGE_C2S = 1003;
	public static final int	PLAYER_READCHATMESSAGE_S2C = 11003;
	public static final int	PLAYER_TRANSLATETEXT_C2S = 1004;
	public static final int	PLAYER_TRANSLATETEXT_S2C = 11004;
	public static final int	PLAYER_BORADCASTCHATMESSAGE_NTF = 1005;
	public static final int	PLAYER_HIDECHAT_C2S = 1006;
	public static final int	PLAYER_HIDECHAT_S2C = 11006;
	public static final int	PLAYER_CREATEGROUPCHAT_C2S = 1007;
	public static final int	PLAYER_CREATEGROUPCHAT_S2C = 11007;
	public static final int	PLAYER_GETCHATMEMBERLIST_C2S = 1008;
	public static final int	PLAYER_GETCHATMEMBERLIST_S2C = 11008;
	public static final int	PLAYER_GETBATCHPLAYERCACHE_C2S = 1009;
	public static final int	PLAYER_GETBATCHPLAYERCACHE_S2C = 11009;
	public static final int	PLAYER_CHECKINGROUPCHAT_C2S = 1010;
	public static final int	PLAYER_CHECKINGROUPCHAT_S2C = 11010;
	public static final int	PLAYER_INVITEPLAYERJOINGROUP_C2S = 1011;
	public static final int	PLAYER_INVITEPLAYERJOINGROUP_S2C = 11011;
	public static final int	PLAYER_REMOVEPLAYERFROMGROUP_C2S = 1012;
	public static final int	PLAYER_REMOVEPLAYERFROMGROUP_S2C = 11012;
	public static final int	PLAYER_PLAYERQUITGROUPCHAT_C2S = 1013;
	public static final int	PLAYER_PLAYERQUITGROUPCHAT_S2C = 11013;
	public static final int	PLAYER_PLAYERTRANSFERGROUPOWNER_C2S = 1014;
	public static final int	PLAYER_PLAYERTRANSFERGROUPOWNER_S2C = 11014;
	public static final int	PLAYER_DISMISSGROUPCHAT_C2S = 1015;
	public static final int	PLAYER_DISMISSGROUPCHAT_S2C = 11015;
	public static final int	PLAYER_PLAYERMODIFYGROUPNAME_C2S = 1016;
	public static final int	PLAYER_PLAYERMODIFYGROUPNAME_S2C = 11016;
	public static final int	PLAYER_OPENCHATNOTDISTURB_C2S = 1017;
	public static final int	PLAYER_OPENCHATNOTDISTURB_S2C = 11017;
	public static final int	PLAYER_CLOSECHATNOTDISTURB_C2S = 1018;
	public static final int	PLAYER_CLOSECHATNOTDISTURB_S2C = 11018;
	public static final int	PLAYER_SETPLAYERNOTDISTURB_C2S = 1019;
	public static final int	PLAYER_SETPLAYERNOTDISTURB_S2C = 11019;
	public static final int	PLAYER_UNSETPLAYERNOTDISTURB_C2S = 1020;
	public static final int	PLAYER_UNSETPLAYERNOTDISTURB_S2C = 11020;
	public static final int	PLAYER_DELETECHATITEM_C2S = 1021;
	public static final int	PLAYER_DELETECHATITEM_S2C = 11021;
	public static final int	PLAYER_SETGROUPCHATSHOW_C2S = 1022;
	public static final int	PLAYER_SETGROUPCHATSHOW_S2C = 11022;
	public static final int	PLAYER_GETSHAREDMAILCONTENT_C2S = 1023;
	public static final int	PLAYER_GETSHAREDMAILCONTENT_S2C = 11023;
	public static final int	PLAYER_CREATECLAN_C2S = 1101;
	public static final int	PLAYER_CREATECLAN_S2C = 11101;
	public static final int	PLAYER_CHECKCLANNAME_C2S = 1102;
	public static final int	PLAYER_CHECKCLANNAME_S2C = 11102;
	public static final int	PLAYER_SEARCHCLAN_C2S = 1103;
	public static final int	PLAYER_SEARCHCLAN_S2C = 11103;
	public static final int	PLAYER_FETCHCLANENTITY_C2S = 1104;
	public static final int	PLAYER_FETCHCLANENTITY_S2C = 11104;
	public static final int	PLAYER_FETCHCLANSIMPLEINFO_C2S = 1105;
	public static final int	PLAYER_FETCHCLANSIMPLEINFO_S2C = 11105;
	public static final int	PLAYER_FETCHCLANMEMBERINFO_C2S = 1106;
	public static final int	PLAYER_FETCHCLANMEMBERINFO_S2C = 11106;
	public static final int	PLAYER_FETCHCLANAPPLYPLAYERS_C2S = 1107;
	public static final int	PLAYER_FETCHCLANAPPLYPLAYERS_S2C = 11107;
	public static final int	PLAYER_NOTIFYCLANAPPLYPLAYERS_NTF = 1108;
	public static final int	PLAYER_APPLYJOINCLAN_C2S = 1109;
	public static final int	PLAYER_APPLYJOINCLAN_S2C = 11109;
	public static final int	PLAYER_CANCELAPPLYJOINCLAN_C2S = 1110;
	public static final int	PLAYER_CANCELAPPLYJOINCLAN_S2C = 11110;
	public static final int	PLAYER_HANDLECLANAPPLY_C2S = 1111;
	public static final int	PLAYER_HANDLECLANAPPLY_S2C = 11111;
	public static final int	PLAYER_QUITCLAN_C2S = 1112;
	public static final int	PLAYER_QUITCLAN_S2C = 11112;
	public static final int	PLAYER_INVITETOCLAN_C2S = 1113;
	public static final int	PLAYER_INVITETOCLAN_S2C = 11113;
	public static final int	PLAYER_HANDLECLANINVITATION_C2S = 1114;
	public static final int	PLAYER_HANDLECLANINVITATION_S2C = 11114;
	public static final int	PLAYER_DISSOLVECLAN_C2S = 1115;
	public static final int	PLAYER_DISSOLVECLAN_S2C = 11115;
	public static final int	PLAYER_CANCELDISSOLVECLAN_C2S = 1116;
	public static final int	PLAYER_CANCELDISSOLVECLAN_S2C = 11116;
	public static final int	PLAYER_APPLYOWNERDISSOLVING_C2S = 1117;
	public static final int	PLAYER_APPLYOWNERDISSOLVING_S2C = 11117;
	public static final int	PLAYER_CANCELAPPLYOWNERDISSOLVING_C2S = 1118;
	public static final int	PLAYER_CANCELAPPLYOWNERDISSOLVING_S2C = 11118;
	public static final int	PLAYER_FETCHDISSOLVINGINFO_C2S = 1119;
	public static final int	PLAYER_FETCHDISSOLVINGINFO_S2C = 11119;
	public static final int	PLAYER_TRANSFERCLANOWNER_C2S = 1120;
	public static final int	PLAYER_TRANSFERCLANOWNER_S2C = 11120;
	public static final int	PLAYER_KICKOFFCLANMEMBER_C2S = 1121;
	public static final int	PLAYER_KICKOFFCLANMEMBER_S2C = 11121;
	public static final int	PLAYER_MODIFYCLANMEMBERSTAFF_C2S = 1122;
	public static final int	PLAYER_MODIFYCLANMEMBERSTAFF_S2C = 11122;
	public static final int	PLAYER_MODIFYCLANNAME_C2S = 1123;
	public static final int	PLAYER_MODIFYCLANNAME_S2C = 11123;
	public static final int	PLAYER_MODIFYCLANSNAME_C2S = 1124;
	public static final int	PLAYER_MODIFYCLANSNAME_S2C = 11124;
	public static final int	PLAYER_MODIFYCLANFLAG_C2S = 1125;
	public static final int	PLAYER_MODIFYCLANFLAG_S2C = 11125;
	public static final int	PLAYER_MODIFYCLANSETTINGMISC_C2S = 1126;
	public static final int	PLAYER_MODIFYCLANSETTINGMISC_S2C = 11126;
	public static final int	PLAYER_FETCHCLANWELCOMELETTER_C2S = 1127;
	public static final int	PLAYER_FETCHCLANWELCOMELETTER_S2C = 11127;
	public static final int	PLAYER_MODIFYCLANWELCOMELETTER_C2S = 1128;
	public static final int	PLAYER_MODIFYCLANWELCOMELETTER_S2C = 11128;
	public static final int	PLAYER_FETCHCLANTERRITORYPAGE_C2S = 1129;
	public static final int	PLAYER_FETCHCLANTERRITORYPAGE_S2C = 11129;
	public static final int	PLAYER_ABANDONMAPBUILDING_C2S = 1130;
	public static final int	PLAYER_ABANDONMAPBUILDING_S2C = 11130;
	public static final int	PLAYER_CHANGEDEFENSIVELEADER_C2S = 1131;
	public static final int	PLAYER_CHANGEDEFENSIVELEADER_S2C = 11131;
	public static final int	PLAYER_OBTAINCLANPOWERREWARD_C2S = 1132;
	public static final int	PLAYER_OBTAINCLANPOWERREWARD_S2C = 11132;
	public static final int	PLAYER_FETCHCLANMEMBERCITY_C2S = 1133;
	public static final int	PLAYER_FETCHCLANMEMBERCITY_S2C = 11133;
	public static final int	PLAYER_FETCHCLANSINGLEMEMBERCITY_C2S = 1134;
	public static final int	PLAYER_FETCHCLANSINGLEMEMBERCITY_S2C = 11134;
	public static final int	PLAYER_FETCHCLANTERRITORYMAP_C2S = 1135;
	public static final int	PLAYER_FETCHCLANTERRITORYMAP_S2C = 11135;
	public static final int	PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_C2S = 1136;
	public static final int	PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_S2C = 11136;
	public static final int	PLAYER_DESTROYCLANSTRONGHOLDBUILDING_C2S = 1137;
	public static final int	PLAYER_DESTROYCLANSTRONGHOLDBUILDING_S2C = 11137;
	public static final int	PLAYER_EXTINGUISHBUILDINGFIRE_C2S = 1138;
	public static final int	PLAYER_EXTINGUISHBUILDINGFIRE_S2C = 11138;
	public static final int	PLAYER_FETCHCLANBUILDINGHP_C2S = 1139;
	public static final int	PLAYER_FETCHCLANBUILDINGHP_S2C = 11139;
	public static final int	PLAYER_FETCHCLANPOWERREWARD_C2S = 1140;
	public static final int	PLAYER_FETCHCLANPOWERREWARD_S2C = 11140;
	public static final int	PLAYER_CHECKCANFREEREBUILD_C2S = 1141;
	public static final int	PLAYER_CHECKCANFREEREBUILD_S2C = 11141;
	public static final int	PLAYER_VERIFYREBUILDMAPBUILDING_C2S = 1142;
	public static final int	PLAYER_VERIFYREBUILDMAPBUILDING_S2C = 11142;
	public static final int	PLAYER_GETCLANRANKPAGEINFO_C2S = 1143;
	public static final int	PLAYER_GETCLANRANKPAGEINFO_S2C = 11143;
	public static final int	PLAYER_GETTOPCLANRANKINFO_C2S = 1144;
	public static final int	PLAYER_GETTOPCLANRANKINFO_S2C = 11144;
	public static final int	PLAYER_CREATECLANHELP_C2S = 1145;
	public static final int	PLAYER_CREATECLANHELP_S2C = 11145;
	public static final int	PLAYER_FETCHCLANHELPS_C2S = 1146;
	public static final int	PLAYER_FETCHCLANHELPS_S2C = 11146;
	public static final int	PLAYER_FINISHALLCLANHELPS_C2S = 1147;
	public static final int	PLAYER_FINISHALLCLANHELPS_S2C = 11147;
	public static final int	PLAYER_SYNCCANHELPITEMIDS_NTF = 1148;
	public static final int	PLAYER_CLANHELPINFOUPDATE_NTF = 1149;
	public static final int	PLAYER_GETCLANGIFT_C2S = 1150;
	public static final int	PLAYER_GETCLANGIFT_S2C = 11150;
	public static final int	PLAYER_CLEARCLANGIFT_C2S = 1151;
	public static final int	PLAYER_CLEARCLANGIFT_S2C = 11151;
	public static final int	PLAYER_GETCLANTREASUREGIFT_C2S = 1152;
	public static final int	PLAYER_GETCLANTREASUREGIFT_S2C = 11152;
	public static final int	PLAYER_NOTIFYCLANGIFTINFOONLINE_NTF = 1153;
	public static final int	PLAYER_FETCHCLANWAREHOUSEINFO_C2S = 1154;
	public static final int	PLAYER_FETCHCLANWAREHOUSEINFO_S2C = 11154;
	public static final int	PLAYER_FETCHCLANRESOURCESNAPSHOT_C2S = 1155;
	public static final int	PLAYER_FETCHCLANRESOURCESNAPSHOT_S2C = 11155;
	public static final int	PLAYER_UPDATECLANINFO_NTF = 1156;
	public static final int	PLAYER_FETCHCLANEVENTLOG_C2S = 1157;
	public static final int	PLAYER_FETCHCLANEVENTLOG_S2C = 11157;
	public static final int	PLAYER_FETCHCLANNUMMAXTIP_C2S = 1158;
	public static final int	PLAYER_FETCHCLANNUMMAXTIP_S2C = 11158;
	public static final int	PLAYER_CLANOWNERCHANGED_NTF = 1159;
	public static final int	PLAYER_FETCHCLANCOMMANDCENTERNUM_C2S = 1160;
	public static final int	PLAYER_FETCHCLANCOMMANDCENTERNUM_S2C = 11160;
	public static final int	PLAYER_FETCHCLANSTORE_C2S = 1201;
	public static final int	PLAYER_FETCHCLANSTORE_S2C = 11201;
	public static final int	PLAYER_OPERATECLANSTOREITEM_C2S = 1202;
	public static final int	PLAYER_OPERATECLANSTOREITEM_S2C = 11202;
	public static final int	PLAYER_FETCHCLANSTORERECORD_C2S = 1203;
	public static final int	PLAYER_FETCHCLANSTORERECORD_S2C = 11203;
	public static final int	PLAYER_ONCLANSTOREITEMCHANGENTF_NTF = 1204;
	public static final int	PLAYER_ONCLANSTORESTOCKNTF_NTF = 1205;
	public static final int	PLAYER_FETCHCLANTECHINFO_C2S = 1301;
	public static final int	PLAYER_FETCHCLANTECHINFO_S2C = 11301;
	public static final int	PLAYER_CLANTECHDONATE_C2S = 1302;
	public static final int	PLAYER_CLANTECHDONATE_S2C = 11302;
	public static final int	PLAYER_CLANTECHRESEARCH_C2S = 1303;
	public static final int	PLAYER_CLANTECHRESEARCH_S2C = 11303;
	public static final int	PLAYER_CLANTECHRECOMMEND_C2S = 1304;
	public static final int	PLAYER_CLANTECHRECOMMEND_S2C = 11304;
	public static final int	PLAYER_CLANTECHDETAIL_C2S = 1305;
	public static final int	PLAYER_CLANTECHDETAIL_S2C = 11305;
	public static final int	PLAYER_NOTIFYCLANTECHINFO_NTF = 1306;
	public static final int	PLAYER_DEBUGCOMMAND_C2S = 1401;
	public static final int	PLAYER_DEBUGCOMMAND_S2C = 11401;
	public static final int	PLAYER_GETDEBUGCOMMANDLIST_C2S = 1402;
	public static final int	PLAYER_GETDEBUGCOMMANDLIST_S2C = 11402;
	public static final int	PLAYER_SEARCHMONSTER_C2S = 1403;
	public static final int	PLAYER_SEARCHMONSTER_S2C = 11403;
	public static final int	PLAYER_VERIFYMOVECITY_C2S = 1404;
	public static final int	PLAYER_VERIFYMOVECITY_S2C = 11404;
	public static final int	PLAYER_BUYANDUSEITEM_C2S = 1405;
	public static final int	PLAYER_BUYANDUSEITEM_S2C = 11405;
	public static final int	PLAYER_USEITEM_C2S = 1406;
	public static final int	PLAYER_USEITEM_S2C = 11406;
	public static final int	PLAYER_SWITCHVIEWSCENE_C2S = 1407;
	public static final int	PLAYER_SWITCHVIEWSCENE_S2C = 11407;
	public static final int	PLAYER_QUERYPLAYERCARDINFO_C2S = 1408;
	public static final int	PLAYER_QUERYPLAYERCARDINFO_S2C = 11408;
	public static final int	PLAYER_QUERYPLAYERCARDINFODETAIL_C2S = 1409;
	public static final int	PLAYER_QUERYPLAYERCARDINFODETAIL_S2C = 11409;
	public static final int	PLAYER_ADDEXP_NTF = 1410;
	public static final int	PLAYER_NOTIFYDISABLEMODULE_NTF = 1411;
	public static final int	PLAYER_NOTIFYOPENMODULE_NTF = 1412;
	public static final int	PLAYER_SETWALLGARRISON_C2S = 1413;
	public static final int	PLAYER_SETWALLGARRISON_S2C = 11413;
	public static final int	PLAYER_REPAIRWALL_C2S = 1414;
	public static final int	PLAYER_REPAIRWALL_S2C = 11414;
	public static final int	PLAYER_OUTFIREWALL_C2S = 1415;
	public static final int	PLAYER_OUTFIREWALL_S2C = 11415;
	public static final int	PLAYER_CITYFALL_C2S = 1416;
	public static final int	PLAYER_CITYFALL_S2C = 11416;
	public static final int	PLAYER_CHANGECLIENTINFO_C2S = 1417;
	public static final int	PLAYER_CHANGECLIENTINFO_S2C = 11417;
	public static final int	PLAYER_SYSTEMTIMENTF_NTF = 1420;
	public static final int	PLAYER_FINISHGUIDANCE_C2S = 1421;
	public static final int	PLAYER_FINISHGUIDANCE_S2C = 11421;
	public static final int	PLAYER_EXPLOREINFONTF_NTF = 1425;
	public static final int	PLAYER_CHANGEPLAYERNAME_C2S = 1426;
	public static final int	PLAYER_CHANGEPLAYERNAME_S2C = 11426;
	public static final int	PLAYER_CHANGEAVATAR_C2S = 1427;
	public static final int	PLAYER_CHANGEAVATAR_S2C = 11427;
	public static final int	PLAYER_MARKNEWBIESTEP_C2S = 1428;
	public static final int	PLAYER_MARKNEWBIESTEP_S2C = 11428;
	public static final int	PLAYER_GIVENEWBIEHERO_C2S = 1429;
	public static final int	PLAYER_GIVENEWBIEHERO_S2C = 11429;
	public static final int	PLAYER_REMOVEREDDOT_C2S = 1430;
	public static final int	PLAYER_REMOVEREDDOT_S2C = 11430;
	public static final int	PLAYER_QUERYPLAYERKILLDETAIL_C2S = 1431;
	public static final int	PLAYER_QUERYPLAYERKILLDETAIL_S2C = 11431;
	public static final int	PLAYER_TAKEDAILYFREEENERGY_C2S = 1432;
	public static final int	PLAYER_TAKEDAILYFREEENERGY_S2C = 11432;
	public static final int	PLAYER_BUYENERGY_C2S = 1433;
	public static final int	PLAYER_BUYENERGY_S2C = 11433;
	public static final int	PLAYER_RECRUIT_C2S = 1434;
	public static final int	PLAYER_RECRUIT_S2C = 11434;
	public static final int	PLAYER_UGCCHECK_C2S = 1435;
	public static final int	PLAYER_UGCCHECK_S2C = 11435;
	public static final int	PLAYER_TRIGGERQLOG_C2S = 1436;
	public static final int	PLAYER_TRIGGERQLOG_S2C = 11436;
	public static final int	PLAYER_QUERYPLAYERCARDHEAD_C2S = 1437;
	public static final int	PLAYER_QUERYPLAYERCARDHEAD_S2C = 11437;
	public static final int	PLAYER_SETSPASSWORD_C2S = 1438;
	public static final int	PLAYER_SETSPASSWORD_S2C = 11438;
	public static final int	PLAYER_CLOSESPASSWORD_C2S = 1439;
	public static final int	PLAYER_CLOSESPASSWORD_S2C = 11439;
	public static final int	PLAYER_CANCELCLOSESPW_C2S = 1440;
	public static final int	PLAYER_CANCELCLOSESPW_S2C = 11440;
	public static final int	PLAYER_USEEXPRESSION_C2S = 1441;
	public static final int	PLAYER_USEEXPRESSION_S2C = 11441;
	public static final int	PLAYER_SETSPASSWORDSTATUS_C2S = 1442;
	public static final int	PLAYER_SETSPASSWORDSTATUS_S2C = 11442;
	public static final int	PLAYER_SETPFLAG_C2S = 1443;
	public static final int	PLAYER_SETPFLAG_S2C = 11443;
	public static final int	PLAYER_GETVIPPRIVILEGEBOX_C2S = 1444;
	public static final int	PLAYER_GETVIPPRIVILEGEBOX_S2C = 11444;
	public static final int	PLAYER_GETVIPDAILYBOX_C2S = 1445;
	public static final int	PLAYER_GETVIPDAILYBOX_S2C = 11445;
	public static final int	PLAYER_SENDSYSASSISTMAIL_C2S = 1446;
	public static final int	PLAYER_SENDSYSASSISTMAIL_S2C = 11446;
	public static final int	PLAYER_SWITCHSETTING_C2S = 1447;
	public static final int	PLAYER_SWITCHSETTING_S2C = 11447;
	public static final int	PLAYER_AFTERBINDACCOUNT_C2S = 1448;
	public static final int	PLAYER_AFTERBINDACCOUNT_S2C = 11448;
	public static final int	PLAYER_MODIFYPUSHSETTING_C2S = 1450;
	public static final int	PLAYER_MODIFYPUSHSETTING_S2C = 11450;
	public static final int	PLAYER_QUERYPUSHSETTING_C2S = 1451;
	public static final int	PLAYER_QUERYPUSHSETTING_S2C = 11451;
	public static final int	PLAYER_GETDAILYDISCOUNTREWARD_C2S = 1501;
	public static final int	PLAYER_GETDAILYDISCOUNTREWARD_S2C = 11501;
	public static final int	PLAYER_SWITCHDAILYDISCOUNTHERO_C2S = 1502;
	public static final int	PLAYER_SWITCHDAILYDISCOUNTHERO_S2C = 11502;
	public static final int	PLAYER_GETSUPERDAILYDISCOUNTREWARD_C2S = 1503;
	public static final int	PLAYER_GETSUPERDAILYDISCOUNTREWARD_S2C = 11503;
	public static final int	PLAYER_GETDISCOUNTSTOREINFO_C2S = 1601;
	public static final int	PLAYER_GETDISCOUNTSTOREINFO_S2C = 11601;
	public static final int	PLAYER_BUYDISCOUNTSTOREITEM_C2S = 1602;
	public static final int	PLAYER_BUYDISCOUNTSTOREITEM_S2C = 11602;
	public static final int	PLAYER_REFRESHDISCOUNTSTORE_C2S = 1603;
	public static final int	PLAYER_REFRESHDISCOUNTSTORE_S2C = 11603;
	public static final int	PLAYER_EDITFORMATION_C2S = 1701;
	public static final int	PLAYER_EDITFORMATION_S2C = 11701;
	public static final int	PLAYER_RENAMEFORMATION_C2S = 1702;
	public static final int	PLAYER_RENAMEFORMATION_S2C = 11702;
	public static final int	PLAYER_REMOVEFORMATION_C2S = 1703;
	public static final int	PLAYER_REMOVEFORMATION_S2C = 11703;
	public static final int	PLAYER_EDITRHTROOP_C2S = 1704;
	public static final int	PLAYER_EDITRHTROOP_S2C = 11704;
	public static final int	PLAYER_EDITVIRTUALFORMATION_C2S = 1705;
	public static final int	PLAYER_EDITVIRTUALFORMATION_S2C = 11705;
	public static final int	PLAYER_SEARCHPLAYER_C2S = 1801;
	public static final int	PLAYER_SEARCHPLAYER_S2C = 11801;
	public static final int	PLAYER_SEARCHPLAYERBYNAME_C2S = 1802;
	public static final int	PLAYER_SEARCHPLAYERBYNAME_S2C = 11802;
	public static final int	PLAYER_ADDFRIENDAPPLY_C2S = 1803;
	public static final int	PLAYER_ADDFRIENDAPPLY_S2C = 11803;
	public static final int	PLAYER_AGREEFRIENDAPPLY_C2S = 1804;
	public static final int	PLAYER_AGREEFRIENDAPPLY_S2C = 11804;
	public static final int	PLAYER_REFUSEFRIENDAPPLY_C2S = 1805;
	public static final int	PLAYER_REFUSEFRIENDAPPLY_S2C = 11805;
	public static final int	PLAYER_DELFRIEND_C2S = 1806;
	public static final int	PLAYER_DELFRIEND_S2C = 11806;
	public static final int	PLAYER_SHIELDPLAYER_C2S = 1807;
	public static final int	PLAYER_SHIELDPLAYER_S2C = 11807;
	public static final int	PLAYER_REMOVESHIELDPLAYER_C2S = 1808;
	public static final int	PLAYER_REMOVESHIELDPLAYER_S2C = 11808;
	public static final int	PLAYER_UNLOCKHERO_C2S = 1901;
	public static final int	PLAYER_UNLOCKHERO_S2C = 11901;
	public static final int	PLAYER_HEROLEVELUP_C2S = 1902;
	public static final int	PLAYER_HEROLEVELUP_S2C = 11902;
	public static final int	PLAYER_HEROSTAGEUP_C2S = 1903;
	public static final int	PLAYER_HEROSTAGEUP_S2C = 11903;
	public static final int	PLAYER_HEROSKILLLEVELUP_C2S = 1904;
	public static final int	PLAYER_HEROSKILLLEVELUP_S2C = 11904;
	public static final int	PLAYER_RESETSKILL_C2S = 1905;
	public static final int	PLAYER_RESETSKILL_S2C = 11905;
	public static final int	PLAYER_CHANGESKILLSLOTLIMIT_C2S = 1906;
	public static final int	PLAYER_CHANGESKILLSLOTLIMIT_S2C = 11906;
	public static final int	PLAYER_HEROTALENTLEVELUP_C2S = 1907;
	public static final int	PLAYER_HEROTALENTLEVELUP_S2C = 11907;
	public static final int	PLAYER_SWITCHTALENTPAGE_C2S = 1908;
	public static final int	PLAYER_SWITCHTALENTPAGE_S2C = 11908;
	public static final int	PLAYER_RESETNAMETALENTPAGE_C2S = 1909;
	public static final int	PLAYER_RESETNAMETALENTPAGE_S2C = 11909;
	public static final int	PLAYER_HEROITEMEXCHANGE_C2S = 1910;
	public static final int	PLAYER_HEROITEMEXCHANGE_S2C = 11910;
	public static final int	PLAYER_HERORESETTALENT_C2S = 1911;
	public static final int	PLAYER_HERORESETTALENT_S2C = 11911;
	public static final int	PLAYER_HEROINTENSIVESKILL_C2S = 1912;
	public static final int	PLAYER_HEROINTENSIVESKILL_S2C = 11912;
	public static final int	PLAYER_HOSPITALTREAT_C2S = 2001;
	public static final int	PLAYER_HOSPITALTREAT_S2C = 12001;
	public static final int	PLAYER_HOSPITALFASTTREAT_C2S = 2002;
	public static final int	PLAYER_HOSPITALFASTTREAT_S2C = 12002;
	public static final int	PLAYER_HOSPITALRETURNSOLDIERS_C2S = 2003;
	public static final int	PLAYER_HOSPITALRETURNSOLDIERS_S2C = 12003;
	public static final int	PLAYER_PROMPTFORPLANEUNLOCK_C2S = 2101;
	public static final int	PLAYER_PROMPTFORPLANEUNLOCK_S2C = 12101;
	public static final int	PLAYER_NOTICEBOARD_C2S = 2102;
	public static final int	PLAYER_NOTICEBOARD_S2C = 12102;
	public static final int	PLAYER_NOTICEBOARDNTF_NTF = 2103;
	public static final int	PLAYER_TAKEPROSPERITYREWARD_C2S = 2104;
	public static final int	PLAYER_TAKEPROSPERITYREWARD_S2C = 12104;
	public static final int	PLAYER_COMPLETEQUESTIONNAIRE_C2S = 2105;
	public static final int	PLAYER_COMPLETEQUESTIONNAIRE_S2C = 12105;
	public static final int	PLAYER_QUESTIONNAIREEXPIRED_C2S = 2106;
	public static final int	PLAYER_QUESTIONNAIREEXPIRED_S2C = 12106;
	public static final int	PLAYER_UPDATECLIENTSTAGE_C2S = 2107;
	public static final int	PLAYER_UPDATECLIENTSTAGE_S2C = 12107;
	public static final int	PLAYER_COMPLETEMAINTASK_C2S = 2108;
	public static final int	PLAYER_COMPLETEMAINTASK_S2C = 12108;
	public static final int	PLAYER_KINGAPPOINT_C2S = 2201;
	public static final int	PLAYER_KINGAPPOINT_S2C = 12201;
	public static final int	PLAYER_KINGOPENBUFF_C2S = 2202;
	public static final int	PLAYER_KINGOPENBUFF_S2C = 12202;
	public static final int	PLAYER_KINGSENDGIFT_C2S = 2203;
	public static final int	PLAYER_KINGSENDGIFT_S2C = 12203;
	public static final int	PLAYER_KINGUSESKILL_C2S = 2204;
	public static final int	PLAYER_KINGUSESKILL_S2C = 12204;
	public static final int	PLAYER_FETCHHISTORYKINGS_C2S = 2205;
	public static final int	PLAYER_FETCHHISTORYKINGS_S2C = 12205;
	public static final int	PLAYER_FETCHKINGDOMGIFTINFO_C2S = 2206;
	public static final int	PLAYER_FETCHKINGDOMGIFTINFO_S2C = 12206;
	public static final int	PLAYER_FETCHKINGDOMOFFICEINFO_C2S = 2207;
	public static final int	PLAYER_FETCHKINGDOMOFFICEINFO_S2C = 12207;
	public static final int	PLAYER_MAILUNREADGET_C2S = 2301;
	public static final int	PLAYER_MAILUNREADGET_S2C = 12301;
	public static final int	PLAYER_GETNEWMAIL_NTF = 2302;
	public static final int	PLAYER_MAILBATCHREAD_C2S = 2303;
	public static final int	PLAYER_MAILBATCHREAD_S2C = 12303;
	public static final int	PLAYER_MAILREWARDGET_C2S = 2304;
	public static final int	PLAYER_MAILREWARDGET_S2C = 12304;
	public static final int	PLAYER_DELMAIL_NTF = 2305;
	public static final int	PLAYER_READBATTLELOG_C2S = 2306;
	public static final int	PLAYER_READBATTLELOG_S2C = 12306;
	public static final int	PLAYER_READBATTLERECORDDETAIL_C2S = 2307;
	public static final int	PLAYER_READBATTLERECORDDETAIL_S2C = 12307;
	public static final int	PLAYER_MAILDEL_C2S = 2308;
	public static final int	PLAYER_MAILDEL_S2C = 12308;
	public static final int	PLAYER_MAILBATCHDEL_C2S = 2309;
	public static final int	PLAYER_MAILBATCHDEL_S2C = 12309;
	public static final int	PLAYER_SENDPERSONALMAIL_C2S = 2310;
	public static final int	PLAYER_SENDPERSONALMAIL_S2C = 12310;
	public static final int	PLAYER_SENDCLANMAIL_C2S = 2311;
	public static final int	PLAYER_SENDCLANMAIL_S2C = 12311;
	public static final int	PLAYER_SENDZONEMAIL_C2S = 2312;
	public static final int	PLAYER_SENDZONEMAIL_S2C = 12312;
	public static final int	PLAYER_SHIELDPERSONMAIL_C2S = 2313;
	public static final int	PLAYER_SHIELDPERSONMAIL_S2C = 12313;
	public static final int	PLAYER_UNSHIELDPERSONMAIL_C2S = 2314;
	public static final int	PLAYER_UNSHIELDPERSONMAIL_S2C = 12314;
	public static final int	PLAYER_READANDGETREWARDALLMAIL_C2S = 2315;
	public static final int	PLAYER_READANDGETREWARDALLMAIL_S2C = 12315;
	public static final int	PLAYER_MAILSTORE_C2S = 2316;
	public static final int	PLAYER_MAILSTORE_S2C = 12316;
	public static final int	PLAYER_PUSHALLMAILHEADER_NTF = 2317;
	public static final int	PLAYER_MAILBATCHGETCONTENT_C2S = 2318;
	public static final int	PLAYER_MAILBATCHGETCONTENT_S2C = 12318;
	public static final int	PLAYER_TAKEMILESTONEREWARDC2S_C2S = 2401;
	public static final int	PLAYER_TAKEMILESTONEREWARDC2S_S2C = 12401;
	public static final int	PLAYER_GETMILESTONEHISTORY_C2S = 2402;
	public static final int	PLAYER_GETMILESTONEHISTORY_S2C = 12402;
	public static final int	PLAYER_GETMILESTONERANK_C2S = 2403;
	public static final int	PLAYER_GETMILESTONERANK_S2C = 12403;
	public static final int	PLAYER_GETZONESUNDERSEASON_C2S = 2501;
	public static final int	PLAYER_GETZONESUNDERSEASON_S2C = 12501;
	public static final int	PLAYER_GETZONESSTATUS_C2S = 2502;
	public static final int	PLAYER_GETZONESSTATUS_S2C = 12502;
	public static final int	PLAYER_PRODUCTNEWINNERBUILD_C2S = 2601;
	public static final int	PLAYER_PRODUCTNEWINNERBUILD_S2C = 12601;
	public static final int	PLAYER_SALEINNERBUILD_C2S = 2602;
	public static final int	PLAYER_SALEINNERBUILD_S2C = 12602;
	public static final int	PLAYER_PRODUCEINNERBUILD_C2S = 2603;
	public static final int	PLAYER_PRODUCEINNERBUILD_S2C = 12603;
	public static final int	PLAYER_PRODUCESECRETWEAPON_C2S = 2604;
	public static final int	PLAYER_PRODUCESECRETWEAPON_S2C = 12604;
	public static final int	PLAYER_RECVINNERMINERES_C2S = 2605;
	public static final int	PLAYER_RECVINNERMINERES_S2C = 12605;
	public static final int	PLAYER_STARTCRISISBATTLE_C2S = 2606;
	public static final int	PLAYER_STARTCRISISBATTLE_S2C = 12606;
	public static final int	PLAYER_ENDCRISISBATTLE_C2S = 2607;
	public static final int	PLAYER_ENDCRISISBATTLE_S2C = 12607;
	public static final int	PLAYER_SEARCHNEWBASES_C2S = 2608;
	public static final int	PLAYER_SEARCHNEWBASES_S2C = 12608;
	public static final int	PLAYER_FLUSHNEWBASES_C2S = 2609;
	public static final int	PLAYER_FLUSHNEWBASES_S2C = 12609;
	public static final int	PLAYER_MIGRATENEWBASE_C2S = 2610;
	public static final int	PLAYER_MIGRATENEWBASE_S2C = 12610;
	public static final int	PLAYER_UPGRADEBUILD_C2S = 2611;
	public static final int	PLAYER_UPGRADEBUILD_S2C = 12611;
	public static final int	PLAYER_ISSUECOMMAND_C2S = 2612;
	public static final int	PLAYER_ISSUECOMMAND_S2C = 12612;
	public static final int	PLAYER_MOVEINNERBUILD_C2S = 2613;
	public static final int	PLAYER_MOVEINNERBUILD_S2C = 12613;
	public static final int	PLAYER_UPGRADEINNERBUILD_C2S = 2614;
	public static final int	PLAYER_UPGRADEINNERBUILD_S2C = 12614;
    public static final int PLAYER_UNLOCKNEWAREA_C2S = 2615;
    public static final int PLAYER_UNLOCKNEWAREA_S2C = 12615;
	public static final int	PLAYER_PAYMENTOVER_C2S = 2701;
	public static final int	PLAYER_PAYMENTOVER_S2C = 12701;
	public static final int	PLAYER_APPLYGOODSORDER_C2S = 2702;
	public static final int	PLAYER_APPLYGOODSORDER_S2C = 12702;
	public static final int	PLAYER_FAKERECHARGE_C2S = 2703;
	public static final int	PLAYER_FAKERECHARGE_S2C = 12703;
	public static final int	PLAYER_JUDGERECHARGEPACKAGE_C2S = 2704;
	public static final int	PLAYER_JUDGERECHARGEPACKAGE_S2C = 12704;
	public static final int	PLAYER_TRIGGERBUNDLESHOW_C2S = 2705;
	public static final int	PLAYER_TRIGGERBUNDLESHOW_S2C = 12705;
	public static final int	PLAYER_POSITIONMARK_C2S = 2801;
	public static final int	PLAYER_POSITIONMARK_S2C = 12801;
	public static final int	PLAYER_FETCHCLANPOSITIONMARK_C2S = 2802;
	public static final int	PLAYER_FETCHCLANPOSITIONMARK_S2C = 12802;
	public static final int	PLAYER_SPEEDQUEUETASK_C2S = 2901;
	public static final int	PLAYER_SPEEDQUEUETASK_S2C = 12901;
	public static final int	PLAYER_FINISHQUEUETASK_C2S = 2902;
	public static final int	PLAYER_FINISHQUEUETASK_S2C = 12902;
	public static final int	PLAYER_CANCELQUEUETASK_C2S = 2903;
	public static final int	PLAYER_CANCELQUEUETASK_S2C = 12903;
	public static final int	PLAYER_QUEUEFREESPEED_C2S = 2904;
	public static final int	PLAYER_QUEUEFREESPEED_S2C = 12904;
	public static final int	PLAYER_QUEUEFINISH_NTF = 2905;
	public static final int	PLAYER_CANCELRALLY_C2S = 3001;
	public static final int	PLAYER_CANCELRALLY_S2C = 13001;
	public static final int	PLAYER_QUERYRALLYLIST_C2S = 3002;
	public static final int	PLAYER_QUERYRALLYLIST_S2C = 13002;
	public static final int	PLAYER_QUERYONERALLY_C2S = 3003;
	public static final int	PLAYER_QUERYONERALLY_S2C = 13003;
	public static final int	PLAYER_REPATRIATEONEARMYINRALLY_C2S = 3004;
	public static final int	PLAYER_REPATRIATEONEARMYINRALLY_S2C = 13004;
	public static final int	PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_C2S = 3005;
	public static final int	PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_S2C = 13005;
	public static final int	PLAYER_FETCHWARNINGLIST_C2S = 3006;
	public static final int	PLAYER_FETCHWARNINGLIST_S2C = 13006;
	public static final int	PLAYER_SETWARNINGITEMIGNORETAG_C2S = 3007;
	public static final int	PLAYER_SETWARNINGITEMIGNORETAG_S2C = 13007;
	public static final int	PLAYER_IGNOREWARNINGALL_C2S = 3008;
	public static final int	PLAYER_IGNOREWARNINGALL_S2C = 13008;
	public static final int	PLAYER_FETCHCITYINNERARMY_C2S = 3009;
	public static final int	PLAYER_FETCHCITYINNERARMY_S2C = 13009;
	public static final int	PLAYER_REPATRIATEONEARMYINASSIST_C2S = 3010;
	public static final int	PLAYER_REPATRIATEONEARMYINASSIST_S2C = 13010;
	public static final int	PLAYER_FETCHCITYASSISTHISTORY_C2S = 3011;
	public static final int	PLAYER_FETCHCITYASSISTHISTORY_S2C = 13011;
	public static final int	PLAYER_ONJOINRALLYARMY_NTF = 3012;
	public static final int	PLAYER_SENDRALLYINVITEMSG_C2S = 3013;
	public static final int	PLAYER_SENDRALLYINVITEMSG_S2C = 13013;
	public static final int	PLAYER_CLICKRALLYINVITE_C2S = 3014;
	public static final int	PLAYER_CLICKRALLYINVITE_S2C = 13014;
	public static final int	PLAYER_GETRANKPAGEINFO_C2S = 3101;
	public static final int	PLAYER_GETRANKPAGEINFO_S2C = 13101;
	public static final int	PLAYER_GETTOPRANKINFO_C2S = 3102;
	public static final int	PLAYER_GETTOPRANKINFO_S2C = 13102;
	public static final int	PLAYER_REPORTMAPVERSION_C2S = 3201;
	public static final int	PLAYER_REPORTMAPVERSION_S2C = 13201;
	public static final int	PLAYER_CHECKZONEVIEW_C2S = 3202;
	public static final int	PLAYER_CHECKZONEVIEW_S2C = 13202;
	public static final int	PLAYER_UPDATEVIEW_C2S = 3203;
	public static final int	PLAYER_UPDATEVIEW_S2C = 13203;
	public static final int	PLAYER_CREATEARMY_C2S = 3204;
	public static final int	PLAYER_CREATEARMY_S2C = 13204;
	public static final int	PLAYER_CHANGEARMYACTION_C2S = 3205;
	public static final int	PLAYER_CHANGEARMYACTION_S2C = 13205;
	public static final int	PLAYER_FORCEDDEFEATARMY_C2S = 3206;
	public static final int	PLAYER_FORCEDDEFEATARMY_S2C = 13206;
	public static final int	PLAYER_SEARCHWALKPATH_C2S = 3207;
	public static final int	PLAYER_SEARCHWALKPATH_S2C = 13207;
	public static final int	PLAYER_CHECKCANATTACK_C2S = 3208;
	public static final int	PLAYER_CHECKCANATTACK_S2C = 13208;
	public static final int	PLAYER_CHANGEMAP_NTF = 3209;
	public static final int	PLAYER_CHANGEMAPDONE_NTF = 3210;
	public static final int	PLAYER_ORDINARYATTACK_NTF = 3211;
	public static final int	PLAYER_MARQUEEMESSAGE_NTF = 3212;
	public static final int	PLAYER_MOVECITYACTIONNTF_NTF = 3213;
	public static final int	PLAYER_MONSTERINVOKE_NTF = 3214;
	public static final int	PLAYER_PLAYANNOUNCEMENT_NTF = 3215;
	public static final int	PLAYER_SKILLFIRE_NTF = 3216;
	public static final int	PLAYER_MONSTERDROPOUT_NTF = 3217;
	public static final int	PLAYER_PLAYDIALOG_NTF = 3218;
	public static final int	PLAYER_SEARCHRESOURCE_C2S = 3219;
	public static final int	PLAYER_SEARCHRESOURCE_S2C = 13219;
	public static final int	PLAYER_ENTEREXPEDITION_C2S = 3220;
	public static final int	PLAYER_ENTEREXPEDITION_S2C = 13220;
	public static final int	PLAYER_LEAVEDUNGEON_C2S = 3221;
	public static final int	PLAYER_LEAVEDUNGEON_S2C = 13221;
	public static final int	PLAYER_REENTERDUNGEON_C2S = 3222;
	public static final int	PLAYER_REENTERDUNGEON_S2C = 13222;
	public static final int	PLAYER_CHANGETOBIGSCENE_C2S = 3223;
	public static final int	PLAYER_CHANGETOBIGSCENE_S2C = 13223;
	public static final int	PLAYER_USEDUNGEONSKILL_C2S = 3229;
	public static final int	PLAYER_USEDUNGEONSKILL_S2C = 13229;
	public static final int	PLAYER_CLOSECLANRECOMMEND_C2S = 3230;
	public static final int	PLAYER_CLOSECLANRECOMMEND_S2C = 13230;
	public static final int	PLAYER_RECOMMENDCLAN_NTF = 3231;
	public static final int	PLAYER_SPECIALSKILLFIRE_NTF = 3234;
	public static final int	PLAYER_SPECIALORDINARYATTACK_NTF = 3235;
	public static final int	PLAYER_CONSTRUCTCLANRESBUILDING_C2S = 3301;
	public static final int	PLAYER_CONSTRUCTCLANRESBUILDING_S2C = 13301;
	public static final int	PLAYER_FETCHCLANRESBUILDINGINFO_C2S = 3302;
	public static final int	PLAYER_FETCHCLANRESBUILDINGINFO_S2C = 13302;
	public static final int	PLAYER_SKYNETDOINGTASK_C2S = 3401;
	public static final int	PLAYER_SKYNETDOINGTASK_S2C = 13401;
	public static final int	PLAYER_SKYNETTAKETASKREWARD_C2S = 3402;
	public static final int	PLAYER_SKYNETTAKETASKREWARD_S2C = 13402;
	public static final int	PLAYER_SKYNETSTOREINFO_C2S = 3403;
	public static final int	PLAYER_SKYNETSTOREINFO_S2C = 13403;
	public static final int	PLAYER_SKYNETCHARGE_C2S = 3404;
	public static final int	PLAYER_SKYNETCHARGE_S2C = 13404;
	public static final int	PLAYER_SKYNETBUYSTORE_C2S = 3405;
	public static final int	PLAYER_SKYNETBUYSTORE_S2C = 13405;
	public static final int	PLAYER_SKYNETFINDMONSTER_C2S = 3406;
	public static final int	PLAYER_SKYNETFINDMONSTER_S2C = 13406;
	public static final int	PLAYER_SOLDIERTRAIN_C2S = 3501;
	public static final int	PLAYER_SOLDIERTRAIN_S2C = 13501;
	public static final int	PLAYER_SOLDIERFASTTRAIN_C2S = 3502;
	public static final int	PLAYER_SOLDIERFASTTRAIN_S2C = 13502;
	public static final int	PLAYER_SOLDIERGATHER_C2S = 3503;
	public static final int	PLAYER_SOLDIERGATHER_S2C = 13503;
	public static final int	PLAYER_SOLDIERRUSHTRAIN_C2S = 3504;
	public static final int	PLAYER_SOLDIERRUSHTRAIN_S2C = 13504;
	public static final int	PLAYER_SOLDIERDISMISS_C2S = 3505;
	public static final int	PLAYER_SOLDIERDISMISS_S2C = 13505;
	public static final int	PLAYER_SOLDIERLEVELUP_C2S = 3506;
	public static final int	PLAYER_SOLDIERLEVELUP_S2C = 13506;
	public static final int	PLAYER_SOLDIERFASTLEVELUP_C2S = 3507;
	public static final int	PLAYER_SOLDIERFASTLEVELUP_S2C = 13507;
	public static final int	PLAYER_FETCHCOMMISSARIATSTOREINFO_C2S = 3601;
	public static final int	PLAYER_FETCHCOMMISSARIATSTOREINFO_S2C = 13601;
	public static final int	PLAYER_BUYSTOREITEM_C2S = 3602;
	public static final int	PLAYER_BUYSTOREITEM_S2C = 13602;
	public static final int	PLAYER_BUYANDUSESTOREITEM_C2S = 3603;
	public static final int	PLAYER_BUYANDUSESTOREITEM_S2C = 13603;
	public static final int	PLAYER_TASKAWARD_C2S = 3701;
	public static final int	PLAYER_TASKAWARD_S2C = 13701;
	public static final int	PLAYER_RESEARCHTECH_C2S = 3801;
	public static final int	PLAYER_RESEARCHTECH_S2C = 13801;
	public static final int	PLAYER_IMMEDIATELYRESEARCHTECH_C2S = 3802;
	public static final int	PLAYER_IMMEDIATELYRESEARCHTECH_S2C = 13802;
	public static final int	PLAYER_RECEIVETECH_C2S = 3803;
	public static final int	PLAYER_RECEIVETECH_S2C = 13803;
	public static final int	PLAYER_GETVIPSTOREINFO_C2S = 3901;
	public static final int	PLAYER_GETVIPSTOREINFO_S2C = 13901;
	public static final int	PLAYER_BUYVIPSTOREITEM_C2S = 3902;
	public static final int	PLAYER_BUYVIPSTOREITEM_S2C = 13902;
	public static final int	PLAYER_SELECTVIPHEROITEM_C2S = 3903;
	public static final int	PLAYER_SELECTVIPHEROITEM_S2C = 13903;

    private static final Map<Integer, GeneratedMessageV3> MSG_ID_2_PROTO_MSG = new HashMap<>();

    static {
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRGETSERVERLIST_C2S_MSG, CsAccount.DirGetServerList_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRGETSERVERLIST_S2C_MSG, CsAccount.DirGetServerList_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRGETZONE_C2S_MSG, CsAccount.DirGetZone_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRGETZONE_S2C_MSG, CsAccount.DirGetZone_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRDELACCOUNT_C2S_MSG, CsAccount.DirDelAccount_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRDELACCOUNT_S2C_MSG, CsAccount.DirDelAccount_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DEVENVRESINFONTF, CsAccount.DevEnvResInfoNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRREGISTERACCOUNT_C2S_MSG, CsAccount.DirRegisterAccount_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRREGISTERACCOUNT_S2C_MSG, CsAccount.DirRegisterAccount_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.KICKOFFNTF, CsAccount.KickOffNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTLOGIN_C2S_MSG, CsAccount.DirAccountLogin_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTLOGIN_S2C_MSG, CsAccount.DirAccountLogin_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTBIND_C2S_MSG, CsAccount.DirAccountBind_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTBIND_S2C_MSG, CsAccount.DirAccountBind_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTUNBIND_C2S_MSG, CsAccount.DirAccountUnbind_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTUNBIND_S2C_MSG, CsAccount.DirAccountUnbind_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTBINDINFO_C2S_MSG, CsAccount.DirAccountBindInfo_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRACCOUNTBINDINFO_S2C_MSG, CsAccount.DirAccountBindInfo_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRPING_C2S_MSG, CsAccount.DirPing_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DIRPING_S2C_MSG, CsAccount.DirPing_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.LOGIN_C2S_MSG, User.Login_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.LOGIN_S2C_MSG, User.Login_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.GETROLELIST_C2S_MSG, User.GetRoleList_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.GETROLELIST_S2C_MSG, User.GetRoleList_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTLOGIN_C2S_MSG, User.AccountLogin_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTLOGIN_S2C_MSG, User.AccountLogin_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTBIND_C2S_MSG, User.AccountBind_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTBIND_S2C_MSG, User.AccountBind_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTUNBIND_C2S_MSG, User.AccountUnbind_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTUNBIND_S2C_MSG, User.AccountUnbind_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTBINDINFO_C2S_MSG, User.AccountBindInfo_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ACCOUNTBINDINFO_S2C_MSG, User.AccountBindInfo_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.RALLYNTFMSG, User.RallyNtfMsg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.COMMANDINVALID_S2C_MSG, User.CommandInvalid_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.HOPECOMMANDNTFMSG, User.HopeCommandNtfMsg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.REPORTEXECUTE_C2S_MSG, User.ReportExecute_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.REPORTEXECUTE_S2C_MSG, User.ReportExecute_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.DISCONNECT, Core.Disconnet_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.WARN, Core.Warn_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.LOGLOADING_C2S_MSG, Core.LogLoading_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.LOGLOADING_S2C_MSG, Core.LogLoading_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.KEEPALIVE_C2S_MSG, Core.KeepAlive_C2S_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.KEEPALIVE_S2C_MSG, Core.KeepAlive_S2C_Msg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.ENTITYNTFMSG, Entity.EntityNtfMsg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.SCENEOBJBRIEFNTFMSG, Entity.SceneObjBriefNtfMsg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.SCENEOBJBRIEFATTR, Entity.SceneObjBriefAttr.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACHIEVEMENT_C2S, PlayerAchievement.Player_FetchAchievement_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACHIEVEMENT_S2C, PlayerAchievement.Player_FetchAchievement_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEACHIEVEMENTREWARD_C2S, PlayerAchievement.Player_TakeAchievementReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEACHIEVEMENTREWARD_S2C, PlayerAchievement.Player_TakeAchievementReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACHIEVEMENTCOMPLETENTF_NTF, PlayerAchievement.AchievementCompleteNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHAREACHIEVEMENT_C2S, PlayerAchievement.Player_ShareAchievement_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHAREACHIEVEMENT_S2C, PlayerAchievement.Player_ShareAchievement_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYTAKEREWARD_C2S, PlayerActivity.Player_ActivityTakeReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYTAKEREWARD_S2C, PlayerActivity.Player_ActivityTakeReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYBUYGOOD_C2S, PlayerActivity.Player_ActivityBuyGood_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYBUYGOOD_S2C, PlayerActivity.Player_ActivityBuyGood_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYBUYSECONDQUEUE_C2S, PlayerActivity.Player_ActivityBuySecondQueue_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ACTIVITYBUYSECONDQUEUE_S2C, PlayerActivity.Player_ActivityBuySecondQueue_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDACTIVITYINVITEMSG_C2S, PlayerActivity.Player_InviteJoinActivity_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDACTIVITYINVITEMSG_S2C, PlayerActivity.Player_InviteJoinActivity_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BESTCOMMANDERFETCH_C2S, PlayerActivity.Player_BestCommanderFetch_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BESTCOMMANDERFETCH_S2C, PlayerActivity.Player_BestCommanderFetch_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BESTCOMMANDERHISTORYRANK_C2S, PlayerActivity.Player_BestCommanderHistoryRank_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BESTCOMMANDERHISTORYRANK_S2C, PlayerActivity.Player_BestCommanderHistoryRank_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OPENSTORERATINGWINDOW_NTF, PlayerActivity.OpenStoreRatingNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPORTSTORERATINGRESULT_C2S, PlayerActivity.Player_StoreRating_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPORTSTORERATINGRESULT_S2C, PlayerActivity.Player_StoreRating_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHOOSESPLEVELREWARD_C2S, PlayerActivity.Player_ChooseSpLevelReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHOOSESPLEVELREWARD_S2C, PlayerActivity.Player_ChooseSpLevelReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BPTICKETEXCHANGE_C2S, PlayerActivity.Player_BpTicketExchange_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BPTICKETEXCHANGE_S2C, PlayerActivity.Player_BpTicketExchange_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYBP_C2S, PlayerActivity.Player_BuyBp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYBP_S2C, PlayerActivity.Player_BuyBp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACTIVITYCALENDAR_C2S, PlayerActivity.Player_FetchActivityCalendar_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACTIVITYCALENDAR_S2C, PlayerActivity.Player_FetchActivityCalendar_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHOOSEBESTCOMMANDERITEM_C2S, PlayerActivity.Player_ChooseBestCommanderItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHOOSEBESTCOMMANDERITEM_S2C, PlayerActivity.Player_ChooseBestCommanderItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACTIVITYCLANDATA_C2S, PlayerActivity.Player_FetchActivityClanData_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHACTIVITYCLANDATA_S2C, PlayerActivity.Player_FetchActivityClanData_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYLOTTERYVOUCHER_C2S, PlayerActivity.Player_BuyLotteryVoucher_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYLOTTERYVOUCHER_S2C, PlayerActivity.Player_BuyLotteryVoucher_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LOTTERYNATASHADRAW_C2S, PlayerActivity.Player_Lottery_Natasha_Draw_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LOTTERYNATASHADRAW_S2C, PlayerActivity.Player_Lottery_Natasha_Draw_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETTLELOTTERYHERO_C2S, PlayerActivity.Player_SettleLotteryHero_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETTLELOTTERYHERO_S2C, PlayerActivity.Player_SettleLotteryHero_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LOTTERYNATASHASHOPEXCHANGE_C2S, PlayerActivity.Player_Lottery_Natasha_Shop_Exchange_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LOTTERYNATASHASHOPEXCHANGE_S2C, PlayerActivity.Player_Lottery_Natasha_Shop_Exchange_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATESPYPLANE_C2S, PlayerAirForce.Player_CreateSpyPlane_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATESPYPLANE_S2C, PlayerAirForce.Player_CreateSpyPlane_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEACTIONSPYPLANE_C2S, PlayerAirForce.Player_ChangeActionSpyPlane_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEACTIONSPYPLANE_S2C, PlayerAirForce.Player_ChangeActionSpyPlane_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKSPY_C2S, PlayerAirForce.Player_CheckSpy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKSPY_S2C, PlayerAirForce.Player_CheckSpy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATELOGISTICSPLANE_C2S, PlayerAirForce.Player_CreateLogisticsPlane_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATELOGISTICSPLANE_S2C, PlayerAirForce.Player_CreateLogisticsPlane_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGELOGISTICSPLANE_C2S, PlayerAirForce.Player_ChangeLogisticsPlane_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGELOGISTICSPLANE_S2C, PlayerAirForce.Player_ChangeLogisticsPlane_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINBATTLEPASSTASKEXP_C2S, PlayerBattlepass.Player_ObtainTaskExp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINBATTLEPASSTASKEXP_S2C, PlayerBattlepass.Player_ObtainTaskExp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINDALIYEXPBOX_C2S, PlayerBattlepass.Player_ObtainExpBox_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINDALIYEXPBOX_S2C, PlayerBattlepass.Player_ObtainExpBox_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINBATTLEPASSLEVELREWARD_C2S, PlayerBattlepass.Player_ObtainLevelReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINBATTLEPASSLEVELREWARD_S2C, PlayerBattlepass.Player_ObtainLevelReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PURCHASEBATTLEPASSLEVEL_C2S, PlayerBattlepass.Player_PurchaseBpLevel_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PURCHASEBATTLEPASSLEVEL_S2C, PlayerBattlepass.Player_PurchaseBpLevel_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_C2S, PlayerBattlepass.Player_ObtainSeasonTaskExp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_S2C, PlayerBattlepass.Player_ObtainSeasonTaskExp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONDALIYEXPBOX_C2S, PlayerBattlepass.Player_ObtainSeasonExpBox_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONDALIYEXPBOX_S2C, PlayerBattlepass.Player_ObtainSeasonExpBox_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_C2S, PlayerBattlepass.Player_ObtainSeasonLevelReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_S2C, PlayerBattlepass.Player_ObtainSeasonLevelReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PURCHASESEASONBATTLEPASSLEVEL_C2S, PlayerBattlepass.Player_PurchaseSeasonBpLevel_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PURCHASESEASONBATTLEPASSLEVEL_S2C, PlayerBattlepass.Player_PurchaseSeasonBpLevel_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTCAMPAIGN_C2S, PlayerCampaign.Player_StartCampaign_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTCAMPAIGN_S2C, PlayerCampaign.Player_StartCampaign_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ABORTCAMPAIGN_C2S, PlayerCampaign.Player_AbortCampaign_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ABORTCAMPAIGN_S2C, PlayerCampaign.Player_AbortCampaign_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTMISSION_C2S, PlayerCampaign.Player_StartMission_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTMISSION_S2C, PlayerCampaign.Player_StartMission_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHMISSION_C2S, PlayerCampaign.Player_FinishMission_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHMISSION_S2C, PlayerCampaign.Player_FinishMission_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MERGECAMPAIGNITEM_C2S, PlayerCampaign.Player_MergeCampaignItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MERGECAMPAIGNITEM_S2C, PlayerCampaign.Player_MergeCampaignItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINALCAMPAIGNRESULT_NTF, PlayerCampaign.FinalCampaignResultNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTREST_C2S, PlayerCampaign.Player_CampaignEventRest_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTREST_S2C, PlayerCampaign.Player_CampaignEventRest_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTBAR_C2S, PlayerCampaign.Player_CampaignEventBar_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTBAR_S2C, PlayerCampaign.Player_CampaignEventBar_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTTRAIN_C2S, PlayerCampaign.Player_CampaignEventTrain_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTTRAIN_S2C, PlayerCampaign.Player_CampaignEventTrain_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTRESTORE_C2S, PlayerCampaign.Player_CampaignEventRestore_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTRESTORE_S2C, PlayerCampaign.Player_CampaignEventRestore_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTREPAIR_C2S, PlayerCampaign.Player_CampaignEventRepair_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTREPAIR_S2C, PlayerCampaign.Player_CampaignEventRepair_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTEXIT_C2S, PlayerCampaign.Player_CampaignEventExit_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNEVENTEXIT_S2C, PlayerCampaign.Player_CampaignEventExit_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNMISSIONSTART_C2S, PlayerCampaign.Player_CampaignMissionStart_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNMISSIONSTART_S2C, PlayerCampaign.Player_CampaignMissionStart_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNMISSIONFINISH_C2S, PlayerCampaign.Player_CampaignMissionFinish_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CAMPAIGNMISSIONFINISH_S2C, PlayerCampaign.Player_CampaignMissionFinish_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TOUCHMISSIONCHEST_C2S, PlayerCampaign.Player_TouchMissionChest_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TOUCHMISSIONCHEST_S2C, PlayerCampaign.Player_TouchMissionChest_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHARACTERLIST_C2S, PlayerCharacterManager.Player_GetCharacterList_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHARACTERLIST_S2C, PlayerCharacterManager.Player_GetCharacterList_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESERVERLIST_C2S, PlayerCharacterManager.Player_GetZoneServerList_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESERVERLIST_S2C, PlayerCharacterManager.Player_GetZoneServerList_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MANAGESTARFORCHARACTER_C2S, PlayerCharacterManager.Player_ManageStarForCharacter_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MANAGESTARFORCHARACTER_S2C, PlayerCharacterManager.Player_ManageStarForCharacter_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKREGISTERNEWCHARACTER_C2S, PlayerCharacterManager.Player_CheckRegisterNewCharacter_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKREGISTERNEWCHARACTER_S2C, PlayerCharacterManager.Player_CheckRegisterNewCharacter_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONEIPPORT_C2S, PlayerCharacterManager.Player_GetZoneIpPort_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONEIPPORT_S2C, PlayerCharacterManager.Player_GetZoneIpPort_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHATMESSAGES_C2S, PlayerChat.Player_GetChatMessages_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHATMESSAGES_S2C, PlayerChat.Player_GetChatMessages_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHATREQUEST_C2S, PlayerChat.Player_ChatRequest_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHATREQUEST_S2C, PlayerChat.Player_ChatRequest_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READCHATMESSAGE_C2S, PlayerChat.Player_ReadChatMessage_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READCHATMESSAGE_S2C, PlayerChat.Player_ReadChatMessage_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRANSLATETEXT_C2S, PlayerChat.Player_TranslateText_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRANSLATETEXT_S2C, PlayerChat.Player_TranslateText_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BORADCASTCHATMESSAGE_NTF, PlayerChat.Player_BoradcastChatMessage_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HIDECHAT_C2S, PlayerChat.Player_HideChat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HIDECHAT_S2C, PlayerChat.Player_HideChat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATEGROUPCHAT_C2S, PlayerChat.Player_CreateGroupChat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATEGROUPCHAT_S2C, PlayerChat.Player_CreateGroupChat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHATMEMBERLIST_C2S, PlayerChat.Player_GetChatMember_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCHATMEMBERLIST_S2C, PlayerChat.Player_GetChatMember_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETBATCHPLAYERCACHE_C2S, PlayerChat.Player_GetBatchPlayerCache_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETBATCHPLAYERCACHE_S2C, PlayerChat.Player_GetBatchPlayerCache_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKINGROUPCHAT_C2S, PlayerChat.Player_CheckInGroup_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKINGROUPCHAT_S2C, PlayerChat.Player_CheckInGroup_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_INVITEPLAYERJOINGROUP_C2S, PlayerChat.Player_InvitePlayer_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_INVITEPLAYERJOINGROUP_S2C, PlayerChat.Player_InvitePlayer_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEPLAYERFROMGROUP_C2S, PlayerChat.Player_OwnerRemoveMember_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEPLAYERFROMGROUP_S2C, PlayerChat.Player_OwnerRemoveMember_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERQUITGROUPCHAT_C2S, PlayerChat.Player_QuitGroupChat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERQUITGROUPCHAT_S2C, PlayerChat.Player_QuitGroupChat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERTRANSFERGROUPOWNER_C2S, PlayerChat.Player_TransferGroupOwner_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERTRANSFERGROUPOWNER_S2C, PlayerChat.Player_TransferGroupOwner_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DISMISSGROUPCHAT_C2S, PlayerChat.Player_DismissGroupChat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DISMISSGROUPCHAT_S2C, PlayerChat.Player_DismissGroupChat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERMODIFYGROUPNAME_C2S, PlayerChat.Player_ModifyGroupName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYERMODIFYGROUPNAME_S2C, PlayerChat.Player_ModifyGroupName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OPENCHATNOTDISTURB_C2S, PlayerChat.Player_ChatNotDisturb_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OPENCHATNOTDISTURB_S2C, PlayerChat.Player_ChatNotDisturb_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSECHATNOTDISTURB_C2S, PlayerChat.Player_CloseChatNotDisturb_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSECHATNOTDISTURB_S2C, PlayerChat.Player_CloseChatNotDisturb_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETPLAYERNOTDISTURB_C2S, PlayerChat.Player_PlayerNotDisturb_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETPLAYERNOTDISTURB_S2C, PlayerChat.Player_PlayerNotDisturb_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNSETPLAYERNOTDISTURB_C2S, PlayerChat.Player_ClosePlayerNotDisturb_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNSETPLAYERNOTDISTURB_S2C, PlayerChat.Player_ClosePlayerNotDisturb_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DELETECHATITEM_C2S, PlayerChat.Player_DeleteChatItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DELETECHATITEM_S2C, PlayerChat.Player_DeleteChatItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETGROUPCHATSHOW_C2S, PlayerChat.Player_ShowGroupChat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETGROUPCHATSHOW_S2C, PlayerChat.Player_ShowGroupChat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETSHAREDMAILCONTENT_C2S, PlayerChat.Player_GetSharedMailContent_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETSHAREDMAILCONTENT_S2C, PlayerChat.Player_GetSharedMailContent_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATECLAN_C2S, PlayerClan.Player_CreateClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATECLAN_S2C, PlayerClan.Player_CreateClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCLANNAME_C2S, PlayerClan.Player_CheckClanName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCLANNAME_S2C, PlayerClan.Player_CheckClanName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHCLAN_C2S, PlayerClan.Player_SearchClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHCLAN_S2C, PlayerClan.Player_SearchClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANENTITY_C2S, PlayerClan.Player_FetchClanEntity_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANENTITY_S2C, PlayerClan.Player_FetchClanEntity_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSIMPLEINFO_C2S, PlayerClan.Player_FetchClanSimpleInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSIMPLEINFO_S2C, PlayerClan.Player_FetchClanSimpleInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANMEMBERINFO_C2S, PlayerClan.Player_FetchClanMemberInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANMEMBERINFO_S2C, PlayerClan.Player_FetchClanMemberInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANAPPLYPLAYERS_C2S, PlayerClan.Player_FetchClanApplyPlayers_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANAPPLYPLAYERS_S2C, PlayerClan.Player_FetchClanApplyPlayers_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTIFYCLANAPPLYPLAYERS_NTF, PlayerClan.Player_NotifyClanApplyPlayers_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYJOINCLAN_C2S, PlayerClan.Player_ApplyJoinClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYJOINCLAN_S2C, PlayerClan.Player_ApplyJoinClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELAPPLYJOINCLAN_C2S, PlayerClan.Player_CancelApplyJoinClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELAPPLYJOINCLAN_S2C, PlayerClan.Player_CancelApplyJoinClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HANDLECLANAPPLY_C2S, PlayerClan.Player_HandleClanApply_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HANDLECLANAPPLY_S2C, PlayerClan.Player_HandleClanApply_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUITCLAN_C2S, PlayerClan.Player_QuitClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUITCLAN_S2C, PlayerClan.Player_QuitClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_INVITETOCLAN_C2S, PlayerClan.Player_InviteToClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_INVITETOCLAN_S2C, PlayerClan.Player_InviteToClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HANDLECLANINVITATION_C2S, PlayerClan.Player_HandleClanInvitation_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HANDLECLANINVITATION_S2C, PlayerClan.Player_HandleClanInvitation_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DISSOLVECLAN_C2S, PlayerClan.Player_DissolveClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DISSOLVECLAN_S2C, PlayerClan.Player_DissolveClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELDISSOLVECLAN_C2S, PlayerClan.Player_CancelDissolveClan_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELDISSOLVECLAN_S2C, PlayerClan.Player_CancelDissolveClan_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYOWNERDISSOLVING_C2S, PlayerClan.Player_ApplyOwnerDissolving_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYOWNERDISSOLVING_S2C, PlayerClan.Player_ApplyOwnerDissolving_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELAPPLYOWNERDISSOLVING_C2S, PlayerClan.Player_CancelApplyOwnerDissolving_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELAPPLYOWNERDISSOLVING_S2C, PlayerClan.Player_CancelApplyOwnerDissolving_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHDISSOLVINGINFO_C2S, PlayerClan.Player_FetchDissolvingInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHDISSOLVINGINFO_S2C, PlayerClan.Player_FetchDissolvingInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRANSFERCLANOWNER_C2S, PlayerClan.Player_TransferClanOwner_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRANSFERCLANOWNER_S2C, PlayerClan.Player_TransferClanOwner_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KICKOFFCLANMEMBER_C2S, PlayerClan.Player_KickOffClanMember_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KICKOFFCLANMEMBER_S2C, PlayerClan.Player_KickOffClanMember_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANMEMBERSTAFF_C2S, PlayerClan.Player_ModifyClanMemberStaff_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANMEMBERSTAFF_S2C, PlayerClan.Player_ModifyClanMemberStaff_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANNAME_C2S, PlayerClan.Player_ModifyClanName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANNAME_S2C, PlayerClan.Player_ModifyClanName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANSNAME_C2S, PlayerClan.Player_ModifyClanSName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANSNAME_S2C, PlayerClan.Player_ModifyClanSName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANFLAG_C2S, PlayerClan.Player_ModifyClanFlag_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANFLAG_S2C, PlayerClan.Player_ModifyClanFlag_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANSETTINGMISC_C2S, PlayerClan.Player_ModifyClanSettingMisc_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANSETTINGMISC_S2C, PlayerClan.Player_ModifyClanSettingMisc_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANWELCOMELETTER_C2S, PlayerClan.Player_FetchClanWelcomeLetter_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANWELCOMELETTER_S2C, PlayerClan.Player_FetchClanWelcomeLetter_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANWELCOMELETTER_C2S, PlayerClan.Player_ModifyClanWelcomeLetter_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYCLANWELCOMELETTER_S2C, PlayerClan.Player_ModifyClanWelcomeLetter_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTERRITORYPAGE_C2S, PlayerClan.Player_FetchClanTerritoryPage_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTERRITORYPAGE_S2C, PlayerClan.Player_FetchClanTerritoryPage_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ABANDONMAPBUILDING_C2S, PlayerClan.Player_AbandonMapBuilding_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ABANDONMAPBUILDING_S2C, PlayerClan.Player_AbandonMapBuilding_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEDEFENSIVELEADER_C2S, PlayerClan.Player_ChangeDefensiveLeader_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEDEFENSIVELEADER_S2C, PlayerClan.Player_ChangeDefensiveLeader_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINCLANPOWERREWARD_C2S, PlayerClan.Player_ObtainClanPowerReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OBTAINCLANPOWERREWARD_S2C, PlayerClan.Player_ObtainClanPowerReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANMEMBERCITY_C2S, PlayerClan.Player_FetchClanMemberCity_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANMEMBERCITY_S2C, PlayerClan.Player_FetchClanMemberCity_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSINGLEMEMBERCITY_C2S, PlayerClan.Player_FetchClanSingleMemberCity_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSINGLEMEMBERCITY_S2C, PlayerClan.Player_FetchClanSingleMemberCity_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTERRITORYMAP_C2S, PlayerClan.Player_FetchClanTerritoryMap_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTERRITORYMAP_S2C, PlayerClan.Player_FetchClanTerritoryMap_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_C2S, PlayerClan.Player_ConstructClanStrongholdBuilding_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_S2C, PlayerClan.Player_ConstructClanStrongholdBuilding_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DESTROYCLANSTRONGHOLDBUILDING_C2S, PlayerClan.Player_DestroyClanStrongholdBuilding_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DESTROYCLANSTRONGHOLDBUILDING_S2C, PlayerClan.Player_DestroyClanStrongholdBuilding_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EXTINGUISHBUILDINGFIRE_C2S, PlayerClan.Player_ExtinguishBuildingFire_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EXTINGUISHBUILDINGFIRE_S2C, PlayerClan.Player_ExtinguishBuildingFire_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANBUILDINGHP_C2S, PlayerClan.Player_FetchClanBuildingHp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANBUILDINGHP_S2C, PlayerClan.Player_FetchClanBuildingHp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANPOWERREWARD_C2S, PlayerClan.Player_FetchClanPowerReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANPOWERREWARD_S2C, PlayerClan.Player_FetchClanPowerReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCANFREEREBUILD_C2S, PlayerClan.Player_CheckCanFreeRebuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCANFREEREBUILD_S2C, PlayerClan.Player_CheckCanFreeRebuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_VERIFYREBUILDMAPBUILDING_C2S, PlayerClan.Player_VerifyRebuildMapBuilding_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_VERIFYREBUILDMAPBUILDING_S2C, PlayerClan.Player_VerifyRebuildMapBuilding_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANRANKPAGEINFO_C2S, PlayerClan.Player_GetClanRankPageInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANRANKPAGEINFO_S2C, PlayerClan.Player_GetClanRankPageInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETTOPCLANRANKINFO_C2S, PlayerClan.Player_GetTopClanRankInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETTOPCLANRANKINFO_S2C, PlayerClan.Player_GetTopClanRankInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATECLANHELP_C2S, PlayerClan.Player_CreateClanHelp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATECLANHELP_S2C, PlayerClan.Player_CreateClanHelp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANHELPS_C2S, PlayerClan.Player_FetchClanHelps_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANHELPS_S2C, PlayerClan.Player_FetchClanHelps_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHALLCLANHELPS_C2S, PlayerClan.Player_FinishAllClanHelps_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHALLCLANHELPS_S2C, PlayerClan.Player_FinishAllClanHelps_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SYNCCANHELPITEMIDS_NTF, PlayerClan.Player_SyncCanHelpItemIds_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANHELPINFOUPDATE_NTF, PlayerClan.Player_ClanHelpInfoUpdate_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANGIFT_C2S, PlayerClan.Player_GetClanGift_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANGIFT_S2C, PlayerClan.Player_GetClanGift_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLEARCLANGIFT_C2S, PlayerClan.Player_ClearClanGift_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLEARCLANGIFT_S2C, PlayerClan.Player_ClearClanGift_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANTREASUREGIFT_C2S, PlayerClan.Player_GetClanTreasureGift_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETCLANTREASUREGIFT_S2C, PlayerClan.Player_GetClanTreasureGift_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTIFYCLANGIFTINFOONLINE_NTF, PlayerClan.Player_ClanGiftInfo_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANWAREHOUSEINFO_C2S, PlayerClan.Player_FetchClanWareHouseInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANWAREHOUSEINFO_S2C, PlayerClan.Player_FetchClanWareHouseInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANRESOURCESNAPSHOT_C2S, PlayerClan.Player_FetchClanResourceSnapshot_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANRESOURCESNAPSHOT_S2C, PlayerClan.Player_FetchClanResourceSnapshot_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPDATECLANINFO_NTF, PlayerClan.Player_UpdateClanInfo_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANEVENTLOG_C2S, PlayerClan.Player_FetchClanEventLog_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANEVENTLOG_S2C, PlayerClan.Player_FetchClanEventLog_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANNUMMAXTIP_C2S, PlayerClan.Player_FetchClanNumMaxTip_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANNUMMAXTIP_S2C, PlayerClan.Player_FetchClanNumMaxTip_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANOWNERCHANGED_NTF, PlayerClan.ClanOwnerChangedNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANCOMMANDCENTERNUM_C2S, PlayerClan.Player_ClanCommandCenterNum_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANCOMMANDCENTERNUM_S2C, PlayerClan.Player_ClanCommandCenterNum_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSTORE_C2S, PlayerClanStore.Player_FetchClanStore_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSTORE_S2C, PlayerClanStore.Player_FetchClanStore_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OPERATECLANSTOREITEM_C2S, PlayerClanStore.Player_OperateClanStoreItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OPERATECLANSTOREITEM_S2C, PlayerClanStore.Player_OperateClanStoreItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSTORERECORD_C2S, PlayerClanStore.Player_FetchClanStoreRecord_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANSTORERECORD_S2C, PlayerClanStore.Player_FetchClanStoreRecord_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ONCLANSTOREITEMCHANGENTF_NTF, PlayerClanStore.OnClanStoreItemChangeNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ONCLANSTORESTOCKNTF_NTF, PlayerClanStore.OnClanStoreStockNtf.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTECHINFO_C2S, PlayerClanTech.Player_FetchClanTechInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANTECHINFO_S2C, PlayerClanTech.Player_FetchClanTechInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHDONATE_C2S, PlayerClanTech.Player_ClanTechDonate_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHDONATE_S2C, PlayerClanTech.Player_ClanTechDonate_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHRESEARCH_C2S, PlayerClanTech.Player_ClanTechResearch_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHRESEARCH_S2C, PlayerClanTech.Player_ClanTechResearch_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHRECOMMEND_C2S, PlayerClanTech.Player_ClanTechRecommend_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHRECOMMEND_S2C, PlayerClanTech.Player_ClanTechRecommend_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHDETAIL_C2S, PlayerClanTech.Player_ClanTechDetail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLANTECHDETAIL_S2C, PlayerClanTech.Player_ClanTechDetail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTIFYCLANTECHINFO_NTF, PlayerClanTech.ClanTechInfoUpdateMsg.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DEBUGCOMMAND_C2S, PlayerCommon.Player_DebugCommand_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DEBUGCOMMAND_S2C, PlayerCommon.Player_DebugCommand_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDEBUGCOMMANDLIST_C2S, PlayerCommon.Player_GetDebugCommandList_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDEBUGCOMMANDLIST_S2C, PlayerCommon.Player_GetDebugCommandList_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHMONSTER_C2S, PlayerCommon.Player_SearchMonster_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHMONSTER_S2C, PlayerCommon.Player_SearchMonster_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_VERIFYMOVECITY_C2S, PlayerCommon.Player_VerifyMoveCity_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_VERIFYMOVECITY_S2C, PlayerCommon.Player_VerifyMoveCity_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYANDUSEITEM_C2S, PlayerCommon.Player_BuyAndUseItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYANDUSEITEM_S2C, PlayerCommon.Player_BuyAndUseItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEITEM_C2S, PlayerCommon.Player_UseItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEITEM_S2C, PlayerCommon.Player_UseItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHVIEWSCENE_C2S, PlayerCommon.Player_SwitchViewScene_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHVIEWSCENE_S2C, PlayerCommon.Player_SwitchViewScene_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDINFO_C2S, PlayerCommon.Player_QueryPlayerCardInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDINFO_S2C, PlayerCommon.Player_QueryPlayerCardInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_C2S, PlayerCommon.Player_QueryPlayerCardInfoDetail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDINFODETAIL_S2C, PlayerCommon.Player_QueryPlayerCardInfoDetail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ADDEXP_NTF, PlayerCommon.Player_AddExp_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTIFYDISABLEMODULE_NTF, PlayerCommon.Player_NotifyDisableModule_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTIFYOPENMODULE_NTF, PlayerCommon.Player_NotifyOpenModule_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETWALLGARRISON_C2S, PlayerCommon.Player_SetWallGarrison_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETWALLGARRISON_S2C, PlayerCommon.Player_SetWallGarrison_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPAIRWALL_C2S, PlayerCommon.Player_RepairWall_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPAIRWALL_S2C, PlayerCommon.Player_RepairWall_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OUTFIREWALL_C2S, PlayerCommon.Player_OutFireWall_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_OUTFIREWALL_S2C, PlayerCommon.Player_OutFireWall_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CITYFALL_C2S, PlayerCommon.Player_CityFall_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CITYFALL_S2C, PlayerCommon.Player_CityFall_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGECLIENTINFO_C2S, PlayerCommon.Player_ChangeClientInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGECLIENTINFO_S2C, PlayerCommon.Player_ChangeClientInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SYSTEMTIMENTF_NTF, PlayerCommon.Player_SystemTime_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHGUIDANCE_C2S, PlayerCommon.Player_FinishGuidance_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHGUIDANCE_S2C, PlayerCommon.Player_FinishGuidance_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EXPLOREINFONTF_NTF, PlayerCommon.Player_ExploreInfo_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEPLAYERNAME_C2S, PlayerCommon.Player_ChangePlayerName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEPLAYERNAME_S2C, PlayerCommon.Player_ChangePlayerName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEAVATAR_C2S, PlayerCommon.Player_ChangeAvatar_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEAVATAR_S2C, PlayerCommon.Player_ChangeAvatar_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MARKNEWBIESTEP_C2S, PlayerCommon.Player_MarkNewbieStep_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MARKNEWBIESTEP_S2C, PlayerCommon.Player_MarkNewbieStep_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GIVENEWBIEHERO_C2S, PlayerCommon.Player_GiveNewbieHero_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GIVENEWBIEHERO_S2C, PlayerCommon.Player_GiveNewbieHero_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEREDDOT_C2S, PlayerCommon.Player_RemoveRedDot_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEREDDOT_S2C, PlayerCommon.Player_RemoveRedDot_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERKILLDETAIL_C2S, PlayerCommon.Player_QueryPlayerKillDetail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERKILLDETAIL_S2C, PlayerCommon.Player_QueryPlayerKillDetail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEDAILYFREEENERGY_C2S, PlayerCommon.Player_TakeDailyFreeEnergy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEDAILYFREEENERGY_S2C, PlayerCommon.Player_TakeDailyFreeEnergy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYENERGY_C2S, PlayerCommon.Player_BuyEnergy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYENERGY_S2C, PlayerCommon.Player_BuyEnergy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECRUIT_C2S, PlayerCommon.Player_Recruit_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECRUIT_S2C, PlayerCommon.Player_Recruit_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UGCCHECK_C2S, PlayerCommon.Player_UgcCheck_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UGCCHECK_S2C, PlayerCommon.Player_UgcCheck_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRIGGERQLOG_C2S, PlayerCommon.Player_TriggerQlog_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRIGGERQLOG_S2C, PlayerCommon.Player_TriggerQlog_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDHEAD_C2S, PlayerCommon.Player_QueryPlayerCardHead_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPLAYERCARDHEAD_S2C, PlayerCommon.Player_QueryPlayerCardHead_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETSPASSWORD_C2S, PlayerCommon.Player_SetSPassWord_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETSPASSWORD_S2C, PlayerCommon.Player_SetSPassWord_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSESPASSWORD_C2S, PlayerCommon.Player_CloseSPassWord_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSESPASSWORD_S2C, PlayerCommon.Player_CloseSPassWord_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELCLOSESPW_C2S, PlayerCommon.Player_CancelCloseSpw_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELCLOSESPW_S2C, PlayerCommon.Player_CancelCloseSpw_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEEXPRESSION_C2S, PlayerCommon.Player_UseExpression_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEEXPRESSION_S2C, PlayerCommon.Player_UseExpression_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETSPASSWORDSTATUS_C2S, PlayerCommon.Player_SetSPassWordStatus_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETSPASSWORDSTATUS_S2C, PlayerCommon.Player_SetSPassWordStatus_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETPFLAG_C2S, PlayerCommon.Player_SetPFlag_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETPFLAG_S2C, PlayerCommon.Player_SetPFlag_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPPRIVILEGEBOX_C2S, PlayerCommon.Player_GetVipPrivilegeBox_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPPRIVILEGEBOX_S2C, PlayerCommon.Player_GetVipPrivilegeBox_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPDAILYBOX_C2S, PlayerCommon.Player_GetVipDailyBox_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPDAILYBOX_S2C, PlayerCommon.Player_GetVipDailyBox_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDSYSASSISTMAIL_C2S, PlayerCommon.Player_SendSysAssistMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDSYSASSISTMAIL_S2C, PlayerCommon.Player_SendSysAssistMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHSETTING_C2S, PlayerCommon.Player_SwitchSetting_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHSETTING_S2C, PlayerCommon.Player_SwitchSetting_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_AFTERBINDACCOUNT_C2S, PlayerCommon.Player_AfterBindAccount_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_AFTERBINDACCOUNT_S2C, PlayerCommon.Player_AfterBindAccount_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYPUSHSETTING_C2S, PlayerCommon.Player_ModifyPushSetting_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MODIFYPUSHSETTING_S2C, PlayerCommon.Player_ModifyPushSetting_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPUSHSETTING_C2S, PlayerCommon.Player_QueryPushSetting_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYPUSHSETTING_S2C, PlayerCommon.Player_QueryPushSetting_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDAILYDISCOUNTREWARD_C2S, PlayerDailyDiscount.Player_GetDailyDiscountReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDAILYDISCOUNTREWARD_S2C, PlayerDailyDiscount.Player_GetDailyDiscountReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHDAILYDISCOUNTHERO_C2S, PlayerDailyDiscount.Player_SwitchDailyDiscountHero_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHDAILYDISCOUNTHERO_S2C, PlayerDailyDiscount.Player_SwitchDailyDiscountHero_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETSUPERDAILYDISCOUNTREWARD_C2S, PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETSUPERDAILYDISCOUNTREWARD_S2C, PlayerDailyDiscount.Player_GetSuperDailyDiscountReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDISCOUNTSTOREINFO_C2S, PlayerDiscountStore.Player_GetDiscountStoreInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETDISCOUNTSTOREINFO_S2C, PlayerDiscountStore.Player_GetDiscountStoreInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYDISCOUNTSTOREITEM_C2S, PlayerDiscountStore.Player_BuyDiscountStoreItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYDISCOUNTSTOREITEM_S2C, PlayerDiscountStore.Player_BuyDiscountStoreItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REFRESHDISCOUNTSTORE_C2S, PlayerDiscountStore.Player_RefreshDiscountStore_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REFRESHDISCOUNTSTORE_S2C, PlayerDiscountStore.Player_RefreshDiscountStore_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITFORMATION_C2S, PlayerFormation.Player_EditFormation_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITFORMATION_S2C, PlayerFormation.Player_EditFormation_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RENAMEFORMATION_C2S, PlayerFormation.Player_RenameFormation_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RENAMEFORMATION_S2C, PlayerFormation.Player_RenameFormation_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEFORMATION_C2S, PlayerFormation.Player_RemoveFormation_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVEFORMATION_S2C, PlayerFormation.Player_RemoveFormation_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITRHTROOP_C2S, PlayerFormation.Player_EditRHTroop_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITRHTROOP_S2C, PlayerFormation.Player_EditRHTroop_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITVIRTUALFORMATION_C2S, PlayerFormation.Player_EditVirtualFormation_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_EDITVIRTUALFORMATION_S2C, PlayerFormation.Player_EditVirtualFormation_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHPLAYER_C2S, PlayerFriend.Player_SearchPlayer_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHPLAYER_S2C, PlayerFriend.Player_SearchPlayer_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHPLAYERBYNAME_C2S, PlayerFriend.Player_SearchPlayerByName_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHPLAYERBYNAME_S2C, PlayerFriend.Player_SearchPlayerByName_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ADDFRIENDAPPLY_C2S, PlayerFriend.Player_AddFriend_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ADDFRIENDAPPLY_S2C, PlayerFriend.Player_AddFriend_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_AGREEFRIENDAPPLY_C2S, PlayerFriend.Player_AgreeFriendApply_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_AGREEFRIENDAPPLY_S2C, PlayerFriend.Player_AgreeFriendApply_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REFUSEFRIENDAPPLY_C2S, PlayerFriend.Player_RefuseFriendApply_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REFUSEFRIENDAPPLY_S2C, PlayerFriend.Player_RefuseFriendApply_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DELFRIEND_C2S, PlayerFriend.Player_DelFriend_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DELFRIEND_S2C, PlayerFriend.Player_DelFriend_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHIELDPLAYER_C2S, PlayerFriend.Player_ShieldPlayer_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHIELDPLAYER_S2C, PlayerFriend.Player_ShieldPlayer_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVESHIELDPLAYER_C2S, PlayerFriend.Player_RemoveShield_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REMOVESHIELDPLAYER_S2C, PlayerFriend.Player_RemoveShield_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNLOCKHERO_C2S, PlayerHero.Player_UnLockHero_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNLOCKHERO_S2C, PlayerHero.Player_UnLockHero_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROLEVELUP_C2S, PlayerHero.Player_HeroLevelUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROLEVELUP_S2C, PlayerHero.Player_HeroLevelUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROSTAGEUP_C2S, PlayerHero.Player_HeroStageUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROSTAGEUP_S2C, PlayerHero.Player_HeroStageUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROSKILLLEVELUP_C2S, PlayerHero.Player_HeroSkillLevelUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROSKILLLEVELUP_S2C, PlayerHero.Player_HeroSkillLevelUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESETSKILL_C2S, PlayerHero.Player_ResetSkill_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESETSKILL_S2C, PlayerHero.Player_ResetSkill_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGESKILLSLOTLIMIT_C2S, PlayerHero.Player_ChangeSkillSlotLimit_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGESKILLSLOTLIMIT_S2C, PlayerHero.Player_ChangeSkillSlotLimit_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROTALENTLEVELUP_C2S, PlayerHero.Player_HeroTalentLevelUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROTALENTLEVELUP_S2C, PlayerHero.Player_HeroTalentLevelUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHTALENTPAGE_C2S, PlayerHero.Player_SwitchTalentPage_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SWITCHTALENTPAGE_S2C, PlayerHero.Player_SwitchTalentPage_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESETNAMETALENTPAGE_C2S, PlayerHero.Player_ResetNameTalentPage_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESETNAMETALENTPAGE_S2C, PlayerHero.Player_ResetNameTalentPage_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROITEMEXCHANGE_C2S, PlayerHero.Player_HeroItemExchange_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROITEMEXCHANGE_S2C, PlayerHero.Player_HeroItemExchange_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HERORESETTALENT_C2S, PlayerHero.Player_HeroResetTalent_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HERORESETTALENT_S2C, PlayerHero.Player_HeroResetTalent_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROINTENSIVESKILL_C2S, PlayerHero.Player_HeroInterensiveSkill_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HEROINTENSIVESKILL_S2C, PlayerHero.Player_HeroInterensiveSkill_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALTREAT_C2S, PlayerHospital.Player_HospitalTreat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALTREAT_S2C, PlayerHospital.Player_HospitalTreat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALFASTTREAT_C2S, PlayerHospital.Player_HospitalFastTreat_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALFASTTREAT_S2C, PlayerHospital.Player_HospitalFastTreat_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALRETURNSOLDIERS_C2S, PlayerHospital.Player_HospitalReturnSoldiers_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_HOSPITALRETURNSOLDIERS_S2C, PlayerHospital.Player_HospitalReturnSoldiers_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PROMPTFORPLANEUNLOCK_C2S, PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PROMPTFORPLANEUNLOCK_S2C, PlayerInnerBuildLogic.Player_PromptForPlaneUnlock_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTICEBOARD_C2S, PlayerInnerBuildLogic.Player_GetNoticeBoard_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTICEBOARD_S2C, PlayerInnerBuildLogic.Player_GetNoticeBoard_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_NOTICEBOARDNTF_NTF, PlayerInnerBuildLogic.Player_NoticeBoard_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEPROSPERITYREWARD_C2S, PlayerInnerBuildLogic.Player_TakeProsperityReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEPROSPERITYREWARD_S2C, PlayerInnerBuildLogic.Player_TakeProsperityReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_COMPLETEQUESTIONNAIRE_C2S, PlayerInnerBuildLogic.CompleteQuestionnaireC2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_COMPLETEQUESTIONNAIRE_S2C, PlayerInnerBuildLogic.CompleteQuestionnaireS2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUESTIONNAIREEXPIRED_C2S, PlayerInnerBuildLogic.QuestionnaireExpiredC2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUESTIONNAIREEXPIRED_S2C, PlayerInnerBuildLogic.QuestionnaireExpiredS2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPDATECLIENTSTAGE_C2S, PlayerInnerBuildLogic.Player_UpdateClientStage_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPDATECLIENTSTAGE_S2C, PlayerInnerBuildLogic.Player_UpdateClientStage_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_COMPLETEMAINTASK_C2S, PlayerInnerBuildLogic.Player_CompleteMainTask_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_COMPLETEMAINTASK_S2C, PlayerInnerBuildLogic.Player_CompleteMainTask_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGAPPOINT_C2S, PlayerKingdom.Player_KingAppoint_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGAPPOINT_S2C, PlayerKingdom.Player_KingAppoint_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGOPENBUFF_C2S, PlayerKingdom.Player_KingOpenBuff_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGOPENBUFF_S2C, PlayerKingdom.Player_KingOpenBuff_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGSENDGIFT_C2S, PlayerKingdom.Player_KingSendGift_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGSENDGIFT_S2C, PlayerKingdom.Player_KingSendGift_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGUSESKILL_C2S, PlayerKingdom.Player_KingUseSkill_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_KINGUSESKILL_S2C, PlayerKingdom.Player_KingUseSkill_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHHISTORYKINGS_C2S, PlayerKingdom.Player_FetchHistoryKings_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHHISTORYKINGS_S2C, PlayerKingdom.Player_FetchHistoryKings_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHKINGDOMGIFTINFO_C2S, PlayerKingdom.Player_FetchKingdomGiftInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHKINGDOMGIFTINFO_S2C, PlayerKingdom.Player_FetchKingdomGiftInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHKINGDOMOFFICEINFO_C2S, PlayerKingdom.Player_FetchKingdomOfficeInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHKINGDOMOFFICEINFO_S2C, PlayerKingdom.Player_FetchKingdomOfficeInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILUNREADGET_C2S, PlayerMail.Player_MailUnreadGet_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILUNREADGET_S2C, PlayerMail.Player_MailUnreadGet_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETNEWMAIL_NTF, PlayerMail.Player_Get_New_Mail_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHREAD_C2S, PlayerMail.Player_MailBatchRead_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHREAD_S2C, PlayerMail.Player_MailBatchRead_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILREWARDGET_C2S, PlayerMail.Player_MailRewardGet_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILREWARDGET_S2C, PlayerMail.Player_MailRewardGet_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_DELMAIL_NTF, PlayerMail.Player_Del_Mail_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READBATTLELOG_C2S, PlayerMail.Player_ReadBattleLog_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READBATTLELOG_S2C, PlayerMail.Player_ReadBattleLog_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READBATTLERECORDDETAIL_C2S, PlayerMail.Player_ReadBattleRecordDetail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READBATTLERECORDDETAIL_S2C, PlayerMail.Player_ReadBattleRecordDetail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILDEL_C2S, PlayerMail.Player_MailDel_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILDEL_S2C, PlayerMail.Player_MailDel_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHDEL_C2S, PlayerMail.Player_MailBatchDel_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHDEL_S2C, PlayerMail.Player_MailBatchDel_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDPERSONALMAIL_C2S, PlayerMail.Player_SendPersonalMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDPERSONALMAIL_S2C, PlayerMail.Player_SendPersonalMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDCLANMAIL_C2S, PlayerMail.Player_SendClanMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDCLANMAIL_S2C, PlayerMail.Player_SendClanMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDZONEMAIL_C2S, PlayerMail.Player_SendZoneMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDZONEMAIL_S2C, PlayerMail.Player_SendZoneMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHIELDPERSONMAIL_C2S, PlayerMail.Player_ShieldPersonMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SHIELDPERSONMAIL_S2C, PlayerMail.Player_ShieldPersonMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNSHIELDPERSONMAIL_C2S, PlayerMail.Player_UnshieldPersonMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNSHIELDPERSONMAIL_S2C, PlayerMail.Player_UnshieldPersonMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READANDGETREWARDALLMAIL_C2S, PlayerMail.Player_ReadAndGetRewardAllMail_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_READANDGETREWARDALLMAIL_S2C, PlayerMail.Player_ReadAndGetRewardAllMail_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILSTORE_C2S, PlayerMail.Player_MailStore_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILSTORE_S2C, PlayerMail.Player_MailStore_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PUSHALLMAILHEADER_NTF, PlayerMail.Player_Push_All_Mail_Header_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHGETCONTENT_C2S, PlayerMail.Player_MailBatchGetContent_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MAILBATCHGETCONTENT_S2C, PlayerMail.Player_MailBatchGetContent_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEMILESTONEREWARDC2S_C2S, PlayerMilestone.Player_TakeMileStoneReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TAKEMILESTONEREWARDC2S_S2C, PlayerMilestone.Player_TakeMileStoneReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETMILESTONEHISTORY_C2S, PlayerMilestone.Player_GetMileStoneHistory_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETMILESTONEHISTORY_S2C, PlayerMilestone.Player_GetMileStoneHistory_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETMILESTONERANK_C2S, PlayerMilestone.Player_GetMileStoneRank_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETMILESTONERANK_S2C, PlayerMilestone.Player_GetMileStoneRank_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESUNDERSEASON_C2S, PlayerMultiServer.Player_GetZonesUnderSeason_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESUNDERSEASON_S2C, PlayerMultiServer.Player_GetZonesUnderSeason_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESSTATUS_C2S, PlayerMultiServer.Player_GetZonesStatus_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETZONESSTATUS_S2C, PlayerMultiServer.Player_GetZonesStatus_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCTNEWINNERBUILD_C2S, PlayerNewInnerBuild.Player_ProductNewInnerBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCTNEWINNERBUILD_S2C, PlayerNewInnerBuild.Player_ProductNewInnerBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SALEINNERBUILD_C2S, PlayerNewInnerBuild.Player_SaleInnerBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SALEINNERBUILD_S2C, PlayerNewInnerBuild.Player_SaleInnnerBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCEINNERBUILD_C2S, PlayerNewInnerBuild.Player_ProduceInnerBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCEINNERBUILD_S2C, PlayerNewInnerBuild.Player_ProduceInnerBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCESECRETWEAPON_C2S, PlayerNewInnerBuild.Player_ProduceSecretWeapon_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PRODUCESECRETWEAPON_S2C, PlayerNewInnerBuild.Player_ProduceSecretWeapon_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECVINNERMINERES_C2S, PlayerNewInnerBuild.Player_RecvInnerMineRes_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECVINNERMINERES_S2C, PlayerNewInnerBuild.Player_RecvInnerMineRes_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTCRISISBATTLE_C2S, PlayerNewInnerBuild.Player_StartCrisisBattle_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_STARTCRISISBATTLE_S2C, PlayerNewInnerBuild.Player_StartCrisisBattle_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ENDCRISISBATTLE_C2S, PlayerNewInnerBuild.Player_EndCrisisBattle_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ENDCRISISBATTLE_S2C, PlayerNewInnerBuild.Player_EndCrisisBattle_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHNEWBASES_C2S, PlayerNewInnerBuild.Player_SearchNewBases_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHNEWBASES_S2C, PlayerNewInnerBuild.Player_SearchNewBases_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FLUSHNEWBASES_C2S, PlayerNewInnerBuild.Player_FlushNewBases_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FLUSHNEWBASES_S2C, PlayerNewInnerBuild.Player_FlushNewBases_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MIGRATENEWBASE_C2S, PlayerNewInnerBuild.Player_MigrateNewBase_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MIGRATENEWBASE_S2C, PlayerNewInnerBuild.Player_MigrateNewBase_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPGRADEBUILD_C2S, PlayerNewInnerBuild.Player_UpgradeBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPGRADEBUILD_S2C, PlayerNewInnerBuild.Player_UpgradeBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ISSUECOMMAND_C2S, PlayerNewInnerBuild.Player_IssueCommand_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ISSUECOMMAND_S2C, PlayerNewInnerBuild.Player_IssueCommand_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MOVEINNERBUILD_C2S, PlayerNewInnerBuild.Player_MoveInnerBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MOVEINNERBUILD_S2C, PlayerNewInnerBuild.Player_MoveInnerBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPGRADEINNERBUILD_C2S, PlayerNewInnerBuild.Player_UpgradeInnerBuild_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPGRADEINNERBUILD_S2C, PlayerNewInnerBuild.Player_UpgradeInnerBuild_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNLOCKNEWAREA_C2S, PlayerNewInnerBuild.Player_UnlockNewArea_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UNLOCKNEWAREA_S2C, PlayerNewInnerBuild.Player_UnlockNewArea_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PAYMENTOVER_C2S, PlayerPayment.Player_PaymentOver_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PAYMENTOVER_S2C, PlayerPayment.Player_PaymentOver_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYGOODSORDER_C2S, PlayerPayment.Player_ApplyGoodsOrder_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_APPLYGOODSORDER_S2C, PlayerPayment.Player_ApplyGoodsOrder_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FAKERECHARGE_C2S, PlayerPayment.Player_FakeRecharge_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FAKERECHARGE_S2C, PlayerPayment.Player_FakeRecharge_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_JUDGERECHARGEPACKAGE_C2S, PlayerPayment.Player_JudgeRecharge_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_JUDGERECHARGEPACKAGE_S2C, PlayerPayment.Player_JudgeRecharge_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRIGGERBUNDLESHOW_C2S, PlayerPayment.Player_TriggerBundleShow_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TRIGGERBUNDLESHOW_S2C, PlayerPayment.Player_TriggerBundleShow_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_POSITIONMARK_C2S, PlayerPositionMark.Player_PositionMarck_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_POSITIONMARK_S2C, PlayerPositionMark.Player_PositionMarck_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANPOSITIONMARK_C2S, PlayerPositionMark.Player_FetchClanPositionMark_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANPOSITIONMARK_S2C, PlayerPositionMark.Player_FetchClanPositionMark_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SPEEDQUEUETASK_C2S, PlayerQueueTask.Player_SpeedQueueTask_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SPEEDQUEUETASK_S2C, PlayerQueueTask.Player_SpeedQueueTask_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHQUEUETASK_C2S, PlayerQueueTask.Player_FinishQueueTask_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FINISHQUEUETASK_S2C, PlayerQueueTask.Player_FinishQueueTask_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELQUEUETASK_C2S, PlayerQueueTask.Player_CancelQueueTask_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELQUEUETASK_S2C, PlayerQueueTask.Player_CancelQueueTask_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUEUEFREESPEED_C2S, PlayerQueueTask.Player_QueueFreeSpeed_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUEUEFREESPEED_S2C, PlayerQueueTask.Player_QueueFreeSpeed_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUEUEFINISH_NTF, PlayerQueueTask.Player_QueueFinish_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELRALLY_C2S, PlayerRally.Player_CancelRally_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CANCELRALLY_S2C, PlayerRally.Player_CancelRally_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYRALLYLIST_C2S, PlayerRally.Player_QueryRallyList_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYRALLYLIST_S2C, PlayerRally.Player_QueryRallyList_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYONERALLY_C2S, PlayerRally.Player_QueryOneRally_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_QUERYONERALLY_S2C, PlayerRally.Player_QueryOneRally_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPATRIATEONEARMYINRALLY_C2S, PlayerRally.Player_RepatriateOneArmyInRally_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPATRIATEONEARMYINRALLY_S2C, PlayerRally.Player_RepatriateOneArmyInRally_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_C2S, PlayerRally.Player_SetRallyRecommendSoldierType_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_S2C, PlayerRally.Player_SetRallyRecommendSoldierType_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHWARNINGLIST_C2S, PlayerRally.Player_FetchWarningList_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHWARNINGLIST_S2C, PlayerRally.Player_FetchWarningList_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETWARNINGITEMIGNORETAG_C2S, PlayerRally.Player_SetWarningItemIgnoreTag_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SETWARNINGITEMIGNORETAG_S2C, PlayerRally.Player_SetWarningItemIgnoreTag_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_IGNOREWARNINGALL_C2S, PlayerRally.Player_IgnoreWarningAll_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_IGNOREWARNINGALL_S2C, PlayerRally.Player_IgnoreWarningAll_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCITYINNERARMY_C2S, PlayerRally.Player_FetchCityInnerArmy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCITYINNERARMY_S2C, PlayerRally.Player_FetchCityInnerArmy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPATRIATEONEARMYINASSIST_C2S, PlayerRally.Player_RepatriateOneArmyInAssist_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPATRIATEONEARMYINASSIST_S2C, PlayerRally.Player_RepatriateOneArmyInAssist_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCITYASSISTHISTORY_C2S, PlayerRally.Player_FetchCityAssistHistory_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCITYASSISTHISTORY_S2C, PlayerRally.Player_FetchCityAssistHistory_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ONJOINRALLYARMY_NTF, PlayerRally.Player_OnJoinRallyArmy_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDRALLYINVITEMSG_C2S, PlayerRally.Player_InviteJoinRally_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SENDRALLYINVITEMSG_S2C, PlayerRally.Player_InviteJoinRally_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLICKRALLYINVITE_C2S, PlayerRally.Player_ClickRallyInvite_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLICKRALLYINVITE_S2C, PlayerRally.Player_ClickRallyInvite_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETRANKPAGEINFO_C2S, PlayerRank.Player_GetRankPageInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETRANKPAGEINFO_S2C, PlayerRank.Player_GetRankPageInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETTOPRANKINFO_C2S, PlayerRank.Player_GetTopRankInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETTOPRANKINFO_S2C, PlayerRank.Player_GetTopRankInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPORTMAPVERSION_C2S, PlayerScene.Player_ReportMapVersion_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REPORTMAPVERSION_S2C, PlayerScene.Player_ReportMapVersion_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKZONEVIEW_C2S, PlayerScene.Player_CheckZoneView_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKZONEVIEW_S2C, PlayerScene.Player_CheckZoneView_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPDATEVIEW_C2S, PlayerScene.Player_UpdateView_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_UPDATEVIEW_S2C, PlayerScene.Player_UpdateView_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATEARMY_C2S, PlayerScene.Player_CreateArmy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CREATEARMY_S2C, PlayerScene.Player_CreateArmy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEARMYACTION_C2S, PlayerScene.Player_ChangeArmyAction_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEARMYACTION_S2C, PlayerScene.Player_ChangeArmyAction_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FORCEDDEFEATARMY_C2S, PlayerScene.Player_ForcedDefeatArmy_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FORCEDDEFEATARMY_S2C, PlayerScene.Player_ForcedDefeatArmy_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHWALKPATH_C2S, PlayerScene.Player_SearchWalkPath_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHWALKPATH_S2C, PlayerScene.Player_SearchWalkPath_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCANATTACK_C2S, PlayerScene.Player_CheckCanAttack_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHECKCANATTACK_S2C, PlayerScene.Player_CheckCanAttack_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEMAP_NTF, PlayerScene.Player_ChangeMap_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGEMAPDONE_NTF, PlayerScene.Player_ChangeMapDone_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ORDINARYATTACK_NTF, PlayerScene.Player_OrdinaryAttack_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MARQUEEMESSAGE_NTF, PlayerScene.Player_MarqueeMessage_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MOVECITYACTIONNTF_NTF, PlayerScene.Player_MoveCityAction_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MONSTERINVOKE_NTF, PlayerScene.Player_MonsterInvoke_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYANNOUNCEMENT_NTF, PlayerScene.Player_PlayAnnouncement_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKILLFIRE_NTF, PlayerScene.Player_SkillFire_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_MONSTERDROPOUT_NTF, PlayerScene.Player_MonsterDropOut_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_PLAYDIALOG_NTF, PlayerScene.Player_PlayDialog_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHRESOURCE_C2S, PlayerScene.Player_SearchResource_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SEARCHRESOURCE_S2C, PlayerScene.Player_SearchResource_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ENTEREXPEDITION_C2S, PlayerScene.Player_EnterExpedition_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_ENTEREXPEDITION_S2C, PlayerScene.Player_EnterExpedition_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LEAVEDUNGEON_C2S, PlayerScene.Player_LeaveDungeon_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_LEAVEDUNGEON_S2C, PlayerScene.Player_LeaveDungeon_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REENTERDUNGEON_C2S, PlayerScene.Player_ReEnterDungeon_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_REENTERDUNGEON_S2C, PlayerScene.Player_ReEnterDungeon_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGETOBIGSCENE_C2S, PlayerScene.Player_ChangeToBigScene_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CHANGETOBIGSCENE_S2C, PlayerScene.Player_ChangeToBigScene_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEDUNGEONSKILL_C2S, PlayerScene.Player_UseDungeonSkill_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_USEDUNGEONSKILL_S2C, PlayerScene.Player_UseDungeonSkill_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSECLANRECOMMEND_C2S, PlayerScene.Player_CloseClanRecommend_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CLOSECLANRECOMMEND_S2C, PlayerScene.Player_CloseClanRecommend_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECOMMENDCLAN_NTF, PlayerScene.Player_RecommendClan_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SPECIALSKILLFIRE_NTF, PlayerScene.Player_SpecialSkillFire_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SPECIALORDINARYATTACK_NTF, PlayerScene.Player_SpecialOrdinaryAttack_NTF.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CONSTRUCTCLANRESBUILDING_C2S, PlayerSceneClan.Player_ConstructClanResBuilding_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_CONSTRUCTCLANRESBUILDING_S2C, PlayerSceneClan.Player_ConstructClanResBuilding_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANRESBUILDINGINFO_C2S, PlayerSceneClan.Player_FetchClanResBuildingInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCLANRESBUILDINGINFO_S2C, PlayerSceneClan.Player_FetchClanResBuildingInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETDOINGTASK_C2S, PlayerSkynet.Player_SkynetDoingTask_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETDOINGTASK_S2C, PlayerSkynet.Player_SkynetDoingTask_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETTAKETASKREWARD_C2S, PlayerSkynet.Player_SkynetTakeTaskReward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETTAKETASKREWARD_S2C, PlayerSkynet.Player_SkynetTakeTaskReward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETSTOREINFO_C2S, PlayerSkynet.Player_SkynetStoreInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETSTOREINFO_S2C, PlayerSkynet.Player_SkynetStoreInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETCHARGE_C2S, PlayerSkynet.Player_SkynetCharge_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETCHARGE_S2C, PlayerSkynet.Player_SkynetCharge_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETBUYSTORE_C2S, PlayerSkynet.Player_SkynetBuyStore_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETBUYSTORE_S2C, PlayerSkynet.Player_SkynetBuyStore_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETFINDMONSTER_C2S, PlayerSkynet.Player_SkynetFindMonster_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SKYNETFINDMONSTER_S2C, PlayerSkynet.Player_SkynetFindMonster_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERTRAIN_C2S, PlayerSoldier.Player_SoldierTrain_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERTRAIN_S2C, PlayerSoldier.Player_SoldierTrain_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERFASTTRAIN_C2S, PlayerSoldier.Player_SoldierFastTrain_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERFASTTRAIN_S2C, PlayerSoldier.Player_SoldierFastTrain_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERGATHER_C2S, PlayerSoldier.Player_SoldierGather_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERGATHER_S2C, PlayerSoldier.Player_SoldierGather_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERRUSHTRAIN_C2S, PlayerSoldier.Player_SoldierRushTrain_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERRUSHTRAIN_S2C, PlayerSoldier.Player_SoldierRushTrain_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERDISMISS_C2S, PlayerSoldier.Player_SoldierDismiss_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERDISMISS_S2C, PlayerSoldier.Player_SoldierDismiss_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERLEVELUP_C2S, PlayerSoldier.Player_SoldierLevelUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERLEVELUP_S2C, PlayerSoldier.Player_SoldierLevelUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERFASTLEVELUP_C2S, PlayerSoldier.Player_SoldierFastLevelUp_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SOLDIERFASTLEVELUP_S2C, PlayerSoldier.Player_SoldierFastLevelUp_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCOMMISSARIATSTOREINFO_C2S, PlayerStore.Player_FetchCommissariatStoreInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_FETCHCOMMISSARIATSTOREINFO_S2C, PlayerStore.Player_FetchCommissariatStoreInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYSTOREITEM_C2S, PlayerStore.Player_BuyStoreItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYSTOREITEM_S2C, PlayerStore.Player_BuyStoreItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYANDUSESTOREITEM_C2S, PlayerStore.Player_BuyAndUseStoreItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYANDUSESTOREITEM_S2C, PlayerStore.Player_BuyAndUseStoreItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TASKAWARD_C2S, PlayerTask.Player_TaskAward_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_TASKAWARD_S2C, PlayerTask.Player_TaskAward_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESEARCHTECH_C2S, PlayerTech.Player_ResearchTech_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RESEARCHTECH_S2C, PlayerTech.Player_ResearchTech_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_IMMEDIATELYRESEARCHTECH_C2S, PlayerTech.Player_ImmediatelyResearchTech_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_IMMEDIATELYRESEARCHTECH_S2C, PlayerTech.Player_ImmediatelyResearchTech_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECEIVETECH_C2S, PlayerTech.Player_ReceiveTech_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_RECEIVETECH_S2C, PlayerTech.Player_ReceiveTech_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPSTOREINFO_C2S, PlayerVipStore.Player_GetVipStoreInfo_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_GETVIPSTOREINFO_S2C, PlayerVipStore.Player_GetVipStoreInfo_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYVIPSTOREITEM_C2S, PlayerVipStore.Player_BuyVipStoreItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_BUYVIPSTOREITEM_S2C, PlayerVipStore.Player_BuyVipStoreItem_S2C.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SELECTVIPHEROITEM_C2S, PlayerVipStore.Player_SelectVipHeroItem_C2S.getDefaultInstance());
        MSG_ID_2_PROTO_MSG.put(MsgType.PLAYER_SELECTVIPHEROITEM_S2C, PlayerVipStore.Player_SelectVipHeroItem_S2C.getDefaultInstance());
    }

    public static Map<Integer, GeneratedMessageV3> getMsgId2ProtoMsgMap() {
        return MSG_ID_2_PROTO_MSG;
    }

    public static GeneratedMessageV3 getMsgFromType(int msgType) {
        return MSG_ID_2_PROTO_MSG.get(msgType);
    }

	/**
	 * @param c2sMsgId 客户端上行ID
	 */
	public static int getRetMsgId(int c2sMsgId){
		switch (c2sMsgId) {
			case DIRGETSERVERLIST_C2S_MSG:
				return DIRGETSERVERLIST_S2C_MSG;
			case DIRGETZONE_C2S_MSG:
				return DIRGETZONE_S2C_MSG;
			case DIRDELACCOUNT_C2S_MSG:
				return DIRDELACCOUNT_S2C_MSG;
			case DIRREGISTERACCOUNT_C2S_MSG:
				return DIRREGISTERACCOUNT_S2C_MSG;
			case DIRACCOUNTLOGIN_C2S_MSG:
				return DIRACCOUNTLOGIN_S2C_MSG;
			case DIRACCOUNTBIND_C2S_MSG:
				return DIRACCOUNTBIND_S2C_MSG;
			case DIRACCOUNTUNBIND_C2S_MSG:
				return DIRACCOUNTUNBIND_S2C_MSG;
			case DIRACCOUNTBINDINFO_C2S_MSG:
				return DIRACCOUNTBINDINFO_S2C_MSG;
			case DIRPING_C2S_MSG:
				return DIRPING_S2C_MSG;
			case LOGIN_C2S_MSG:
				return LOGIN_S2C_MSG;
			case GETROLELIST_C2S_MSG:
				return GETROLELIST_S2C_MSG;
			case ACCOUNTLOGIN_C2S_MSG:
				return ACCOUNTLOGIN_S2C_MSG;
			case ACCOUNTBIND_C2S_MSG:
				return ACCOUNTBIND_S2C_MSG;
			case ACCOUNTUNBIND_C2S_MSG:
				return ACCOUNTUNBIND_S2C_MSG;
			case ACCOUNTBINDINFO_C2S_MSG:
				return ACCOUNTBINDINFO_S2C_MSG;
			case REPORTEXECUTE_C2S_MSG:
				return REPORTEXECUTE_S2C_MSG;
			case LOGLOADING_C2S_MSG:
				return LOGLOADING_S2C_MSG;
			case KEEPALIVE_C2S_MSG:
				return KEEPALIVE_S2C_MSG;
			case PLAYER_FETCHACHIEVEMENT_C2S:
				return PLAYER_FETCHACHIEVEMENT_S2C;
			case PLAYER_TAKEACHIEVEMENTREWARD_C2S:
				return PLAYER_TAKEACHIEVEMENTREWARD_S2C;
			case PLAYER_SHAREACHIEVEMENT_C2S:
				return PLAYER_SHAREACHIEVEMENT_S2C;
			case PLAYER_ACTIVITYTAKEREWARD_C2S:
				return PLAYER_ACTIVITYTAKEREWARD_S2C;
			case PLAYER_ACTIVITYBUYGOOD_C2S:
				return PLAYER_ACTIVITYBUYGOOD_S2C;
			case PLAYER_ACTIVITYBUYSECONDQUEUE_C2S:
				return PLAYER_ACTIVITYBUYSECONDQUEUE_S2C;
			case PLAYER_SENDACTIVITYINVITEMSG_C2S:
				return PLAYER_SENDACTIVITYINVITEMSG_S2C;
			case PLAYER_BESTCOMMANDERFETCH_C2S:
				return PLAYER_BESTCOMMANDERFETCH_S2C;
			case PLAYER_BESTCOMMANDERHISTORYRANK_C2S:
				return PLAYER_BESTCOMMANDERHISTORYRANK_S2C;
			case PLAYER_REPORTSTORERATINGRESULT_C2S:
				return PLAYER_REPORTSTORERATINGRESULT_S2C;
			case PLAYER_CHOOSESPLEVELREWARD_C2S:
				return PLAYER_CHOOSESPLEVELREWARD_S2C;
			case PLAYER_BPTICKETEXCHANGE_C2S:
				return PLAYER_BPTICKETEXCHANGE_S2C;
			case PLAYER_BUYBP_C2S:
				return PLAYER_BUYBP_S2C;
			case PLAYER_FETCHACTIVITYCALENDAR_C2S:
				return PLAYER_FETCHACTIVITYCALENDAR_S2C;
			case PLAYER_CHOOSEBESTCOMMANDERITEM_C2S:
				return PLAYER_CHOOSEBESTCOMMANDERITEM_S2C;
			case PLAYER_FETCHACTIVITYCLANDATA_C2S:
				return PLAYER_FETCHACTIVITYCLANDATA_S2C;
			case PLAYER_BUYLOTTERYVOUCHER_C2S:
				return PLAYER_BUYLOTTERYVOUCHER_S2C;
			case PLAYER_LOTTERYNATASHADRAW_C2S:
				return PLAYER_LOTTERYNATASHADRAW_S2C;
			case PLAYER_SETTLELOTTERYHERO_C2S:
				return PLAYER_SETTLELOTTERYHERO_S2C;
			case PLAYER_LOTTERYNATASHASHOPEXCHANGE_C2S:
				return PLAYER_LOTTERYNATASHASHOPEXCHANGE_S2C;
			case PLAYER_CREATESPYPLANE_C2S:
				return PLAYER_CREATESPYPLANE_S2C;
			case PLAYER_CHANGEACTIONSPYPLANE_C2S:
				return PLAYER_CHANGEACTIONSPYPLANE_S2C;
			case PLAYER_CHECKSPY_C2S:
				return PLAYER_CHECKSPY_S2C;
			case PLAYER_CREATELOGISTICSPLANE_C2S:
				return PLAYER_CREATELOGISTICSPLANE_S2C;
			case PLAYER_CHANGELOGISTICSPLANE_C2S:
				return PLAYER_CHANGELOGISTICSPLANE_S2C;
			case PLAYER_OBTAINBATTLEPASSTASKEXP_C2S:
				return PLAYER_OBTAINBATTLEPASSTASKEXP_S2C;
			case PLAYER_OBTAINDALIYEXPBOX_C2S:
				return PLAYER_OBTAINDALIYEXPBOX_S2C;
			case PLAYER_OBTAINBATTLEPASSLEVELREWARD_C2S:
				return PLAYER_OBTAINBATTLEPASSLEVELREWARD_S2C;
			case PLAYER_PURCHASEBATTLEPASSLEVEL_C2S:
				return PLAYER_PURCHASEBATTLEPASSLEVEL_S2C;
			case PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_C2S:
				return PLAYER_OBTAINSEASONBATTLEPASSTASKEXP_S2C;
			case PLAYER_OBTAINSEASONDALIYEXPBOX_C2S:
				return PLAYER_OBTAINSEASONDALIYEXPBOX_S2C;
			case PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_C2S:
				return PLAYER_OBTAINSEASONBATTLEPASSLEVELREWARD_S2C;
			case PLAYER_PURCHASESEASONBATTLEPASSLEVEL_C2S:
				return PLAYER_PURCHASESEASONBATTLEPASSLEVEL_S2C;
			case PLAYER_STARTCAMPAIGN_C2S:
				return PLAYER_STARTCAMPAIGN_S2C;
			case PLAYER_ABORTCAMPAIGN_C2S:
				return PLAYER_ABORTCAMPAIGN_S2C;
			case PLAYER_STARTMISSION_C2S:
				return PLAYER_STARTMISSION_S2C;
			case PLAYER_FINISHMISSION_C2S:
				return PLAYER_FINISHMISSION_S2C;
			case PLAYER_MERGECAMPAIGNITEM_C2S:
				return PLAYER_MERGECAMPAIGNITEM_S2C;
			case PLAYER_CAMPAIGNEVENTREST_C2S:
				return PLAYER_CAMPAIGNEVENTREST_S2C;
			case PLAYER_CAMPAIGNEVENTBAR_C2S:
				return PLAYER_CAMPAIGNEVENTBAR_S2C;
			case PLAYER_CAMPAIGNEVENTTRAIN_C2S:
				return PLAYER_CAMPAIGNEVENTTRAIN_S2C;
			case PLAYER_CAMPAIGNEVENTRESTORE_C2S:
				return PLAYER_CAMPAIGNEVENTRESTORE_S2C;
			case PLAYER_CAMPAIGNEVENTREPAIR_C2S:
				return PLAYER_CAMPAIGNEVENTREPAIR_S2C;
			case PLAYER_CAMPAIGNEVENTEXIT_C2S:
				return PLAYER_CAMPAIGNEVENTEXIT_S2C;
			case PLAYER_CAMPAIGNMISSIONSTART_C2S:
				return PLAYER_CAMPAIGNMISSIONSTART_S2C;
			case PLAYER_CAMPAIGNMISSIONFINISH_C2S:
				return PLAYER_CAMPAIGNMISSIONFINISH_S2C;
			case PLAYER_TOUCHMISSIONCHEST_C2S:
				return PLAYER_TOUCHMISSIONCHEST_S2C;
			case PLAYER_GETCHARACTERLIST_C2S:
				return PLAYER_GETCHARACTERLIST_S2C;
			case PLAYER_GETZONESERVERLIST_C2S:
				return PLAYER_GETZONESERVERLIST_S2C;
			case PLAYER_MANAGESTARFORCHARACTER_C2S:
				return PLAYER_MANAGESTARFORCHARACTER_S2C;
			case PLAYER_CHECKREGISTERNEWCHARACTER_C2S:
				return PLAYER_CHECKREGISTERNEWCHARACTER_S2C;
			case PLAYER_GETZONEIPPORT_C2S:
				return PLAYER_GETZONEIPPORT_S2C;
			case PLAYER_GETCHATMESSAGES_C2S:
				return PLAYER_GETCHATMESSAGES_S2C;
			case PLAYER_CHATREQUEST_C2S:
				return PLAYER_CHATREQUEST_S2C;
			case PLAYER_READCHATMESSAGE_C2S:
				return PLAYER_READCHATMESSAGE_S2C;
			case PLAYER_TRANSLATETEXT_C2S:
				return PLAYER_TRANSLATETEXT_S2C;
			case PLAYER_HIDECHAT_C2S:
				return PLAYER_HIDECHAT_S2C;
			case PLAYER_CREATEGROUPCHAT_C2S:
				return PLAYER_CREATEGROUPCHAT_S2C;
			case PLAYER_GETCHATMEMBERLIST_C2S:
				return PLAYER_GETCHATMEMBERLIST_S2C;
			case PLAYER_GETBATCHPLAYERCACHE_C2S:
				return PLAYER_GETBATCHPLAYERCACHE_S2C;
			case PLAYER_CHECKINGROUPCHAT_C2S:
				return PLAYER_CHECKINGROUPCHAT_S2C;
			case PLAYER_INVITEPLAYERJOINGROUP_C2S:
				return PLAYER_INVITEPLAYERJOINGROUP_S2C;
			case PLAYER_REMOVEPLAYERFROMGROUP_C2S:
				return PLAYER_REMOVEPLAYERFROMGROUP_S2C;
			case PLAYER_PLAYERQUITGROUPCHAT_C2S:
				return PLAYER_PLAYERQUITGROUPCHAT_S2C;
			case PLAYER_PLAYERTRANSFERGROUPOWNER_C2S:
				return PLAYER_PLAYERTRANSFERGROUPOWNER_S2C;
			case PLAYER_DISMISSGROUPCHAT_C2S:
				return PLAYER_DISMISSGROUPCHAT_S2C;
			case PLAYER_PLAYERMODIFYGROUPNAME_C2S:
				return PLAYER_PLAYERMODIFYGROUPNAME_S2C;
			case PLAYER_OPENCHATNOTDISTURB_C2S:
				return PLAYER_OPENCHATNOTDISTURB_S2C;
			case PLAYER_CLOSECHATNOTDISTURB_C2S:
				return PLAYER_CLOSECHATNOTDISTURB_S2C;
			case PLAYER_SETPLAYERNOTDISTURB_C2S:
				return PLAYER_SETPLAYERNOTDISTURB_S2C;
			case PLAYER_UNSETPLAYERNOTDISTURB_C2S:
				return PLAYER_UNSETPLAYERNOTDISTURB_S2C;
			case PLAYER_DELETECHATITEM_C2S:
				return PLAYER_DELETECHATITEM_S2C;
			case PLAYER_SETGROUPCHATSHOW_C2S:
				return PLAYER_SETGROUPCHATSHOW_S2C;
			case PLAYER_GETSHAREDMAILCONTENT_C2S:
				return PLAYER_GETSHAREDMAILCONTENT_S2C;
			case PLAYER_CREATECLAN_C2S:
				return PLAYER_CREATECLAN_S2C;
			case PLAYER_CHECKCLANNAME_C2S:
				return PLAYER_CHECKCLANNAME_S2C;
			case PLAYER_SEARCHCLAN_C2S:
				return PLAYER_SEARCHCLAN_S2C;
			case PLAYER_FETCHCLANENTITY_C2S:
				return PLAYER_FETCHCLANENTITY_S2C;
			case PLAYER_FETCHCLANSIMPLEINFO_C2S:
				return PLAYER_FETCHCLANSIMPLEINFO_S2C;
			case PLAYER_FETCHCLANMEMBERINFO_C2S:
				return PLAYER_FETCHCLANMEMBERINFO_S2C;
			case PLAYER_FETCHCLANAPPLYPLAYERS_C2S:
				return PLAYER_FETCHCLANAPPLYPLAYERS_S2C;
			case PLAYER_APPLYJOINCLAN_C2S:
				return PLAYER_APPLYJOINCLAN_S2C;
			case PLAYER_CANCELAPPLYJOINCLAN_C2S:
				return PLAYER_CANCELAPPLYJOINCLAN_S2C;
			case PLAYER_HANDLECLANAPPLY_C2S:
				return PLAYER_HANDLECLANAPPLY_S2C;
			case PLAYER_QUITCLAN_C2S:
				return PLAYER_QUITCLAN_S2C;
			case PLAYER_INVITETOCLAN_C2S:
				return PLAYER_INVITETOCLAN_S2C;
			case PLAYER_HANDLECLANINVITATION_C2S:
				return PLAYER_HANDLECLANINVITATION_S2C;
			case PLAYER_DISSOLVECLAN_C2S:
				return PLAYER_DISSOLVECLAN_S2C;
			case PLAYER_CANCELDISSOLVECLAN_C2S:
				return PLAYER_CANCELDISSOLVECLAN_S2C;
			case PLAYER_APPLYOWNERDISSOLVING_C2S:
				return PLAYER_APPLYOWNERDISSOLVING_S2C;
			case PLAYER_CANCELAPPLYOWNERDISSOLVING_C2S:
				return PLAYER_CANCELAPPLYOWNERDISSOLVING_S2C;
			case PLAYER_FETCHDISSOLVINGINFO_C2S:
				return PLAYER_FETCHDISSOLVINGINFO_S2C;
			case PLAYER_TRANSFERCLANOWNER_C2S:
				return PLAYER_TRANSFERCLANOWNER_S2C;
			case PLAYER_KICKOFFCLANMEMBER_C2S:
				return PLAYER_KICKOFFCLANMEMBER_S2C;
			case PLAYER_MODIFYCLANMEMBERSTAFF_C2S:
				return PLAYER_MODIFYCLANMEMBERSTAFF_S2C;
			case PLAYER_MODIFYCLANNAME_C2S:
				return PLAYER_MODIFYCLANNAME_S2C;
			case PLAYER_MODIFYCLANSNAME_C2S:
				return PLAYER_MODIFYCLANSNAME_S2C;
			case PLAYER_MODIFYCLANFLAG_C2S:
				return PLAYER_MODIFYCLANFLAG_S2C;
			case PLAYER_MODIFYCLANSETTINGMISC_C2S:
				return PLAYER_MODIFYCLANSETTINGMISC_S2C;
			case PLAYER_FETCHCLANWELCOMELETTER_C2S:
				return PLAYER_FETCHCLANWELCOMELETTER_S2C;
			case PLAYER_MODIFYCLANWELCOMELETTER_C2S:
				return PLAYER_MODIFYCLANWELCOMELETTER_S2C;
			case PLAYER_FETCHCLANTERRITORYPAGE_C2S:
				return PLAYER_FETCHCLANTERRITORYPAGE_S2C;
			case PLAYER_ABANDONMAPBUILDING_C2S:
				return PLAYER_ABANDONMAPBUILDING_S2C;
			case PLAYER_CHANGEDEFENSIVELEADER_C2S:
				return PLAYER_CHANGEDEFENSIVELEADER_S2C;
			case PLAYER_OBTAINCLANPOWERREWARD_C2S:
				return PLAYER_OBTAINCLANPOWERREWARD_S2C;
			case PLAYER_FETCHCLANMEMBERCITY_C2S:
				return PLAYER_FETCHCLANMEMBERCITY_S2C;
			case PLAYER_FETCHCLANSINGLEMEMBERCITY_C2S:
				return PLAYER_FETCHCLANSINGLEMEMBERCITY_S2C;
			case PLAYER_FETCHCLANTERRITORYMAP_C2S:
				return PLAYER_FETCHCLANTERRITORYMAP_S2C;
			case PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_C2S:
				return PLAYER_CONSTRUCTCLANSTRONGHOLDBUILDING_S2C;
			case PLAYER_DESTROYCLANSTRONGHOLDBUILDING_C2S:
				return PLAYER_DESTROYCLANSTRONGHOLDBUILDING_S2C;
			case PLAYER_EXTINGUISHBUILDINGFIRE_C2S:
				return PLAYER_EXTINGUISHBUILDINGFIRE_S2C;
			case PLAYER_FETCHCLANBUILDINGHP_C2S:
				return PLAYER_FETCHCLANBUILDINGHP_S2C;
			case PLAYER_FETCHCLANPOWERREWARD_C2S:
				return PLAYER_FETCHCLANPOWERREWARD_S2C;
			case PLAYER_CHECKCANFREEREBUILD_C2S:
				return PLAYER_CHECKCANFREEREBUILD_S2C;
			case PLAYER_VERIFYREBUILDMAPBUILDING_C2S:
				return PLAYER_VERIFYREBUILDMAPBUILDING_S2C;
			case PLAYER_GETCLANRANKPAGEINFO_C2S:
				return PLAYER_GETCLANRANKPAGEINFO_S2C;
			case PLAYER_GETTOPCLANRANKINFO_C2S:
				return PLAYER_GETTOPCLANRANKINFO_S2C;
			case PLAYER_CREATECLANHELP_C2S:
				return PLAYER_CREATECLANHELP_S2C;
			case PLAYER_FETCHCLANHELPS_C2S:
				return PLAYER_FETCHCLANHELPS_S2C;
			case PLAYER_FINISHALLCLANHELPS_C2S:
				return PLAYER_FINISHALLCLANHELPS_S2C;
			case PLAYER_GETCLANGIFT_C2S:
				return PLAYER_GETCLANGIFT_S2C;
			case PLAYER_CLEARCLANGIFT_C2S:
				return PLAYER_CLEARCLANGIFT_S2C;
			case PLAYER_GETCLANTREASUREGIFT_C2S:
				return PLAYER_GETCLANTREASUREGIFT_S2C;
			case PLAYER_FETCHCLANWAREHOUSEINFO_C2S:
				return PLAYER_FETCHCLANWAREHOUSEINFO_S2C;
			case PLAYER_FETCHCLANRESOURCESNAPSHOT_C2S:
				return PLAYER_FETCHCLANRESOURCESNAPSHOT_S2C;
			case PLAYER_FETCHCLANEVENTLOG_C2S:
				return PLAYER_FETCHCLANEVENTLOG_S2C;
			case PLAYER_FETCHCLANNUMMAXTIP_C2S:
				return PLAYER_FETCHCLANNUMMAXTIP_S2C;
			case PLAYER_FETCHCLANCOMMANDCENTERNUM_C2S:
				return PLAYER_FETCHCLANCOMMANDCENTERNUM_S2C;
			case PLAYER_FETCHCLANSTORE_C2S:
				return PLAYER_FETCHCLANSTORE_S2C;
			case PLAYER_OPERATECLANSTOREITEM_C2S:
				return PLAYER_OPERATECLANSTOREITEM_S2C;
			case PLAYER_FETCHCLANSTORERECORD_C2S:
				return PLAYER_FETCHCLANSTORERECORD_S2C;
			case PLAYER_FETCHCLANTECHINFO_C2S:
				return PLAYER_FETCHCLANTECHINFO_S2C;
			case PLAYER_CLANTECHDONATE_C2S:
				return PLAYER_CLANTECHDONATE_S2C;
			case PLAYER_CLANTECHRESEARCH_C2S:
				return PLAYER_CLANTECHRESEARCH_S2C;
			case PLAYER_CLANTECHRECOMMEND_C2S:
				return PLAYER_CLANTECHRECOMMEND_S2C;
			case PLAYER_CLANTECHDETAIL_C2S:
				return PLAYER_CLANTECHDETAIL_S2C;
			case PLAYER_DEBUGCOMMAND_C2S:
				return PLAYER_DEBUGCOMMAND_S2C;
			case PLAYER_GETDEBUGCOMMANDLIST_C2S:
				return PLAYER_GETDEBUGCOMMANDLIST_S2C;
			case PLAYER_SEARCHMONSTER_C2S:
				return PLAYER_SEARCHMONSTER_S2C;
			case PLAYER_VERIFYMOVECITY_C2S:
				return PLAYER_VERIFYMOVECITY_S2C;
			case PLAYER_BUYANDUSEITEM_C2S:
				return PLAYER_BUYANDUSEITEM_S2C;
			case PLAYER_USEITEM_C2S:
				return PLAYER_USEITEM_S2C;
			case PLAYER_SWITCHVIEWSCENE_C2S:
				return PLAYER_SWITCHVIEWSCENE_S2C;
			case PLAYER_QUERYPLAYERCARDINFO_C2S:
				return PLAYER_QUERYPLAYERCARDINFO_S2C;
			case PLAYER_QUERYPLAYERCARDINFODETAIL_C2S:
				return PLAYER_QUERYPLAYERCARDINFODETAIL_S2C;
			case PLAYER_SETWALLGARRISON_C2S:
				return PLAYER_SETWALLGARRISON_S2C;
			case PLAYER_REPAIRWALL_C2S:
				return PLAYER_REPAIRWALL_S2C;
			case PLAYER_OUTFIREWALL_C2S:
				return PLAYER_OUTFIREWALL_S2C;
			case PLAYER_CITYFALL_C2S:
				return PLAYER_CITYFALL_S2C;
			case PLAYER_CHANGECLIENTINFO_C2S:
				return PLAYER_CHANGECLIENTINFO_S2C;
			case PLAYER_FINISHGUIDANCE_C2S:
				return PLAYER_FINISHGUIDANCE_S2C;
			case PLAYER_CHANGEPLAYERNAME_C2S:
				return PLAYER_CHANGEPLAYERNAME_S2C;
			case PLAYER_CHANGEAVATAR_C2S:
				return PLAYER_CHANGEAVATAR_S2C;
			case PLAYER_MARKNEWBIESTEP_C2S:
				return PLAYER_MARKNEWBIESTEP_S2C;
			case PLAYER_GIVENEWBIEHERO_C2S:
				return PLAYER_GIVENEWBIEHERO_S2C;
			case PLAYER_REMOVEREDDOT_C2S:
				return PLAYER_REMOVEREDDOT_S2C;
			case PLAYER_QUERYPLAYERKILLDETAIL_C2S:
				return PLAYER_QUERYPLAYERKILLDETAIL_S2C;
			case PLAYER_TAKEDAILYFREEENERGY_C2S:
				return PLAYER_TAKEDAILYFREEENERGY_S2C;
			case PLAYER_BUYENERGY_C2S:
				return PLAYER_BUYENERGY_S2C;
			case PLAYER_RECRUIT_C2S:
				return PLAYER_RECRUIT_S2C;
			case PLAYER_UGCCHECK_C2S:
				return PLAYER_UGCCHECK_S2C;
			case PLAYER_TRIGGERQLOG_C2S:
				return PLAYER_TRIGGERQLOG_S2C;
			case PLAYER_QUERYPLAYERCARDHEAD_C2S:
				return PLAYER_QUERYPLAYERCARDHEAD_S2C;
			case PLAYER_SETSPASSWORD_C2S:
				return PLAYER_SETSPASSWORD_S2C;
			case PLAYER_CLOSESPASSWORD_C2S:
				return PLAYER_CLOSESPASSWORD_S2C;
			case PLAYER_CANCELCLOSESPW_C2S:
				return PLAYER_CANCELCLOSESPW_S2C;
			case PLAYER_USEEXPRESSION_C2S:
				return PLAYER_USEEXPRESSION_S2C;
			case PLAYER_SETSPASSWORDSTATUS_C2S:
				return PLAYER_SETSPASSWORDSTATUS_S2C;
			case PLAYER_SETPFLAG_C2S:
				return PLAYER_SETPFLAG_S2C;
			case PLAYER_GETVIPPRIVILEGEBOX_C2S:
				return PLAYER_GETVIPPRIVILEGEBOX_S2C;
			case PLAYER_GETVIPDAILYBOX_C2S:
				return PLAYER_GETVIPDAILYBOX_S2C;
			case PLAYER_SENDSYSASSISTMAIL_C2S:
				return PLAYER_SENDSYSASSISTMAIL_S2C;
			case PLAYER_SWITCHSETTING_C2S:
				return PLAYER_SWITCHSETTING_S2C;
			case PLAYER_AFTERBINDACCOUNT_C2S:
				return PLAYER_AFTERBINDACCOUNT_S2C;
			case PLAYER_MODIFYPUSHSETTING_C2S:
				return PLAYER_MODIFYPUSHSETTING_S2C;
			case PLAYER_QUERYPUSHSETTING_C2S:
				return PLAYER_QUERYPUSHSETTING_S2C;
			case PLAYER_GETDAILYDISCOUNTREWARD_C2S:
				return PLAYER_GETDAILYDISCOUNTREWARD_S2C;
			case PLAYER_SWITCHDAILYDISCOUNTHERO_C2S:
				return PLAYER_SWITCHDAILYDISCOUNTHERO_S2C;
			case PLAYER_GETSUPERDAILYDISCOUNTREWARD_C2S:
				return PLAYER_GETSUPERDAILYDISCOUNTREWARD_S2C;
			case PLAYER_GETDISCOUNTSTOREINFO_C2S:
				return PLAYER_GETDISCOUNTSTOREINFO_S2C;
			case PLAYER_BUYDISCOUNTSTOREITEM_C2S:
				return PLAYER_BUYDISCOUNTSTOREITEM_S2C;
			case PLAYER_REFRESHDISCOUNTSTORE_C2S:
				return PLAYER_REFRESHDISCOUNTSTORE_S2C;
			case PLAYER_EDITFORMATION_C2S:
				return PLAYER_EDITFORMATION_S2C;
			case PLAYER_RENAMEFORMATION_C2S:
				return PLAYER_RENAMEFORMATION_S2C;
			case PLAYER_REMOVEFORMATION_C2S:
				return PLAYER_REMOVEFORMATION_S2C;
			case PLAYER_EDITRHTROOP_C2S:
				return PLAYER_EDITRHTROOP_S2C;
			case PLAYER_EDITVIRTUALFORMATION_C2S:
				return PLAYER_EDITVIRTUALFORMATION_S2C;
			case PLAYER_SEARCHPLAYER_C2S:
				return PLAYER_SEARCHPLAYER_S2C;
			case PLAYER_SEARCHPLAYERBYNAME_C2S:
				return PLAYER_SEARCHPLAYERBYNAME_S2C;
			case PLAYER_ADDFRIENDAPPLY_C2S:
				return PLAYER_ADDFRIENDAPPLY_S2C;
			case PLAYER_AGREEFRIENDAPPLY_C2S:
				return PLAYER_AGREEFRIENDAPPLY_S2C;
			case PLAYER_REFUSEFRIENDAPPLY_C2S:
				return PLAYER_REFUSEFRIENDAPPLY_S2C;
			case PLAYER_DELFRIEND_C2S:
				return PLAYER_DELFRIEND_S2C;
			case PLAYER_SHIELDPLAYER_C2S:
				return PLAYER_SHIELDPLAYER_S2C;
			case PLAYER_REMOVESHIELDPLAYER_C2S:
				return PLAYER_REMOVESHIELDPLAYER_S2C;
			case PLAYER_UNLOCKHERO_C2S:
				return PLAYER_UNLOCKHERO_S2C;
			case PLAYER_HEROLEVELUP_C2S:
				return PLAYER_HEROLEVELUP_S2C;
			case PLAYER_HEROSTAGEUP_C2S:
				return PLAYER_HEROSTAGEUP_S2C;
			case PLAYER_HEROSKILLLEVELUP_C2S:
				return PLAYER_HEROSKILLLEVELUP_S2C;
			case PLAYER_RESETSKILL_C2S:
				return PLAYER_RESETSKILL_S2C;
			case PLAYER_CHANGESKILLSLOTLIMIT_C2S:
				return PLAYER_CHANGESKILLSLOTLIMIT_S2C;
			case PLAYER_HEROTALENTLEVELUP_C2S:
				return PLAYER_HEROTALENTLEVELUP_S2C;
			case PLAYER_SWITCHTALENTPAGE_C2S:
				return PLAYER_SWITCHTALENTPAGE_S2C;
			case PLAYER_RESETNAMETALENTPAGE_C2S:
				return PLAYER_RESETNAMETALENTPAGE_S2C;
			case PLAYER_HEROITEMEXCHANGE_C2S:
				return PLAYER_HEROITEMEXCHANGE_S2C;
			case PLAYER_HERORESETTALENT_C2S:
				return PLAYER_HERORESETTALENT_S2C;
			case PLAYER_HEROINTENSIVESKILL_C2S:
				return PLAYER_HEROINTENSIVESKILL_S2C;
			case PLAYER_HOSPITALTREAT_C2S:
				return PLAYER_HOSPITALTREAT_S2C;
			case PLAYER_HOSPITALFASTTREAT_C2S:
				return PLAYER_HOSPITALFASTTREAT_S2C;
			case PLAYER_HOSPITALRETURNSOLDIERS_C2S:
				return PLAYER_HOSPITALRETURNSOLDIERS_S2C;
			case PLAYER_PROMPTFORPLANEUNLOCK_C2S:
				return PLAYER_PROMPTFORPLANEUNLOCK_S2C;
			case PLAYER_NOTICEBOARD_C2S:
				return PLAYER_NOTICEBOARD_S2C;
			case PLAYER_TAKEPROSPERITYREWARD_C2S:
				return PLAYER_TAKEPROSPERITYREWARD_S2C;
			case PLAYER_COMPLETEQUESTIONNAIRE_C2S:
				return PLAYER_COMPLETEQUESTIONNAIRE_S2C;
			case PLAYER_QUESTIONNAIREEXPIRED_C2S:
				return PLAYER_QUESTIONNAIREEXPIRED_S2C;
			case PLAYER_UPDATECLIENTSTAGE_C2S:
				return PLAYER_UPDATECLIENTSTAGE_S2C;
			case PLAYER_COMPLETEMAINTASK_C2S:
				return PLAYER_COMPLETEMAINTASK_S2C;
			case PLAYER_KINGAPPOINT_C2S:
				return PLAYER_KINGAPPOINT_S2C;
			case PLAYER_KINGOPENBUFF_C2S:
				return PLAYER_KINGOPENBUFF_S2C;
			case PLAYER_KINGSENDGIFT_C2S:
				return PLAYER_KINGSENDGIFT_S2C;
			case PLAYER_KINGUSESKILL_C2S:
				return PLAYER_KINGUSESKILL_S2C;
			case PLAYER_FETCHHISTORYKINGS_C2S:
				return PLAYER_FETCHHISTORYKINGS_S2C;
			case PLAYER_FETCHKINGDOMGIFTINFO_C2S:
				return PLAYER_FETCHKINGDOMGIFTINFO_S2C;
			case PLAYER_FETCHKINGDOMOFFICEINFO_C2S:
				return PLAYER_FETCHKINGDOMOFFICEINFO_S2C;
			case PLAYER_MAILUNREADGET_C2S:
				return PLAYER_MAILUNREADGET_S2C;
			case PLAYER_MAILBATCHREAD_C2S:
				return PLAYER_MAILBATCHREAD_S2C;
			case PLAYER_MAILREWARDGET_C2S:
				return PLAYER_MAILREWARDGET_S2C;
			case PLAYER_READBATTLELOG_C2S:
				return PLAYER_READBATTLELOG_S2C;
			case PLAYER_READBATTLERECORDDETAIL_C2S:
				return PLAYER_READBATTLERECORDDETAIL_S2C;
			case PLAYER_MAILDEL_C2S:
				return PLAYER_MAILDEL_S2C;
			case PLAYER_MAILBATCHDEL_C2S:
				return PLAYER_MAILBATCHDEL_S2C;
			case PLAYER_SENDPERSONALMAIL_C2S:
				return PLAYER_SENDPERSONALMAIL_S2C;
			case PLAYER_SENDCLANMAIL_C2S:
				return PLAYER_SENDCLANMAIL_S2C;
			case PLAYER_SENDZONEMAIL_C2S:
				return PLAYER_SENDZONEMAIL_S2C;
			case PLAYER_SHIELDPERSONMAIL_C2S:
				return PLAYER_SHIELDPERSONMAIL_S2C;
			case PLAYER_UNSHIELDPERSONMAIL_C2S:
				return PLAYER_UNSHIELDPERSONMAIL_S2C;
			case PLAYER_READANDGETREWARDALLMAIL_C2S:
				return PLAYER_READANDGETREWARDALLMAIL_S2C;
			case PLAYER_MAILSTORE_C2S:
				return PLAYER_MAILSTORE_S2C;
			case PLAYER_MAILBATCHGETCONTENT_C2S:
				return PLAYER_MAILBATCHGETCONTENT_S2C;
			case PLAYER_TAKEMILESTONEREWARDC2S_C2S:
				return PLAYER_TAKEMILESTONEREWARDC2S_S2C;
			case PLAYER_GETMILESTONEHISTORY_C2S:
				return PLAYER_GETMILESTONEHISTORY_S2C;
			case PLAYER_GETMILESTONERANK_C2S:
				return PLAYER_GETMILESTONERANK_S2C;
			case PLAYER_GETZONESUNDERSEASON_C2S:
				return PLAYER_GETZONESUNDERSEASON_S2C;
			case PLAYER_GETZONESSTATUS_C2S:
				return PLAYER_GETZONESSTATUS_S2C;
			case PLAYER_PRODUCTNEWINNERBUILD_C2S:
				return PLAYER_PRODUCTNEWINNERBUILD_S2C;
			case PLAYER_SALEINNERBUILD_C2S:
				return PLAYER_SALEINNERBUILD_S2C;
			case PLAYER_PRODUCEINNERBUILD_C2S:
				return PLAYER_PRODUCEINNERBUILD_S2C;
			case PLAYER_PRODUCESECRETWEAPON_C2S:
				return PLAYER_PRODUCESECRETWEAPON_S2C;
			case PLAYER_RECVINNERMINERES_C2S:
				return PLAYER_RECVINNERMINERES_S2C;
			case PLAYER_STARTCRISISBATTLE_C2S:
				return PLAYER_STARTCRISISBATTLE_S2C;
			case PLAYER_ENDCRISISBATTLE_C2S:
				return PLAYER_ENDCRISISBATTLE_S2C;
			case PLAYER_SEARCHNEWBASES_C2S:
				return PLAYER_SEARCHNEWBASES_S2C;
			case PLAYER_FLUSHNEWBASES_C2S:
				return PLAYER_FLUSHNEWBASES_S2C;
			case PLAYER_MIGRATENEWBASE_C2S:
				return PLAYER_MIGRATENEWBASE_S2C;
			case PLAYER_UPGRADEBUILD_C2S:
				return PLAYER_UPGRADEBUILD_S2C;
			case PLAYER_ISSUECOMMAND_C2S:
				return PLAYER_ISSUECOMMAND_S2C;
			case PLAYER_MOVEINNERBUILD_C2S:
				return PLAYER_MOVEINNERBUILD_S2C;
			case PLAYER_UPGRADEINNERBUILD_C2S:
				return PLAYER_UPGRADEINNERBUILD_S2C;
            case PLAYER_UNLOCKNEWAREA_C2S:
                return PLAYER_UNLOCKNEWAREA_S2C;
			case PLAYER_PAYMENTOVER_C2S:
				return PLAYER_PAYMENTOVER_S2C;
			case PLAYER_APPLYGOODSORDER_C2S:
				return PLAYER_APPLYGOODSORDER_S2C;
			case PLAYER_FAKERECHARGE_C2S:
				return PLAYER_FAKERECHARGE_S2C;
			case PLAYER_JUDGERECHARGEPACKAGE_C2S:
				return PLAYER_JUDGERECHARGEPACKAGE_S2C;
			case PLAYER_TRIGGERBUNDLESHOW_C2S:
				return PLAYER_TRIGGERBUNDLESHOW_S2C;
			case PLAYER_POSITIONMARK_C2S:
				return PLAYER_POSITIONMARK_S2C;
			case PLAYER_FETCHCLANPOSITIONMARK_C2S:
				return PLAYER_FETCHCLANPOSITIONMARK_S2C;
			case PLAYER_SPEEDQUEUETASK_C2S:
				return PLAYER_SPEEDQUEUETASK_S2C;
			case PLAYER_FINISHQUEUETASK_C2S:
				return PLAYER_FINISHQUEUETASK_S2C;
			case PLAYER_CANCELQUEUETASK_C2S:
				return PLAYER_CANCELQUEUETASK_S2C;
			case PLAYER_QUEUEFREESPEED_C2S:
				return PLAYER_QUEUEFREESPEED_S2C;
			case PLAYER_CANCELRALLY_C2S:
				return PLAYER_CANCELRALLY_S2C;
			case PLAYER_QUERYRALLYLIST_C2S:
				return PLAYER_QUERYRALLYLIST_S2C;
			case PLAYER_QUERYONERALLY_C2S:
				return PLAYER_QUERYONERALLY_S2C;
			case PLAYER_REPATRIATEONEARMYINRALLY_C2S:
				return PLAYER_REPATRIATEONEARMYINRALLY_S2C;
			case PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_C2S:
				return PLAYER_SETRALLYRECOMMENDSOLDIERTYPE_S2C;
			case PLAYER_FETCHWARNINGLIST_C2S:
				return PLAYER_FETCHWARNINGLIST_S2C;
			case PLAYER_SETWARNINGITEMIGNORETAG_C2S:
				return PLAYER_SETWARNINGITEMIGNORETAG_S2C;
			case PLAYER_IGNOREWARNINGALL_C2S:
				return PLAYER_IGNOREWARNINGALL_S2C;
			case PLAYER_FETCHCITYINNERARMY_C2S:
				return PLAYER_FETCHCITYINNERARMY_S2C;
			case PLAYER_REPATRIATEONEARMYINASSIST_C2S:
				return PLAYER_REPATRIATEONEARMYINASSIST_S2C;
			case PLAYER_FETCHCITYASSISTHISTORY_C2S:
				return PLAYER_FETCHCITYASSISTHISTORY_S2C;
			case PLAYER_SENDRALLYINVITEMSG_C2S:
				return PLAYER_SENDRALLYINVITEMSG_S2C;
			case PLAYER_CLICKRALLYINVITE_C2S:
				return PLAYER_CLICKRALLYINVITE_S2C;
			case PLAYER_GETRANKPAGEINFO_C2S:
				return PLAYER_GETRANKPAGEINFO_S2C;
			case PLAYER_GETTOPRANKINFO_C2S:
				return PLAYER_GETTOPRANKINFO_S2C;
			case PLAYER_REPORTMAPVERSION_C2S:
				return PLAYER_REPORTMAPVERSION_S2C;
			case PLAYER_CHECKZONEVIEW_C2S:
				return PLAYER_CHECKZONEVIEW_S2C;
			case PLAYER_UPDATEVIEW_C2S:
				return PLAYER_UPDATEVIEW_S2C;
			case PLAYER_CREATEARMY_C2S:
				return PLAYER_CREATEARMY_S2C;
			case PLAYER_CHANGEARMYACTION_C2S:
				return PLAYER_CHANGEARMYACTION_S2C;
			case PLAYER_FORCEDDEFEATARMY_C2S:
				return PLAYER_FORCEDDEFEATARMY_S2C;
			case PLAYER_SEARCHWALKPATH_C2S:
				return PLAYER_SEARCHWALKPATH_S2C;
			case PLAYER_CHECKCANATTACK_C2S:
				return PLAYER_CHECKCANATTACK_S2C;
			case PLAYER_SEARCHRESOURCE_C2S:
				return PLAYER_SEARCHRESOURCE_S2C;
			case PLAYER_ENTEREXPEDITION_C2S:
				return PLAYER_ENTEREXPEDITION_S2C;
			case PLAYER_LEAVEDUNGEON_C2S:
				return PLAYER_LEAVEDUNGEON_S2C;
			case PLAYER_REENTERDUNGEON_C2S:
				return PLAYER_REENTERDUNGEON_S2C;
			case PLAYER_CHANGETOBIGSCENE_C2S:
				return PLAYER_CHANGETOBIGSCENE_S2C;
			case PLAYER_USEDUNGEONSKILL_C2S:
				return PLAYER_USEDUNGEONSKILL_S2C;
			case PLAYER_CLOSECLANRECOMMEND_C2S:
				return PLAYER_CLOSECLANRECOMMEND_S2C;
			case PLAYER_CONSTRUCTCLANRESBUILDING_C2S:
				return PLAYER_CONSTRUCTCLANRESBUILDING_S2C;
			case PLAYER_FETCHCLANRESBUILDINGINFO_C2S:
				return PLAYER_FETCHCLANRESBUILDINGINFO_S2C;
			case PLAYER_SKYNETDOINGTASK_C2S:
				return PLAYER_SKYNETDOINGTASK_S2C;
			case PLAYER_SKYNETTAKETASKREWARD_C2S:
				return PLAYER_SKYNETTAKETASKREWARD_S2C;
			case PLAYER_SKYNETSTOREINFO_C2S:
				return PLAYER_SKYNETSTOREINFO_S2C;
			case PLAYER_SKYNETCHARGE_C2S:
				return PLAYER_SKYNETCHARGE_S2C;
			case PLAYER_SKYNETBUYSTORE_C2S:
				return PLAYER_SKYNETBUYSTORE_S2C;
			case PLAYER_SKYNETFINDMONSTER_C2S:
				return PLAYER_SKYNETFINDMONSTER_S2C;
			case PLAYER_SOLDIERTRAIN_C2S:
				return PLAYER_SOLDIERTRAIN_S2C;
			case PLAYER_SOLDIERFASTTRAIN_C2S:
				return PLAYER_SOLDIERFASTTRAIN_S2C;
			case PLAYER_SOLDIERGATHER_C2S:
				return PLAYER_SOLDIERGATHER_S2C;
			case PLAYER_SOLDIERRUSHTRAIN_C2S:
				return PLAYER_SOLDIERRUSHTRAIN_S2C;
			case PLAYER_SOLDIERDISMISS_C2S:
				return PLAYER_SOLDIERDISMISS_S2C;
			case PLAYER_SOLDIERLEVELUP_C2S:
				return PLAYER_SOLDIERLEVELUP_S2C;
			case PLAYER_SOLDIERFASTLEVELUP_C2S:
				return PLAYER_SOLDIERFASTLEVELUP_S2C;
			case PLAYER_FETCHCOMMISSARIATSTOREINFO_C2S:
				return PLAYER_FETCHCOMMISSARIATSTOREINFO_S2C;
			case PLAYER_BUYSTOREITEM_C2S:
				return PLAYER_BUYSTOREITEM_S2C;
			case PLAYER_BUYANDUSESTOREITEM_C2S:
				return PLAYER_BUYANDUSESTOREITEM_S2C;
			case PLAYER_TASKAWARD_C2S:
				return PLAYER_TASKAWARD_S2C;
			case PLAYER_RESEARCHTECH_C2S:
				return PLAYER_RESEARCHTECH_S2C;
			case PLAYER_IMMEDIATELYRESEARCHTECH_C2S:
				return PLAYER_IMMEDIATELYRESEARCHTECH_S2C;
			case PLAYER_RECEIVETECH_C2S:
				return PLAYER_RECEIVETECH_S2C;
			case PLAYER_GETVIPSTOREINFO_C2S:
				return PLAYER_GETVIPSTOREINFO_S2C;
			case PLAYER_BUYVIPSTOREITEM_C2S:
				return PLAYER_BUYVIPSTOREITEM_S2C;
			case PLAYER_SELECTVIPHEROITEM_C2S:
				return PLAYER_SELECTVIPHEROITEM_S2C;
			default:
				return 0;
		}
	}
}
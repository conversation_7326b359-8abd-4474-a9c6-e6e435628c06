package com.yorha.common.actor;

import com.yorha.proto.SsGateSession.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface GateSessionServices {
    Logger LOGGER = LogManager.getLogger(GateSessionServices.class);

    GateSessionService getGateSessionService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.SENDMSGTOSESSIONCMD:
                getGateSessionService().handleSendMsgToSessionCmd((SendMsgToSessionCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERBOUNDCMD:
                getGateSessionService().handlePlayerBoundCmd((PlayerBoundCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATECLIENTINFOCMD:
                getGateSessionService().handleUpdateClientInfoCmd((UpdateClientInfoCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDMSGTOSESSIONWITHLANGUAGECMD:
                getGateSessionService().handleSendMsgToSessionWithLanguageCmd((SendMsgToSessionWithLanguageCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.KICKOFFSESSIONCMD:
                getGateSessionService().handleKickOffSessionCmd((KickOffSessionCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
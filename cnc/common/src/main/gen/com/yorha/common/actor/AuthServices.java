package com.yorha.common.actor;

import com.yorha.proto.SsAuth.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface AuthServices {
    Logger LOGGER = LogManager.getLogger(AuthServices.class);

    AuthService getAuthService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.AUTHINTLASK:
                getAuthService().handleAuthIntlAsk((AuthIntlAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.AUTHMSDKASK:
                getAuthService().handleAuthMsdkAsk((AuthMsdkAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsClanTech.*;

public interface ClanTechService {

    void handleClanTechFetchInfoAsk(ClanTechFetchInfoAsk ask); // 拉取联盟科技信息

    void handleClanTechDonateCheckAsk(ClanTechDonateCheckAsk ask); // 校验是否可以捐献

    void handleClanTechDonateAsk(ClanTechDonateAsk ask); // 科技捐献

    void handleClanTechResearchAsk(ClanTechResearchAsk ask); // 军团科技研究

    void handleClanTechRecommendAsk(ClanTechRecommendAsk ask); // 军团科技设置推荐位

    void handleClanTechDetailAsk(ClanTechDetailAsk ask); // 军团科技信息查询

}
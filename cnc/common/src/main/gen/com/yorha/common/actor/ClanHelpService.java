package com.yorha.common.actor;

import com.yorha.proto.SsClanHelp.*;

public interface ClanHelpService {

    void handleSyncPlayerQueueTaskCmd(SyncPlayerQueueTaskCmd ask); // 同步玩家某个加速队列到联盟上

    void handleFetchClanHelpsAsk(FetchClanHelpsAsk ask); // 拉取联盟帮助信息

    void handleFinishAllClanHelpsAsk(FinishAllClanHelpsAsk ask); // 一键完成所有联盟帮助

    void handleOnQueueTaskFinishedCmd(OnQueueTaskFinishedCmd ask); // 同步玩家已完成或取消的队列信息

    void handleNeedClanHelpItemIdsNtfCmd(NeedClanHelpItemIdsNtfCmd ask); // 告知联盟需要发送帮助id信息ntf

}
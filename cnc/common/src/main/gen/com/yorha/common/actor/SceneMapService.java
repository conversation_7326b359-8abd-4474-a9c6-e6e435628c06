package com.yorha.common.actor;

import com.yorha.proto.SsSceneMap.*;

public interface SceneMapService {

    void handleExecuteSceneGmAsk(ExecuteSceneGmAsk ask);

    void handleSearchPathAsk(SearchPathAsk ask); // 客户端简单查询路径

    void handleFetchClanCityPointListAsk(FetchClanCityPointListAsk ask); // 拉取地图盟友城池数据

    void handleFetchSingleClanMemberCityPointAsk(FetchSingleClanMemberCityPointAsk ask); // 拉取单个盟友城池位置

    void handleFetchTerritoryMapAsk(FetchTerritoryMapAsk ask); // 拉取地图联盟势力图

}
package com.yorha.common.actor;

import com.yorha.proto.SsPlayerMisc.*;

public interface PlayerMiscService {

    void handleQueryPlayerCardInfoDetailAsk(QueryPlayerCardInfoDetailAsk ask); // 查询玩家名片更多信息

    void handleBanPlayerFixMsAsk(BanPlayerFixMsAsk ask); // 封禁玩家固定时长

    void handleCancelBanPlayerAsk(CancelBanPlayerAsk ask); // 取消封禁玩家

    void handleGetSpyDataAsk(GetSpyDataAsk ask); // 获取侦查数据

    void handleEnergyRollbackCmd(EnergyRollbackCmd ask);

    void handleKillBigSceneMonsterCmd(KillBigSceneMonsterCmd ask);

    void handleAddRewardCmd(AddRewardCmd ask); // 发奖励

    void handleMainCityDefendLoseCmd(MainCityDefendLoseCmd ask); // 大地图主城战败

    void handleOnSettleRoundCmd(OnSettleRoundCmd ask); // 每回合战斗结算，同步player侧

    void handleAddAssistHistoryCmd(AddAssistHistoryCmd ask);

    void handleUpdateScenePowerCmd(UpdateScenePowerCmd ask); // 更新玩家战力

    void handleOnBattleRelationEndCmd(OnBattleRelationEndCmd ask); // 更新玩家在场景上的数据统计

    void handleRecordZoneSnapshotCmd(RecordZoneSnapshotCmd ask); // 记录玩家0点快照

    void handleOnReceiveMailCmd(OnReceiveMailCmd ask);

    void handleViewBuildingsAsk(ViewBuildingsAsk ask);

    void handleGetIdIpPlayerInfoAsk(GetIdIpPlayerInfoAsk ask); // 获取玩家信息

    void handleSyncPlaneStatusWithArmyBackCmd(SyncPlaneStatusWithArmyBackCmd ask); // 部队回城时释放飞机

    void handleOnArmyReturnCmd(OnArmyReturnCmd ask); // 部队回城

    void handleOnCityBattleEndAsk(OnCityBattleEndAsk ask); // 攻城战结束

    void handleEnterBattleCmd(EnterBattleCmd ask); // 进入战斗

    void handleReleasePlaneCmd(ReleasePlaneCmd ask); // 释放飞机

    void handleReturnTransportPlaneCmd(ReturnTransportPlaneCmd ask); // 返航运输机

    void handleSendSurveyMailCmd(SendSurveyMailCmd ask); // 发送调查邮件

    void handleUpdateRedDotCmd(UpdateRedDotCmd ask); // 更新玩家红点

    void handleSoldierNumCmd(SoldierNumCmd ask); // 发送玩家各个士兵类型的数量

    void handleClanBuildingInfoCmd(ClanBuildingInfoCmd ask); // 发送联盟建筑数据

    void handleQueryPlayerKillDetailAsk(QueryPlayerKillDetailAsk ask); // 查询玩家击杀积分的详细信息

    void handleOnCollectEndCmd(OnCollectEndCmd ask); // 采集结束

    void handleBroadcastMileStoneSwitchCmd(BroadcastMileStoneSwitchCmd ask); // 广播里程碑切换

    void handleBroadcastMileStoneResetCmd(BroadcastMileStoneResetCmd ask); // GM广播里程碑重置

    void handleMutePlayerFixMsAsk(MutePlayerFixMsAsk ask); // 禁言玩家固定时长

    void handleCancelMutePlayerAsk(CancelMutePlayerAsk ask); // 取消禁言玩家

    void handleResetPlayerInfoAsk(ResetPlayerInfoAsk ask); // 重置玩家信息

    void handleSyncServerOpenTsMsCmd(SyncServerOpenTsMsCmd ask); // 同步开服时间

    void handleGiveCurrencyCmd(GiveCurrencyCmd ask); // 通知player给资源，通常用于资源返还，使用时需谨慎考虑避免出现多次返还的情况

    void handleSendSysAssistCmd(SendSysAssistCmd ask); // 发起系统援助

    void handleCheckImurAsk(CheckImurAsk ask); // 检查Imur问卷是否已答

    void handleSpyMoveCmd(SpyMoveCmd ask); // 侦察机位移变更

    void handleAddDevBuffFromSceneCmd(AddDevBuffFromSceneCmd ask); // 同步玩家devBuff

    void handleRemoveDevBuffFromSceneCmd(RemoveDevBuffFromSceneCmd ask);

    void handleUpdateAdditionFromSceneCmd(UpdateAdditionFromSceneCmd ask); // 同步玩家加成

    void handleUpdateAdditionFromZoneCmd(UpdateAdditionFromZoneCmd ask); // 王国增益同步

    void handleAddClanGiftForPlayerCmd(AddClanGiftForPlayerCmd ask); // 添加军团礼物

    void handleOnAllBattleEndCmd(OnAllBattleEndCmd ask); // 所有战斗结束

    void handleAddResourceAssistRecordCmd(AddResourceAssistRecordCmd ask); // 增加玩家资源援助统计项

    void handleLogOffAccountAsk(LogOffAccountAsk ask); // 注销账号

    void handleGetLastLoginTimeAsk(GetLastLoginTimeAsk ask); // 获取玩家最近登录时间

    void handleAddResourceAfterPlunderCmd(AddResourceAfterPlunderCmd ask); // 用于异常的资源掠夺获取

    void handleOnCityFallCmd(OnCityFallCmd ask); // 落堡

    void handleOnBaseBeAttackCmd(OnBaseBeAttackCmd ask); // 基地被攻击

    void handleUpdateContactCmd(UpdateContactCmd ask);

    void handleMonsterDeadNtfOwnerCmd(MonsterDeadNtfOwnerCmd ask); // 野怪死亡额外通知召唤者

}
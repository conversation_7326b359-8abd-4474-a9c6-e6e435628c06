package com.yorha.common.actor;

import com.yorha.proto.SsScenePlane.*;

public interface ScenePlaneService {

    void handleCreateSpyPlaneAsk(CreateSpyPlaneAsk ask); // 创建侦察机

    void handleChangeActionSpyPlaneAsk(ChangeActionSpyPlaneAsk ask); // 修改侦察机行为

    void handleCheckMapCreateSpyPlaneAsk(CheckMapCreateSpyPlaneAsk ask); // 大地图侧检测侦查机是否可创建

    void handleCreateLogisticsPlaneAsk(CreateLogisticsPlaneAsk ask); // 创建物流飞机

    void handleCheckLogisticsActionAsk(CheckLogisticsActionAsk ask); // 校验物流飞机行为合法性

    void handleChangeLogisticActionAsk(ChangeLogisticActionAsk ask); // 修改物流飞机行为

}
package com.yorha.common.actor;

import com.yorha.proto.SsPlayer.*;
import com.yorha.proto.SsPlayerChat.*;
import com.yorha.proto.SsPlayerClan.*;
import com.yorha.proto.SsPlayerDungeon.*;
import com.yorha.proto.SsPlayerFriend.*;
import com.yorha.proto.SsPlayerIdip.*;
import com.yorha.proto.SsPlayerKingdom.*;
import com.yorha.proto.SsPlayerMisc.*;
import com.yorha.proto.SsPlayerPayment.*;
import com.yorha.proto.SsPlayerScene.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface PlayerServices {
    Logger LOGGER = LogManager.getLogger(PlayerServices.class);

    PlayerBaseService getPlayerBaseService();
    PlayerChatService getPlayerChatService();
    PlayerClanService getPlayerClanService();
    PlayerDungeonService getPlayerDungeonService();
    PlayerFriendService getPlayerFriendService();
    PlayerIdipService getPlayerIdipService();
    PlayerKingdomService getPlayerKingdomService();
    PlayerMiscService getPlayerMiscService();
    PlayerPaymentService getPlayerPaymentService();
    PlayerSceneService getPlayerSceneService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.LOGINPLAYERCMD:
                getPlayerBaseService().handleLoginPlayerCmd((LoginPlayerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONPLAYERDISCONNECTCMD:
                getPlayerBaseService().handleOnPlayerDisconnectCmd((OnPlayerDisconnectCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RECVCLIENTMSGCMD:
                getPlayerBaseService().handleRecvClientMsgCmd((RecvClientMsgCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.KICKOFFPLAYERCMD:
                getPlayerBaseService().handleKickOffPlayerCmd((KickOffPlayerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.APPLYDATAPATCHCMD:
                getPlayerBaseService().handleApplyDataPatchCmd((ApplyDataPatchCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.PREPAREJOINGROUPCHATASK:
                getPlayerChatService().handlePrepareJoinGroupChatAsk((PrepareJoinGroupChatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLEARPREPAREGROUPCHATCMD:
                getPlayerChatService().handleClearPrepareGroupChatCmd((ClearPrepareGroupChatCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.JOINGROUPCHATCMD:
                getPlayerChatService().handleJoinGroupChatCmd((JoinGroupChatCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.LEAVEGROUPCHATCMD:
                getPlayerChatService().handleLeaveGroupChatCmd((LeaveGroupChatCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.HANDLENEWMESSAGEASK:
                getPlayerChatService().handleHandleNewMessageAsk((HandleNewMessageAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GROUPINFOCHANGENTF:
                getPlayerChatService().handleGroupInfoChangeNtf((GroupInfoChangeNtf) typedMsg.getMsg());
                break;
            case SsMsgTypes.GROUPDISMISSNTF:
                getPlayerChatService().handleGroupDismissNtf((GroupDismissNtf) typedMsg.getMsg());
                break;
            case SsMsgTypes.IGNOREMSGNTF:
                getPlayerChatService().handleIgnoreMsgNtf((IgnoreMsgNtf) typedMsg.getMsg());
                break;
            case SsMsgTypes.TRYCLEARPREPARECHATNTF:
                getPlayerChatService().handleTryClearPrepareChatNtf((TryClearPrepareChatNtf) typedMsg.getMsg());
                break;
            case SsMsgTypes.RECEIVEPRIVATEMSGASK:
                getPlayerChatService().handleReceivePrivateMsgAsk((ReceivePrivateMsgAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GROUPCHATEXPIRECMD:
                getPlayerChatService().handleGroupChatExpireCmd((GroupChatExpireCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANAPPLYRESULTASK:
                getPlayerClanService().handleClanApplyResultAsk((ClanApplyResultAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONNTFCLANKICKOFFRESULTCMD:
                getPlayerClanService().handleOnNtfClanKickOffResultCmd((OnNtfClanKickOffResultCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANADDITIONUPDATECMD:
                getPlayerClanService().handleOnClanAdditionUpdateCmd((OnClanAdditionUpdateCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANDEVBUFFUPDATECMD:
                getPlayerClanService().handleOnClanDevBuffUpdateCmd((OnClanDevBuffUpdateCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONADDCLANSCORECMD:
                getPlayerClanService().handleOnAddClanScoreCmd((OnAddClanScoreCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONADDCLANPOWERRESOURCECMD:
                getPlayerClanService().handleOnAddClanPowerResourceCmd((OnAddClanPowerResourceCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANHELPHAPPENASK:
                getPlayerClanService().handleOnClanHelpHappenAsk((OnClanHelpHappenAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONPLAYERNEEDCLANINFOCHANGECMD:
                getPlayerClanService().handleOnPlayerNeedClanInfoChangeCmd((OnPlayerNeedClanInfoChangeCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANTERRITORYLVCHANGECMD:
                getPlayerClanService().handleOnClanTerritoryLvChangeCmd((OnClanTerritoryLvChangeCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONDUNGEONDESTROYCMD:
                getPlayerDungeonService().handleOnDungeonDestroyCmd((OnDungeonDestroyCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDNEWFRIENDASK:
                getPlayerFriendService().handleAddNewFriendAsk((AddNewFriendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.AGREEADDFRIENDASK:
                getPlayerFriendService().handleAgreeAddFriendAsk((AgreeAddFriendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REFUSEADDFRIENDASK:
                getPlayerFriendService().handleRefuseAddFriendAsk((RefuseAddFriendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DELFRIENDASK:
                getPlayerFriendService().handleDelFriendAsk((DelFriendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.JUDGEBESHIELDASK:
                getPlayerFriendService().handleJudgeBeShieldAsk((JudgeBeShieldAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CONSUMEDIAMONDASK:
                getPlayerIdipService().handleConsumeDiamondAsk((ConsumeDiamondAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CONSUMEITEMSASK:
                getPlayerIdipService().handleConsumeItemsAsk((ConsumeItemsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PULLMIDASCMD:
                getPlayerIdipService().handlePullMidasCmd((PullMidasCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RESOURCEASK:
                getPlayerIdipService().handleResourceAsk((ResourceAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYVIPEXPASK:
                getPlayerIdipService().handleModifyVipExpAsk((ModifyVipExpAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYSOLDIERASK:
                getPlayerIdipService().handleModifySoldierAsk((ModifySoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGDOMOFFICECHANGECMD:
                getPlayerKingdomService().handleKingdomOfficeChangeCmd((KingdomOfficeChangeCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGDOMSETTLEGAINTAXCMD:
                getPlayerKingdomService().handleKingdomSettleGainTaxCmd((KingdomSettleGainTaxCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYPLAYERCARDINFODETAILASK:
                getPlayerMiscService().handleQueryPlayerCardInfoDetailAsk((QueryPlayerCardInfoDetailAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BANPLAYERFIXMSASK:
                getPlayerMiscService().handleBanPlayerFixMsAsk((BanPlayerFixMsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CANCELBANPLAYERASK:
                getPlayerMiscService().handleCancelBanPlayerAsk((CancelBanPlayerAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETSPYDATAASK:
                getPlayerMiscService().handleGetSpyDataAsk((GetSpyDataAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ENERGYROLLBACKCMD:
                getPlayerMiscService().handleEnergyRollbackCmd((EnergyRollbackCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.KILLBIGSCENEMONSTERCMD:
                getPlayerMiscService().handleKillBigSceneMonsterCmd((KillBigSceneMonsterCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDREWARDCMD:
                getPlayerMiscService().handleAddRewardCmd((AddRewardCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.MAINCITYDEFENDLOSECMD:
                getPlayerMiscService().handleMainCityDefendLoseCmd((MainCityDefendLoseCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONSETTLEROUNDCMD:
                getPlayerMiscService().handleOnSettleRoundCmd((OnSettleRoundCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDASSISTHISTORYCMD:
                getPlayerMiscService().handleAddAssistHistoryCmd((AddAssistHistoryCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATESCENEPOWERCMD:
                getPlayerMiscService().handleUpdateScenePowerCmd((UpdateScenePowerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONBATTLERELATIONENDCMD:
                getPlayerMiscService().handleOnBattleRelationEndCmd((OnBattleRelationEndCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RECORDZONESNAPSHOTCMD:
                getPlayerMiscService().handleRecordZoneSnapshotCmd((RecordZoneSnapshotCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONRECEIVEMAILCMD:
                getPlayerMiscService().handleOnReceiveMailCmd((OnReceiveMailCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.VIEWBUILDINGSASK:
                getPlayerMiscService().handleViewBuildingsAsk((ViewBuildingsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETIDIPPLAYERINFOASK:
                getPlayerMiscService().handleGetIdIpPlayerInfoAsk((GetIdIpPlayerInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLANESTATUSWITHARMYBACKCMD:
                getPlayerMiscService().handleSyncPlaneStatusWithArmyBackCmd((SyncPlaneStatusWithArmyBackCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONARMYRETURNCMD:
                getPlayerMiscService().handleOnArmyReturnCmd((OnArmyReturnCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCITYBATTLEENDASK:
                getPlayerMiscService().handleOnCityBattleEndAsk((OnCityBattleEndAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ENTERBATTLECMD:
                getPlayerMiscService().handleEnterBattleCmd((EnterBattleCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RELEASEPLANECMD:
                getPlayerMiscService().handleReleasePlaneCmd((ReleasePlaneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.RETURNTRANSPORTPLANECMD:
                getPlayerMiscService().handleReturnTransportPlaneCmd((ReturnTransportPlaneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDSURVEYMAILCMD:
                getPlayerMiscService().handleSendSurveyMailCmd((SendSurveyMailCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEREDDOTCMD:
                getPlayerMiscService().handleUpdateRedDotCmd((UpdateRedDotCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SOLDIERNUMCMD:
                getPlayerMiscService().handleSoldierNumCmd((SoldierNumCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLANBUILDINGINFOCMD:
                getPlayerMiscService().handleClanBuildingInfoCmd((ClanBuildingInfoCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYPLAYERKILLDETAILASK:
                getPlayerMiscService().handleQueryPlayerKillDetailAsk((QueryPlayerKillDetailAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCOLLECTENDCMD:
                getPlayerMiscService().handleOnCollectEndCmd((OnCollectEndCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTMILESTONESWITCHCMD:
                getPlayerMiscService().handleBroadcastMileStoneSwitchCmd((BroadcastMileStoneSwitchCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTMILESTONERESETCMD:
                getPlayerMiscService().handleBroadcastMileStoneResetCmd((BroadcastMileStoneResetCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.MUTEPLAYERFIXMSASK:
                getPlayerMiscService().handleMutePlayerFixMsAsk((MutePlayerFixMsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CANCELMUTEPLAYERASK:
                getPlayerMiscService().handleCancelMutePlayerAsk((CancelMutePlayerAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.RESETPLAYERINFOASK:
                getPlayerMiscService().handleResetPlayerInfoAsk((ResetPlayerInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCSERVEROPENTSMSCMD:
                getPlayerMiscService().handleSyncServerOpenTsMsCmd((SyncServerOpenTsMsCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GIVECURRENCYCMD:
                getPlayerMiscService().handleGiveCurrencyCmd((GiveCurrencyCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDSYSASSISTCMD:
                getPlayerMiscService().handleSendSysAssistCmd((SendSysAssistCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKIMURASK:
                getPlayerMiscService().handleCheckImurAsk((CheckImurAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SPYMOVECMD:
                getPlayerMiscService().handleSpyMoveCmd((SpyMoveCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDDEVBUFFFROMSCENECMD:
                getPlayerMiscService().handleAddDevBuffFromSceneCmd((AddDevBuffFromSceneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.REMOVEDEVBUFFFROMSCENECMD:
                getPlayerMiscService().handleRemoveDevBuffFromSceneCmd((RemoveDevBuffFromSceneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEADDITIONFROMSCENECMD:
                getPlayerMiscService().handleUpdateAdditionFromSceneCmd((UpdateAdditionFromSceneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEADDITIONFROMZONECMD:
                getPlayerMiscService().handleUpdateAdditionFromZoneCmd((UpdateAdditionFromZoneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDCLANGIFTFORPLAYERCMD:
                getPlayerMiscService().handleAddClanGiftForPlayerCmd((AddClanGiftForPlayerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONALLBATTLEENDCMD:
                getPlayerMiscService().handleOnAllBattleEndCmd((OnAllBattleEndCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDRESOURCEASSISTRECORDCMD:
                getPlayerMiscService().handleAddResourceAssistRecordCmd((AddResourceAssistRecordCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.LOGOFFACCOUNTASK:
                getPlayerMiscService().handleLogOffAccountAsk((LogOffAccountAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETLASTLOGINTIMEASK:
                getPlayerMiscService().handleGetLastLoginTimeAsk((GetLastLoginTimeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDRESOURCEAFTERPLUNDERCMD:
                getPlayerMiscService().handleAddResourceAfterPlunderCmd((AddResourceAfterPlunderCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCITYFALLCMD:
                getPlayerMiscService().handleOnCityFallCmd((OnCityFallCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONBASEBEATTACKCMD:
                getPlayerMiscService().handleOnBaseBeAttackCmd((OnBaseBeAttackCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATECONTACTCMD:
                getPlayerMiscService().handleUpdateContactCmd((UpdateContactCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.MONSTERDEADNTFOWNERCMD:
                getPlayerMiscService().handleMonsterDeadNtfOwnerCmd((MonsterDeadNtfOwnerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.MIDASCALLBACKASK:
                getPlayerPaymentService().handleMidasCallbackAsk((MidasCallbackAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONOCCUPYSUCCESSCMD:
                getPlayerSceneService().handleOnOccupySuccessCmd((OnOccupySuccessCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.COMSUMEASSETASK:
                getPlayerSceneService().handleComsumeAssetAsk((ComsumeAssetAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
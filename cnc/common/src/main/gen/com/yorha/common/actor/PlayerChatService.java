package com.yorha.common.actor;

import com.yorha.proto.SsPlayerChat.*;

public interface PlayerChatService {

    void handlePrepareJoinGroupChatAsk(PrepareJoinGroupChatAsk ask); // 加入群聊第一阶段，检查是否能够加入群聊

    void handleClearPrepareGroupChatCmd(ClearPrepareGroupChatCmd ask); // 加入群聊第二阶段，删除prepare状态的群聊

    void handleJoinGroupChatCmd(JoinGroupChatCmd ask); // 加入群聊第二阶段，玩家加入群聊

    void handleLeaveGroupChatCmd(LeaveGroupChatCmd ask); // 玩家离开群聊

    void handleHandleNewMessageAsk(HandleNewMessageAsk ask); // 收到新消息

    void handleGroupInfoChangeNtf(GroupInfoChangeNtf ask); // 群聊信息变更（版本号、群主及群名）

    void handleGroupDismissNtf(GroupDismissNtf ask); // 解散群聊

    void handleIgnoreMsgNtf(IgnoreMsgNtf ask); // 通知玩家有需要忽略的消息

    void handleTryClearPrepareChatNtf(TryClearPrepareChatNtf ask); // 加载时清除prepareItem

    void handleReceivePrivateMsgAsk(ReceivePrivateMsgAsk ask); // 收到私聊消息

    void handleGroupChatExpireCmd(GroupChatExpireCmd ask); // 群聊过期

}
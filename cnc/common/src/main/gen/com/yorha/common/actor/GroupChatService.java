package com.yorha.common.actor;

import com.yorha.proto.SsGroupChat.*;

public interface GroupChatService {

    void handleCreateChatAsk(CreateChatAsk ask); // 创建聊天

    void handleSendChatMessageAsk(SendChatMessageAsk ask); // 发送私聊或群聊信息

    void handleQueryChatMessageAsk(QueryChatMessageAsk ask); // 获取聊天信息列表

    void handleFetchChatMemberAsk(FetchChatMemberAsk ask); // 获取群聊成员

    void handleJudgeMemberAsk(JudgeMemberAsk ask); // 判断是否是群聊成员

    void handleInviteNewMemberAsk(InviteNewMemberAsk ask); // 邀请玩家加入群聊

    void handleRemoveGroupMemberAsk(RemoveGroupMemberAsk ask); // 删除群成员

    void handleTransferGroupOwnerAsk(TransferGroupOwnerAsk ask); // 转移群主

    void handleDismissChatGroupAsk(DismissChatGroupAsk ask); // 解散群聊

    void handleModifyGroupNameAsk(ModifyGroupNameAsk ask); // 修改群名

}
package com.yorha.common.actor;

import com.yorha.proto.SsClanTerritory.*;

public interface ClanTerritoryService {

    void handleGetClanPowerRewardAsk(GetClanPowerRewardAsk ask); // 获取联盟势力等级奖励

    void handleCheckRebuildClanBuildingAsk(CheckRebuildClanBuildingAsk ask); // 检查重建条件是否满足 科技+资源，权限以预先检查

    void handlePlaceClanResBuildAsk(PlaceClanResBuildAsk ask); // 做军团侧的检查，资源扣除；放置军团资源中心

    void handleCheckExtinguishClanBuildingAsk(CheckExtinguishClanBuildingAsk ask); // 检查灭火条件是否满足 资源

    void handleFetchClanBuildingInfoAsk(FetchClanBuildingInfoAsk ask); // 拉取联盟建造建筑信息 任务侧使用 只管大世界不管kvk

    void handleFetchTerritoryPageAllAsk(FetchTerritoryPageAllAsk ask); // 拉取联盟领地页面数据

    void handleSyncTerritoryBuffSnapshotCmd(SyncTerritoryBuffSnapshotCmd ask); // 同步联盟领土模块的buff快照

    void handleSyncTerritoryInfoCmd(SyncTerritoryInfoCmd ask); // 同步联盟领土信息

    void handleSyncClanBuildStatusCmd(SyncClanBuildStatusCmd ask); // 同步军团建设状态

}
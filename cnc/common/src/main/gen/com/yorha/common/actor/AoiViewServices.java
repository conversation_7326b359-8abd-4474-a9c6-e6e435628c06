package com.yorha.common.actor;

import com.yorha.proto.SsAoiView.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface AoiViewServices {
    Logger LOGGER = LogManager.getLogger(AoiViewServices.class);

    AoiViewService getAoiViewService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.UPDATEVIEWASK:
                getAoiViewService().handleUpdateViewAsk((UpdateViewAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLEARVIEWASK:
                getAoiViewService().handleClearViewAsk((ClearViewAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDSCENEOBJCMD:
                getAoiViewService().handleAddSceneObjCmd((AddSceneObjCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGESCENEOBJCMD:
                getAoiViewService().handleChangeSceneObjCmd((ChangeSceneObjCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsPlayerCard.*;

public interface PlayerCardService {

    void handleQueryPlayerCardAsk(QueryPlayerCardAsk ask); // 查询一个玩家简单名片数据

    void handleBatchQueryPlayerCardAsk(BatchQueryPlayerCardAsk ask); // 批量查询玩家简单名片数据

    void handleQueryPlayerCardHeadAsk(QueryPlayerCardHeadAsk ask); // 查询一个玩家头像

    void handleBatchQueryPlayerCardHeadAsk(BatchQueryPlayerCardHeadAsk ask); // 批量查询玩家头像

    void handleUpdatePlayerCardCmd(UpdatePlayerCardCmd ask); // 更新玩家数据

    void handleBatchQueryPlayerZoneIdAsk(BatchQueryPlayerZoneIdAsk ask); // 批量查询玩家所在zoneId

}
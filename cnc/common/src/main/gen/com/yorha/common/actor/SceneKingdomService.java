package com.yorha.common.actor;

import com.yorha.proto.SsSceneKingdom.*;

public interface SceneKingdomService {

    void handleKingAppointAsk(KingAppointAsk ask); // 国王任命

    void handleKingOpenBuffAsk(KingOpenBuffAsk ask); // 开启增益

    void handleKingSendGiftAsk(KingSendGiftAsk ask); // 赠送礼物

    void handleKingCheckCanUseSkillAsk(KingCheckCanUseSkillAsk ask); // 使用技能

    void handleKingUseSkillAsk(KingUseSkillAsk ask); // 使用技能

    void handleFetchHistoryKingAsk(FetchHistoryKingAsk ask); // 拉取历任国王

    void handleFetchKingdomGiftAsk(FetchKingdomGiftAsk ask); // 拉取礼物信息

    void handleFetchKingdomOfficeAsk(FetchKingdomOfficeAsk ask); // 拉取王国职位信息

}
package com.yorha.common.actor;

import com.yorha.proto.SsBattle.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface BattleServices {
    Logger LOGGER = LogManager.getLogger(BattleServices.class);

    BattleService getBattleService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.CHECKBATTLEREQ:
                getBattleService().handleCheckBattleReq((CheckBattleReq) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
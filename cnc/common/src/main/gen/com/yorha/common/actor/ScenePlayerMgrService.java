package com.yorha.common.actor;

import com.yorha.proto.SsScenePlayer.*;

public interface ScenePlayerMgrService {

    void handleFirstEnterBigSceneAsk(FirstEnterBigSceneAsk ask); // 首次进入大世界

    void handleUpdatePlayerViewAsk(UpdatePlayerViewAsk ask); // 视野更新

    void handleClearPlayerViewAsk(ClearPlayerViewAsk ask); // 清理视野  在视野切换时使用

    void handlePlayerAddSoldierAsk(PlayerAddSoldierAsk ask); // 加士兵

    void handleDismissInCitySoldiersAsk(DismissInCitySoldiersAsk ask); // 解散城内的士兵

    void handleReturnTreatOverSoldiersAsk(ReturnTreatOverSoldiersAsk ask);

    void handleGetAllSoldierAsk(GetAllSoldierAsk ask);

    void handleHospitalTreatCheckAsk(HospitalTreatCheckAsk ask); // 医院尝试治疗士兵的检查

    void handleHospitalTreatAsk(HospitalTreatAsk ask); // 医院治疗士兵

    void handleHospitalFastTreatAsk(HospitalFastTreatAsk ask); // 医院一键治疗

    void handleHospitalTreatFinishCmd(HospitalTreatFinishCmd ask); // 医院治疗完毕

    void handleAddDevBuffFromPlayerAsk(AddDevBuffFromPlayerAsk ask);

    void handleRemoveDevBuffFromPlayerAsk(RemoveDevBuffFromPlayerAsk ask);

    void handleUpdateAdditionFromPlayerCmd(UpdateAdditionFromPlayerCmd ask); // 同步玩家加成

    void handleGetSceneAdditionSysAsk(GetSceneAdditionSysAsk ask); // 获取玩家scene加成

    void handleGetSceneDevBuffAsk(GetSceneDevBuffAsk ask); // 获取玩家scene devBuff

    void handleMarkPositionAsk(MarkPositionAsk ask); // 坐标标记

    void handleSetMarkReadedCmd(SetMarkReadedCmd ask); // 已读标记

    void handleSyncPlayerClanIdNameCmd(SyncPlayerClanIdNameCmd ask); // 同步玩家联盟id名字(出入联盟用)

    void handleSyncPlayerNameCmd(SyncPlayerNameCmd ask); // 同步玩家名字

    void handleSyncPlayerSoldierCmd(SyncPlayerSoldierCmd ask); // 同步士兵数据到场景

    void handleSyncPlayerCityBuildLevelCmd(SyncPlayerCityBuildLevelCmd ask); // 同步主堡等级

    void handleSyncPlaneCmd(SyncPlaneCmd ask); // 同步飞机数据

    void handleSyncPlayerHeroCmd(SyncPlayerHeroCmd ask); // 同步玩家英雄

    void handleSyncTechDataCmd(SyncTechDataCmd ask); // 同步科技数据  目前有 侦查等级、解锁资源

    void handleSyncPlayerPicCmd(SyncPlayerPicCmd ask); // 同步玩家头像

    void handleSyncPlayerPicFrameCmd(SyncPlayerPicFrameCmd ask); // 同步玩家头像框

    void handleSyncPlayerWallHeroPlaneCmd(SyncPlayerWallHeroPlaneCmd ask); // 同步驻防英雄战机

    void handleSyncPlayerEraCmd(SyncPlayerEraCmd ask); // 同步时代等级

    void handleMarkNewbieOverAsk(MarkNewbieOverAsk ask); // 新手结束，数据处理

    void handleBroadcastOnlinePlayerCsCmd(BroadcastOnlinePlayerCsCmd ask); // 广播在线玩家cs消息

    void handleBroadcastOnlinePlayerCsWithMultiLanguageCmd(BroadcastOnlinePlayerCsWithMultiLanguageCmd ask); // 广播在线玩家cs消息（多语言版）

    void handleGetMileStoneHistoryAsk(GetMileStoneHistoryAsk ask); // 查询里程碑数据

    void handleGetMileStoneRankInfoAsk(GetMileStoneRankInfoAsk ask); // 查询里程碑排行数据

    void handleFetchMainCityInfoAsk(FetchMainCityInfoAsk ask); // 拉主城坐标

    void handleSetExpressionCmd(SetExpressionCmd ask); // 设置表情

    void handleSetPFlagCmd(SetPFlagCmd ask); // 设置个人旗帜

    void handleGetBattleLoseAsk(GetBattleLoseAsk ask); // 获取系统援助场景当前累计战损

    void handleUseDungeonSkillAsk(UseDungeonSkillAsk ask); // 使用副本技能

    void handleChangeCityDressAsk(ChangeCityDressAsk ask); // 设置个人基地皮肤

    void handleCheckCanAddDevBuffAsk(CheckCanAddDevBuffAsk ask); // 是否可以添加devBuff

    void handleIdIpReturnAllArmyAsk(IdIpReturnAllArmyAsk ask); // IDIP遣返所有行军

    void handleIdIpGlobalPeaceShieldAsk(IdIpGlobalPeaceShieldAsk ask); // IDIP全服保护罩

    void handleSyncPlayerPushNtfInfoAsk(SyncPlayerPushNtfInfoAsk ask); // 同步玩家的推送相关的数据，推送当且仅当玩家不在线的时候产生作用

    void handleQueryPlayerPushNtfAsk(QueryPlayerPushNtfAsk ask); // 获取推送设置

    void handleIdIpModifySoldierAsk(IdIpModifySoldierAsk ask); // IDIP修改士兵数量

    void handleBroadOnlinePlayerSsCmd(BroadOnlinePlayerSsCmd ask); // 在线玩家广播cmd

    void handleSkynetFindMonsterAsk(SkynetFindMonsterAsk ask); // 天网查询野怪

    void handleGetZoneSeasonAsk(GetZoneSeasonAsk ask); // 获取服务器赛季

}
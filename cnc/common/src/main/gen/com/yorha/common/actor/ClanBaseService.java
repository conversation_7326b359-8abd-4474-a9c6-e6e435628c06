package com.yorha.common.actor;

import com.yorha.proto.SsClanBase.*;

public interface ClanBaseService {

    void handleCreateClanEntityAsk(CreateClanEntityAsk ask); // 创建军团

    void handleFetchClanCardAsk(FetchClanCardAsk ask); // 拉取联盟名片 只能对自己的军团用

    void handleFetchClanRankSimpleInfoAsk(FetchClanRankSimpleInfoAsk ask); // 拉取排行榜所需的简要信息

    void handleFetchClanMemberInfoAsk(FetchClanMemberInfoAsk ask); // 拉取军团成员信息

    void handleFetchClanApplyMembersAsk(FetchClanApplyMembersAsk ask); // 拉取申请加入军团的玩家信息

    void handleFetchClanWelcomeLetterAsk(FetchClanWelcomeLetterAsk ask); // 拉取军团欢迎邮件

    void handleGetClanBaseInfoAsk(GetClanBaseInfoAsk ask);

    void handleSendClanMailAndSaveAsk(SendClanMailAndSaveAsk ask); // 通过军团发送军团邮件，并记录在clan上

    void handleExecuteClanGmAsk(ExecuteClanGmAsk ask); // 执行军团gm

    void handlePlayerDissolveClanAsk(PlayerDissolveClanAsk ask); // 解散军团

    void handlePlayerCancelDissolveClanAsk(PlayerCancelDissolveClanAsk ask); // 取消解散军团

    void handleRecordClanSnapshotCmd(RecordClanSnapshotCmd ask); // 执行联盟快照

    void handleBroadcastClanOnlineMemberCsCmd(BroadcastClanOnlineMemberCsCmd ask);

    void handleAddClanResourcesCmd(AddClanResourcesCmd ask); // 增加联盟资源

    void handleInviteOtherToClanAsk(InviteOtherToClanAsk ask); // 邀请他人加入军团，call到军团上主要是为了获取军团的信息

    void handleCheckInviteExistAsk(CheckInviteExistAsk ask); // 检查邀请是否存在

    void handleRefuseClanInviteCmd(RefuseClanInviteCmd ask); // 拒绝军团邀请

    void handleRefreshClanThingsAsk(RefreshClanThingsAsk ask);

    void handlePushNotifyCmd(PushNotifyCmd ask); // 联盟推送

}
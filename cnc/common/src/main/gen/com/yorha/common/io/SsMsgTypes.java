package com.yorha.common.io;

import com.google.protobuf.GeneratedMessageV3;
import com.yorha.proto.*;

import java.util.HashMap;
import java.util.Map;

public class SsMsgTypes {

    public static final int TAKEACTIVITYREWARDASK = 20001;
    public static final int TAKEACTIVITYREWARDANS = 30001;
    public static final int GETALLACTIVITYSTATUSASK = 20002;
    public static final int GETALLACTIVITYSTATUSANS = 30002;
    public static final int INITACTIVITYACTORASK = 20003;
    public static final int INITACTIVITYACTORANS = 30003;
    public static final int JUDGETIMINGASK = 20101;
    public static final int JUDGETIMINGANS = 30101;
    public static final int JUDGEPAYASK = 20102;
    public static final int JUDGEPAYANS = 30102;
    public static final int REPORTPAYCMD = 20103;
    public static final int REPORTEXECUTECMD = 20104;
    public static final int UPDATEVIEWASK = 20201;
    public static final int UPDATEVIEWANS = 30201;
    public static final int CLEARVIEWASK = 20202;
    public static final int CLEARVIEWANS = 30202;
    public static final int ADDSCENEOBJCMD = 20203;
    public static final int CHANGESCENEOBJCMD = 20204;
    public static final int AUTHINTLASK = 20301;
    public static final int AUTHINTLANS = 30301;
    public static final int AUTHMSDKASK = 20302;
    public static final int AUTHMSDKANS = 30302;
    public static final int CHECKBATTLEREQ = 20401;
    public static final int CHECKBATTLERESP = 30401;
    public static final int ADDCLANACTSCORECMD = 20601;
    public static final int FETCHACTIVITYDATAASK = 20602;
    public static final int FETCHACTIVITYDATAANS = 30602;
    public static final int MODIFYCLANPRIVILEGEASK = 20701;
    public static final int MODIFYCLANPRIVILEGEANS = 30701;
    public static final int CHECKMODIFYNAMEQUIETTIMEASK = 20702;
    public static final int CHECKMODIFYNAMEQUIETTIMEANS = 30702;
    public static final int MODIFYCLANNAMEASK = 20703;
    public static final int MODIFYCLANNAMEANS = 30703;
    public static final int MODIFYCLANSIMPLENAMEASK = 20704;
    public static final int MODIFYCLANSIMPLENAMEANS = 30704;
    public static final int CHECKCLANFLAGANDTERRITORYCOLORASK = 20705;
    public static final int CHECKCLANFLAGANDTERRITORYCOLORANS = 30705;
    public static final int MODIFYCLANFLAGANDTERRITORYCOLORASK = 20706;
    public static final int MODIFYCLANFLAGANDTERRITORYCOLORANS = 30706;
    public static final int MODIFYCLANWELCOMELETTERASK = 20707;
    public static final int MODIFYCLANWELCOMELETTERANS = 30707;
    public static final int MODIFYCLANSETTINGMISCASK = 20708;
    public static final int MODIFYCLANSETTINGMISCANS = 30708;
    public static final int CLANMARKPOSITIONCMD = 20709;
    public static final int SYNCREDDOTTOCLANCMD = 20710;
    public static final int FETCHRESOURCESSNAPSHOTASK = 20711;
    public static final int FETCHRESOURCESSNAPSHOTANS = 30711;
    public static final int FETCHCLANWAREHOUSEINFOASK = 20712;
    public static final int FETCHCLANWAREHOUSEINFOANS = 30712;
    public static final int ONADDCLANSCOREFORCLANCMD = 20713;
    public static final int CREATECLANLOGCMD = 20714;
    public static final int FETCHCLANLOGASK = 20715;
    public static final int FETCHCLANLOGANS = 30715;
    public static final int FETCHDISSOLVINGINFOASK = 20716;
    public static final int FETCHDISSOLVINGINFOANS = 30716;
    public static final int IDIPQUERYCLANINFOASK = 20717;
    public static final int IDIPQUERYCLANINFOANS = 30717;
    public static final int IDIPRESETCLANINFOASK = 20718;
    public static final int IDIPRESETCLANINFOANS = 30718;
    public static final int CHECKMODIFYSNAMEQUIETTIMEASK = 20719;
    public static final int CHECKMODIFYSNAMEQUIETTIMEANS = 30719;
    public static final int FETCHCLANPOSITIONMARKASK = 20720;
    public static final int FETCHCLANPOSITIONMARKANS = 30720;
    public static final int FETCHCLANNUMTIPASK = 20721;
    public static final int FETCHCLANNUMTIPANS = 30721;
    public static final int CREATECLANENTITYASK = 20801;
    public static final int CREATECLANENTITYANS = 30801;
    public static final int FETCHCLANCARDASK = 20802;
    public static final int FETCHCLANCARDANS = 30802;
    public static final int FETCHCLANRANKSIMPLEINFOASK = 20803;
    public static final int FETCHCLANRANKSIMPLEINFOANS = 30803;
    public static final int FETCHCLANMEMBERINFOASK = 20804;
    public static final int FETCHCLANMEMBERINFOANS = 30804;
    public static final int FETCHCLANAPPLYMEMBERSASK = 20805;
    public static final int FETCHCLANAPPLYMEMBERSANS = 30805;
    public static final int FETCHCLANWELCOMELETTERASK = 20806;
    public static final int FETCHCLANWELCOMELETTERANS = 30806;
    public static final int GETCLANBASEINFOASK = 20807;
    public static final int GETCLANBASEINFOANS = 30807;
    public static final int SENDCLANMAILANDSAVEASK = 20808;
    public static final int SENDCLANMAILANDSAVEANS = 30808;
    public static final int EXECUTECLANGMASK = 20809;
    public static final int EXECUTECLANGMANS = 30809;
    public static final int PLAYERDISSOLVECLANASK = 20810;
    public static final int PLAYERDISSOLVECLANANS = 30810;
    public static final int PLAYERCANCELDISSOLVECLANASK = 20811;
    public static final int PLAYERCANCELDISSOLVECLANANS = 30811;
    public static final int RECORDCLANSNAPSHOTCMD = 20812;
    public static final int BROADCASTCLANONLINEMEMBERCSCMD = 20813;
    public static final int ADDCLANRESOURCESCMD = 20814;
    public static final int INVITEOTHERTOCLANASK = 20815;
    public static final int INVITEOTHERTOCLANANS = 30815;
    public static final int CHECKINVITEEXISTASK = 20816;
    public static final int CHECKINVITEEXISTANS = 30816;
    public static final int REFUSECLANINVITECMD = 20817;
    public static final int REFRESHCLANTHINGSASK = 20818;
    public static final int REFRESHCLANTHINGSANS = 30818;
    public static final int PUSHNOTIFYCMD = 20819;
    public static final int FETCHCLANCHATASK = 20901;
    public static final int FETCHCLANCHATANS = 30901;
    public static final int SENDCLANCHATASK = 20902;
    public static final int SENDCLANCHATANS = 30902;
    public static final int ADDGIFTPOINTANDKEYASK = 21001;
    public static final int ADDGIFTPOINTANDKEYANS = 31001;
    public static final int ADDCLANGIFTCMD = 21002;
    public static final int SYNCPLAYERQUEUETASKCMD = 21101;
    public static final int FETCHCLANHELPSASK = 21102;
    public static final int FETCHCLANHELPSANS = 31102;
    public static final int FINISHALLCLANHELPSASK = 21103;
    public static final int FINISHALLCLANHELPSANS = 31103;
    public static final int ONQUEUETASKFINISHEDCMD = 21104;
    public static final int NEEDCLANHELPITEMIDSNTFCMD = 21105;
    public static final int PLAYERAPPLYCLANASK = 21201;
    public static final int PLAYERAPPLYCLANANS = 31201;
    public static final int PROCESSCLANAPPLYASK = 21202;
    public static final int PROCESSCLANAPPLYANS = 31202;
    public static final int PLAYERQUITCLANASK = 21203;
    public static final int PLAYERQUITCLANANS = 31203;
    public static final int KICKOUTCLANMEMBERASK = 21204;
    public static final int KICKOUTCLANMEMBERANS = 31204;
    public static final int CHECKINCLANASK = 21205;
    public static final int CHECKINCLANANS = 31205;
    public static final int CHECKOUTCLANASK = 21206;
    public static final int CHECKOUTCLANANS = 31206;
    public static final int GRANTCLANSTAFFASK = 21207;
    public static final int GRANTCLANSTAFFANS = 31207;
    public static final int FETCHCLANPOWERREWARDASK = 21208;
    public static final int FETCHCLANPOWERREWARDANS = 31208;
    public static final int UPDATECLANMEMBERINFOCMD = 21209;
    public static final int PLAYERCANCELAPPLYCMD = 21210;
    public static final int APPLYOWNERDISSOLVINGASK = 21211;
    public static final int APPLYOWNERDISSOLVINGANS = 31211;
    public static final int CANCELAPPLYOWNERDISSOLVINGASK = 21212;
    public static final int CANCELAPPLYOWNERDISSOLVINGANS = 31212;
    public static final int SYNCPLAYERTOPNHEROCMD = 21213;
    public static final int GETCLANRANKPAGEINFOASK = 21301;
    public static final int GETCLANRANKPAGEINFOANS = 31301;
    public static final int GETTOPCLANRANKINFOASK = 21302;
    public static final int GETTOPCLANRANKINFOANS = 31302;
    public static final int UPDATECLANRANKINGCMD = 21303;
    public static final int FETCHCLANSTOREASK = 21401;
    public static final int FETCHCLANSTOREANS = 31401;
    public static final int FETCHCLANSTORERECORDASK = 21402;
    public static final int FETCHCLANSTORERECORDANS = 31402;
    public static final int OPERATECLANSTOREITEMASK = 21403;
    public static final int OPERATECLANSTOREITEMANS = 31403;
    public static final int CLANTECHFETCHINFOASK = 21501;
    public static final int CLANTECHFETCHINFOANS = 31501;
    public static final int CLANTECHDONATECHECKASK = 21502;
    public static final int CLANTECHDONATECHECKANS = 31502;
    public static final int CLANTECHDONATEASK = 21503;
    public static final int CLANTECHDONATEANS = 31503;
    public static final int CLANTECHRESEARCHASK = 21504;
    public static final int CLANTECHRESEARCHANS = 31504;
    public static final int CLANTECHRECOMMENDASK = 21505;
    public static final int CLANTECHRECOMMENDANS = 31505;
    public static final int CLANTECHDETAILASK = 21506;
    public static final int CLANTECHDETAILANS = 31506;
    public static final int GETCLANPOWERREWARDASK = 21601;
    public static final int GETCLANPOWERREWARDANS = 31601;
    public static final int CHECKREBUILDCLANBUILDINGASK = 21602;
    public static final int CHECKREBUILDCLANBUILDINGANS = 31602;
    public static final int PLACECLANRESBUILDASK = 21603;
    public static final int PLACECLANRESBUILDANS = 31603;
    public static final int CHECKEXTINGUISHCLANBUILDINGASK = 21604;
    public static final int CHECKEXTINGUISHCLANBUILDINGANS = 31604;
    public static final int FETCHCLANBUILDINGINFOASK = 21605;
    public static final int FETCHCLANBUILDINGINFOANS = 31605;
    public static final int FETCHTERRITORYPAGEALLASK = 21606;
    public static final int FETCHTERRITORYPAGEALLANS = 31606;
    public static final int SYNCTERRITORYBUFFSNAPSHOTCMD = 21607;
    public static final int SYNCTERRITORYINFOCMD = 21608;
    public static final int SYNCCLANBUILDSTATUSCMD = 21609;
    public static final int QUERYCLANNAMEASK = 20501;
    public static final int QUERYCLANNAMEANS = 30501;
    public static final int QUERYCLANSIMPLEASK = 20502;
    public static final int QUERYCLANSIMPLEANS = 30502;
    public static final int QUERYCLANCARDASK = 20503;
    public static final int QUERYCLANCARDANS = 30503;
    public static final int BATCHQUERYCLANNAMEASK = 20504;
    public static final int BATCHQUERYCLANNAMEANS = 30504;
    public static final int BATCHQUERYCLANSIMPLEASK = 20505;
    public static final int BATCHQUERYCLANSIMPLEANS = 30505;
    public static final int BATCHQUERYCLANCARDASK = 20506;
    public static final int BATCHQUERYCLANCARDANS = 30506;
    public static final int UPDATECLANCARDCMD = 20507;
    public static final int SHUTDOWNCMD = 21801;
    public static final int BROADCASTALLCMD = 21802;
    public static final int BROADCASTONLINEPLAYERSSCMD = 21803;
    public static final int SENDMSGTOSESSIONCMD = 21701;
    public static final int PLAYERBOUNDCMD = 21702;
    public static final int UPDATECLIENTINFOCMD = 21703;
    public static final int SENDMSGTOSESSIONWITHLANGUAGECMD = 21704;
    public static final int KICKOFFSESSIONCMD = 21705;
    public static final int CREATECHATASK = 21901;
    public static final int CREATECHATANS = 31901;
    public static final int SENDCHATMESSAGEASK = 21902;
    public static final int SENDCHATMESSAGEANS = 31902;
    public static final int QUERYCHATMESSAGEASK = 21903;
    public static final int QUERYCHATMESSAGEANS = 31903;
    public static final int FETCHCHATMEMBERASK = 21904;
    public static final int FETCHCHATMEMBERANS = 31904;
    public static final int JUDGEMEMBERASK = 21905;
    public static final int JUDGEMEMBERANS = 31905;
    public static final int INVITENEWMEMBERASK = 21906;
    public static final int INVITENEWMEMBERANS = 31906;
    public static final int REMOVEGROUPMEMBERASK = 21907;
    public static final int REMOVEGROUPMEMBERANS = 31907;
    public static final int TRANSFERGROUPOWNERASK = 21908;
    public static final int TRANSFERGROUPOWNERANS = 31908;
    public static final int DISMISSCHATGROUPASK = 21909;
    public static final int DISMISSCHATGROUPANS = 31909;
    public static final int MODIFYGROUPNAMEASK = 21910;
    public static final int MODIFYGROUPNAMEANS = 31910;
    public static final int MIDASQUERYASK = 22001;
    public static final int MIDASQUERYANS = 32001;
    public static final int MIDASCONSUMEASK = 22002;
    public static final int MIDASCONSUMEANS = 32002;
    public static final int MIDASROLLBACKCONSUMEASK = 22003;
    public static final int MIDASROLLBACKCONSUMEANS = 32003;
    public static final int MIDASPRESENTASK = 22004;
    public static final int MIDASPRESENTANS = 32004;
    public static final int MIDASSWITCHSTRESSTESTINGMODECMD = 22005;
    public static final int MONITORDAILYPLAYERNUMCMD = 22101;
    public static final int OCCUPYBATCHNAMECMD = 22201;
    public static final int RELEASENAMECMD = 22202;
    public static final int SEARCHCLANNAMEASK = 22203;
    public static final int SEARCHCLANNAMEANS = 32203;
    public static final int SEARCHPLAYERNAMEASK = 22204;
    public static final int SEARCHPLAYERNAMEANS = 32204;
    public static final int SEARCHNAMEMATCHEDASK = 22205;
    public static final int SEARCHNAMEMATCHEDANS = 32205;
    public static final int NODETASKCMD = 22301;
    public static final int SEARCHPATHASYNCASK = 22401;
    public static final int SEARCHPATHASYNCANS = 32401;
    public static final int LOGINPLAYERCMD = 22501;
    public static final int ONPLAYERDISCONNECTCMD = 22502;
    public static final int RECVCLIENTMSGCMD = 22503;
    public static final int KICKOFFPLAYERCMD = 22504;
    public static final int APPLYDATAPATCHCMD = 22505;
    public static final int PREPAREJOINGROUPCHATASK = 22601;
    public static final int PREPAREJOINGROUPCHATANS = 32601;
    public static final int CLEARPREPAREGROUPCHATCMD = 22602;
    public static final int JOINGROUPCHATCMD = 22603;
    public static final int LEAVEGROUPCHATCMD = 22604;
    public static final int HANDLENEWMESSAGEASK = 22605;
    public static final int HANDLENEWMESSAGEANS = 32605;
    public static final int GROUPINFOCHANGENTF = 22606;
    public static final int GROUPDISMISSNTF = 22607;
    public static final int IGNOREMSGNTF = 22608;
    public static final int TRYCLEARPREPARECHATNTF = 22609;
    public static final int RECEIVEPRIVATEMSGASK = 22610;
    public static final int RECEIVEPRIVATEMSGANS = 32610;
    public static final int GROUPCHATEXPIRECMD = 22611;
    public static final int CLANAPPLYRESULTASK = 22701;
    public static final int CLANAPPLYRESULTANS = 32701;
    public static final int ONNTFCLANKICKOFFRESULTCMD = 22702;
    public static final int ONCLANADDITIONUPDATECMD = 22703;
    public static final int ONCLANDEVBUFFUPDATECMD = 22704;
    public static final int ONADDCLANSCORECMD = 22705;
    public static final int ONADDCLANPOWERRESOURCECMD = 22706;
    public static final int ONCLANHELPHAPPENASK = 22707;
    public static final int ONCLANHELPHAPPENANS = 32707;
    public static final int ONPLAYERNEEDCLANINFOCHANGECMD = 22708;
    public static final int ONCLANTERRITORYLVCHANGECMD = 22709;
    public static final int ONDUNGEONDESTROYCMD = 22801;
    public static final int ADDNEWFRIENDASK = 22901;
    public static final int ADDNEWFRIENDANS = 32901;
    public static final int AGREEADDFRIENDASK = 22902;
    public static final int AGREEADDFRIENDANS = 32902;
    public static final int REFUSEADDFRIENDASK = 22903;
    public static final int REFUSEADDFRIENDANS = 32903;
    public static final int DELFRIENDASK = 22904;
    public static final int DELFRIENDANS = 32904;
    public static final int JUDGEBESHIELDASK = 22905;
    public static final int JUDGEBESHIELDANS = 32905;
    public static final int CONSUMEDIAMONDASK = 23001;
    public static final int CONSUMEDIAMONDANS = 33001;
    public static final int CONSUMEITEMSASK = 23002;
    public static final int CONSUMEITEMSANS = 33002;
    public static final int PULLMIDASCMD = 23003;
    public static final int RESOURCEASK = 23004;
    public static final int RESOURCEANS = 33004;
    public static final int MODIFYVIPEXPASK = 23005;
    public static final int MODIFYVIPEXPANS = 33005;
    public static final int MODIFYSOLDIERASK = 23006;
    public static final int MODIFYSOLDIERANS = 33006;
    public static final int KINGDOMOFFICECHANGECMD = 23101;
    public static final int KINGDOMSETTLEGAINTAXCMD = 23102;
    public static final int QUERYPLAYERCARDINFODETAILASK = 23201;
    public static final int QUERYPLAYERCARDINFODETAILANS = 33201;
    public static final int BANPLAYERFIXMSASK = 23202;
    public static final int BANPLAYERFIXMSANS = 33202;
    public static final int CANCELBANPLAYERASK = 23203;
    public static final int CANCELBANPLAYERANS = 33203;
    public static final int GETSPYDATAASK = 23204;
    public static final int GETSPYDATAANS = 33204;
    public static final int ENERGYROLLBACKCMD = 23205;
    public static final int KILLBIGSCENEMONSTERCMD = 23206;
    public static final int ADDREWARDCMD = 23207;
    public static final int MAINCITYDEFENDLOSECMD = 23209;
    public static final int ONSETTLEROUNDCMD = 23210;
    public static final int ADDASSISTHISTORYCMD = 23211;
    public static final int UPDATESCENEPOWERCMD = 23212;
    public static final int ONBATTLERELATIONENDCMD = 23213;
    public static final int RECORDZONESNAPSHOTCMD = 23214;
    public static final int ONRECEIVEMAILCMD = 23215;
    public static final int VIEWBUILDINGSASK = 23216;
    public static final int VIEWBUILDINGSANS = 33216;
    public static final int GETIDIPPLAYERINFOASK = 23217;
    public static final int GETIDIPPLAYERINFOANS = 33217;
    public static final int SYNCPLANESTATUSWITHARMYBACKCMD = 23218;
    public static final int ONARMYRETURNCMD = 23219;
    public static final int ONCITYBATTLEENDASK = 23220;
    public static final int ONCITYBATTLEENDANS = 33220;
    public static final int ENTERBATTLECMD = 23222;
    public static final int RELEASEPLANECMD = 23223;
    public static final int RETURNTRANSPORTPLANECMD = 23224;
    public static final int SENDSURVEYMAILCMD = 23226;
    public static final int UPDATEREDDOTCMD = 23227;
    public static final int SOLDIERNUMCMD = 23228;
    public static final int CLANBUILDINGINFOCMD = 23229;
    public static final int QUERYPLAYERKILLDETAILASK = 23230;
    public static final int QUERYPLAYERKILLDETAILANS = 33230;
    public static final int ONCOLLECTENDCMD = 23231;
    public static final int BROADCASTMILESTONESWITCHCMD = 23232;
    public static final int BROADCASTMILESTONERESETCMD = 23233;
    public static final int MUTEPLAYERFIXMSASK = 23236;
    public static final int MUTEPLAYERFIXMSANS = 33236;
    public static final int CANCELMUTEPLAYERASK = 23237;
    public static final int CANCELMUTEPLAYERANS = 33237;
    public static final int RESETPLAYERINFOASK = 23238;
    public static final int RESETPLAYERINFOANS = 33238;
    public static final int SYNCSERVEROPENTSMSCMD = 23239;
    public static final int GIVECURRENCYCMD = 23240;
    public static final int SENDSYSASSISTCMD = 23241;
    public static final int CHECKIMURASK = 23242;
    public static final int CHECKIMURANS = 33242;
    public static final int SPYMOVECMD = 23243;
    public static final int ADDDEVBUFFFROMSCENECMD = 23244;
    public static final int REMOVEDEVBUFFFROMSCENECMD = 23245;
    public static final int UPDATEADDITIONFROMSCENECMD = 23246;
    public static final int UPDATEADDITIONFROMZONECMD = 23247;
    public static final int ADDCLANGIFTFORPLAYERCMD = 23248;
    public static final int ONALLBATTLEENDCMD = 23249;
    public static final int ADDRESOURCEASSISTRECORDCMD = 23250;
    public static final int LOGOFFACCOUNTASK = 23251;
    public static final int LOGOFFACCOUNTANS = 33251;
    public static final int GETLASTLOGINTIMEASK = 23252;
    public static final int GETLASTLOGINTIMEANS = 33252;
    public static final int ADDRESOURCEAFTERPLUNDERCMD = 23253;
    public static final int ONCITYFALLCMD = 23254;
    public static final int ONBASEBEATTACKCMD = 23255;
    public static final int UPDATECONTACTCMD = 23256;
    public static final int MONSTERDEADNTFOWNERCMD = 23257;
    public static final int MIDASCALLBACKASK = 23301;
    public static final int MIDASCALLBACKANS = 33301;
    public static final int ONOCCUPYSUCCESSCMD = 23401;
    public static final int COMSUMEASSETASK = 23402;
    public static final int COMSUMEASSETANS = 33402;
    public static final int QUERYPLAYERCARDASK = 23501;
    public static final int QUERYPLAYERCARDANS = 33501;
    public static final int BATCHQUERYPLAYERCARDASK = 23502;
    public static final int BATCHQUERYPLAYERCARDANS = 33502;
    public static final int QUERYPLAYERCARDHEADASK = 23503;
    public static final int QUERYPLAYERCARDHEADANS = 33503;
    public static final int BATCHQUERYPLAYERCARDHEADASK = 23504;
    public static final int BATCHQUERYPLAYERCARDHEADANS = 33504;
    public static final int UPDATEPLAYERCARDCMD = 23505;
    public static final int BATCHQUERYPLAYERZONEIDASK = 23506;
    public static final int BATCHQUERYPLAYERZONEIDANS = 33506;
    public static final int PUSHSINGLENOTIFICATIONCMD = 23601;
    public static final int PUSHMULTIPLENOTIFICATIONCMD = 23602;
    public static final int PUSHTOPICNOTIFICATIONCMD = 23603;
    public static final int OPENRANKASK = 23701;
    public static final int OPENRANKANS = 33701;
    public static final int GETTOPRANKINFOASK = 23702;
    public static final int GETTOPRANKINFOANS = 33702;
    public static final int GETRANKPAGEINFOASK = 23703;
    public static final int GETRANKPAGEINFOANS = 33703;
    public static final int RANKINFOBYRANKSASK = 23704;
    public static final int RANKINFOBYRANKSANS = 33704;
    public static final int RANKINFOBYPLAYERSASK = 23705;
    public static final int RANKINFOBYPLAYERSANS = 33705;
    public static final int GETRANKINFOBYLIMITASK = 23706;
    public static final int GETRANKINFOBYLIMITANS = 33706;
    public static final int UPDATERANKINGCMD = 23707;
    public static final int DELETERANKINGCMD = 23708;
    public static final int UPDATEBATCHRANKINGASK = 23709;
    public static final int UPDATEBATCHRANKINGANS = 33709;
    public static final int CLEARRANKCMD = 23710;
    public static final int GETRANGEASK = 23711;
    public static final int GETRANGEANS = 33711;
    public static final int DELETERANKANDGETTOPASK = 23712;
    public static final int DELETERANKANDGETTOPANS = 33712;
    public static final int DELETEALLRANKABOUTMECMD = 23713;
    public static final int GETALLTARGETRANKASK = 23714;
    public static final int GETALLTARGETRANKANS = 33714;
    public static final int GETRANKINFOBYPLAYERASK = 23715;
    public static final int GETRANKINFOBYPLAYERANS = 33715;
    public static final int ENABLEACTIVITYEFFECTASK = 23801;
    public static final int ENABLEACTIVITYEFFECTANS = 33801;
    public static final int BESTCOMMANDERFETCHASK = 23901;
    public static final int BESTCOMMANDERFETCHANS = 33901;
    public static final int BESTCOMMANDERHISTORYRANKASK = 23902;
    public static final int BESTCOMMANDERHISTORYRANKANS = 33902;
    public static final int BESTCOMMANDERCHOOSEITEMASK = 23903;
    public static final int BESTCOMMANDERCHOOSEITEMANS = 33903;
    public static final int BESTCOMMANDERGETVOLUMEASK = 23904;
    public static final int BESTCOMMANDERGETVOLUMEANS = 33904;
    public static final int GETLOTTERYINFOASK = 23905;
    public static final int GETLOTTERYINFOANS = 33905;
    public static final int REPAIRCITYWALLASK = 24001;
    public static final int REPAIRCITYWALLANS = 34001;
    public static final int OUTFIRECITYWALLASK = 24002;
    public static final int OUTFIRECITYWALLANS = 34002;
    public static final int SETCITYFALLASK = 24003;
    public static final int SETCITYFALLANS = 34003;
    public static final int MOVECITYFIXEDASK = 24004;
    public static final int MOVECITYFIXEDANS = 34004;
    public static final int MOVECITYRANDOMASK = 24005;
    public static final int MOVECITYRANDOMANS = 34005;
    public static final int MOVECITYVERIFYASK = 24006;
    public static final int MOVECITYVERIFYANS = 34006;
    public static final int CREATEARMYCHECKASK = 24007;
    public static final int CREATEARMYCHECKANS = 34007;
    public static final int CREATEPLAYERARMYASK = 24008;
    public static final int CREATEPLAYERARMYANS = 34008;
    public static final int CHANGEARMYACTIONCHECKASK = 24009;
    public static final int CHANGEARMYACTIONCHECKANS = 34009;
    public static final int CHANGEPLAYERARMYACTIONASK = 24010;
    public static final int CHANGEPLAYERARMYACTIONANS = 34010;
    public static final int FORCEDDEFEATARMYASK = 24011;
    public static final int FORCEDDEFEATARMYANS = 34011;
    public static final int FETCHTERRITORYPAGEASK = 24101;
    public static final int FETCHTERRITORYPAGEANS = 34101;
    public static final int ABANDONCLANMAPBUILDINGASK = 24102;
    public static final int ABANDONCLANMAPBUILDINGANS = 34102;
    public static final int ONCLANCREATEDCMD = 24103;
    public static final int ONCLANDISSOLUTIONCMD = 24104;
    public static final int SYNCSCENECLANCMD = 24105;
    public static final int SYNCCLANACTORSTATUSCMD = 24106;
    public static final int CONSTRUCTCLANBUILDINGASK = 24107;
    public static final int CONSTRUCTCLANBUILDINGANS = 34107;
    public static final int DESTROYCLANBUILDINGASK = 24108;
    public static final int DESTROYCLANBUILDINGANS = 34108;
    public static final int EXTINGUISHBUILDINGFIREASK = 24109;
    public static final int EXTINGUISHBUILDINGFIREANS = 34109;
    public static final int FETCHCLANBUILDINGHPASK = 24110;
    public static final int FETCHCLANBUILDINGHPANS = 34110;
    public static final int CHECKCANFREEREBUILDASK = 24111;
    public static final int CHECKCANFREEREBUILDANS = 34111;
    public static final int VERIFYCANREBUILDASK = 24112;
    public static final int VERIFYCANREBUILDANS = 34112;
    public static final int VERIFYCANEXTINGUISHASK = 24113;
    public static final int VERIFYCANEXTINGUISHANS = 34113;
    public static final int SYNCCLANADDITIONCMD = 24114;
    public static final int ASKFORCLANRECOMMENDASK = 24115;
    public static final int ASKFORCLANRECOMMENDANS = 34115;
    public static final int FETCHDEFAULTCLANLISTASK = 24116;
    public static final int FETCHDEFAULTCLANLISTANS = 34116;
    public static final int VERIFYCANPLACECLANRESASK = 24117;
    public static final int VERIFYCANPLACECLANRESANS = 34117;
    public static final int PLACECLANRESBUILDINSCENEASK = 24118;
    public static final int PLACECLANRESBUILDINSCENEANS = 34118;
    public static final int FETCHCLANRESBUILDSIMPLEINFOASK = 24119;
    public static final int FETCHCLANRESBUILDSIMPLEINFOANS = 34119;
    public static final int CHECKPLAYERCITYINTERRITORYASK = 24120;
    public static final int CHECKPLAYERCITYINTERRITORYANS = 34120;
    public static final int ADDDEVBUFFFROMCLANCMD = 24121;
    public static final int REMOVEDEVBUFFFROMCLANCMD = 24122;
    public static final int UPDATEADDITIONFROMCLANCMD = 24123;
    public static final int GETCLANCOMMANDCENTERNUMASK = 24124;
    public static final int GETCLANCOMMANDCENTERNUMANS = 34124;
    public static final int SEARCHRESOURCEASK = 24201;
    public static final int SEARCHRESOURCEANS = 34201;
    public static final int CREATEDUNGEONASK = 24301;
    public static final int CREATEDUNGEONANS = 34301;
    public static final int ENTERDUNGEONASK = 24302;
    public static final int ENTERDUNGEONANS = 34302;
    public static final int LEAVEDUNGEONASK = 24303;
    public static final int LEAVEDUNGEONANS = 34303;
    public static final int PLAYERLOGINASK = 24304;
    public static final int PLAYERLOGINANS = 34304;
    public static final int PLAYERLOGOUTASK = 24305;
    public static final int PLAYERLOGOUTANS = 34305;
    public static final int PERFORMACTIONASK = 24306;
    public static final int PERFORMACTIONANS = 34306;
    public static final int SOLDIERASK = 24401;
    public static final int SOLDIERANS = 34401;
    public static final int GETONLINEPLAYERIDASK = 24501;
    public static final int GETONLINEPLAYERIDANS = 34501;
    public static final int GETONLINEPLAYERIDFILTERBYCREATETIMEASK = 24502;
    public static final int GETONLINEPLAYERIDFILTERBYCREATETIMEANS = 34502;
    public static final int CHECKPLAYERONLINEASK = 24503;
    public static final int CHECKPLAYERONLINEANS = 34503;
    public static final int GETZONEIPPORTASK = 24504;
    public static final int GETZONEIPPORTANS = 34504;
    public static final int KINGAPPOINTASK = 24601;
    public static final int KINGAPPOINTANS = 34601;
    public static final int KINGOPENBUFFASK = 24602;
    public static final int KINGOPENBUFFANS = 34602;
    public static final int KINGSENDGIFTASK = 24603;
    public static final int KINGSENDGIFTANS = 34603;
    public static final int KINGCHECKCANUSESKILLASK = 24604;
    public static final int KINGCHECKCANUSESKILLANS = 34604;
    public static final int KINGUSESKILLASK = 24605;
    public static final int KINGUSESKILLANS = 34605;
    public static final int FETCHHISTORYKINGASK = 24606;
    public static final int FETCHHISTORYKINGANS = 34606;
    public static final int FETCHKINGDOMGIFTASK = 24607;
    public static final int FETCHKINGDOMGIFTANS = 34607;
    public static final int FETCHKINGDOMOFFICEASK = 24608;
    public static final int FETCHKINGDOMOFFICEANS = 34608;
    public static final int SYNCPLAYEROFFLINEMAILSASK = 24701;
    public static final int SYNCPLAYEROFFLINEMAILSANS = 34701;
    public static final int SENDZONEMAILCMD = 24702;
    public static final int EXECUTESCENEGMASK = 24801;
    public static final int EXECUTESCENEGMANS = 34801;
    public static final int SEARCHPATHASK = 24802;
    public static final int SEARCHPATHANS = 34802;
    public static final int FETCHCLANCITYPOINTLISTASK = 24803;
    public static final int FETCHCLANCITYPOINTLISTANS = 34803;
    public static final int FETCHSINGLECLANMEMBERCITYPOINTASK = 24804;
    public static final int FETCHSINGLECLANMEMBERCITYPOINTANS = 34804;
    public static final int FETCHTERRITORYMAPASK = 24805;
    public static final int FETCHTERRITORYMAPANS = 34805;
    public static final int SENDSCENEMARQUEECMD = 24901;
    public static final int SENDSCENELOOPMARQUEEWITHIDIPASK = 24902;
    public static final int SENDSCENELOOPMARQUEEWITHIDIPANS = 34902;
    public static final int PLAYERSEARCHMONSTERASK = 25001;
    public static final int PLAYERSEARCHMONSTERANS = 35001;
    public static final int ADDMONSTERASK = 25002;
    public static final int ADDMONSTERANS = 35002;
    public static final int CHECKCANBEATTACKASK = 25003;
    public static final int CHECKCANBEATTACKANS = 35003;
    public static final int QUERYMAPBUILDINGIDASK = 25004;
    public static final int QUERYMAPBUILDINGIDANS = 35004;
    public static final int GETMONSTERNUMASK = 25005;
    public static final int GETMONSTERNUMANS = 35005;
    public static final int REFRESHACTMONSTERASK = 25006;
    public static final int REFRESHACTMONSTERANS = 35006;
    public static final int SUMMONSKYNETMONSTERASK = 25008;
    public static final int SUMMONSKYNETMONSTERANS = 35008;
    public static final int CREATESPYPLANEASK = 25101;
    public static final int CREATESPYPLANEANS = 35101;
    public static final int CHANGEACTIONSPYPLANEASK = 25102;
    public static final int CHANGEACTIONSPYPLANEANS = 35102;
    public static final int CHECKMAPCREATESPYPLANEASK = 25103;
    public static final int CHECKMAPCREATESPYPLANEANS = 35103;
    public static final int CREATELOGISTICSPLANEASK = 25104;
    public static final int CREATELOGISTICSPLANEANS = 35104;
    public static final int CHECKLOGISTICSACTIONASK = 25105;
    public static final int CHECKLOGISTICSACTIONANS = 35105;
    public static final int CHANGELOGISTICACTIONASK = 25106;
    public static final int CHANGELOGISTICSACTIONANS = 35106;
    public static final int FIRSTENTERBIGSCENEASK = 25201;
    public static final int FIRSTENTERBIGSCENEANS = 35201;
    public static final int UPDATEPLAYERVIEWASK = 25202;
    public static final int UPDATEPLAYERVIEWANS = 35202;
    public static final int CLEARPLAYERVIEWASK = 25203;
    public static final int CLEARPLAYERVIEWANS = 35203;
    public static final int PLAYERADDSOLDIERASK = 25204;
    public static final int PLAYERADDSOLDIERANS = 35204;
    public static final int DISMISSINCITYSOLDIERSASK = 25205;
    public static final int DISMISSINCITYSOLDIERSANS = 35205;
    public static final int RETURNTREATOVERSOLDIERSASK = 25206;
    public static final int RETURNTREATOVERSOLDIERSANS = 35206;
    public static final int GETALLSOLDIERASK = 25207;
    public static final int GETALLSOLDIERANS = 35207;
    public static final int HOSPITALTREATCHECKASK = 25208;
    public static final int HOSPITALTREATCHECKANS = 35208;
    public static final int HOSPITALTREATASK = 25209;
    public static final int HOSPITALTREATANS = 35209;
    public static final int HOSPITALFASTTREATASK = 25210;
    public static final int HOSPITALFASTTREATANS = 35210;
    public static final int HOSPITALTREATFINISHCMD = 25211;
    public static final int ADDDEVBUFFFROMPLAYERASK = 25212;
    public static final int ADDDEVBUFFFROMPLAYERANS = 35212;
    public static final int REMOVEDEVBUFFFROMPLAYERASK = 25213;
    public static final int REMOVEDEVBUFFFROMPLAYERANS = 35213;
    public static final int UPDATEADDITIONFROMPLAYERCMD = 25214;
    public static final int GETSCENEADDITIONSYSASK = 25215;
    public static final int GETSCENEADDITIONSYSANS = 35215;
    public static final int GETSCENEDEVBUFFASK = 25216;
    public static final int GETSCENEDEVBUFFANS = 35216;
    public static final int MARKPOSITIONASK = 25217;
    public static final int MARKPOSITIONANS = 35217;
    public static final int SETMARKREADEDCMD = 25218;
    public static final int SYNCPLAYERCLANIDNAMECMD = 25219;
    public static final int SYNCPLAYERNAMECMD = 25220;
    public static final int SYNCPLAYERSOLDIERCMD = 25221;
    public static final int SYNCPLAYERCITYBUILDLEVELCMD = 25222;
    public static final int SYNCPLANECMD = 25223;
    public static final int SYNCPLAYERHEROCMD = 25224;
    public static final int SYNCTECHDATACMD = 25225;
    public static final int SYNCPLAYERPICCMD = 25226;
    public static final int SYNCPLAYERPICFRAMECMD = 25227;
    public static final int SYNCPLAYERWALLHEROPLANECMD = 25228;
    public static final int SYNCPLAYERERACMD = 25229;
    public static final int MARKNEWBIEOVERASK = 25232;
    public static final int MARKNEWBIEOVERANS = 35232;
    public static final int BROADCASTONLINEPLAYERCSCMD = 25233;
    public static final int BROADCASTONLINEPLAYERCSWITHMULTILANGUAGECMD = 25234;
    public static final int GETMILESTONEHISTORYASK = 25235;
    public static final int GETMILESTONEHISTORYANS = 35235;
    public static final int GETMILESTONERANKINFOASK = 25236;
    public static final int GETMILESTONERANKINFOANS = 35236;
    public static final int FETCHMAINCITYINFOASK = 25237;
    public static final int FETCHMAINCITYINFOANS = 35237;
    public static final int SETEXPRESSIONCMD = 25238;
    public static final int SETPFLAGCMD = 25239;
    public static final int GETBATTLELOSEASK = 25240;
    public static final int GETBATTLELOSEANS = 35240;
    public static final int USEDUNGEONSKILLASK = 25241;
    public static final int USEDUNGEONSKILLANS = 35241;
    public static final int CHANGECITYDRESSASK = 25242;
    public static final int CHANGECITYDRESSANS = 35242;
    public static final int CHECKCANADDDEVBUFFASK = 25243;
    public static final int CHECKCANADDDEVBUFFANS = 35243;
    public static final int IDIPRETURNALLARMYASK = 25244;
    public static final int IDIPRETURNALLARMYANS = 35244;
    public static final int IDIPGLOBALPEACESHIELDASK = 25245;
    public static final int IDIPGLOBALPEACESHIELDANS = 35245;
    public static final int SYNCPLAYERPUSHNTFINFOASK = 25246;
    public static final int SYNCPLAYERPUSHNTFINFOANS = 35246;
    public static final int QUERYPLAYERPUSHNTFASK = 25247;
    public static final int QUERYPLAYERPUSHNTFANS = 35247;
    public static final int IDIPMODIFYSOLDIERASK = 25248;
    public static final int IDIPMODIFYSOLDIERANS = 35248;
    public static final int BROADONLINEPLAYERSSCMD = 25249;
    public static final int SKYNETFINDMONSTERASK = 25250;
    public static final int SKYNETFINDMONSTERANS = 35250;
    public static final int GETZONESEASONASK = 25251;
    public static final int GETZONESEASONANS = 35251;
    public static final int FETCHRALLYLISTASK = 25301;
    public static final int FETCHRALLYLISTANS = 35301;
    public static final int FETCHONERALLYASK = 25302;
    public static final int FETCHONERALLYANS = 35302;
    public static final int PLAYERCANCELRALLYASK = 25303;
    public static final int PLAYERCANCELRALLYANS = 35303;
    public static final int REPATRIATERALLYMEMBERASK = 25304;
    public static final int REPATRIATERALLYMEMBERANS = 35304;
    public static final int SETRALLYRECOMMENDSOLDIERASK = 25305;
    public static final int SETRALLYRECOMMENDSOLDIERANS = 35305;
    public static final int FETCHWARNINGASK = 25306;
    public static final int FETCHWARNINGANS = 35306;
    public static final int SETWARNINGITEMTAGASK = 25307;
    public static final int SETWARNINGITEMTAGANS = 35307;
    public static final int IGNOREALLWARNINGASK = 25308;
    public static final int IGNOREALLWARNINGANS = 35308;
    public static final int FETCHINNERARMYASK = 25309;
    public static final int FETCHINNERARMYANS = 35309;
    public static final int REPATRIATEASSISTMEMBERASK = 25310;
    public static final int REPATRIATEASSISTMEMBERANS = 35310;
    public static final int CHANGEASSISTLEADERASK = 25311;
    public static final int CHANGEASSISTLEADERANS = 35311;
    public static final int CHECKTEXTASK = 25401;
    public static final int CHECKTEXTANS = 35401;
    public static final int BATCHCHECKTEXTASK = 25402;
    public static final int BATCHCHECKTEXTANS = 35402;
    public static final int TRANSLATETEXTASK = 25501;
    public static final int TRANSLATETEXTANS = 35501;
    public static final int GETALLZONEINFOASK = 25601;
    public static final int GETALLZONEINFOANS = 35601;
    public static final int GETMULTIZONESTATUSASK = 25602;
    public static final int GETMULTIZONESTATUSANS = 35602;
    public static final int GETZONESUNDERSEASONASK = 25603;
    public static final int GETZONESUNDERSEASONANS = 35603;
    public static final int GETZONEMILESTONEASK = 25604;
    public static final int GETZONEMILESTONEANS = 35604;
    public static final int FETCHCHATMSGASK = 25701;
    public static final int FETCHCHATMSGANS = 35701;
    public static final int SENDCHATMSGASK = 25702;
    public static final int SENDCHATMSGANS = 35702;
    public static final int IDIPSENDCHATMSGASK = 25703;
    public static final int IDIPSENDCHATMSGANS = 35703;
    public static final int IDIPEXECSCRIPTMSGASK = 25704;
    public static final int IDIPEXECSCRIPTMSGANS = 35704;

    private static final Map<Integer, GeneratedMessageV3> MSG_ID_2_PROTO_MSG = new HashMap<>();
    private static final Map<Class<? extends GeneratedMessageV3>, Integer> PROTO_MSG_2_MSG_ID = new HashMap<>();

    static {
        MSG_ID_2_PROTO_MSG.put(TAKEACTIVITYREWARDASK, SsActivity.TakeActivityRewardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.TakeActivityRewardAsk.class, TAKEACTIVITYREWARDASK);
        MSG_ID_2_PROTO_MSG.put(TAKEACTIVITYREWARDANS, SsActivity.TakeActivityRewardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.TakeActivityRewardAns.class, TAKEACTIVITYREWARDANS);
        MSG_ID_2_PROTO_MSG.put(GETALLACTIVITYSTATUSASK, SsActivity.GetAllActivityStatusAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.GetAllActivityStatusAsk.class, GETALLACTIVITYSTATUSASK);
        MSG_ID_2_PROTO_MSG.put(GETALLACTIVITYSTATUSANS, SsActivity.GetAllActivityStatusAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.GetAllActivityStatusAns.class, GETALLACTIVITYSTATUSANS);
        MSG_ID_2_PROTO_MSG.put(INITACTIVITYACTORASK, SsActivity.InitActivityActorAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.InitActivityActorAsk.class, INITACTIVITYACTORASK);
        MSG_ID_2_PROTO_MSG.put(INITACTIVITYACTORANS, SsActivity.InitActivityActorAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsActivity.InitActivityActorAns.class, INITACTIVITYACTORANS);
        MSG_ID_2_PROTO_MSG.put(JUDGETIMINGASK, SsAntiAddiction.JudgeTimingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.JudgeTimingAsk.class, JUDGETIMINGASK);
        MSG_ID_2_PROTO_MSG.put(JUDGETIMINGANS, SsAntiAddiction.JudgeTimingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.JudgeTimingAns.class, JUDGETIMINGANS);
        MSG_ID_2_PROTO_MSG.put(JUDGEPAYASK, SsAntiAddiction.JudgePayAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.JudgePayAsk.class, JUDGEPAYASK);
        MSG_ID_2_PROTO_MSG.put(JUDGEPAYANS, SsAntiAddiction.JudgePayAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.JudgePayAns.class, JUDGEPAYANS);
        MSG_ID_2_PROTO_MSG.put(REPORTPAYCMD, SsAntiAddiction.ReportPayCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.ReportPayCmd.class, REPORTPAYCMD);
        MSG_ID_2_PROTO_MSG.put(REPORTEXECUTECMD, SsAntiAddiction.ReportExecuteCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAntiAddiction.ReportExecuteCmd.class, REPORTEXECUTECMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEVIEWASK, SsAoiView.UpdateViewAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.UpdateViewAsk.class, UPDATEVIEWASK);
        MSG_ID_2_PROTO_MSG.put(UPDATEVIEWANS, SsAoiView.UpdateViewAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.UpdateViewAns.class, UPDATEVIEWANS);
        MSG_ID_2_PROTO_MSG.put(CLEARVIEWASK, SsAoiView.ClearViewAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.ClearViewAsk.class, CLEARVIEWASK);
        MSG_ID_2_PROTO_MSG.put(CLEARVIEWANS, SsAoiView.ClearViewAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.ClearViewAns.class, CLEARVIEWANS);
        MSG_ID_2_PROTO_MSG.put(ADDSCENEOBJCMD, SsAoiView.AddSceneObjCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.AddSceneObjCmd.class, ADDSCENEOBJCMD);
        MSG_ID_2_PROTO_MSG.put(CHANGESCENEOBJCMD, SsAoiView.ChangeSceneObjCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAoiView.ChangeSceneObjCmd.class, CHANGESCENEOBJCMD);
        MSG_ID_2_PROTO_MSG.put(AUTHINTLASK, SsAuth.AuthIntlAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAuth.AuthIntlAsk.class, AUTHINTLASK);
        MSG_ID_2_PROTO_MSG.put(AUTHINTLANS, SsAuth.AuthIntlAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAuth.AuthIntlAns.class, AUTHINTLANS);
        MSG_ID_2_PROTO_MSG.put(AUTHMSDKASK, SsAuth.AuthMsdkAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAuth.AuthMsdkAsk.class, AUTHMSDKASK);
        MSG_ID_2_PROTO_MSG.put(AUTHMSDKANS, SsAuth.AuthMsdkAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsAuth.AuthMsdkAns.class, AUTHMSDKANS);
        MSG_ID_2_PROTO_MSG.put(CHECKBATTLEREQ, SsBattle.CheckBattleReq.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsBattle.CheckBattleReq.class, CHECKBATTLEREQ);
        MSG_ID_2_PROTO_MSG.put(CHECKBATTLERESP, SsBattle.CheckBattleResp.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsBattle.CheckBattleResp.class, CHECKBATTLERESP);
        MSG_ID_2_PROTO_MSG.put(ADDCLANACTSCORECMD, SsClanActivity.AddClanActScoreCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanActivity.AddClanActScoreCmd.class, ADDCLANACTSCORECMD);
        MSG_ID_2_PROTO_MSG.put(FETCHACTIVITYDATAASK, SsClanActivity.FetchActivityDataAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanActivity.FetchActivityDataAsk.class, FETCHACTIVITYDATAASK);
        MSG_ID_2_PROTO_MSG.put(FETCHACTIVITYDATAANS, SsClanActivity.FetchActivityDataAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanActivity.FetchActivityDataAns.class, FETCHACTIVITYDATAANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANPRIVILEGEASK, SsClanAttr.ModifyClanPrivilegeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanPrivilegeAsk.class, MODIFYCLANPRIVILEGEASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANPRIVILEGEANS, SsClanAttr.ModifyClanPrivilegeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanPrivilegeAns.class, MODIFYCLANPRIVILEGEANS);
        MSG_ID_2_PROTO_MSG.put(CHECKMODIFYNAMEQUIETTIMEASK, SsClanAttr.CheckModifyNameQuietTimeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckModifyNameQuietTimeAsk.class, CHECKMODIFYNAMEQUIETTIMEASK);
        MSG_ID_2_PROTO_MSG.put(CHECKMODIFYNAMEQUIETTIMEANS, SsClanAttr.CheckModifyNameQuietTimeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckModifyNameQuietTimeAns.class, CHECKMODIFYNAMEQUIETTIMEANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANNAMEASK, SsClanAttr.ModifyClanNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanNameAsk.class, MODIFYCLANNAMEASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANNAMEANS, SsClanAttr.ModifyClanNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanNameAns.class, MODIFYCLANNAMEANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANSIMPLENAMEASK, SsClanAttr.ModifyClanSimpleNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanSimpleNameAsk.class, MODIFYCLANSIMPLENAMEASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANSIMPLENAMEANS, SsClanAttr.ModifyClanSimpleNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanSimpleNameAns.class, MODIFYCLANSIMPLENAMEANS);
        MSG_ID_2_PROTO_MSG.put(CHECKCLANFLAGANDTERRITORYCOLORASK, SsClanAttr.CheckClanFlagAndTerritoryColorAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckClanFlagAndTerritoryColorAsk.class, CHECKCLANFLAGANDTERRITORYCOLORASK);
        MSG_ID_2_PROTO_MSG.put(CHECKCLANFLAGANDTERRITORYCOLORANS, SsClanAttr.CheckClanFlagAndTerritoryColorAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckClanFlagAndTerritoryColorAns.class, CHECKCLANFLAGANDTERRITORYCOLORANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANFLAGANDTERRITORYCOLORASK, SsClanAttr.ModifyClanFlagAndTerritoryColorAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanFlagAndTerritoryColorAsk.class, MODIFYCLANFLAGANDTERRITORYCOLORASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANFLAGANDTERRITORYCOLORANS, SsClanAttr.ModifyClanFlagAndTerritoryColorAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanFlagAndTerritoryColorAns.class, MODIFYCLANFLAGANDTERRITORYCOLORANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANWELCOMELETTERASK, SsClanAttr.ModifyClanWelcomeLetterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanWelcomeLetterAsk.class, MODIFYCLANWELCOMELETTERASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANWELCOMELETTERANS, SsClanAttr.ModifyClanWelcomeLetterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanWelcomeLetterAns.class, MODIFYCLANWELCOMELETTERANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANSETTINGMISCASK, SsClanAttr.ModifyClanSettingMiscAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanSettingMiscAsk.class, MODIFYCLANSETTINGMISCASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYCLANSETTINGMISCANS, SsClanAttr.ModifyClanSettingMiscAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ModifyClanSettingMiscAns.class, MODIFYCLANSETTINGMISCANS);
        MSG_ID_2_PROTO_MSG.put(CLANMARKPOSITIONCMD, SsClanAttr.ClanMarkPositionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.ClanMarkPositionCmd.class, CLANMARKPOSITIONCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCREDDOTTOCLANCMD, SsClanAttr.SyncRedDotToClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.SyncRedDotToClanCmd.class, SYNCREDDOTTOCLANCMD);
        MSG_ID_2_PROTO_MSG.put(FETCHRESOURCESSNAPSHOTASK, SsClanAttr.FetchResourcesSnapshotAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchResourcesSnapshotAsk.class, FETCHRESOURCESSNAPSHOTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHRESOURCESSNAPSHOTANS, SsClanAttr.FetchResourcesSnapshotAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchResourcesSnapshotAns.class, FETCHRESOURCESSNAPSHOTANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANWAREHOUSEINFOASK, SsClanAttr.FetchClanWareHouseInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanWareHouseInfoAsk.class, FETCHCLANWAREHOUSEINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANWAREHOUSEINFOANS, SsClanAttr.FetchClanWareHouseInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanWareHouseInfoAns.class, FETCHCLANWAREHOUSEINFOANS);
        MSG_ID_2_PROTO_MSG.put(ONADDCLANSCOREFORCLANCMD, SsClanAttr.OnAddClanScoreForClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.OnAddClanScoreForClanCmd.class, ONADDCLANSCOREFORCLANCMD);
        MSG_ID_2_PROTO_MSG.put(CREATECLANLOGCMD, SsClanAttr.CreateClanLogCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CreateClanLogCmd.class, CREATECLANLOGCMD);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANLOGASK, SsClanAttr.FetchClanLogAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanLogAsk.class, FETCHCLANLOGASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANLOGANS, SsClanAttr.FetchClanLogAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanLogAns.class, FETCHCLANLOGANS);
        MSG_ID_2_PROTO_MSG.put(FETCHDISSOLVINGINFOASK, SsClanAttr.FetchDissolvingInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchDissolvingInfoAsk.class, FETCHDISSOLVINGINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHDISSOLVINGINFOANS, SsClanAttr.FetchDissolvingInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchDissolvingInfoAns.class, FETCHDISSOLVINGINFOANS);
        MSG_ID_2_PROTO_MSG.put(IDIPQUERYCLANINFOASK, SsClanAttr.IDIPQueryClanInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.IDIPQueryClanInfoAsk.class, IDIPQUERYCLANINFOASK);
        MSG_ID_2_PROTO_MSG.put(IDIPQUERYCLANINFOANS, SsClanAttr.IDIPQueryClanInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.IDIPQueryClanInfoAns.class, IDIPQUERYCLANINFOANS);
        MSG_ID_2_PROTO_MSG.put(IDIPRESETCLANINFOASK, SsClanAttr.IDIPResetClanInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.IDIPResetClanInfoAsk.class, IDIPRESETCLANINFOASK);
        MSG_ID_2_PROTO_MSG.put(IDIPRESETCLANINFOANS, SsClanAttr.IDIPResetClanInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.IDIPResetClanInfoAns.class, IDIPRESETCLANINFOANS);
        MSG_ID_2_PROTO_MSG.put(CHECKMODIFYSNAMEQUIETTIMEASK, SsClanAttr.CheckModifySNameQuietTimeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckModifySNameQuietTimeAsk.class, CHECKMODIFYSNAMEQUIETTIMEASK);
        MSG_ID_2_PROTO_MSG.put(CHECKMODIFYSNAMEQUIETTIMEANS, SsClanAttr.CheckModifySNameQuietTimeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.CheckModifySNameQuietTimeAns.class, CHECKMODIFYSNAMEQUIETTIMEANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANPOSITIONMARKASK, SsClanAttr.FetchClanPositionMarkAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanPositionMarkAsk.class, FETCHCLANPOSITIONMARKASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANPOSITIONMARKANS, SsClanAttr.FetchClanPositionMarkAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanPositionMarkAns.class, FETCHCLANPOSITIONMARKANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANNUMTIPASK, SsClanAttr.FetchClanNumTipAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanNumTipAsk.class, FETCHCLANNUMTIPASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANNUMTIPANS, SsClanAttr.FetchClanNumTipAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanAttr.FetchClanNumTipAns.class, FETCHCLANNUMTIPANS);
        MSG_ID_2_PROTO_MSG.put(CREATECLANENTITYASK, SsClanBase.CreateClanEntityAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.CreateClanEntityAsk.class, CREATECLANENTITYASK);
        MSG_ID_2_PROTO_MSG.put(CREATECLANENTITYANS, SsClanBase.CreateClanEntityAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.CreateClanEntityAns.class, CREATECLANENTITYANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCARDASK, SsClanBase.FetchClanCardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanCardAsk.class, FETCHCLANCARDASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCARDANS, SsClanBase.FetchClanCardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanCardAns.class, FETCHCLANCARDANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANRANKSIMPLEINFOASK, SsClanBase.FetchClanRankSimpleInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanRankSimpleInfoAsk.class, FETCHCLANRANKSIMPLEINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANRANKSIMPLEINFOANS, SsClanBase.FetchClanRankSimpleInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanRankSimpleInfoAns.class, FETCHCLANRANKSIMPLEINFOANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANMEMBERINFOASK, SsClanBase.FetchClanMemberInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanMemberInfoAsk.class, FETCHCLANMEMBERINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANMEMBERINFOANS, SsClanBase.FetchClanMemberInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanMemberInfoAns.class, FETCHCLANMEMBERINFOANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANAPPLYMEMBERSASK, SsClanBase.FetchClanApplyMembersAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanApplyMembersAsk.class, FETCHCLANAPPLYMEMBERSASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANAPPLYMEMBERSANS, SsClanBase.FetchClanApplyMembersAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanApplyMembersAns.class, FETCHCLANAPPLYMEMBERSANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANWELCOMELETTERASK, SsClanBase.FetchClanWelcomeLetterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanWelcomeLetterAsk.class, FETCHCLANWELCOMELETTERASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANWELCOMELETTERANS, SsClanBase.FetchClanWelcomeLetterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.FetchClanWelcomeLetterAns.class, FETCHCLANWELCOMELETTERANS);
        MSG_ID_2_PROTO_MSG.put(GETCLANBASEINFOASK, SsClanBase.GetClanBaseInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.GetClanBaseInfoAsk.class, GETCLANBASEINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETCLANBASEINFOANS, SsClanBase.GetClanBaseInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.GetClanBaseInfoAns.class, GETCLANBASEINFOANS);
        MSG_ID_2_PROTO_MSG.put(SENDCLANMAILANDSAVEASK, SsClanBase.SendClanMailAndSaveAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.SendClanMailAndSaveAsk.class, SENDCLANMAILANDSAVEASK);
        MSG_ID_2_PROTO_MSG.put(SENDCLANMAILANDSAVEANS, SsClanBase.SendClanMailAndSaveAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.SendClanMailAndSaveAns.class, SENDCLANMAILANDSAVEANS);
        MSG_ID_2_PROTO_MSG.put(EXECUTECLANGMASK, SsClanBase.ExecuteClanGmAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.ExecuteClanGmAsk.class, EXECUTECLANGMASK);
        MSG_ID_2_PROTO_MSG.put(EXECUTECLANGMANS, SsClanBase.ExecuteClanGmAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.ExecuteClanGmAns.class, EXECUTECLANGMANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERDISSOLVECLANASK, SsClanBase.PlayerDissolveClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.PlayerDissolveClanAsk.class, PLAYERDISSOLVECLANASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERDISSOLVECLANANS, SsClanBase.PlayerDissolveClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.PlayerDissolveClanAns.class, PLAYERDISSOLVECLANANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERCANCELDISSOLVECLANASK, SsClanBase.PlayerCancelDissolveClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.PlayerCancelDissolveClanAsk.class, PLAYERCANCELDISSOLVECLANASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERCANCELDISSOLVECLANANS, SsClanBase.PlayerCancelDissolveClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.PlayerCancelDissolveClanAns.class, PLAYERCANCELDISSOLVECLANANS);
        MSG_ID_2_PROTO_MSG.put(RECORDCLANSNAPSHOTCMD, SsClanBase.RecordClanSnapshotCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.RecordClanSnapshotCmd.class, RECORDCLANSNAPSHOTCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTCLANONLINEMEMBERCSCMD, SsClanBase.BroadcastClanOnlineMemberCsCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.BroadcastClanOnlineMemberCsCmd.class, BROADCASTCLANONLINEMEMBERCSCMD);
        MSG_ID_2_PROTO_MSG.put(ADDCLANRESOURCESCMD, SsClanBase.AddClanResourcesCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.AddClanResourcesCmd.class, ADDCLANRESOURCESCMD);
        MSG_ID_2_PROTO_MSG.put(INVITEOTHERTOCLANASK, SsClanBase.InviteOtherToClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.InviteOtherToClanAsk.class, INVITEOTHERTOCLANASK);
        MSG_ID_2_PROTO_MSG.put(INVITEOTHERTOCLANANS, SsClanBase.InviteOtherToClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.InviteOtherToClanAns.class, INVITEOTHERTOCLANANS);
        MSG_ID_2_PROTO_MSG.put(CHECKINVITEEXISTASK, SsClanBase.CheckInviteExistAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.CheckInviteExistAsk.class, CHECKINVITEEXISTASK);
        MSG_ID_2_PROTO_MSG.put(CHECKINVITEEXISTANS, SsClanBase.CheckInviteExistAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.CheckInviteExistAns.class, CHECKINVITEEXISTANS);
        MSG_ID_2_PROTO_MSG.put(REFUSECLANINVITECMD, SsClanBase.RefuseClanInviteCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.RefuseClanInviteCmd.class, REFUSECLANINVITECMD);
        MSG_ID_2_PROTO_MSG.put(REFRESHCLANTHINGSASK, SsClanBase.RefreshClanThingsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.RefreshClanThingsAsk.class, REFRESHCLANTHINGSASK);
        MSG_ID_2_PROTO_MSG.put(REFRESHCLANTHINGSANS, SsClanBase.RefreshClanThingsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.RefreshClanThingsAns.class, REFRESHCLANTHINGSANS);
        MSG_ID_2_PROTO_MSG.put(PUSHNOTIFYCMD, SsClanBase.PushNotifyCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanBase.PushNotifyCmd.class, PUSHNOTIFYCMD);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCHATASK, SsClanChat.FetchClanChatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanChat.FetchClanChatAsk.class, FETCHCLANCHATASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCHATANS, SsClanChat.FetchClanChatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanChat.FetchClanChatAns.class, FETCHCLANCHATANS);
        MSG_ID_2_PROTO_MSG.put(SENDCLANCHATASK, SsClanChat.SendClanChatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanChat.SendClanChatAsk.class, SENDCLANCHATASK);
        MSG_ID_2_PROTO_MSG.put(SENDCLANCHATANS, SsClanChat.SendClanChatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanChat.SendClanChatAns.class, SENDCLANCHATANS);
        MSG_ID_2_PROTO_MSG.put(ADDGIFTPOINTANDKEYASK, SsClanGift.AddGiftPointAndKeyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanGift.AddGiftPointAndKeyAsk.class, ADDGIFTPOINTANDKEYASK);
        MSG_ID_2_PROTO_MSG.put(ADDGIFTPOINTANDKEYANS, SsClanGift.AddGiftPointAndKeyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanGift.AddGiftPointAndKeyAns.class, ADDGIFTPOINTANDKEYANS);
        MSG_ID_2_PROTO_MSG.put(ADDCLANGIFTCMD, SsClanGift.AddClanGiftCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanGift.AddClanGiftCmd.class, ADDCLANGIFTCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERQUEUETASKCMD, SsClanHelp.SyncPlayerQueueTaskCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.SyncPlayerQueueTaskCmd.class, SYNCPLAYERQUEUETASKCMD);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANHELPSASK, SsClanHelp.FetchClanHelpsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.FetchClanHelpsAsk.class, FETCHCLANHELPSASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANHELPSANS, SsClanHelp.FetchClanHelpsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.FetchClanHelpsAns.class, FETCHCLANHELPSANS);
        MSG_ID_2_PROTO_MSG.put(FINISHALLCLANHELPSASK, SsClanHelp.FinishAllClanHelpsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.FinishAllClanHelpsAsk.class, FINISHALLCLANHELPSASK);
        MSG_ID_2_PROTO_MSG.put(FINISHALLCLANHELPSANS, SsClanHelp.FinishAllClanHelpsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.FinishAllClanHelpsAns.class, FINISHALLCLANHELPSANS);
        MSG_ID_2_PROTO_MSG.put(ONQUEUETASKFINISHEDCMD, SsClanHelp.OnQueueTaskFinishedCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.OnQueueTaskFinishedCmd.class, ONQUEUETASKFINISHEDCMD);
        MSG_ID_2_PROTO_MSG.put(NEEDCLANHELPITEMIDSNTFCMD, SsClanHelp.NeedClanHelpItemIdsNtfCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanHelp.NeedClanHelpItemIdsNtfCmd.class, NEEDCLANHELPITEMIDSNTFCMD);
        MSG_ID_2_PROTO_MSG.put(PLAYERAPPLYCLANASK, SsClanMember.PlayerApplyClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.PlayerApplyClanAsk.class, PLAYERAPPLYCLANASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERAPPLYCLANANS, SsClanMember.PlayerApplyClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.PlayerApplyClanAns.class, PLAYERAPPLYCLANANS);
        MSG_ID_2_PROTO_MSG.put(PROCESSCLANAPPLYASK, SsClanMember.ProcessClanApplyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.ProcessClanApplyAsk.class, PROCESSCLANAPPLYASK);
        MSG_ID_2_PROTO_MSG.put(PROCESSCLANAPPLYANS, SsClanMember.ProcessClanApplyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.ProcessClanApplyAns.class, PROCESSCLANAPPLYANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERQUITCLANASK, SsClanMember.PlayerQuitClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.PlayerQuitClanAsk.class, PLAYERQUITCLANASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERQUITCLANANS, SsClanMember.PlayerQuitClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.PlayerQuitClanAns.class, PLAYERQUITCLANANS);
        MSG_ID_2_PROTO_MSG.put(KICKOUTCLANMEMBERASK, SsClanMember.KickOutClanMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.KickOutClanMemberAsk.class, KICKOUTCLANMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(KICKOUTCLANMEMBERANS, SsClanMember.KickOutClanMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.KickOutClanMemberAns.class, KICKOUTCLANMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(CHECKINCLANASK, SsClanMember.CheckInClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CheckInClanAsk.class, CHECKINCLANASK);
        MSG_ID_2_PROTO_MSG.put(CHECKINCLANANS, SsClanMember.CheckInClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CheckInClanAns.class, CHECKINCLANANS);
        MSG_ID_2_PROTO_MSG.put(CHECKOUTCLANASK, SsClanMember.CheckOutClanAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CheckOutClanAsk.class, CHECKOUTCLANASK);
        MSG_ID_2_PROTO_MSG.put(CHECKOUTCLANANS, SsClanMember.CheckOutClanAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CheckOutClanAns.class, CHECKOUTCLANANS);
        MSG_ID_2_PROTO_MSG.put(GRANTCLANSTAFFASK, SsClanMember.GrantClanStaffAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.GrantClanStaffAsk.class, GRANTCLANSTAFFASK);
        MSG_ID_2_PROTO_MSG.put(GRANTCLANSTAFFANS, SsClanMember.GrantClanStaffAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.GrantClanStaffAns.class, GRANTCLANSTAFFANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANPOWERREWARDASK, SsClanMember.FetchClanPowerRewardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.FetchClanPowerRewardAsk.class, FETCHCLANPOWERREWARDASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANPOWERREWARDANS, SsClanMember.FetchClanPowerRewardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.FetchClanPowerRewardAns.class, FETCHCLANPOWERREWARDANS);
        MSG_ID_2_PROTO_MSG.put(UPDATECLANMEMBERINFOCMD, SsClanMember.UpdateClanMemberInfoCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.UpdateClanMemberInfoCmd.class, UPDATECLANMEMBERINFOCMD);
        MSG_ID_2_PROTO_MSG.put(PLAYERCANCELAPPLYCMD, SsClanMember.PlayerCancelApplyCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.PlayerCancelApplyCmd.class, PLAYERCANCELAPPLYCMD);
        MSG_ID_2_PROTO_MSG.put(APPLYOWNERDISSOLVINGASK, SsClanMember.ApplyOwnerDissolvingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.ApplyOwnerDissolvingAsk.class, APPLYOWNERDISSOLVINGASK);
        MSG_ID_2_PROTO_MSG.put(APPLYOWNERDISSOLVINGANS, SsClanMember.ApplyOwnerDissolvingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.ApplyOwnerDissolvingAns.class, APPLYOWNERDISSOLVINGANS);
        MSG_ID_2_PROTO_MSG.put(CANCELAPPLYOWNERDISSOLVINGASK, SsClanMember.CancelApplyOwnerDissolvingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CancelApplyOwnerDissolvingAsk.class, CANCELAPPLYOWNERDISSOLVINGASK);
        MSG_ID_2_PROTO_MSG.put(CANCELAPPLYOWNERDISSOLVINGANS, SsClanMember.CancelApplyOwnerDissolvingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.CancelApplyOwnerDissolvingAns.class, CANCELAPPLYOWNERDISSOLVINGANS);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERTOPNHEROCMD, SsClanMember.SyncPlayerTopNHeroCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanMember.SyncPlayerTopNHeroCmd.class, SYNCPLAYERTOPNHEROCMD);
        MSG_ID_2_PROTO_MSG.put(GETCLANRANKPAGEINFOASK, SsClanRank.GetClanRankPageInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanRank.GetClanRankPageInfoAsk.class, GETCLANRANKPAGEINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETCLANRANKPAGEINFOANS, SsClanRank.GetClanRankPageInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanRank.GetClanRankPageInfoAns.class, GETCLANRANKPAGEINFOANS);
        MSG_ID_2_PROTO_MSG.put(GETTOPCLANRANKINFOASK, SsClanRank.GetTopClanRankInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanRank.GetTopClanRankInfoAsk.class, GETTOPCLANRANKINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETTOPCLANRANKINFOANS, SsClanRank.GetTopClanRankInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanRank.GetTopClanRankInfoAns.class, GETTOPCLANRANKINFOANS);
        MSG_ID_2_PROTO_MSG.put(UPDATECLANRANKINGCMD, SsClanRank.UpdateClanRankingCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanRank.UpdateClanRankingCmd.class, UPDATECLANRANKINGCMD);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANSTOREASK, SsClanStore.FetchClanStoreAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.FetchClanStoreAsk.class, FETCHCLANSTOREASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANSTOREANS, SsClanStore.FetchClanStoreAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.FetchClanStoreAns.class, FETCHCLANSTOREANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANSTORERECORDASK, SsClanStore.FetchClanStoreRecordAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.FetchClanStoreRecordAsk.class, FETCHCLANSTORERECORDASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANSTORERECORDANS, SsClanStore.FetchClanStoreRecordAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.FetchClanStoreRecordAns.class, FETCHCLANSTORERECORDANS);
        MSG_ID_2_PROTO_MSG.put(OPERATECLANSTOREITEMASK, SsClanStore.OperateClanStoreItemAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.OperateClanStoreItemAsk.class, OPERATECLANSTOREITEMASK);
        MSG_ID_2_PROTO_MSG.put(OPERATECLANSTOREITEMANS, SsClanStore.OperateClanStoreItemAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanStore.OperateClanStoreItemAns.class, OPERATECLANSTOREITEMANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHFETCHINFOASK, SsClanTech.ClanTechFetchInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechFetchInfoAsk.class, CLANTECHFETCHINFOASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHFETCHINFOANS, SsClanTech.ClanTechFetchInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechFetchInfoAns.class, CLANTECHFETCHINFOANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDONATECHECKASK, SsClanTech.ClanTechDonateCheckAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDonateCheckAsk.class, CLANTECHDONATECHECKASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDONATECHECKANS, SsClanTech.ClanTechDonateCheckAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDonateCheckAns.class, CLANTECHDONATECHECKANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDONATEASK, SsClanTech.ClanTechDonateAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDonateAsk.class, CLANTECHDONATEASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDONATEANS, SsClanTech.ClanTechDonateAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDonateAns.class, CLANTECHDONATEANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHRESEARCHASK, SsClanTech.ClanTechResearchAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechResearchAsk.class, CLANTECHRESEARCHASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHRESEARCHANS, SsClanTech.ClanTechResearchAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechResearchAns.class, CLANTECHRESEARCHANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHRECOMMENDASK, SsClanTech.ClanTechRecommendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechRecommendAsk.class, CLANTECHRECOMMENDASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHRECOMMENDANS, SsClanTech.ClanTechRecommendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechRecommendAns.class, CLANTECHRECOMMENDANS);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDETAILASK, SsClanTech.ClanTechDetailAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDetailAsk.class, CLANTECHDETAILASK);
        MSG_ID_2_PROTO_MSG.put(CLANTECHDETAILANS, SsClanTech.ClanTechDetailAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTech.ClanTechDetailAns.class, CLANTECHDETAILANS);
        MSG_ID_2_PROTO_MSG.put(GETCLANPOWERREWARDASK, SsClanTerritory.GetClanPowerRewardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.GetClanPowerRewardAsk.class, GETCLANPOWERREWARDASK);
        MSG_ID_2_PROTO_MSG.put(GETCLANPOWERREWARDANS, SsClanTerritory.GetClanPowerRewardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.GetClanPowerRewardAns.class, GETCLANPOWERREWARDANS);
        MSG_ID_2_PROTO_MSG.put(CHECKREBUILDCLANBUILDINGASK, SsClanTerritory.CheckRebuildClanBuildingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.CheckRebuildClanBuildingAsk.class, CHECKREBUILDCLANBUILDINGASK);
        MSG_ID_2_PROTO_MSG.put(CHECKREBUILDCLANBUILDINGANS, SsClanTerritory.CheckRebuildClanBuildingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.CheckRebuildClanBuildingAns.class, CHECKREBUILDCLANBUILDINGANS);
        MSG_ID_2_PROTO_MSG.put(PLACECLANRESBUILDASK, SsClanTerritory.PlaceClanResBuildAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.PlaceClanResBuildAsk.class, PLACECLANRESBUILDASK);
        MSG_ID_2_PROTO_MSG.put(PLACECLANRESBUILDANS, SsClanTerritory.PlaceClanResBuildAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.PlaceClanResBuildAns.class, PLACECLANRESBUILDANS);
        MSG_ID_2_PROTO_MSG.put(CHECKEXTINGUISHCLANBUILDINGASK, SsClanTerritory.CheckExtinguishClanBuildingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.CheckExtinguishClanBuildingAsk.class, CHECKEXTINGUISHCLANBUILDINGASK);
        MSG_ID_2_PROTO_MSG.put(CHECKEXTINGUISHCLANBUILDINGANS, SsClanTerritory.CheckExtinguishClanBuildingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.CheckExtinguishClanBuildingAns.class, CHECKEXTINGUISHCLANBUILDINGANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANBUILDINGINFOASK, SsClanTerritory.FetchClanBuildingInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.FetchClanBuildingInfoAsk.class, FETCHCLANBUILDINGINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANBUILDINGINFOANS, SsClanTerritory.FetchClanBuildingInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.FetchClanBuildingInfoAns.class, FETCHCLANBUILDINGINFOANS);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYPAGEALLASK, SsClanTerritory.FetchTerritoryPageAllAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.FetchTerritoryPageAllAsk.class, FETCHTERRITORYPAGEALLASK);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYPAGEALLANS, SsClanTerritory.FetchTerritoryPageAllAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.FetchTerritoryPageAllAns.class, FETCHTERRITORYPAGEALLANS);
        MSG_ID_2_PROTO_MSG.put(SYNCTERRITORYBUFFSNAPSHOTCMD, SsClanTerritory.SyncTerritoryBuffSnapshotCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.SyncTerritoryBuffSnapshotCmd.class, SYNCTERRITORYBUFFSNAPSHOTCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCTERRITORYINFOCMD, SsClanTerritory.SyncTerritoryInfoCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.SyncTerritoryInfoCmd.class, SYNCTERRITORYINFOCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCCLANBUILDSTATUSCMD, SsClanTerritory.SyncClanBuildStatusCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanTerritory.SyncClanBuildStatusCmd.class, SYNCCLANBUILDSTATUSCMD);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANNAMEASK, SsClanCard.QueryClanNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanNameAsk.class, QUERYCLANNAMEASK);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANNAMEANS, SsClanCard.QueryClanNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanNameAns.class, QUERYCLANNAMEANS);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANSIMPLEASK, SsClanCard.QueryClanSimpleAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanSimpleAsk.class, QUERYCLANSIMPLEASK);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANSIMPLEANS, SsClanCard.QueryClanSimpleAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanSimpleAns.class, QUERYCLANSIMPLEANS);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANCARDASK, SsClanCard.QueryClanCardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanCardAsk.class, QUERYCLANCARDASK);
        MSG_ID_2_PROTO_MSG.put(QUERYCLANCARDANS, SsClanCard.QueryClanCardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.QueryClanCardAns.class, QUERYCLANCARDANS);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANNAMEASK, SsClanCard.BatchQueryClanNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanNameAsk.class, BATCHQUERYCLANNAMEASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANNAMEANS, SsClanCard.BatchQueryClanNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanNameAns.class, BATCHQUERYCLANNAMEANS);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANSIMPLEASK, SsClanCard.BatchQueryClanSimpleAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanSimpleAsk.class, BATCHQUERYCLANSIMPLEASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANSIMPLEANS, SsClanCard.BatchQueryClanSimpleAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanSimpleAns.class, BATCHQUERYCLANSIMPLEANS);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANCARDASK, SsClanCard.BatchQueryClanCardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanCardAsk.class, BATCHQUERYCLANCARDASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYCLANCARDANS, SsClanCard.BatchQueryClanCardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.BatchQueryClanCardAns.class, BATCHQUERYCLANCARDANS);
        MSG_ID_2_PROTO_MSG.put(UPDATECLANCARDCMD, SsClanCard.UpdateClanCardCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsClanCard.UpdateClanCardCmd.class, UPDATECLANCARDCMD);
        MSG_ID_2_PROTO_MSG.put(SHUTDOWNCMD, SsGate.ShutdownCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGate.ShutdownCmd.class, SHUTDOWNCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTALLCMD, SsGate.BroadcastAllCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGate.BroadcastAllCmd.class, BROADCASTALLCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTONLINEPLAYERSSCMD, SsGate.BroadcastOnlinePlayerSsCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGate.BroadcastOnlinePlayerSsCmd.class, BROADCASTONLINEPLAYERSSCMD);
        MSG_ID_2_PROTO_MSG.put(SENDMSGTOSESSIONCMD, SsGateSession.SendMsgToSessionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGateSession.SendMsgToSessionCmd.class, SENDMSGTOSESSIONCMD);
        MSG_ID_2_PROTO_MSG.put(PLAYERBOUNDCMD, SsGateSession.PlayerBoundCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGateSession.PlayerBoundCmd.class, PLAYERBOUNDCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATECLIENTINFOCMD, SsGateSession.UpdateClientInfoCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGateSession.UpdateClientInfoCmd.class, UPDATECLIENTINFOCMD);
        MSG_ID_2_PROTO_MSG.put(SENDMSGTOSESSIONWITHLANGUAGECMD, SsGateSession.SendMsgToSessionWithLanguageCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGateSession.SendMsgToSessionWithLanguageCmd.class, SENDMSGTOSESSIONWITHLANGUAGECMD);
        MSG_ID_2_PROTO_MSG.put(KICKOFFSESSIONCMD, SsGateSession.KickOffSessionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGateSession.KickOffSessionCmd.class, KICKOFFSESSIONCMD);
        MSG_ID_2_PROTO_MSG.put(CREATECHATASK, SsGroupChat.CreateChatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.CreateChatAsk.class, CREATECHATASK);
        MSG_ID_2_PROTO_MSG.put(CREATECHATANS, SsGroupChat.CreateChatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.CreateChatAns.class, CREATECHATANS);
        MSG_ID_2_PROTO_MSG.put(SENDCHATMESSAGEASK, SsGroupChat.SendChatMessageAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.SendChatMessageAsk.class, SENDCHATMESSAGEASK);
        MSG_ID_2_PROTO_MSG.put(SENDCHATMESSAGEANS, SsGroupChat.SendChatMessageAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.SendChatMessageAns.class, SENDCHATMESSAGEANS);
        MSG_ID_2_PROTO_MSG.put(QUERYCHATMESSAGEASK, SsGroupChat.QueryChatMessageAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.QueryChatMessageAsk.class, QUERYCHATMESSAGEASK);
        MSG_ID_2_PROTO_MSG.put(QUERYCHATMESSAGEANS, SsGroupChat.QueryChatMessageAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.QueryChatMessageAns.class, QUERYCHATMESSAGEANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCHATMEMBERASK, SsGroupChat.FetchChatMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.FetchChatMemberAsk.class, FETCHCHATMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCHATMEMBERANS, SsGroupChat.FetchChatMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.FetchChatMemberAns.class, FETCHCHATMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(JUDGEMEMBERASK, SsGroupChat.JudgeMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.JudgeMemberAsk.class, JUDGEMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(JUDGEMEMBERANS, SsGroupChat.JudgeMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.JudgeMemberAns.class, JUDGEMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(INVITENEWMEMBERASK, SsGroupChat.InviteNewMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.InviteNewMemberAsk.class, INVITENEWMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(INVITENEWMEMBERANS, SsGroupChat.InviteNewMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.InviteNewMemberAns.class, INVITENEWMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(REMOVEGROUPMEMBERASK, SsGroupChat.RemoveGroupMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.RemoveGroupMemberAsk.class, REMOVEGROUPMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(REMOVEGROUPMEMBERANS, SsGroupChat.RemoveGroupMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.RemoveGroupMemberAns.class, REMOVEGROUPMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(TRANSFERGROUPOWNERASK, SsGroupChat.TransferGroupOwnerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.TransferGroupOwnerAsk.class, TRANSFERGROUPOWNERASK);
        MSG_ID_2_PROTO_MSG.put(TRANSFERGROUPOWNERANS, SsGroupChat.TransferGroupOwnerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.TransferGroupOwnerAns.class, TRANSFERGROUPOWNERANS);
        MSG_ID_2_PROTO_MSG.put(DISMISSCHATGROUPASK, SsGroupChat.DismissChatGroupAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.DismissChatGroupAsk.class, DISMISSCHATGROUPASK);
        MSG_ID_2_PROTO_MSG.put(DISMISSCHATGROUPANS, SsGroupChat.DismissChatGroupAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.DismissChatGroupAns.class, DISMISSCHATGROUPANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYGROUPNAMEASK, SsGroupChat.ModifyGroupNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.ModifyGroupNameAsk.class, MODIFYGROUPNAMEASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYGROUPNAMEANS, SsGroupChat.ModifyGroupNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsGroupChat.ModifyGroupNameAns.class, MODIFYGROUPNAMEANS);
        MSG_ID_2_PROTO_MSG.put(MIDASQUERYASK, SsMidasAgent.MidasQueryAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasQueryAsk.class, MIDASQUERYASK);
        MSG_ID_2_PROTO_MSG.put(MIDASQUERYANS, SsMidasAgent.MidasQueryAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasQueryAns.class, MIDASQUERYANS);
        MSG_ID_2_PROTO_MSG.put(MIDASCONSUMEASK, SsMidasAgent.MidasConsumeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasConsumeAsk.class, MIDASCONSUMEASK);
        MSG_ID_2_PROTO_MSG.put(MIDASCONSUMEANS, SsMidasAgent.MidasConsumeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasConsumeAns.class, MIDASCONSUMEANS);
        MSG_ID_2_PROTO_MSG.put(MIDASROLLBACKCONSUMEASK, SsMidasAgent.MidasRollbackConsumeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasRollbackConsumeAsk.class, MIDASROLLBACKCONSUMEASK);
        MSG_ID_2_PROTO_MSG.put(MIDASROLLBACKCONSUMEANS, SsMidasAgent.MidasRollbackConsumeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasRollbackConsumeAns.class, MIDASROLLBACKCONSUMEANS);
        MSG_ID_2_PROTO_MSG.put(MIDASPRESENTASK, SsMidasAgent.MidasPresentAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasPresentAsk.class, MIDASPRESENTASK);
        MSG_ID_2_PROTO_MSG.put(MIDASPRESENTANS, SsMidasAgent.MidasPresentAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasPresentAns.class, MIDASPRESENTANS);
        MSG_ID_2_PROTO_MSG.put(MIDASSWITCHSTRESSTESTINGMODECMD, SsMidasAgent.MidasSwitchStressTestingModeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMidasAgent.MidasSwitchStressTestingModeCmd.class, MIDASSWITCHSTRESSTESTINGMODECMD);
        MSG_ID_2_PROTO_MSG.put(MONITORDAILYPLAYERNUMCMD, SsMonitor.MonitorDailyPlayerNumCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsMonitor.MonitorDailyPlayerNumCmd.class, MONITORDAILYPLAYERNUMCMD);
        MSG_ID_2_PROTO_MSG.put(OCCUPYBATCHNAMECMD, SsName.OccupyBatchNameCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.OccupyBatchNameCmd.class, OCCUPYBATCHNAMECMD);
        MSG_ID_2_PROTO_MSG.put(RELEASENAMECMD, SsName.ReleaseNameCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.ReleaseNameCmd.class, RELEASENAMECMD);
        MSG_ID_2_PROTO_MSG.put(SEARCHCLANNAMEASK, SsName.SearchClanNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchClanNameAsk.class, SEARCHCLANNAMEASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHCLANNAMEANS, SsName.SearchClanNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchClanNameAns.class, SEARCHCLANNAMEANS);
        MSG_ID_2_PROTO_MSG.put(SEARCHPLAYERNAMEASK, SsName.SearchPlayerNameAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchPlayerNameAsk.class, SEARCHPLAYERNAMEASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHPLAYERNAMEANS, SsName.SearchPlayerNameAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchPlayerNameAns.class, SEARCHPLAYERNAMEANS);
        MSG_ID_2_PROTO_MSG.put(SEARCHNAMEMATCHEDASK, SsName.SearchNameMatchedAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchNameMatchedAsk.class, SEARCHNAMEMATCHEDASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHNAMEMATCHEDANS, SsName.SearchNameMatchedAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsName.SearchNameMatchedAns.class, SEARCHNAMEMATCHEDANS);
        MSG_ID_2_PROTO_MSG.put(NODETASKCMD, SsNode.NodeTaskCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsNode.NodeTaskCmd.class, NODETASKCMD);
        MSG_ID_2_PROTO_MSG.put(SEARCHPATHASYNCASK, SsPathFinding.SearchPathAsyncAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPathFinding.SearchPathAsyncAsk.class, SEARCHPATHASYNCASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHPATHASYNCANS, SsPathFinding.SearchPathAsyncAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPathFinding.SearchPathAsyncAns.class, SEARCHPATHASYNCANS);
        MSG_ID_2_PROTO_MSG.put(LOGINPLAYERCMD, SsPlayer.LoginPlayerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayer.LoginPlayerCmd.class, LOGINPLAYERCMD);
        MSG_ID_2_PROTO_MSG.put(ONPLAYERDISCONNECTCMD, SsPlayer.OnPlayerDisconnectCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayer.OnPlayerDisconnectCmd.class, ONPLAYERDISCONNECTCMD);
        MSG_ID_2_PROTO_MSG.put(RECVCLIENTMSGCMD, SsPlayer.RecvClientMsgCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayer.RecvClientMsgCmd.class, RECVCLIENTMSGCMD);
        MSG_ID_2_PROTO_MSG.put(KICKOFFPLAYERCMD, SsPlayer.KickOffPlayerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayer.KickOffPlayerCmd.class, KICKOFFPLAYERCMD);
        MSG_ID_2_PROTO_MSG.put(APPLYDATAPATCHCMD, SsPlayer.ApplyDataPatchCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayer.ApplyDataPatchCmd.class, APPLYDATAPATCHCMD);
        MSG_ID_2_PROTO_MSG.put(PREPAREJOINGROUPCHATASK, SsPlayerChat.PrepareJoinGroupChatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.PrepareJoinGroupChatAsk.class, PREPAREJOINGROUPCHATASK);
        MSG_ID_2_PROTO_MSG.put(PREPAREJOINGROUPCHATANS, SsPlayerChat.PrepareJoinGroupChatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.PrepareJoinGroupChatAns.class, PREPAREJOINGROUPCHATANS);
        MSG_ID_2_PROTO_MSG.put(CLEARPREPAREGROUPCHATCMD, SsPlayerChat.ClearPrepareGroupChatCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.ClearPrepareGroupChatCmd.class, CLEARPREPAREGROUPCHATCMD);
        MSG_ID_2_PROTO_MSG.put(JOINGROUPCHATCMD, SsPlayerChat.JoinGroupChatCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.JoinGroupChatCmd.class, JOINGROUPCHATCMD);
        MSG_ID_2_PROTO_MSG.put(LEAVEGROUPCHATCMD, SsPlayerChat.LeaveGroupChatCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.LeaveGroupChatCmd.class, LEAVEGROUPCHATCMD);
        MSG_ID_2_PROTO_MSG.put(HANDLENEWMESSAGEASK, SsPlayerChat.HandleNewMessageAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.HandleNewMessageAsk.class, HANDLENEWMESSAGEASK);
        MSG_ID_2_PROTO_MSG.put(HANDLENEWMESSAGEANS, SsPlayerChat.HandleNewMessageAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.HandleNewMessageAns.class, HANDLENEWMESSAGEANS);
        MSG_ID_2_PROTO_MSG.put(GROUPINFOCHANGENTF, SsPlayerChat.GroupInfoChangeNtf.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.GroupInfoChangeNtf.class, GROUPINFOCHANGENTF);
        MSG_ID_2_PROTO_MSG.put(GROUPDISMISSNTF, SsPlayerChat.GroupDismissNtf.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.GroupDismissNtf.class, GROUPDISMISSNTF);
        MSG_ID_2_PROTO_MSG.put(IGNOREMSGNTF, SsPlayerChat.IgnoreMsgNtf.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.IgnoreMsgNtf.class, IGNOREMSGNTF);
        MSG_ID_2_PROTO_MSG.put(TRYCLEARPREPARECHATNTF, SsPlayerChat.TryClearPrepareChatNtf.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.TryClearPrepareChatNtf.class, TRYCLEARPREPARECHATNTF);
        MSG_ID_2_PROTO_MSG.put(RECEIVEPRIVATEMSGASK, SsPlayerChat.ReceivePrivateMsgAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.ReceivePrivateMsgAsk.class, RECEIVEPRIVATEMSGASK);
        MSG_ID_2_PROTO_MSG.put(RECEIVEPRIVATEMSGANS, SsPlayerChat.ReceivePrivateMsgAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.ReceivePrivateMsgAns.class, RECEIVEPRIVATEMSGANS);
        MSG_ID_2_PROTO_MSG.put(GROUPCHATEXPIRECMD, SsPlayerChat.GroupChatExpireCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerChat.GroupChatExpireCmd.class, GROUPCHATEXPIRECMD);
        MSG_ID_2_PROTO_MSG.put(CLANAPPLYRESULTASK, SsPlayerClan.ClanApplyResultAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.ClanApplyResultAsk.class, CLANAPPLYRESULTASK);
        MSG_ID_2_PROTO_MSG.put(CLANAPPLYRESULTANS, SsPlayerClan.ClanApplyResultAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.ClanApplyResultAns.class, CLANAPPLYRESULTANS);
        MSG_ID_2_PROTO_MSG.put(ONNTFCLANKICKOFFRESULTCMD, SsPlayerClan.OnNtfClanKickOffResultCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnNtfClanKickOffResultCmd.class, ONNTFCLANKICKOFFRESULTCMD);
        MSG_ID_2_PROTO_MSG.put(ONCLANADDITIONUPDATECMD, SsPlayerClan.OnClanAdditionUpdateCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnClanAdditionUpdateCmd.class, ONCLANADDITIONUPDATECMD);
        MSG_ID_2_PROTO_MSG.put(ONCLANDEVBUFFUPDATECMD, SsPlayerClan.OnClanDevBuffUpdateCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnClanDevBuffUpdateCmd.class, ONCLANDEVBUFFUPDATECMD);
        MSG_ID_2_PROTO_MSG.put(ONADDCLANSCORECMD, SsPlayerClan.OnAddClanScoreCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnAddClanScoreCmd.class, ONADDCLANSCORECMD);
        MSG_ID_2_PROTO_MSG.put(ONADDCLANPOWERRESOURCECMD, SsPlayerClan.OnAddClanPowerResourceCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnAddClanPowerResourceCmd.class, ONADDCLANPOWERRESOURCECMD);
        MSG_ID_2_PROTO_MSG.put(ONCLANHELPHAPPENASK, SsPlayerClan.OnClanHelpHappenAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnClanHelpHappenAsk.class, ONCLANHELPHAPPENASK);
        MSG_ID_2_PROTO_MSG.put(ONCLANHELPHAPPENANS, SsPlayerClan.OnClanHelpHappenAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnClanHelpHappenAns.class, ONCLANHELPHAPPENANS);
        MSG_ID_2_PROTO_MSG.put(ONPLAYERNEEDCLANINFOCHANGECMD, SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnPlayerNeedClanInfoChangeCmd.class, ONPLAYERNEEDCLANINFOCHANGECMD);
        MSG_ID_2_PROTO_MSG.put(ONCLANTERRITORYLVCHANGECMD, SsPlayerClan.OnClanTerritoryLvChangeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerClan.OnClanTerritoryLvChangeCmd.class, ONCLANTERRITORYLVCHANGECMD);
        MSG_ID_2_PROTO_MSG.put(ONDUNGEONDESTROYCMD, SsPlayerDungeon.OnDungeonDestroyCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerDungeon.OnDungeonDestroyCmd.class, ONDUNGEONDESTROYCMD);
        MSG_ID_2_PROTO_MSG.put(ADDNEWFRIENDASK, SsPlayerFriend.AddNewFriendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.AddNewFriendAsk.class, ADDNEWFRIENDASK);
        MSG_ID_2_PROTO_MSG.put(ADDNEWFRIENDANS, SsPlayerFriend.AddNewFriendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.AddNewFriendAns.class, ADDNEWFRIENDANS);
        MSG_ID_2_PROTO_MSG.put(AGREEADDFRIENDASK, SsPlayerFriend.AgreeAddFriendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.AgreeAddFriendAsk.class, AGREEADDFRIENDASK);
        MSG_ID_2_PROTO_MSG.put(AGREEADDFRIENDANS, SsPlayerFriend.AgreeAddFriendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.AgreeAddFriendAns.class, AGREEADDFRIENDANS);
        MSG_ID_2_PROTO_MSG.put(REFUSEADDFRIENDASK, SsPlayerFriend.RefuseAddFriendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.RefuseAddFriendAsk.class, REFUSEADDFRIENDASK);
        MSG_ID_2_PROTO_MSG.put(REFUSEADDFRIENDANS, SsPlayerFriend.RefuseAddFriendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.RefuseAddFriendAns.class, REFUSEADDFRIENDANS);
        MSG_ID_2_PROTO_MSG.put(DELFRIENDASK, SsPlayerFriend.DelFriendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.DelFriendAsk.class, DELFRIENDASK);
        MSG_ID_2_PROTO_MSG.put(DELFRIENDANS, SsPlayerFriend.DelFriendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.DelFriendAns.class, DELFRIENDANS);
        MSG_ID_2_PROTO_MSG.put(JUDGEBESHIELDASK, SsPlayerFriend.JudgeBeShieldAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.JudgeBeShieldAsk.class, JUDGEBESHIELDASK);
        MSG_ID_2_PROTO_MSG.put(JUDGEBESHIELDANS, SsPlayerFriend.JudgeBeShieldAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerFriend.JudgeBeShieldAns.class, JUDGEBESHIELDANS);
        MSG_ID_2_PROTO_MSG.put(CONSUMEDIAMONDASK, SsPlayerIdip.ConsumeDiamondAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ConsumeDiamondAsk.class, CONSUMEDIAMONDASK);
        MSG_ID_2_PROTO_MSG.put(CONSUMEDIAMONDANS, SsPlayerIdip.ConsumeDiamondAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ConsumeDiamondAns.class, CONSUMEDIAMONDANS);
        MSG_ID_2_PROTO_MSG.put(CONSUMEITEMSASK, SsPlayerIdip.ConsumeItemsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ConsumeItemsAsk.class, CONSUMEITEMSASK);
        MSG_ID_2_PROTO_MSG.put(CONSUMEITEMSANS, SsPlayerIdip.ConsumeItemsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ConsumeItemsAns.class, CONSUMEITEMSANS);
        MSG_ID_2_PROTO_MSG.put(PULLMIDASCMD, SsPlayerIdip.PullMidasCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.PullMidasCmd.class, PULLMIDASCMD);
        MSG_ID_2_PROTO_MSG.put(RESOURCEASK, SsPlayerIdip.ResourceAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ResourceAsk.class, RESOURCEASK);
        MSG_ID_2_PROTO_MSG.put(RESOURCEANS, SsPlayerIdip.ResourceAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ResourceAns.class, RESOURCEANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYVIPEXPASK, SsPlayerIdip.ModifyVipExpAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ModifyVipExpAsk.class, MODIFYVIPEXPASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYVIPEXPANS, SsPlayerIdip.ModifyVipExpAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ModifyVipExpAns.class, MODIFYVIPEXPANS);
        MSG_ID_2_PROTO_MSG.put(MODIFYSOLDIERASK, SsPlayerIdip.ModifySoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ModifySoldierAsk.class, MODIFYSOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(MODIFYSOLDIERANS, SsPlayerIdip.ModifySoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerIdip.ModifySoldierAns.class, MODIFYSOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(KINGDOMOFFICECHANGECMD, SsPlayerKingdom.KingdomOfficeChangeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerKingdom.KingdomOfficeChangeCmd.class, KINGDOMOFFICECHANGECMD);
        MSG_ID_2_PROTO_MSG.put(KINGDOMSETTLEGAINTAXCMD, SsPlayerKingdom.KingdomSettleGainTaxCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerKingdom.KingdomSettleGainTaxCmd.class, KINGDOMSETTLEGAINTAXCMD);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDINFODETAILASK, SsPlayerMisc.QueryPlayerCardInfoDetailAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.QueryPlayerCardInfoDetailAsk.class, QUERYPLAYERCARDINFODETAILASK);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDINFODETAILANS, SsPlayerMisc.QueryPlayerCardInfoDetailAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.QueryPlayerCardInfoDetailAns.class, QUERYPLAYERCARDINFODETAILANS);
        MSG_ID_2_PROTO_MSG.put(BANPLAYERFIXMSASK, SsPlayerMisc.BanPlayerFixMsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.BanPlayerFixMsAsk.class, BANPLAYERFIXMSASK);
        MSG_ID_2_PROTO_MSG.put(BANPLAYERFIXMSANS, SsPlayerMisc.BanPlayerFixMsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.BanPlayerFixMsAns.class, BANPLAYERFIXMSANS);
        MSG_ID_2_PROTO_MSG.put(CANCELBANPLAYERASK, SsPlayerMisc.CancelBanPlayerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CancelBanPlayerAsk.class, CANCELBANPLAYERASK);
        MSG_ID_2_PROTO_MSG.put(CANCELBANPLAYERANS, SsPlayerMisc.CancelBanPlayerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CancelBanPlayerAns.class, CANCELBANPLAYERANS);
        MSG_ID_2_PROTO_MSG.put(GETSPYDATAASK, SsPlayerMisc.GetSpyDataAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetSpyDataAsk.class, GETSPYDATAASK);
        MSG_ID_2_PROTO_MSG.put(GETSPYDATAANS, SsPlayerMisc.GetSpyDataAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetSpyDataAns.class, GETSPYDATAANS);
        MSG_ID_2_PROTO_MSG.put(ENERGYROLLBACKCMD, SsPlayerMisc.EnergyRollbackCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.EnergyRollbackCmd.class, ENERGYROLLBACKCMD);
        MSG_ID_2_PROTO_MSG.put(KILLBIGSCENEMONSTERCMD, SsPlayerMisc.KillBigSceneMonsterCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.KillBigSceneMonsterCmd.class, KILLBIGSCENEMONSTERCMD);
        MSG_ID_2_PROTO_MSG.put(ADDREWARDCMD, SsPlayerMisc.AddRewardCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddRewardCmd.class, ADDREWARDCMD);
        MSG_ID_2_PROTO_MSG.put(MAINCITYDEFENDLOSECMD, SsPlayerMisc.MainCityDefendLoseCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.MainCityDefendLoseCmd.class, MAINCITYDEFENDLOSECMD);
        MSG_ID_2_PROTO_MSG.put(ONSETTLEROUNDCMD, SsPlayerMisc.OnSettleRoundCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnSettleRoundCmd.class, ONSETTLEROUNDCMD);
        MSG_ID_2_PROTO_MSG.put(ADDASSISTHISTORYCMD, SsPlayerMisc.AddAssistHistoryCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddAssistHistoryCmd.class, ADDASSISTHISTORYCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATESCENEPOWERCMD, SsPlayerMisc.UpdateScenePowerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.UpdateScenePowerCmd.class, UPDATESCENEPOWERCMD);
        MSG_ID_2_PROTO_MSG.put(ONBATTLERELATIONENDCMD, SsPlayerMisc.OnBattleRelationEndCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnBattleRelationEndCmd.class, ONBATTLERELATIONENDCMD);
        MSG_ID_2_PROTO_MSG.put(RECORDZONESNAPSHOTCMD, SsPlayerMisc.RecordZoneSnapshotCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.RecordZoneSnapshotCmd.class, RECORDZONESNAPSHOTCMD);
        MSG_ID_2_PROTO_MSG.put(ONRECEIVEMAILCMD, SsPlayerMisc.OnReceiveMailCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnReceiveMailCmd.class, ONRECEIVEMAILCMD);
        MSG_ID_2_PROTO_MSG.put(VIEWBUILDINGSASK, SsPlayerMisc.ViewBuildingsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ViewBuildingsAsk.class, VIEWBUILDINGSASK);
        MSG_ID_2_PROTO_MSG.put(VIEWBUILDINGSANS, SsPlayerMisc.ViewBuildingsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ViewBuildingsAns.class, VIEWBUILDINGSANS);
        MSG_ID_2_PROTO_MSG.put(GETIDIPPLAYERINFOASK, SsPlayerMisc.GetIdIpPlayerInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetIdIpPlayerInfoAsk.class, GETIDIPPLAYERINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETIDIPPLAYERINFOANS, SsPlayerMisc.GetIdIpPlayerInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetIdIpPlayerInfoAns.class, GETIDIPPLAYERINFOANS);
        MSG_ID_2_PROTO_MSG.put(SYNCPLANESTATUSWITHARMYBACKCMD, SsPlayerMisc.SyncPlaneStatusWithArmyBackCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SyncPlaneStatusWithArmyBackCmd.class, SYNCPLANESTATUSWITHARMYBACKCMD);
        MSG_ID_2_PROTO_MSG.put(ONARMYRETURNCMD, SsPlayerMisc.OnArmyReturnCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnArmyReturnCmd.class, ONARMYRETURNCMD);
        MSG_ID_2_PROTO_MSG.put(ONCITYBATTLEENDASK, SsPlayerMisc.OnCityBattleEndAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnCityBattleEndAsk.class, ONCITYBATTLEENDASK);
        MSG_ID_2_PROTO_MSG.put(ONCITYBATTLEENDANS, SsPlayerMisc.OnCityBattleEndAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnCityBattleEndAns.class, ONCITYBATTLEENDANS);
        MSG_ID_2_PROTO_MSG.put(ENTERBATTLECMD, SsPlayerMisc.EnterBattleCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.EnterBattleCmd.class, ENTERBATTLECMD);
        MSG_ID_2_PROTO_MSG.put(RELEASEPLANECMD, SsPlayerMisc.ReleasePlaneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ReleasePlaneCmd.class, RELEASEPLANECMD);
        MSG_ID_2_PROTO_MSG.put(RETURNTRANSPORTPLANECMD, SsPlayerMisc.ReturnTransportPlaneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ReturnTransportPlaneCmd.class, RETURNTRANSPORTPLANECMD);
        MSG_ID_2_PROTO_MSG.put(SENDSURVEYMAILCMD, SsPlayerMisc.SendSurveyMailCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SendSurveyMailCmd.class, SENDSURVEYMAILCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEREDDOTCMD, SsPlayerMisc.UpdateRedDotCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.UpdateRedDotCmd.class, UPDATEREDDOTCMD);
        MSG_ID_2_PROTO_MSG.put(SOLDIERNUMCMD, SsPlayerMisc.SoldierNumCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SoldierNumCmd.class, SOLDIERNUMCMD);
        MSG_ID_2_PROTO_MSG.put(CLANBUILDINGINFOCMD, SsPlayerMisc.ClanBuildingInfoCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ClanBuildingInfoCmd.class, CLANBUILDINGINFOCMD);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERKILLDETAILASK, SsPlayerMisc.QueryPlayerKillDetailAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.QueryPlayerKillDetailAsk.class, QUERYPLAYERKILLDETAILASK);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERKILLDETAILANS, SsPlayerMisc.QueryPlayerKillDetailAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.QueryPlayerKillDetailAns.class, QUERYPLAYERKILLDETAILANS);
        MSG_ID_2_PROTO_MSG.put(ONCOLLECTENDCMD, SsPlayerMisc.OnCollectEndCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnCollectEndCmd.class, ONCOLLECTENDCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTMILESTONESWITCHCMD, SsPlayerMisc.BroadcastMileStoneSwitchCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.BroadcastMileStoneSwitchCmd.class, BROADCASTMILESTONESWITCHCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTMILESTONERESETCMD, SsPlayerMisc.BroadcastMileStoneResetCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.BroadcastMileStoneResetCmd.class, BROADCASTMILESTONERESETCMD);
        MSG_ID_2_PROTO_MSG.put(MUTEPLAYERFIXMSASK, SsPlayerMisc.MutePlayerFixMsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.MutePlayerFixMsAsk.class, MUTEPLAYERFIXMSASK);
        MSG_ID_2_PROTO_MSG.put(MUTEPLAYERFIXMSANS, SsPlayerMisc.MutePlayerFixMsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.MutePlayerFixMsAns.class, MUTEPLAYERFIXMSANS);
        MSG_ID_2_PROTO_MSG.put(CANCELMUTEPLAYERASK, SsPlayerMisc.CancelMutePlayerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CancelMutePlayerAsk.class, CANCELMUTEPLAYERASK);
        MSG_ID_2_PROTO_MSG.put(CANCELMUTEPLAYERANS, SsPlayerMisc.CancelMutePlayerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CancelMutePlayerAns.class, CANCELMUTEPLAYERANS);
        MSG_ID_2_PROTO_MSG.put(RESETPLAYERINFOASK, SsPlayerMisc.ResetPlayerInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ResetPlayerInfoAsk.class, RESETPLAYERINFOASK);
        MSG_ID_2_PROTO_MSG.put(RESETPLAYERINFOANS, SsPlayerMisc.ResetPlayerInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.ResetPlayerInfoAns.class, RESETPLAYERINFOANS);
        MSG_ID_2_PROTO_MSG.put(SYNCSERVEROPENTSMSCMD, SsPlayerMisc.SyncServerOpenTsMsCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SyncServerOpenTsMsCmd.class, SYNCSERVEROPENTSMSCMD);
        MSG_ID_2_PROTO_MSG.put(GIVECURRENCYCMD, SsPlayerMisc.GiveCurrencyCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GiveCurrencyCmd.class, GIVECURRENCYCMD);
        MSG_ID_2_PROTO_MSG.put(SENDSYSASSISTCMD, SsPlayerMisc.SendSysAssistCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SendSysAssistCmd.class, SENDSYSASSISTCMD);
        MSG_ID_2_PROTO_MSG.put(CHECKIMURASK, SsPlayerMisc.CheckImurAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CheckImurAsk.class, CHECKIMURASK);
        MSG_ID_2_PROTO_MSG.put(CHECKIMURANS, SsPlayerMisc.CheckImurAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.CheckImurAns.class, CHECKIMURANS);
        MSG_ID_2_PROTO_MSG.put(SPYMOVECMD, SsPlayerMisc.SpyMoveCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.SpyMoveCmd.class, SPYMOVECMD);
        MSG_ID_2_PROTO_MSG.put(ADDDEVBUFFFROMSCENECMD, SsPlayerMisc.AddDevBuffFromSceneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddDevBuffFromSceneCmd.class, ADDDEVBUFFFROMSCENECMD);
        MSG_ID_2_PROTO_MSG.put(REMOVEDEVBUFFFROMSCENECMD, SsPlayerMisc.RemoveDevBuffFromSceneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.RemoveDevBuffFromSceneCmd.class, REMOVEDEVBUFFFROMSCENECMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEADDITIONFROMSCENECMD, SsPlayerMisc.UpdateAdditionFromSceneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.UpdateAdditionFromSceneCmd.class, UPDATEADDITIONFROMSCENECMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEADDITIONFROMZONECMD, SsPlayerMisc.UpdateAdditionFromZoneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.UpdateAdditionFromZoneCmd.class, UPDATEADDITIONFROMZONECMD);
        MSG_ID_2_PROTO_MSG.put(ADDCLANGIFTFORPLAYERCMD, SsPlayerMisc.AddClanGiftForPlayerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddClanGiftForPlayerCmd.class, ADDCLANGIFTFORPLAYERCMD);
        MSG_ID_2_PROTO_MSG.put(ONALLBATTLEENDCMD, SsPlayerMisc.OnAllBattleEndCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnAllBattleEndCmd.class, ONALLBATTLEENDCMD);
        MSG_ID_2_PROTO_MSG.put(ADDRESOURCEASSISTRECORDCMD, SsPlayerMisc.AddResourceAssistRecordCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddResourceAssistRecordCmd.class, ADDRESOURCEASSISTRECORDCMD);
        MSG_ID_2_PROTO_MSG.put(LOGOFFACCOUNTASK, SsPlayerMisc.LogOffAccountAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.LogOffAccountAsk.class, LOGOFFACCOUNTASK);
        MSG_ID_2_PROTO_MSG.put(LOGOFFACCOUNTANS, SsPlayerMisc.LogOffAccountAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.LogOffAccountAns.class, LOGOFFACCOUNTANS);
        MSG_ID_2_PROTO_MSG.put(GETLASTLOGINTIMEASK, SsPlayerMisc.GetLastLoginTimeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetLastLoginTimeAsk.class, GETLASTLOGINTIMEASK);
        MSG_ID_2_PROTO_MSG.put(GETLASTLOGINTIMEANS, SsPlayerMisc.GetLastLoginTimeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.GetLastLoginTimeAns.class, GETLASTLOGINTIMEANS);
        MSG_ID_2_PROTO_MSG.put(ADDRESOURCEAFTERPLUNDERCMD, SsPlayerMisc.AddResourceAfterPlunderCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.AddResourceAfterPlunderCmd.class, ADDRESOURCEAFTERPLUNDERCMD);
        MSG_ID_2_PROTO_MSG.put(ONCITYFALLCMD, SsPlayerMisc.OnCityFallCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnCityFallCmd.class, ONCITYFALLCMD);
        MSG_ID_2_PROTO_MSG.put(ONBASEBEATTACKCMD, SsPlayerMisc.OnBaseBeAttackCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.OnBaseBeAttackCmd.class, ONBASEBEATTACKCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATECONTACTCMD, SsPlayerMisc.UpdateContactCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.UpdateContactCmd.class, UPDATECONTACTCMD);
        MSG_ID_2_PROTO_MSG.put(MONSTERDEADNTFOWNERCMD, SsPlayerMisc.MonsterDeadNtfOwnerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerMisc.MonsterDeadNtfOwnerCmd.class, MONSTERDEADNTFOWNERCMD);
        MSG_ID_2_PROTO_MSG.put(MIDASCALLBACKASK, SsPlayerPayment.MidasCallbackAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerPayment.MidasCallbackAsk.class, MIDASCALLBACKASK);
        MSG_ID_2_PROTO_MSG.put(MIDASCALLBACKANS, SsPlayerPayment.MidasCallbackAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerPayment.MidasCallbackAns.class, MIDASCALLBACKANS);
        MSG_ID_2_PROTO_MSG.put(ONOCCUPYSUCCESSCMD, SsPlayerScene.OnOccupySuccessCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerScene.OnOccupySuccessCmd.class, ONOCCUPYSUCCESSCMD);
        MSG_ID_2_PROTO_MSG.put(COMSUMEASSETASK, SsPlayerScene.ComsumeAssetAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerScene.ComsumeAssetAsk.class, COMSUMEASSETASK);
        MSG_ID_2_PROTO_MSG.put(COMSUMEASSETANS, SsPlayerScene.ComsumeAssetAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerScene.ComsumeAssetAns.class, COMSUMEASSETANS);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDASK, SsPlayerCard.QueryPlayerCardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.QueryPlayerCardAsk.class, QUERYPLAYERCARDASK);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDANS, SsPlayerCard.QueryPlayerCardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.QueryPlayerCardAns.class, QUERYPLAYERCARDANS);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERCARDASK, SsPlayerCard.BatchQueryPlayerCardAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerCardAsk.class, BATCHQUERYPLAYERCARDASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERCARDANS, SsPlayerCard.BatchQueryPlayerCardAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerCardAns.class, BATCHQUERYPLAYERCARDANS);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDHEADASK, SsPlayerCard.QueryPlayerCardHeadAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.QueryPlayerCardHeadAsk.class, QUERYPLAYERCARDHEADASK);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERCARDHEADANS, SsPlayerCard.QueryPlayerCardHeadAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.QueryPlayerCardHeadAns.class, QUERYPLAYERCARDHEADANS);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERCARDHEADASK, SsPlayerCard.BatchQueryPlayerCardHeadAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerCardHeadAsk.class, BATCHQUERYPLAYERCARDHEADASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERCARDHEADANS, SsPlayerCard.BatchQueryPlayerCardHeadAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerCardHeadAns.class, BATCHQUERYPLAYERCARDHEADANS);
        MSG_ID_2_PROTO_MSG.put(UPDATEPLAYERCARDCMD, SsPlayerCard.UpdatePlayerCardCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.UpdatePlayerCardCmd.class, UPDATEPLAYERCARDCMD);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERZONEIDASK, SsPlayerCard.BatchQueryPlayerZoneIdAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerZoneIdAsk.class, BATCHQUERYPLAYERZONEIDASK);
        MSG_ID_2_PROTO_MSG.put(BATCHQUERYPLAYERZONEIDANS, SsPlayerCard.BatchQueryPlayerZoneIdAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPlayerCard.BatchQueryPlayerZoneIdAns.class, BATCHQUERYPLAYERZONEIDANS);
        MSG_ID_2_PROTO_MSG.put(PUSHSINGLENOTIFICATIONCMD, SsPushNotification.PushSingleNotificationCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPushNotification.PushSingleNotificationCmd.class, PUSHSINGLENOTIFICATIONCMD);
        MSG_ID_2_PROTO_MSG.put(PUSHMULTIPLENOTIFICATIONCMD, SsPushNotification.PushMultipleNotificationCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPushNotification.PushMultipleNotificationCmd.class, PUSHMULTIPLENOTIFICATIONCMD);
        MSG_ID_2_PROTO_MSG.put(PUSHTOPICNOTIFICATIONCMD, SsPushNotification.PushTopicNotificationCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsPushNotification.PushTopicNotificationCmd.class, PUSHTOPICNOTIFICATIONCMD);
        MSG_ID_2_PROTO_MSG.put(OPENRANKASK, SsRank.OpenRankAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.OpenRankAsk.class, OPENRANKASK);
        MSG_ID_2_PROTO_MSG.put(OPENRANKANS, SsRank.OpenRankAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.OpenRankAns.class, OPENRANKANS);
        MSG_ID_2_PROTO_MSG.put(GETTOPRANKINFOASK, SsRank.GetTopRankInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetTopRankInfoAsk.class, GETTOPRANKINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETTOPRANKINFOANS, SsRank.GetTopRankInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetTopRankInfoAns.class, GETTOPRANKINFOANS);
        MSG_ID_2_PROTO_MSG.put(GETRANKPAGEINFOASK, SsRank.GetRankPageInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankPageInfoAsk.class, GETRANKPAGEINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETRANKPAGEINFOANS, SsRank.GetRankPageInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankPageInfoAns.class, GETRANKPAGEINFOANS);
        MSG_ID_2_PROTO_MSG.put(RANKINFOBYRANKSASK, SsRank.RankInfoByRanksAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.RankInfoByRanksAsk.class, RANKINFOBYRANKSASK);
        MSG_ID_2_PROTO_MSG.put(RANKINFOBYRANKSANS, SsRank.RankInfoByRanksAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.RankInfoByRanksAns.class, RANKINFOBYRANKSANS);
        MSG_ID_2_PROTO_MSG.put(RANKINFOBYPLAYERSASK, SsRank.RankInfoByPlayersAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.RankInfoByPlayersAsk.class, RANKINFOBYPLAYERSASK);
        MSG_ID_2_PROTO_MSG.put(RANKINFOBYPLAYERSANS, SsRank.RankInfoByPlayersAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.RankInfoByPlayersAns.class, RANKINFOBYPLAYERSANS);
        MSG_ID_2_PROTO_MSG.put(GETRANKINFOBYLIMITASK, SsRank.GetRankInfoByLimitAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankInfoByLimitAsk.class, GETRANKINFOBYLIMITASK);
        MSG_ID_2_PROTO_MSG.put(GETRANKINFOBYLIMITANS, SsRank.GetRankInfoByLimitAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankInfoByLimitAns.class, GETRANKINFOBYLIMITANS);
        MSG_ID_2_PROTO_MSG.put(UPDATERANKINGCMD, SsRank.UpdateRankingCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.UpdateRankingCmd.class, UPDATERANKINGCMD);
        MSG_ID_2_PROTO_MSG.put(DELETERANKINGCMD, SsRank.DeleteRankingCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.DeleteRankingCmd.class, DELETERANKINGCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEBATCHRANKINGASK, SsRank.UpdateBatchRankingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.UpdateBatchRankingAsk.class, UPDATEBATCHRANKINGASK);
        MSG_ID_2_PROTO_MSG.put(UPDATEBATCHRANKINGANS, SsRank.UpdateBatchRankingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.UpdateBatchRankingAns.class, UPDATEBATCHRANKINGANS);
        MSG_ID_2_PROTO_MSG.put(CLEARRANKCMD, SsRank.ClearRankCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.ClearRankCmd.class, CLEARRANKCMD);
        MSG_ID_2_PROTO_MSG.put(GETRANGEASK, SsRank.GetRangeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRangeAsk.class, GETRANGEASK);
        MSG_ID_2_PROTO_MSG.put(GETRANGEANS, SsRank.GetRangeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRangeAns.class, GETRANGEANS);
        MSG_ID_2_PROTO_MSG.put(DELETERANKANDGETTOPASK, SsRank.DeleteRankAndGetTopAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.DeleteRankAndGetTopAsk.class, DELETERANKANDGETTOPASK);
        MSG_ID_2_PROTO_MSG.put(DELETERANKANDGETTOPANS, SsRank.DeleteRankAndGetTopAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.DeleteRankAndGetTopAns.class, DELETERANKANDGETTOPANS);
        MSG_ID_2_PROTO_MSG.put(DELETEALLRANKABOUTMECMD, SsRank.DeleteAllRankAboutMeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.DeleteAllRankAboutMeCmd.class, DELETEALLRANKABOUTMECMD);
        MSG_ID_2_PROTO_MSG.put(GETALLTARGETRANKASK, SsRank.GetAllTargetRankAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetAllTargetRankAsk.class, GETALLTARGETRANKASK);
        MSG_ID_2_PROTO_MSG.put(GETALLTARGETRANKANS, SsRank.GetAllTargetRankAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetAllTargetRankAns.class, GETALLTARGETRANKANS);
        MSG_ID_2_PROTO_MSG.put(GETRANKINFOBYPLAYERASK, SsRank.GetRankInfoByPlayerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankInfoByPlayerAsk.class, GETRANKINFOBYPLAYERASK);
        MSG_ID_2_PROTO_MSG.put(GETRANKINFOBYPLAYERANS, SsRank.GetRankInfoByPlayerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsRank.GetRankInfoByPlayerAns.class, GETRANKINFOBYPLAYERANS);
        MSG_ID_2_PROTO_MSG.put(ENABLEACTIVITYEFFECTASK, SsSceneActivity.EnableActivityEffectAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivity.EnableActivityEffectAsk.class, ENABLEACTIVITYEFFECTASK);
        MSG_ID_2_PROTO_MSG.put(ENABLEACTIVITYEFFECTANS, SsSceneActivity.EnableActivityEffectAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivity.EnableActivityEffectAns.class, ENABLEACTIVITYEFFECTANS);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERFETCHASK, SsSceneActivitySchedule.BestCommanderFetchAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderFetchAsk.class, BESTCOMMANDERFETCHASK);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERFETCHANS, SsSceneActivitySchedule.BestCommanderFetchAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderFetchAns.class, BESTCOMMANDERFETCHANS);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERHISTORYRANKASK, SsSceneActivitySchedule.BestCommanderHistoryRankAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderHistoryRankAsk.class, BESTCOMMANDERHISTORYRANKASK);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERHISTORYRANKANS, SsSceneActivitySchedule.BestCommanderHistoryRankAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderHistoryRankAns.class, BESTCOMMANDERHISTORYRANKANS);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERCHOOSEITEMASK, SsSceneActivitySchedule.BestCommanderChooseItemAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderChooseItemAsk.class, BESTCOMMANDERCHOOSEITEMASK);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERCHOOSEITEMANS, SsSceneActivitySchedule.BestCommanderChooseItemAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderChooseItemAns.class, BESTCOMMANDERCHOOSEITEMANS);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERGETVOLUMEASK, SsSceneActivitySchedule.BestCommanderGetVolumeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderGetVolumeAsk.class, BESTCOMMANDERGETVOLUMEASK);
        MSG_ID_2_PROTO_MSG.put(BESTCOMMANDERGETVOLUMEANS, SsSceneActivitySchedule.BestCommanderGetVolumeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.BestCommanderGetVolumeAns.class, BESTCOMMANDERGETVOLUMEANS);
        MSG_ID_2_PROTO_MSG.put(GETLOTTERYINFOASK, SsSceneActivitySchedule.GetLotteryInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.GetLotteryInfoAsk.class, GETLOTTERYINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETLOTTERYINFOANS, SsSceneActivitySchedule.GetLotteryInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneActivitySchedule.GetLotteryInfoAns.class, GETLOTTERYINFOANS);
        MSG_ID_2_PROTO_MSG.put(REPAIRCITYWALLASK, SsSceneCityArmy.RepairCityWallAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.RepairCityWallAsk.class, REPAIRCITYWALLASK);
        MSG_ID_2_PROTO_MSG.put(REPAIRCITYWALLANS, SsSceneCityArmy.RepairCityWallAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.RepairCityWallAns.class, REPAIRCITYWALLANS);
        MSG_ID_2_PROTO_MSG.put(OUTFIRECITYWALLASK, SsSceneCityArmy.OutFireCityWallAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.OutFireCityWallAsk.class, OUTFIRECITYWALLASK);
        MSG_ID_2_PROTO_MSG.put(OUTFIRECITYWALLANS, SsSceneCityArmy.OutFireCityWallAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.OutFireCityWallAns.class, OUTFIRECITYWALLANS);
        MSG_ID_2_PROTO_MSG.put(SETCITYFALLASK, SsSceneCityArmy.SetCityFallAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.SetCityFallAsk.class, SETCITYFALLASK);
        MSG_ID_2_PROTO_MSG.put(SETCITYFALLANS, SsSceneCityArmy.SetCityFallAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.SetCityFallAns.class, SETCITYFALLANS);
        MSG_ID_2_PROTO_MSG.put(MOVECITYFIXEDASK, SsSceneCityArmy.MoveCityFixedAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityFixedAsk.class, MOVECITYFIXEDASK);
        MSG_ID_2_PROTO_MSG.put(MOVECITYFIXEDANS, SsSceneCityArmy.MoveCityFixedAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityFixedAns.class, MOVECITYFIXEDANS);
        MSG_ID_2_PROTO_MSG.put(MOVECITYRANDOMASK, SsSceneCityArmy.MoveCityRandomAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityRandomAsk.class, MOVECITYRANDOMASK);
        MSG_ID_2_PROTO_MSG.put(MOVECITYRANDOMANS, SsSceneCityArmy.MoveCityRandomAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityRandomAns.class, MOVECITYRANDOMANS);
        MSG_ID_2_PROTO_MSG.put(MOVECITYVERIFYASK, SsSceneCityArmy.MoveCityVerifyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityVerifyAsk.class, MOVECITYVERIFYASK);
        MSG_ID_2_PROTO_MSG.put(MOVECITYVERIFYANS, SsSceneCityArmy.MoveCityVerifyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.MoveCityVerifyAns.class, MOVECITYVERIFYANS);
        MSG_ID_2_PROTO_MSG.put(CREATEARMYCHECKASK, SsSceneCityArmy.CreateArmyCheckAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.CreateArmyCheckAsk.class, CREATEARMYCHECKASK);
        MSG_ID_2_PROTO_MSG.put(CREATEARMYCHECKANS, SsSceneCityArmy.CreateArmyCheckAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.CreateArmyCheckAns.class, CREATEARMYCHECKANS);
        MSG_ID_2_PROTO_MSG.put(CREATEPLAYERARMYASK, SsSceneCityArmy.CreatePlayerArmyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.CreatePlayerArmyAsk.class, CREATEPLAYERARMYASK);
        MSG_ID_2_PROTO_MSG.put(CREATEPLAYERARMYANS, SsSceneCityArmy.CreatePlayerArmyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.CreatePlayerArmyAns.class, CREATEPLAYERARMYANS);
        MSG_ID_2_PROTO_MSG.put(CHANGEARMYACTIONCHECKASK, SsSceneCityArmy.ChangeArmyActionCheckAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ChangeArmyActionCheckAsk.class, CHANGEARMYACTIONCHECKASK);
        MSG_ID_2_PROTO_MSG.put(CHANGEARMYACTIONCHECKANS, SsSceneCityArmy.ChangeArmyActionCheckAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ChangeArmyActionCheckAns.class, CHANGEARMYACTIONCHECKANS);
        MSG_ID_2_PROTO_MSG.put(CHANGEPLAYERARMYACTIONASK, SsSceneCityArmy.ChangePlayerArmyActionAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ChangePlayerArmyActionAsk.class, CHANGEPLAYERARMYACTIONASK);
        MSG_ID_2_PROTO_MSG.put(CHANGEPLAYERARMYACTIONANS, SsSceneCityArmy.ChangePlayerArmyActionAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ChangePlayerArmyActionAns.class, CHANGEPLAYERARMYACTIONANS);
        MSG_ID_2_PROTO_MSG.put(FORCEDDEFEATARMYASK, SsSceneCityArmy.ForcedDefeatArmyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ForcedDefeatArmyAsk.class, FORCEDDEFEATARMYASK);
        MSG_ID_2_PROTO_MSG.put(FORCEDDEFEATARMYANS, SsSceneCityArmy.ForcedDefeatArmyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCityArmy.ForcedDefeatArmyAns.class, FORCEDDEFEATARMYANS);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYPAGEASK, SsSceneClan.FetchTerritoryPageAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchTerritoryPageAsk.class, FETCHTERRITORYPAGEASK);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYPAGEANS, SsSceneClan.FetchTerritoryPageAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchTerritoryPageAns.class, FETCHTERRITORYPAGEANS);
        MSG_ID_2_PROTO_MSG.put(ABANDONCLANMAPBUILDINGASK, SsSceneClan.AbandonClanMapBuildingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.AbandonClanMapBuildingAsk.class, ABANDONCLANMAPBUILDINGASK);
        MSG_ID_2_PROTO_MSG.put(ABANDONCLANMAPBUILDINGANS, SsSceneClan.AbandonClanMapBuildingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.AbandonClanMapBuildingAns.class, ABANDONCLANMAPBUILDINGANS);
        MSG_ID_2_PROTO_MSG.put(ONCLANCREATEDCMD, SsSceneClan.OnClanCreatedCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.OnClanCreatedCmd.class, ONCLANCREATEDCMD);
        MSG_ID_2_PROTO_MSG.put(ONCLANDISSOLUTIONCMD, SsSceneClan.OnClanDissolutionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.OnClanDissolutionCmd.class, ONCLANDISSOLUTIONCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCSCENECLANCMD, SsSceneClan.SyncSceneClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.SyncSceneClanCmd.class, SYNCSCENECLANCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCCLANACTORSTATUSCMD, SsSceneClan.SyncClanActorStatusCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.SyncClanActorStatusCmd.class, SYNCCLANACTORSTATUSCMD);
        MSG_ID_2_PROTO_MSG.put(CONSTRUCTCLANBUILDINGASK, SsSceneClan.ConstructClanBuildingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.ConstructClanBuildingAsk.class, CONSTRUCTCLANBUILDINGASK);
        MSG_ID_2_PROTO_MSG.put(CONSTRUCTCLANBUILDINGANS, SsSceneClan.ConstructClanBuildingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.ConstructClanBuildingAns.class, CONSTRUCTCLANBUILDINGANS);
        MSG_ID_2_PROTO_MSG.put(DESTROYCLANBUILDINGASK, SsSceneClan.DestroyClanBuildingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.DestroyClanBuildingAsk.class, DESTROYCLANBUILDINGASK);
        MSG_ID_2_PROTO_MSG.put(DESTROYCLANBUILDINGANS, SsSceneClan.DestroyClanBuildingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.DestroyClanBuildingAns.class, DESTROYCLANBUILDINGANS);
        MSG_ID_2_PROTO_MSG.put(EXTINGUISHBUILDINGFIREASK, SsSceneClan.ExtinguishBuildingFireAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.ExtinguishBuildingFireAsk.class, EXTINGUISHBUILDINGFIREASK);
        MSG_ID_2_PROTO_MSG.put(EXTINGUISHBUILDINGFIREANS, SsSceneClan.ExtinguishBuildingFireAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.ExtinguishBuildingFireAns.class, EXTINGUISHBUILDINGFIREANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANBUILDINGHPASK, SsSceneClan.FetchClanBuildingHpAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchClanBuildingHpAsk.class, FETCHCLANBUILDINGHPASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANBUILDINGHPANS, SsSceneClan.FetchClanBuildingHpAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchClanBuildingHpAns.class, FETCHCLANBUILDINGHPANS);
        MSG_ID_2_PROTO_MSG.put(CHECKCANFREEREBUILDASK, SsSceneClan.CheckCanFreeRebuildAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.CheckCanFreeRebuildAsk.class, CHECKCANFREEREBUILDASK);
        MSG_ID_2_PROTO_MSG.put(CHECKCANFREEREBUILDANS, SsSceneClan.CheckCanFreeRebuildAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.CheckCanFreeRebuildAns.class, CHECKCANFREEREBUILDANS);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANREBUILDASK, SsSceneClan.VerifyCanRebuildAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanRebuildAsk.class, VERIFYCANREBUILDASK);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANREBUILDANS, SsSceneClan.VerifyCanRebuildAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanRebuildAns.class, VERIFYCANREBUILDANS);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANEXTINGUISHASK, SsSceneClan.VerifyCanExtinguishAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanExtinguishAsk.class, VERIFYCANEXTINGUISHASK);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANEXTINGUISHANS, SsSceneClan.VerifyCanExtinguishAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanExtinguishAns.class, VERIFYCANEXTINGUISHANS);
        MSG_ID_2_PROTO_MSG.put(SYNCCLANADDITIONCMD, SsSceneClan.SyncClanAdditionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.SyncClanAdditionCmd.class, SYNCCLANADDITIONCMD);
        MSG_ID_2_PROTO_MSG.put(ASKFORCLANRECOMMENDASK, SsSceneClan.AskForClanRecommendAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.AskForClanRecommendAsk.class, ASKFORCLANRECOMMENDASK);
        MSG_ID_2_PROTO_MSG.put(ASKFORCLANRECOMMENDANS, SsSceneClan.AskForClanRecommendAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.AskForClanRecommendAns.class, ASKFORCLANRECOMMENDANS);
        MSG_ID_2_PROTO_MSG.put(FETCHDEFAULTCLANLISTASK, SsSceneClan.FetchDefaultClanListAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchDefaultClanListAsk.class, FETCHDEFAULTCLANLISTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHDEFAULTCLANLISTANS, SsSceneClan.FetchDefaultClanListAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchDefaultClanListAns.class, FETCHDEFAULTCLANLISTANS);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANPLACECLANRESASK, SsSceneClan.VerifyCanPlaceClanResAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanPlaceClanResAsk.class, VERIFYCANPLACECLANRESASK);
        MSG_ID_2_PROTO_MSG.put(VERIFYCANPLACECLANRESANS, SsSceneClan.VerifyCanPlaceClanResAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.VerifyCanPlaceClanResAns.class, VERIFYCANPLACECLANRESANS);
        MSG_ID_2_PROTO_MSG.put(PLACECLANRESBUILDINSCENEASK, SsSceneClan.PlaceClanResBuildInSceneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.PlaceClanResBuildInSceneAsk.class, PLACECLANRESBUILDINSCENEASK);
        MSG_ID_2_PROTO_MSG.put(PLACECLANRESBUILDINSCENEANS, SsSceneClan.PlaceClanResBuildInSceneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.PlaceClanResBuildInSceneAns.class, PLACECLANRESBUILDINSCENEANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANRESBUILDSIMPLEINFOASK, SsSceneClan.FetchClanResBuildSimpleInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchClanResBuildSimpleInfoAsk.class, FETCHCLANRESBUILDSIMPLEINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANRESBUILDSIMPLEINFOANS, SsSceneClan.FetchClanResBuildSimpleInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.FetchClanResBuildSimpleInfoAns.class, FETCHCLANRESBUILDSIMPLEINFOANS);
        MSG_ID_2_PROTO_MSG.put(CHECKPLAYERCITYINTERRITORYASK, SsSceneClan.CheckPlayerCityInTerritoryAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.CheckPlayerCityInTerritoryAsk.class, CHECKPLAYERCITYINTERRITORYASK);
        MSG_ID_2_PROTO_MSG.put(CHECKPLAYERCITYINTERRITORYANS, SsSceneClan.CheckPlayerCityInTerritoryAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.CheckPlayerCityInTerritoryAns.class, CHECKPLAYERCITYINTERRITORYANS);
        MSG_ID_2_PROTO_MSG.put(ADDDEVBUFFFROMCLANCMD, SsSceneClan.AddDevBuffFromClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.AddDevBuffFromClanCmd.class, ADDDEVBUFFFROMCLANCMD);
        MSG_ID_2_PROTO_MSG.put(REMOVEDEVBUFFFROMCLANCMD, SsSceneClan.RemoveDevBuffFromClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.RemoveDevBuffFromClanCmd.class, REMOVEDEVBUFFFROMCLANCMD);
        MSG_ID_2_PROTO_MSG.put(UPDATEADDITIONFROMCLANCMD, SsSceneClan.UpdateAdditionFromClanCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.UpdateAdditionFromClanCmd.class, UPDATEADDITIONFROMCLANCMD);
        MSG_ID_2_PROTO_MSG.put(GETCLANCOMMANDCENTERNUMASK, SsSceneClan.GetClanCommandCenterNumAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.GetClanCommandCenterNumAsk.class, GETCLANCOMMANDCENTERNUMASK);
        MSG_ID_2_PROTO_MSG.put(GETCLANCOMMANDCENTERNUMANS, SsSceneClan.GetClanCommandCenterNumAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneClan.GetClanCommandCenterNumAns.class, GETCLANCOMMANDCENTERNUMANS);
        MSG_ID_2_PROTO_MSG.put(SEARCHRESOURCEASK, SsSceneCollect.SearchResourceAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCollect.SearchResourceAsk.class, SEARCHRESOURCEASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHRESOURCEANS, SsSceneCollect.SearchResourceAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneCollect.SearchResourceAns.class, SEARCHRESOURCEANS);
        MSG_ID_2_PROTO_MSG.put(CREATEDUNGEONASK, SsSceneDungeon.CreateDungeonAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.CreateDungeonAsk.class, CREATEDUNGEONASK);
        MSG_ID_2_PROTO_MSG.put(CREATEDUNGEONANS, SsSceneDungeon.CreateDungeonAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.CreateDungeonAns.class, CREATEDUNGEONANS);
        MSG_ID_2_PROTO_MSG.put(ENTERDUNGEONASK, SsSceneDungeon.EnterDungeonAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.EnterDungeonAsk.class, ENTERDUNGEONASK);
        MSG_ID_2_PROTO_MSG.put(ENTERDUNGEONANS, SsSceneDungeon.EnterDungeonAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.EnterDungeonAns.class, ENTERDUNGEONANS);
        MSG_ID_2_PROTO_MSG.put(LEAVEDUNGEONASK, SsSceneDungeon.LeaveDungeonAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.LeaveDungeonAsk.class, LEAVEDUNGEONASK);
        MSG_ID_2_PROTO_MSG.put(LEAVEDUNGEONANS, SsSceneDungeon.LeaveDungeonAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.LeaveDungeonAns.class, LEAVEDUNGEONANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERLOGINASK, SsSceneDungeon.PlayerLoginAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PlayerLoginAsk.class, PLAYERLOGINASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERLOGINANS, SsSceneDungeon.PlayerLoginAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PlayerLoginAns.class, PLAYERLOGINANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERLOGOUTASK, SsSceneDungeon.PlayerLogoutAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PlayerLogoutAsk.class, PLAYERLOGOUTASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERLOGOUTANS, SsSceneDungeon.PlayerLogoutAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PlayerLogoutAns.class, PLAYERLOGOUTANS);
        MSG_ID_2_PROTO_MSG.put(PERFORMACTIONASK, SsSceneDungeon.PerformActionAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PerformActionAsk.class, PERFORMACTIONASK);
        MSG_ID_2_PROTO_MSG.put(PERFORMACTIONANS, SsSceneDungeon.PerformActionAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneDungeon.PerformActionAns.class, PERFORMACTIONANS);
        MSG_ID_2_PROTO_MSG.put(SOLDIERASK, SsSceneIdip.SoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneIdip.SoldierAsk.class, SOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(SOLDIERANS, SsSceneIdip.SoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneIdip.SoldierAns.class, SOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(GETONLINEPLAYERIDASK, SsSceneInfoMgr.GetOnlinePlayerIdAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetOnlinePlayerIdAsk.class, GETONLINEPLAYERIDASK);
        MSG_ID_2_PROTO_MSG.put(GETONLINEPLAYERIDANS, SsSceneInfoMgr.GetOnlinePlayerIdAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetOnlinePlayerIdAns.class, GETONLINEPLAYERIDANS);
        MSG_ID_2_PROTO_MSG.put(GETONLINEPLAYERIDFILTERBYCREATETIMEASK, SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAsk.class, GETONLINEPLAYERIDFILTERBYCREATETIMEASK);
        MSG_ID_2_PROTO_MSG.put(GETONLINEPLAYERIDFILTERBYCREATETIMEANS, SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetOnlinePlayerIdFilterByCreateTimeAns.class, GETONLINEPLAYERIDFILTERBYCREATETIMEANS);
        MSG_ID_2_PROTO_MSG.put(CHECKPLAYERONLINEASK, SsSceneInfoMgr.CheckPlayerOnlineAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.CheckPlayerOnlineAsk.class, CHECKPLAYERONLINEASK);
        MSG_ID_2_PROTO_MSG.put(CHECKPLAYERONLINEANS, SsSceneInfoMgr.CheckPlayerOnlineAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.CheckPlayerOnlineAns.class, CHECKPLAYERONLINEANS);
        MSG_ID_2_PROTO_MSG.put(GETZONEIPPORTASK, SsSceneInfoMgr.GetZoneIpPortAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetZoneIpPortAsk.class, GETZONEIPPORTASK);
        MSG_ID_2_PROTO_MSG.put(GETZONEIPPORTANS, SsSceneInfoMgr.GetZoneIpPortAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneInfoMgr.GetZoneIpPortAns.class, GETZONEIPPORTANS);
        MSG_ID_2_PROTO_MSG.put(KINGAPPOINTASK, SsSceneKingdom.KingAppointAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingAppointAsk.class, KINGAPPOINTASK);
        MSG_ID_2_PROTO_MSG.put(KINGAPPOINTANS, SsSceneKingdom.KingAppointAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingAppointAns.class, KINGAPPOINTANS);
        MSG_ID_2_PROTO_MSG.put(KINGOPENBUFFASK, SsSceneKingdom.KingOpenBuffAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingOpenBuffAsk.class, KINGOPENBUFFASK);
        MSG_ID_2_PROTO_MSG.put(KINGOPENBUFFANS, SsSceneKingdom.KingOpenBuffAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingOpenBuffAns.class, KINGOPENBUFFANS);
        MSG_ID_2_PROTO_MSG.put(KINGSENDGIFTASK, SsSceneKingdom.KingSendGiftAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingSendGiftAsk.class, KINGSENDGIFTASK);
        MSG_ID_2_PROTO_MSG.put(KINGSENDGIFTANS, SsSceneKingdom.KingSendGiftAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingSendGiftAns.class, KINGSENDGIFTANS);
        MSG_ID_2_PROTO_MSG.put(KINGCHECKCANUSESKILLASK, SsSceneKingdom.KingCheckCanUseSkillAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingCheckCanUseSkillAsk.class, KINGCHECKCANUSESKILLASK);
        MSG_ID_2_PROTO_MSG.put(KINGCHECKCANUSESKILLANS, SsSceneKingdom.KingCheckCanUseSkillAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingCheckCanUseSkillAns.class, KINGCHECKCANUSESKILLANS);
        MSG_ID_2_PROTO_MSG.put(KINGUSESKILLASK, SsSceneKingdom.KingUseSkillAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingUseSkillAsk.class, KINGUSESKILLASK);
        MSG_ID_2_PROTO_MSG.put(KINGUSESKILLANS, SsSceneKingdom.KingUseSkillAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.KingUseSkillAns.class, KINGUSESKILLANS);
        MSG_ID_2_PROTO_MSG.put(FETCHHISTORYKINGASK, SsSceneKingdom.FetchHistoryKingAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchHistoryKingAsk.class, FETCHHISTORYKINGASK);
        MSG_ID_2_PROTO_MSG.put(FETCHHISTORYKINGANS, SsSceneKingdom.FetchHistoryKingAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchHistoryKingAns.class, FETCHHISTORYKINGANS);
        MSG_ID_2_PROTO_MSG.put(FETCHKINGDOMGIFTASK, SsSceneKingdom.FetchKingdomGiftAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchKingdomGiftAsk.class, FETCHKINGDOMGIFTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHKINGDOMGIFTANS, SsSceneKingdom.FetchKingdomGiftAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchKingdomGiftAns.class, FETCHKINGDOMGIFTANS);
        MSG_ID_2_PROTO_MSG.put(FETCHKINGDOMOFFICEASK, SsSceneKingdom.FetchKingdomOfficeAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchKingdomOfficeAsk.class, FETCHKINGDOMOFFICEASK);
        MSG_ID_2_PROTO_MSG.put(FETCHKINGDOMOFFICEANS, SsSceneKingdom.FetchKingdomOfficeAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneKingdom.FetchKingdomOfficeAns.class, FETCHKINGDOMOFFICEANS);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYEROFFLINEMAILSASK, SsSceneMail.SyncPlayerOfflineMailsAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMail.SyncPlayerOfflineMailsAsk.class, SYNCPLAYEROFFLINEMAILSASK);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYEROFFLINEMAILSANS, SsSceneMail.SyncPlayerOfflineMailsAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMail.SyncPlayerOfflineMailsAns.class, SYNCPLAYEROFFLINEMAILSANS);
        MSG_ID_2_PROTO_MSG.put(SENDZONEMAILCMD, SsSceneMail.SendZoneMailCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMail.SendZoneMailCmd.class, SENDZONEMAILCMD);
        MSG_ID_2_PROTO_MSG.put(EXECUTESCENEGMASK, SsSceneMap.ExecuteSceneGmAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.ExecuteSceneGmAsk.class, EXECUTESCENEGMASK);
        MSG_ID_2_PROTO_MSG.put(EXECUTESCENEGMANS, SsSceneMap.ExecuteSceneGmAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.ExecuteSceneGmAns.class, EXECUTESCENEGMANS);
        MSG_ID_2_PROTO_MSG.put(SEARCHPATHASK, SsSceneMap.SearchPathAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.SearchPathAsk.class, SEARCHPATHASK);
        MSG_ID_2_PROTO_MSG.put(SEARCHPATHANS, SsSceneMap.SearchPathAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.SearchPathAns.class, SEARCHPATHANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCITYPOINTLISTASK, SsSceneMap.FetchClanCityPointListAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchClanCityPointListAsk.class, FETCHCLANCITYPOINTLISTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCLANCITYPOINTLISTANS, SsSceneMap.FetchClanCityPointListAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchClanCityPointListAns.class, FETCHCLANCITYPOINTLISTANS);
        MSG_ID_2_PROTO_MSG.put(FETCHSINGLECLANMEMBERCITYPOINTASK, SsSceneMap.FetchSingleClanMemberCityPointAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchSingleClanMemberCityPointAsk.class, FETCHSINGLECLANMEMBERCITYPOINTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHSINGLECLANMEMBERCITYPOINTANS, SsSceneMap.FetchSingleClanMemberCityPointAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchSingleClanMemberCityPointAns.class, FETCHSINGLECLANMEMBERCITYPOINTANS);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYMAPASK, SsSceneMap.FetchTerritoryMapAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchTerritoryMapAsk.class, FETCHTERRITORYMAPASK);
        MSG_ID_2_PROTO_MSG.put(FETCHTERRITORYMAPANS, SsSceneMap.FetchTerritoryMapAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMap.FetchTerritoryMapAns.class, FETCHTERRITORYMAPANS);
        MSG_ID_2_PROTO_MSG.put(SENDSCENEMARQUEECMD, SsSceneMarquee.SendSceneMarqueeCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMarquee.SendSceneMarqueeCmd.class, SENDSCENEMARQUEECMD);
        MSG_ID_2_PROTO_MSG.put(SENDSCENELOOPMARQUEEWITHIDIPASK, SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAsk.class, SENDSCENELOOPMARQUEEWITHIDIPASK);
        MSG_ID_2_PROTO_MSG.put(SENDSCENELOOPMARQUEEWITHIDIPANS, SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneMarquee.SendSceneLoopMarqueeWithIdIpAns.class, SENDSCENELOOPMARQUEEWITHIDIPANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERSEARCHMONSTERASK, SsSceneObj.PlayerSearchMonsterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.PlayerSearchMonsterAsk.class, PLAYERSEARCHMONSTERASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERSEARCHMONSTERANS, SsSceneObj.PlayerSearchMonsterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.PlayerSearchMonsterAns.class, PLAYERSEARCHMONSTERANS);
        MSG_ID_2_PROTO_MSG.put(ADDMONSTERASK, SsSceneObj.AddMonsterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.AddMonsterAsk.class, ADDMONSTERASK);
        MSG_ID_2_PROTO_MSG.put(ADDMONSTERANS, SsSceneObj.AddMonsterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.AddMonsterAns.class, ADDMONSTERANS);
        MSG_ID_2_PROTO_MSG.put(CHECKCANBEATTACKASK, SsSceneObj.CheckCanBeAttackAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.CheckCanBeAttackAsk.class, CHECKCANBEATTACKASK);
        MSG_ID_2_PROTO_MSG.put(CHECKCANBEATTACKANS, SsSceneObj.CheckCanBeAttackAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.CheckCanBeAttackAns.class, CHECKCANBEATTACKANS);
        MSG_ID_2_PROTO_MSG.put(QUERYMAPBUILDINGIDASK, SsSceneObj.QueryMapBuildingIdAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.QueryMapBuildingIdAsk.class, QUERYMAPBUILDINGIDASK);
        MSG_ID_2_PROTO_MSG.put(QUERYMAPBUILDINGIDANS, SsSceneObj.QueryMapBuildingIdAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.QueryMapBuildingIdAns.class, QUERYMAPBUILDINGIDANS);
        MSG_ID_2_PROTO_MSG.put(GETMONSTERNUMASK, SsSceneObj.GetMonsterNumAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.GetMonsterNumAsk.class, GETMONSTERNUMASK);
        MSG_ID_2_PROTO_MSG.put(GETMONSTERNUMANS, SsSceneObj.GetMonsterNumAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.GetMonsterNumAns.class, GETMONSTERNUMANS);
        MSG_ID_2_PROTO_MSG.put(REFRESHACTMONSTERASK, SsSceneObj.RefreshActMonsterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.RefreshActMonsterAsk.class, REFRESHACTMONSTERASK);
        MSG_ID_2_PROTO_MSG.put(REFRESHACTMONSTERANS, SsSceneObj.RefreshActMonsterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.RefreshActMonsterAns.class, REFRESHACTMONSTERANS);
        MSG_ID_2_PROTO_MSG.put(SUMMONSKYNETMONSTERASK, SsSceneObj.SummonSkynetMonsterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.SummonSkynetMonsterAsk.class, SUMMONSKYNETMONSTERASK);
        MSG_ID_2_PROTO_MSG.put(SUMMONSKYNETMONSTERANS, SsSceneObj.SummonSkynetMonsterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneObj.SummonSkynetMonsterAns.class, SUMMONSKYNETMONSTERANS);
        MSG_ID_2_PROTO_MSG.put(CREATESPYPLANEASK, SsScenePlane.CreateSpyPlaneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CreateSpyPlaneAsk.class, CREATESPYPLANEASK);
        MSG_ID_2_PROTO_MSG.put(CREATESPYPLANEANS, SsScenePlane.CreateSpyPlaneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CreateSpyPlaneAns.class, CREATESPYPLANEANS);
        MSG_ID_2_PROTO_MSG.put(CHANGEACTIONSPYPLANEASK, SsScenePlane.ChangeActionSpyPlaneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.ChangeActionSpyPlaneAsk.class, CHANGEACTIONSPYPLANEASK);
        MSG_ID_2_PROTO_MSG.put(CHANGEACTIONSPYPLANEANS, SsScenePlane.ChangeActionSpyPlaneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.ChangeActionSpyPlaneAns.class, CHANGEACTIONSPYPLANEANS);
        MSG_ID_2_PROTO_MSG.put(CHECKMAPCREATESPYPLANEASK, SsScenePlane.CheckMapCreateSpyPlaneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CheckMapCreateSpyPlaneAsk.class, CHECKMAPCREATESPYPLANEASK);
        MSG_ID_2_PROTO_MSG.put(CHECKMAPCREATESPYPLANEANS, SsScenePlane.CheckMapCreateSpyPlaneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CheckMapCreateSpyPlaneAns.class, CHECKMAPCREATESPYPLANEANS);
        MSG_ID_2_PROTO_MSG.put(CREATELOGISTICSPLANEASK, SsScenePlane.CreateLogisticsPlaneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CreateLogisticsPlaneAsk.class, CREATELOGISTICSPLANEASK);
        MSG_ID_2_PROTO_MSG.put(CREATELOGISTICSPLANEANS, SsScenePlane.CreateLogisticsPlaneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CreateLogisticsPlaneAns.class, CREATELOGISTICSPLANEANS);
        MSG_ID_2_PROTO_MSG.put(CHECKLOGISTICSACTIONASK, SsScenePlane.CheckLogisticsActionAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CheckLogisticsActionAsk.class, CHECKLOGISTICSACTIONASK);
        MSG_ID_2_PROTO_MSG.put(CHECKLOGISTICSACTIONANS, SsScenePlane.CheckLogisticsActionAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.CheckLogisticsActionAns.class, CHECKLOGISTICSACTIONANS);
        MSG_ID_2_PROTO_MSG.put(CHANGELOGISTICACTIONASK, SsScenePlane.ChangeLogisticActionAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.ChangeLogisticActionAsk.class, CHANGELOGISTICACTIONASK);
        MSG_ID_2_PROTO_MSG.put(CHANGELOGISTICSACTIONANS, SsScenePlane.ChangeLogisticsActionAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlane.ChangeLogisticsActionAns.class, CHANGELOGISTICSACTIONANS);
        MSG_ID_2_PROTO_MSG.put(FIRSTENTERBIGSCENEASK, SsScenePlayer.FirstEnterBigSceneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.FirstEnterBigSceneAsk.class, FIRSTENTERBIGSCENEASK);
        MSG_ID_2_PROTO_MSG.put(FIRSTENTERBIGSCENEANS, SsScenePlayer.FirstEnterBigSceneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.FirstEnterBigSceneAns.class, FIRSTENTERBIGSCENEANS);
        MSG_ID_2_PROTO_MSG.put(UPDATEPLAYERVIEWASK, SsScenePlayer.UpdatePlayerViewAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.UpdatePlayerViewAsk.class, UPDATEPLAYERVIEWASK);
        MSG_ID_2_PROTO_MSG.put(UPDATEPLAYERVIEWANS, SsScenePlayer.UpdatePlayerViewAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.UpdatePlayerViewAns.class, UPDATEPLAYERVIEWANS);
        MSG_ID_2_PROTO_MSG.put(CLEARPLAYERVIEWASK, SsScenePlayer.ClearPlayerViewAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ClearPlayerViewAsk.class, CLEARPLAYERVIEWASK);
        MSG_ID_2_PROTO_MSG.put(CLEARPLAYERVIEWANS, SsScenePlayer.ClearPlayerViewAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ClearPlayerViewAns.class, CLEARPLAYERVIEWANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERADDSOLDIERASK, SsScenePlayer.PlayerAddSoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.PlayerAddSoldierAsk.class, PLAYERADDSOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERADDSOLDIERANS, SsScenePlayer.PlayerAddSoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.PlayerAddSoldierAns.class, PLAYERADDSOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(DISMISSINCITYSOLDIERSASK, SsScenePlayer.DismissInCitySoldiersAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.DismissInCitySoldiersAsk.class, DISMISSINCITYSOLDIERSASK);
        MSG_ID_2_PROTO_MSG.put(DISMISSINCITYSOLDIERSANS, SsScenePlayer.DismissInCitySoldiersAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.DismissInCitySoldiersAns.class, DISMISSINCITYSOLDIERSANS);
        MSG_ID_2_PROTO_MSG.put(RETURNTREATOVERSOLDIERSASK, SsScenePlayer.ReturnTreatOverSoldiersAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ReturnTreatOverSoldiersAsk.class, RETURNTREATOVERSOLDIERSASK);
        MSG_ID_2_PROTO_MSG.put(RETURNTREATOVERSOLDIERSANS, SsScenePlayer.ReturnTreatOverSoldiersAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ReturnTreatOverSoldiersAns.class, RETURNTREATOVERSOLDIERSANS);
        MSG_ID_2_PROTO_MSG.put(GETALLSOLDIERASK, SsScenePlayer.GetAllSoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetAllSoldierAsk.class, GETALLSOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(GETALLSOLDIERANS, SsScenePlayer.GetAllSoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetAllSoldierAns.class, GETALLSOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(HOSPITALTREATCHECKASK, SsScenePlayer.HospitalTreatCheckAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalTreatCheckAsk.class, HOSPITALTREATCHECKASK);
        MSG_ID_2_PROTO_MSG.put(HOSPITALTREATCHECKANS, SsScenePlayer.HospitalTreatCheckAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalTreatCheckAns.class, HOSPITALTREATCHECKANS);
        MSG_ID_2_PROTO_MSG.put(HOSPITALTREATASK, SsScenePlayer.HospitalTreatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalTreatAsk.class, HOSPITALTREATASK);
        MSG_ID_2_PROTO_MSG.put(HOSPITALTREATANS, SsScenePlayer.HospitalTreatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalTreatAns.class, HOSPITALTREATANS);
        MSG_ID_2_PROTO_MSG.put(HOSPITALFASTTREATASK, SsScenePlayer.HospitalFastTreatAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalFastTreatAsk.class, HOSPITALFASTTREATASK);
        MSG_ID_2_PROTO_MSG.put(HOSPITALFASTTREATANS, SsScenePlayer.HospitalFastTreatAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalFastTreatAns.class, HOSPITALFASTTREATANS);
        MSG_ID_2_PROTO_MSG.put(HOSPITALTREATFINISHCMD, SsScenePlayer.HospitalTreatFinishCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.HospitalTreatFinishCmd.class, HOSPITALTREATFINISHCMD);
        MSG_ID_2_PROTO_MSG.put(ADDDEVBUFFFROMPLAYERASK, SsScenePlayer.AddDevBuffFromPlayerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.AddDevBuffFromPlayerAsk.class, ADDDEVBUFFFROMPLAYERASK);
        MSG_ID_2_PROTO_MSG.put(ADDDEVBUFFFROMPLAYERANS, SsScenePlayer.AddDevBuffFromPlayerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.AddDevBuffFromPlayerAns.class, ADDDEVBUFFFROMPLAYERANS);
        MSG_ID_2_PROTO_MSG.put(REMOVEDEVBUFFFROMPLAYERASK, SsScenePlayer.RemoveDevBuffFromPlayerAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.RemoveDevBuffFromPlayerAsk.class, REMOVEDEVBUFFFROMPLAYERASK);
        MSG_ID_2_PROTO_MSG.put(REMOVEDEVBUFFFROMPLAYERANS, SsScenePlayer.RemoveDevBuffFromPlayerAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.RemoveDevBuffFromPlayerAns.class, REMOVEDEVBUFFFROMPLAYERANS);
        MSG_ID_2_PROTO_MSG.put(UPDATEADDITIONFROMPLAYERCMD, SsScenePlayer.UpdateAdditionFromPlayerCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.UpdateAdditionFromPlayerCmd.class, UPDATEADDITIONFROMPLAYERCMD);
        MSG_ID_2_PROTO_MSG.put(GETSCENEADDITIONSYSASK, SsScenePlayer.GetSceneAdditionSysAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetSceneAdditionSysAsk.class, GETSCENEADDITIONSYSASK);
        MSG_ID_2_PROTO_MSG.put(GETSCENEADDITIONSYSANS, SsScenePlayer.GetSceneAdditionSysAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetSceneAdditionSysAns.class, GETSCENEADDITIONSYSANS);
        MSG_ID_2_PROTO_MSG.put(GETSCENEDEVBUFFASK, SsScenePlayer.GetSceneDevBuffAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetSceneDevBuffAsk.class, GETSCENEDEVBUFFASK);
        MSG_ID_2_PROTO_MSG.put(GETSCENEDEVBUFFANS, SsScenePlayer.GetSceneDevBuffAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetSceneDevBuffAns.class, GETSCENEDEVBUFFANS);
        MSG_ID_2_PROTO_MSG.put(MARKPOSITIONASK, SsScenePlayer.MarkPositionAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.MarkPositionAsk.class, MARKPOSITIONASK);
        MSG_ID_2_PROTO_MSG.put(MARKPOSITIONANS, SsScenePlayer.MarkPositionAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.MarkPositionAns.class, MARKPOSITIONANS);
        MSG_ID_2_PROTO_MSG.put(SETMARKREADEDCMD, SsScenePlayer.SetMarkReadedCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SetMarkReadedCmd.class, SETMARKREADEDCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERCLANIDNAMECMD, SsScenePlayer.SyncPlayerClanIdNameCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerClanIdNameCmd.class, SYNCPLAYERCLANIDNAMECMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERNAMECMD, SsScenePlayer.SyncPlayerNameCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerNameCmd.class, SYNCPLAYERNAMECMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERSOLDIERCMD, SsScenePlayer.SyncPlayerSoldierCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerSoldierCmd.class, SYNCPLAYERSOLDIERCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERCITYBUILDLEVELCMD, SsScenePlayer.SyncPlayerCityBuildLevelCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerCityBuildLevelCmd.class, SYNCPLAYERCITYBUILDLEVELCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLANECMD, SsScenePlayer.SyncPlaneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlaneCmd.class, SYNCPLANECMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERHEROCMD, SsScenePlayer.SyncPlayerHeroCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerHeroCmd.class, SYNCPLAYERHEROCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCTECHDATACMD, SsScenePlayer.SyncTechDataCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncTechDataCmd.class, SYNCTECHDATACMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERPICCMD, SsScenePlayer.SyncPlayerPicCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerPicCmd.class, SYNCPLAYERPICCMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERPICFRAMECMD, SsScenePlayer.SyncPlayerPicFrameCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerPicFrameCmd.class, SYNCPLAYERPICFRAMECMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERWALLHEROPLANECMD, SsScenePlayer.SyncPlayerWallHeroPlaneCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerWallHeroPlaneCmd.class, SYNCPLAYERWALLHEROPLANECMD);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERERACMD, SsScenePlayer.SyncPlayerEraCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerEraCmd.class, SYNCPLAYERERACMD);
        MSG_ID_2_PROTO_MSG.put(MARKNEWBIEOVERASK, SsScenePlayer.MarkNewbieOverAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.MarkNewbieOverAsk.class, MARKNEWBIEOVERASK);
        MSG_ID_2_PROTO_MSG.put(MARKNEWBIEOVERANS, SsScenePlayer.MarkNewbieOverAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.MarkNewbieOverAns.class, MARKNEWBIEOVERANS);
        MSG_ID_2_PROTO_MSG.put(BROADCASTONLINEPLAYERCSCMD, SsScenePlayer.BroadcastOnlinePlayerCsCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.BroadcastOnlinePlayerCsCmd.class, BROADCASTONLINEPLAYERCSCMD);
        MSG_ID_2_PROTO_MSG.put(BROADCASTONLINEPLAYERCSWITHMULTILANGUAGECMD, SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.BroadcastOnlinePlayerCsWithMultiLanguageCmd.class, BROADCASTONLINEPLAYERCSWITHMULTILANGUAGECMD);
        MSG_ID_2_PROTO_MSG.put(GETMILESTONEHISTORYASK, SsScenePlayer.GetMileStoneHistoryAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetMileStoneHistoryAsk.class, GETMILESTONEHISTORYASK);
        MSG_ID_2_PROTO_MSG.put(GETMILESTONEHISTORYANS, SsScenePlayer.GetMileStoneHistoryAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetMileStoneHistoryAns.class, GETMILESTONEHISTORYANS);
        MSG_ID_2_PROTO_MSG.put(GETMILESTONERANKINFOASK, SsScenePlayer.GetMileStoneRankInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetMileStoneRankInfoAsk.class, GETMILESTONERANKINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETMILESTONERANKINFOANS, SsScenePlayer.GetMileStoneRankInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetMileStoneRankInfoAns.class, GETMILESTONERANKINFOANS);
        MSG_ID_2_PROTO_MSG.put(FETCHMAINCITYINFOASK, SsScenePlayer.FetchMainCityInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.FetchMainCityInfoAsk.class, FETCHMAINCITYINFOASK);
        MSG_ID_2_PROTO_MSG.put(FETCHMAINCITYINFOANS, SsScenePlayer.FetchMainCityInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.FetchMainCityInfoAns.class, FETCHMAINCITYINFOANS);
        MSG_ID_2_PROTO_MSG.put(SETEXPRESSIONCMD, SsScenePlayer.SetExpressionCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SetExpressionCmd.class, SETEXPRESSIONCMD);
        MSG_ID_2_PROTO_MSG.put(SETPFLAGCMD, SsScenePlayer.SetPFlagCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SetPFlagCmd.class, SETPFLAGCMD);
        MSG_ID_2_PROTO_MSG.put(GETBATTLELOSEASK, SsScenePlayer.GetBattleLoseAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetBattleLoseAsk.class, GETBATTLELOSEASK);
        MSG_ID_2_PROTO_MSG.put(GETBATTLELOSEANS, SsScenePlayer.GetBattleLoseAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetBattleLoseAns.class, GETBATTLELOSEANS);
        MSG_ID_2_PROTO_MSG.put(USEDUNGEONSKILLASK, SsScenePlayer.UseDungeonSkillAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.UseDungeonSkillAsk.class, USEDUNGEONSKILLASK);
        MSG_ID_2_PROTO_MSG.put(USEDUNGEONSKILLANS, SsScenePlayer.UseDungeonSkillAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.UseDungeonSkillAns.class, USEDUNGEONSKILLANS);
        MSG_ID_2_PROTO_MSG.put(CHANGECITYDRESSASK, SsScenePlayer.ChangeCityDressAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ChangeCityDressAsk.class, CHANGECITYDRESSASK);
        MSG_ID_2_PROTO_MSG.put(CHANGECITYDRESSANS, SsScenePlayer.ChangeCityDressAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.ChangeCityDressAns.class, CHANGECITYDRESSANS);
        MSG_ID_2_PROTO_MSG.put(CHECKCANADDDEVBUFFASK, SsScenePlayer.CheckCanAddDevBuffAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.CheckCanAddDevBuffAsk.class, CHECKCANADDDEVBUFFASK);
        MSG_ID_2_PROTO_MSG.put(CHECKCANADDDEVBUFFANS, SsScenePlayer.CheckCanAddDevBuffAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.CheckCanAddDevBuffAns.class, CHECKCANADDDEVBUFFANS);
        MSG_ID_2_PROTO_MSG.put(IDIPRETURNALLARMYASK, SsScenePlayer.IdIpReturnAllArmyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpReturnAllArmyAsk.class, IDIPRETURNALLARMYASK);
        MSG_ID_2_PROTO_MSG.put(IDIPRETURNALLARMYANS, SsScenePlayer.IdIpReturnAllArmyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpReturnAllArmyAns.class, IDIPRETURNALLARMYANS);
        MSG_ID_2_PROTO_MSG.put(IDIPGLOBALPEACESHIELDASK, SsScenePlayer.IdIpGlobalPeaceShieldAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpGlobalPeaceShieldAsk.class, IDIPGLOBALPEACESHIELDASK);
        MSG_ID_2_PROTO_MSG.put(IDIPGLOBALPEACESHIELDANS, SsScenePlayer.IdIpGlobalPeaceShieldAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpGlobalPeaceShieldAns.class, IDIPGLOBALPEACESHIELDANS);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERPUSHNTFINFOASK, SsScenePlayer.SyncPlayerPushNtfInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerPushNtfInfoAsk.class, SYNCPLAYERPUSHNTFINFOASK);
        MSG_ID_2_PROTO_MSG.put(SYNCPLAYERPUSHNTFINFOANS, SsScenePlayer.SyncPlayerPushNtfInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SyncPlayerPushNtfInfoAns.class, SYNCPLAYERPUSHNTFINFOANS);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERPUSHNTFASK, SsScenePlayer.QueryPlayerPushNtfAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.QueryPlayerPushNtfAsk.class, QUERYPLAYERPUSHNTFASK);
        MSG_ID_2_PROTO_MSG.put(QUERYPLAYERPUSHNTFANS, SsScenePlayer.QueryPlayerPushNtfAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.QueryPlayerPushNtfAns.class, QUERYPLAYERPUSHNTFANS);
        MSG_ID_2_PROTO_MSG.put(IDIPMODIFYSOLDIERASK, SsScenePlayer.IdIpModifySoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpModifySoldierAsk.class, IDIPMODIFYSOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(IDIPMODIFYSOLDIERANS, SsScenePlayer.IdIpModifySoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.IdIpModifySoldierAns.class, IDIPMODIFYSOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(BROADONLINEPLAYERSSCMD, SsScenePlayer.BroadOnlinePlayerSsCmd.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.BroadOnlinePlayerSsCmd.class, BROADONLINEPLAYERSSCMD);
        MSG_ID_2_PROTO_MSG.put(SKYNETFINDMONSTERASK, SsScenePlayer.SkynetFindMonsterAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SkynetFindMonsterAsk.class, SKYNETFINDMONSTERASK);
        MSG_ID_2_PROTO_MSG.put(SKYNETFINDMONSTERANS, SsScenePlayer.SkynetFindMonsterAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.SkynetFindMonsterAns.class, SKYNETFINDMONSTERANS);
        MSG_ID_2_PROTO_MSG.put(GETZONESEASONASK, SsScenePlayer.GetZoneSeasonAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetZoneSeasonAsk.class, GETZONESEASONASK);
        MSG_ID_2_PROTO_MSG.put(GETZONESEASONANS, SsScenePlayer.GetZoneSeasonAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsScenePlayer.GetZoneSeasonAns.class, GETZONESEASONANS);
        MSG_ID_2_PROTO_MSG.put(FETCHRALLYLISTASK, SsSceneRallyAssist.FetchRallyListAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchRallyListAsk.class, FETCHRALLYLISTASK);
        MSG_ID_2_PROTO_MSG.put(FETCHRALLYLISTANS, SsSceneRallyAssist.FetchRallyListAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchRallyListAns.class, FETCHRALLYLISTANS);
        MSG_ID_2_PROTO_MSG.put(FETCHONERALLYASK, SsSceneRallyAssist.FetchOneRallyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchOneRallyAsk.class, FETCHONERALLYASK);
        MSG_ID_2_PROTO_MSG.put(FETCHONERALLYANS, SsSceneRallyAssist.FetchOneRallyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchOneRallyAns.class, FETCHONERALLYANS);
        MSG_ID_2_PROTO_MSG.put(PLAYERCANCELRALLYASK, SsSceneRallyAssist.PlayerCancelRallyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.PlayerCancelRallyAsk.class, PLAYERCANCELRALLYASK);
        MSG_ID_2_PROTO_MSG.put(PLAYERCANCELRALLYANS, SsSceneRallyAssist.PlayerCancelRallyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.PlayerCancelRallyAns.class, PLAYERCANCELRALLYANS);
        MSG_ID_2_PROTO_MSG.put(REPATRIATERALLYMEMBERASK, SsSceneRallyAssist.RepatriateRallyMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.RepatriateRallyMemberAsk.class, REPATRIATERALLYMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(REPATRIATERALLYMEMBERANS, SsSceneRallyAssist.RepatriateRallyMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.RepatriateRallyMemberAns.class, REPATRIATERALLYMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(SETRALLYRECOMMENDSOLDIERASK, SsSceneRallyAssist.SetRallyRecommendSoldierAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.SetRallyRecommendSoldierAsk.class, SETRALLYRECOMMENDSOLDIERASK);
        MSG_ID_2_PROTO_MSG.put(SETRALLYRECOMMENDSOLDIERANS, SsSceneRallyAssist.SetRallyRecommendSoldierAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.SetRallyRecommendSoldierAns.class, SETRALLYRECOMMENDSOLDIERANS);
        MSG_ID_2_PROTO_MSG.put(FETCHWARNINGASK, SsSceneRallyAssist.FetchWarningAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchWarningAsk.class, FETCHWARNINGASK);
        MSG_ID_2_PROTO_MSG.put(FETCHWARNINGANS, SsSceneRallyAssist.FetchWarningAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchWarningAns.class, FETCHWARNINGANS);
        MSG_ID_2_PROTO_MSG.put(SETWARNINGITEMTAGASK, SsSceneRallyAssist.SetWarningItemTagAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.SetWarningItemTagAsk.class, SETWARNINGITEMTAGASK);
        MSG_ID_2_PROTO_MSG.put(SETWARNINGITEMTAGANS, SsSceneRallyAssist.SetWarningItemTagAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.SetWarningItemTagAns.class, SETWARNINGITEMTAGANS);
        MSG_ID_2_PROTO_MSG.put(IGNOREALLWARNINGASK, SsSceneRallyAssist.IgnoreAllWarningAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.IgnoreAllWarningAsk.class, IGNOREALLWARNINGASK);
        MSG_ID_2_PROTO_MSG.put(IGNOREALLWARNINGANS, SsSceneRallyAssist.IgnoreAllWarningAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.IgnoreAllWarningAns.class, IGNOREALLWARNINGANS);
        MSG_ID_2_PROTO_MSG.put(FETCHINNERARMYASK, SsSceneRallyAssist.FetchInnerArmyAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchInnerArmyAsk.class, FETCHINNERARMYASK);
        MSG_ID_2_PROTO_MSG.put(FETCHINNERARMYANS, SsSceneRallyAssist.FetchInnerArmyAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.FetchInnerArmyAns.class, FETCHINNERARMYANS);
        MSG_ID_2_PROTO_MSG.put(REPATRIATEASSISTMEMBERASK, SsSceneRallyAssist.RepatriateAssistMemberAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.RepatriateAssistMemberAsk.class, REPATRIATEASSISTMEMBERASK);
        MSG_ID_2_PROTO_MSG.put(REPATRIATEASSISTMEMBERANS, SsSceneRallyAssist.RepatriateAssistMemberAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.RepatriateAssistMemberAns.class, REPATRIATEASSISTMEMBERANS);
        MSG_ID_2_PROTO_MSG.put(CHANGEASSISTLEADERASK, SsSceneRallyAssist.ChangeAssistLeaderAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.ChangeAssistLeaderAsk.class, CHANGEASSISTLEADERASK);
        MSG_ID_2_PROTO_MSG.put(CHANGEASSISTLEADERANS, SsSceneRallyAssist.ChangeAssistLeaderAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsSceneRallyAssist.ChangeAssistLeaderAns.class, CHANGEASSISTLEADERANS);
        MSG_ID_2_PROTO_MSG.put(CHECKTEXTASK, SsTextFilter.CheckTextAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTextFilter.CheckTextAsk.class, CHECKTEXTASK);
        MSG_ID_2_PROTO_MSG.put(CHECKTEXTANS, SsTextFilter.CheckTextAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTextFilter.CheckTextAns.class, CHECKTEXTANS);
        MSG_ID_2_PROTO_MSG.put(BATCHCHECKTEXTASK, SsTextFilter.BatchCheckTextAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTextFilter.BatchCheckTextAsk.class, BATCHCHECKTEXTASK);
        MSG_ID_2_PROTO_MSG.put(BATCHCHECKTEXTANS, SsTextFilter.BatchCheckTextAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTextFilter.BatchCheckTextAns.class, BATCHCHECKTEXTANS);
        MSG_ID_2_PROTO_MSG.put(TRANSLATETEXTASK, SsTranslator.TranslateTextAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTranslator.TranslateTextAsk.class, TRANSLATETEXTASK);
        MSG_ID_2_PROTO_MSG.put(TRANSLATETEXTANS, SsTranslator.TranslateTextAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsTranslator.TranslateTextAns.class, TRANSLATETEXTANS);
        MSG_ID_2_PROTO_MSG.put(GETALLZONEINFOASK, SsZoneCard.GetAllZoneInfoAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetAllZoneInfoAsk.class, GETALLZONEINFOASK);
        MSG_ID_2_PROTO_MSG.put(GETALLZONEINFOANS, SsZoneCard.GetAllZoneInfoAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetAllZoneInfoAns.class, GETALLZONEINFOANS);
        MSG_ID_2_PROTO_MSG.put(GETMULTIZONESTATUSASK, SsZoneCard.GetMultiZoneStatusAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetMultiZoneStatusAsk.class, GETMULTIZONESTATUSASK);
        MSG_ID_2_PROTO_MSG.put(GETMULTIZONESTATUSANS, SsZoneCard.GetMultiZoneStatusAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetMultiZoneStatusAns.class, GETMULTIZONESTATUSANS);
        MSG_ID_2_PROTO_MSG.put(GETZONESUNDERSEASONASK, SsZoneCard.GetZonesUnderSeasonAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetZonesUnderSeasonAsk.class, GETZONESUNDERSEASONASK);
        MSG_ID_2_PROTO_MSG.put(GETZONESUNDERSEASONANS, SsZoneCard.GetZonesUnderSeasonAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetZonesUnderSeasonAns.class, GETZONESUNDERSEASONANS);
        MSG_ID_2_PROTO_MSG.put(GETZONEMILESTONEASK, SsZoneCard.GetZoneMileStoneAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetZoneMileStoneAsk.class, GETZONEMILESTONEASK);
        MSG_ID_2_PROTO_MSG.put(GETZONEMILESTONEANS, SsZoneCard.GetZoneMileStoneAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneCard.GetZoneMileStoneAns.class, GETZONEMILESTONEANS);
        MSG_ID_2_PROTO_MSG.put(FETCHCHATMSGASK, SsZoneChat.FetchChatMsgAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.FetchChatMsgAsk.class, FETCHCHATMSGASK);
        MSG_ID_2_PROTO_MSG.put(FETCHCHATMSGANS, SsZoneChat.FetchChatMsgAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.FetchChatMsgAns.class, FETCHCHATMSGANS);
        MSG_ID_2_PROTO_MSG.put(SENDCHATMSGASK, SsZoneChat.SendChatMsgAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.SendChatMsgAsk.class, SENDCHATMSGASK);
        MSG_ID_2_PROTO_MSG.put(SENDCHATMSGANS, SsZoneChat.SendChatMsgAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.SendChatMsgAns.class, SENDCHATMSGANS);
        MSG_ID_2_PROTO_MSG.put(IDIPSENDCHATMSGASK, SsZoneChat.IdIpSendChatMsgAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.IdIpSendChatMsgAsk.class, IDIPSENDCHATMSGASK);
        MSG_ID_2_PROTO_MSG.put(IDIPSENDCHATMSGANS, SsZoneChat.IdIpSendChatMsgAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.IdIpSendChatMsgAns.class, IDIPSENDCHATMSGANS);
        MSG_ID_2_PROTO_MSG.put(IDIPEXECSCRIPTMSGASK, SsZoneChat.IdIpExecScriptMsgAsk.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.IdIpExecScriptMsgAsk.class, IDIPEXECSCRIPTMSGASK);
        MSG_ID_2_PROTO_MSG.put(IDIPEXECSCRIPTMSGANS, SsZoneChat.IdIpExecScriptMsgAns.getDefaultInstance());
        PROTO_MSG_2_MSG_ID.put(SsZoneChat.IdIpExecScriptMsgAns.class, IDIPEXECSCRIPTMSGANS);
    }

    public static Map<Integer, GeneratedMessageV3> getMsgId2ProtoMsgMap() {
        return MSG_ID_2_PROTO_MSG;
    }

    public static GeneratedMessageV3 getMsgFromType(int msgType) {
        return MSG_ID_2_PROTO_MSG.get(msgType);
    }

    public static int getTypeFromMsg(GeneratedMessageV3 msg) {
        return PROTO_MSG_2_MSG_ID.get(msg.getClass());
    }

}
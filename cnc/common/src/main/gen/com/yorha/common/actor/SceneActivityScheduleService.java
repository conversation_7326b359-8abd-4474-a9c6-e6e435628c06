package com.yorha.common.actor;

import com.yorha.proto.SsSceneActivitySchedule.*;

public interface SceneActivityScheduleService {

    void handleBestCommanderFetchAsk(BestCommanderFetchAsk ask); // 拉取我的排行信息

    void handleBestCommanderHistoryRankAsk(BestCommanderHistoryRankAsk ask); // 拉取历史排行信息

    void handleBestCommanderChooseItemAsk(BestCommanderChooseItemAsk ask); // 最强执政官活动选择奖励

    void handleBestCommanderGetVolumeAsk(BestCommanderGetVolumeAsk ask); // 最强执政官活动活动初始时获取本次是第几个赛季第几期

    void handleGetLotteryInfoAsk(GetLotteryInfoAsk ask); // 幸运大转盘info（英雄自选）

}
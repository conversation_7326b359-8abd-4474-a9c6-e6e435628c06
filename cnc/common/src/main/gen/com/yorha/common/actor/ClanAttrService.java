package com.yorha.common.actor;

import com.yorha.proto.SsClanAttr.*;

public interface ClanAttrService {

    void handleModifyClanPrivilegeAsk(ModifyClanPrivilegeAsk ask); // 修改联盟各个角色的操作权限

    void handleCheckModifyNameQuietTimeAsk(CheckModifyNameQuietTimeAsk ask); // 检查更改联盟名称静默期

    void handleModifyClanNameAsk(ModifyClanNameAsk ask); // 修改联盟名

    void handleModifyClanSimpleNameAsk(ModifyClanSimpleNameAsk ask); // 修改联盟简称

    void handleCheckClanFlagAndTerritoryColorAsk(CheckClanFlagAndTerritoryColorAsk ask); // 检查联盟旗帜是否真正变更

    void handleModifyClanFlagAndTerritoryColorAsk(ModifyClanFlagAndTerritoryColorAsk ask); // 修改联盟旗帜

    void handleModifyClanWelcomeLetterAsk(ModifyClanWelcomeLetterAsk ask); // 修改联盟信件

    void handleModifyClanSettingMiscAsk(ModifyClanSettingMiscAsk ask); // 修改联盟设置杂项（公告、入团要求、语言设置）

    void handleClanMarkPositionCmd(ClanMarkPositionCmd ask); // 联盟坐标收藏

    void handleSyncRedDotToClanCmd(SyncRedDotToClanCmd ask); // 同步军团红点信息

    void handleFetchResourcesSnapshotAsk(FetchResourcesSnapshotAsk ask); // 拉取并结算资源快照

    void handleFetchClanWareHouseInfoAsk(FetchClanWareHouseInfoAsk ask); // 拉取联盟仓库信息

    void handleOnAddClanScoreForClanCmd(OnAddClanScoreForClanCmd ask); // 增加军团的积分

    void handleCreateClanLogCmd(CreateClanLogCmd ask); // 创建军团日志

    void handleFetchClanLogAsk(FetchClanLogAsk ask); // 拉取军团日志信息

    void handleFetchDissolvingInfoAsk(FetchDissolvingInfoAsk ask); // 拉取预解散状态信息

    void handleIDIPQueryClanInfoAsk(IDIPQueryClanInfoAsk ask); // idip查数据

    void handleIDIPResetClanInfoAsk(IDIPResetClanInfoAsk ask); // 重置联盟信息

    void handleCheckModifySNameQuietTimeAsk(CheckModifySNameQuietTimeAsk ask); // 检查更改联盟简称静默期

    void handleFetchClanPositionMarkAsk(FetchClanPositionMarkAsk ask); // 拉取联盟坐标收藏信息

    void handleFetchClanNumTipAsk(FetchClanNumTipAsk ask); // 拉取军团最大人数上限来源组成

}
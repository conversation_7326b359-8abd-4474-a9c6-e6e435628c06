package com.yorha.common.actor;

import com.yorha.proto.SsGroupChat.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface GroupChatServices {
    Logger LOGGER = LogManager.getLogger(GroupChatServices.class);

    GroupChatService getGroupChatService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.CREATECHATASK:
                getGroupChatService().handleCreateChatAsk((CreateChatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDCHATMESSAGEASK:
                getGroupChatService().handleSendChatMessageAsk((SendChatMessageAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYCHATMESSAGEASK:
                getGroupChatService().handleQueryChatMessageAsk((QueryChatMessageAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCHATMEMBERASK:
                getGroupChatService().handleFetchChatMemberAsk((FetchChatMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.JUDGEMEMBERASK:
                getGroupChatService().handleJudgeMemberAsk((JudgeMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.INVITENEWMEMBERASK:
                getGroupChatService().handleInviteNewMemberAsk((InviteNewMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REMOVEGROUPMEMBERASK:
                getGroupChatService().handleRemoveGroupMemberAsk((RemoveGroupMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.TRANSFERGROUPOWNERASK:
                getGroupChatService().handleTransferGroupOwnerAsk((TransferGroupOwnerAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DISMISSCHATGROUPASK:
                getGroupChatService().handleDismissChatGroupAsk((DismissChatGroupAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MODIFYGROUPNAMEASK:
                getGroupChatService().handleModifyGroupNameAsk((ModifyGroupNameAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsMidasAgent.*;

public interface MidasAgentService {

    void handleMidasQueryAsk(MidasQueryAsk ask); // 查询

    void handleMidasConsumeAsk(MidasConsumeAsk ask); // 消耗

    void handleMidasRollbackConsumeAsk(MidasRollbackConsumeAsk ask); // 回滚

    void handleMidasPresentAsk(MidasPresentAsk ask); // 赠送

    void handleMidasSwitchStressTestingModeCmd(MidasSwitchStressTestingModeCmd ask); // 开关压测模式

}
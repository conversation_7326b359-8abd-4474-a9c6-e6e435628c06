package com.yorha.common.actor;

import com.yorha.proto.SsTextFilter.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface TextFilterServices {
    Logger LOGGER = LogManager.getLogger(TextFilterServices.class);

    TextFilterService getTextFilterService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.CHECKTEXTASK:
                getTextFilterService().handleCheckTextAsk((CheckTextAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHCHECKTEXTASK:
                getTextFilterService().handleBatchCheckTextAsk((BatchCheckTextAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
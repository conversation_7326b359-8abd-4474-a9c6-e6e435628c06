package com.yorha.common.actor;

import com.yorha.proto.SsPlayer.*;

public interface PlayerBaseService {

    void handleLoginPlayerCmd(LoginPlayerCmd ask); // 登录

    void handleOnPlayerDisconnectCmd(OnPlayerDisconnectCmd ask); // 断连下线

    void handleRecvClientMsgCmd(RecvClientMsgCmd ask); // 接收客户端发来的消息

    void handleKickOffPlayerCmd(KickOffPlayerCmd ask); // 踢玩家下线

    void handleApplyDataPatchCmd(ApplyDataPatchCmd ask); // 通知在线玩家应用数据修复patch

}
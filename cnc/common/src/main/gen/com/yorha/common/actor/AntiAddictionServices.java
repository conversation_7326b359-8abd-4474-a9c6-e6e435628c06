package com.yorha.common.actor;

import com.yorha.proto.SsAntiAddiction.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface AntiAddictionServices {
    Logger LOGGER = LogManager.getLogger(AntiAddictionServices.class);

    AntiAddictionService getAntiAddictionService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.JUDGETIMINGASK:
                getAntiAddictionService().handleJudgeTimingAsk((JudgeTimingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.JUDGEPAYASK:
                getAntiAddictionService().handleJudgePayAsk((JudgePayAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REPORTPAYCMD:
                getAntiAddictionService().handleReportPayCmd((ReportPayCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.REPORTEXECUTECMD:
                getAntiAddictionService().handleReportExecuteCmd((ReportExecuteCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
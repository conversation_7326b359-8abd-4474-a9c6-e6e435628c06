package com.yorha.common.actor;

import com.yorha.proto.SsSceneActivity.*;
import com.yorha.proto.SsSceneActivitySchedule.*;
import com.yorha.proto.SsSceneCityArmy.*;
import com.yorha.proto.SsSceneClan.*;
import com.yorha.proto.SsSceneCollect.*;
import com.yorha.proto.SsSceneDungeon.*;
import com.yorha.proto.SsSceneIdip.*;
import com.yorha.proto.SsSceneInfoMgr.*;
import com.yorha.proto.SsSceneKingdom.*;
import com.yorha.proto.SsSceneMail.*;
import com.yorha.proto.SsSceneMap.*;
import com.yorha.proto.SsSceneMarquee.*;
import com.yorha.proto.SsSceneObj.*;
import com.yorha.proto.SsScenePlane.*;
import com.yorha.proto.SsScenePlayer.*;
import com.yorha.proto.SsSceneRallyAssist.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface SceneServices {
    Logger LOGGER = LogManager.getLogger(SceneServices.class);

    SceneActivityService getSceneActivityService();
    SceneActivityScheduleService getSceneActivityScheduleService();
    SceneCityArmyService getSceneCityArmyService();
    SceneClanMgrService getSceneClanMgrService();
    SceneCollectService getSceneCollectService();
    SceneDungeonService getSceneDungeonService();
    SceneIdipService getSceneIdipService();
    SceneInformationMgrService getSceneInformationMgrService();
    SceneKingdomService getSceneKingdomService();
    SceneMailService getSceneMailService();
    SceneMapService getSceneMapService();
    SceneMarqueeService getSceneMarqueeService();
    SceneObjectService getSceneObjectService();
    ScenePlaneService getScenePlaneService();
    ScenePlayerMgrService getScenePlayerMgrService();
    SceneRallyAssistService getSceneRallyAssistService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.ENABLEACTIVITYEFFECTASK:
                getSceneActivityService().handleEnableActivityEffectAsk((EnableActivityEffectAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BESTCOMMANDERFETCHASK:
                getSceneActivityScheduleService().handleBestCommanderFetchAsk((BestCommanderFetchAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BESTCOMMANDERHISTORYRANKASK:
                getSceneActivityScheduleService().handleBestCommanderHistoryRankAsk((BestCommanderHistoryRankAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BESTCOMMANDERCHOOSEITEMASK:
                getSceneActivityScheduleService().handleBestCommanderChooseItemAsk((BestCommanderChooseItemAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BESTCOMMANDERGETVOLUMEASK:
                getSceneActivityScheduleService().handleBestCommanderGetVolumeAsk((BestCommanderGetVolumeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETLOTTERYINFOASK:
                getSceneActivityScheduleService().handleGetLotteryInfoAsk((GetLotteryInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REPAIRCITYWALLASK:
                getSceneCityArmyService().handleRepairCityWallAsk((RepairCityWallAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.OUTFIRECITYWALLASK:
                getSceneCityArmyService().handleOutFireCityWallAsk((OutFireCityWallAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETCITYFALLASK:
                getSceneCityArmyService().handleSetCityFallAsk((SetCityFallAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MOVECITYFIXEDASK:
                getSceneCityArmyService().handleMoveCityFixedAsk((MoveCityFixedAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MOVECITYRANDOMASK:
                getSceneCityArmyService().handleMoveCityRandomAsk((MoveCityRandomAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MOVECITYVERIFYASK:
                getSceneCityArmyService().handleMoveCityVerifyAsk((MoveCityVerifyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATEARMYCHECKASK:
                getSceneCityArmyService().handleCreateArmyCheckAsk((CreateArmyCheckAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATEPLAYERARMYASK:
                getSceneCityArmyService().handleCreatePlayerArmyAsk((CreatePlayerArmyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGEARMYACTIONCHECKASK:
                getSceneCityArmyService().handleChangeArmyActionCheckAsk((ChangeArmyActionCheckAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGEPLAYERARMYACTIONASK:
                getSceneCityArmyService().handleChangePlayerArmyActionAsk((ChangePlayerArmyActionAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FORCEDDEFEATARMYASK:
                getSceneCityArmyService().handleForcedDefeatArmyAsk((ForcedDefeatArmyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHTERRITORYPAGEASK:
                getSceneClanMgrService().handleFetchTerritoryPageAsk((FetchTerritoryPageAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ABANDONCLANMAPBUILDINGASK:
                getSceneClanMgrService().handleAbandonClanMapBuildingAsk((AbandonClanMapBuildingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANCREATEDCMD:
                getSceneClanMgrService().handleOnClanCreatedCmd((OnClanCreatedCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ONCLANDISSOLUTIONCMD:
                getSceneClanMgrService().handleOnClanDissolutionCmd((OnClanDissolutionCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCSCENECLANCMD:
                getSceneClanMgrService().handleSyncSceneClanCmd((SyncSceneClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCCLANACTORSTATUSCMD:
                getSceneClanMgrService().handleSyncClanActorStatusCmd((SyncClanActorStatusCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.CONSTRUCTCLANBUILDINGASK:
                getSceneClanMgrService().handleConstructClanBuildingAsk((ConstructClanBuildingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DESTROYCLANBUILDINGASK:
                getSceneClanMgrService().handleDestroyClanBuildingAsk((DestroyClanBuildingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.EXTINGUISHBUILDINGFIREASK:
                getSceneClanMgrService().handleExtinguishBuildingFireAsk((ExtinguishBuildingFireAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANBUILDINGHPASK:
                getSceneClanMgrService().handleFetchClanBuildingHpAsk((FetchClanBuildingHpAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKCANFREEREBUILDASK:
                getSceneClanMgrService().handleCheckCanFreeRebuildAsk((CheckCanFreeRebuildAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.VERIFYCANREBUILDASK:
                getSceneClanMgrService().handleVerifyCanRebuildAsk((VerifyCanRebuildAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.VERIFYCANEXTINGUISHASK:
                getSceneClanMgrService().handleVerifyCanExtinguishAsk((VerifyCanExtinguishAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCCLANADDITIONCMD:
                getSceneClanMgrService().handleSyncClanAdditionCmd((SyncClanAdditionCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ASKFORCLANRECOMMENDASK:
                getSceneClanMgrService().handleAskForClanRecommendAsk((AskForClanRecommendAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHDEFAULTCLANLISTASK:
                getSceneClanMgrService().handleFetchDefaultClanListAsk((FetchDefaultClanListAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.VERIFYCANPLACECLANRESASK:
                getSceneClanMgrService().handleVerifyCanPlaceClanResAsk((VerifyCanPlaceClanResAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLACECLANRESBUILDINSCENEASK:
                getSceneClanMgrService().handlePlaceClanResBuildInSceneAsk((PlaceClanResBuildInSceneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANRESBUILDSIMPLEINFOASK:
                getSceneClanMgrService().handleFetchClanResBuildSimpleInfoAsk((FetchClanResBuildSimpleInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKPLAYERCITYINTERRITORYASK:
                getSceneClanMgrService().handleCheckPlayerCityInTerritoryAsk((CheckPlayerCityInTerritoryAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDDEVBUFFFROMCLANCMD:
                getSceneClanMgrService().handleAddDevBuffFromClanCmd((AddDevBuffFromClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.REMOVEDEVBUFFFROMCLANCMD:
                getSceneClanMgrService().handleRemoveDevBuffFromClanCmd((RemoveDevBuffFromClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEADDITIONFROMCLANCMD:
                getSceneClanMgrService().handleUpdateAdditionFromClanCmd((UpdateAdditionFromClanCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETCLANCOMMANDCENTERNUMASK:
                getSceneClanMgrService().handleGetClanCommandCenterNumAsk((GetClanCommandCenterNumAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SEARCHRESOURCEASK:
                getSceneCollectService().handleSearchResourceAsk((SearchResourceAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATEDUNGEONASK:
                getSceneDungeonService().handleCreateDungeonAsk((CreateDungeonAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ENTERDUNGEONASK:
                getSceneDungeonService().handleEnterDungeonAsk((EnterDungeonAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.LEAVEDUNGEONASK:
                getSceneDungeonService().handleLeaveDungeonAsk((LeaveDungeonAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERLOGINASK:
                getSceneDungeonService().handlePlayerLoginAsk((PlayerLoginAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERLOGOUTASK:
                getSceneDungeonService().handlePlayerLogoutAsk((PlayerLogoutAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PERFORMACTIONASK:
                getSceneDungeonService().handlePerformActionAsk((PerformActionAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SOLDIERASK:
                getSceneIdipService().handleSoldierAsk((SoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETONLINEPLAYERIDASK:
                getSceneInformationMgrService().handleGetOnlinePlayerIdAsk((GetOnlinePlayerIdAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETONLINEPLAYERIDFILTERBYCREATETIMEASK:
                getSceneInformationMgrService().handleGetOnlinePlayerIdFilterByCreateTimeAsk((GetOnlinePlayerIdFilterByCreateTimeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKPLAYERONLINEASK:
                getSceneInformationMgrService().handleCheckPlayerOnlineAsk((CheckPlayerOnlineAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETZONEIPPORTASK:
                getSceneInformationMgrService().handleGetZoneIpPortAsk((GetZoneIpPortAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGAPPOINTASK:
                getSceneKingdomService().handleKingAppointAsk((KingAppointAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGOPENBUFFASK:
                getSceneKingdomService().handleKingOpenBuffAsk((KingOpenBuffAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGSENDGIFTASK:
                getSceneKingdomService().handleKingSendGiftAsk((KingSendGiftAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGCHECKCANUSESKILLASK:
                getSceneKingdomService().handleKingCheckCanUseSkillAsk((KingCheckCanUseSkillAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.KINGUSESKILLASK:
                getSceneKingdomService().handleKingUseSkillAsk((KingUseSkillAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHHISTORYKINGASK:
                getSceneKingdomService().handleFetchHistoryKingAsk((FetchHistoryKingAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHKINGDOMGIFTASK:
                getSceneKingdomService().handleFetchKingdomGiftAsk((FetchKingdomGiftAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHKINGDOMOFFICEASK:
                getSceneKingdomService().handleFetchKingdomOfficeAsk((FetchKingdomOfficeAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYEROFFLINEMAILSASK:
                getSceneMailService().handleSyncPlayerOfflineMailsAsk((SyncPlayerOfflineMailsAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDZONEMAILCMD:
                getSceneMailService().handleSendZoneMailCmd((SendZoneMailCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.EXECUTESCENEGMASK:
                getSceneMapService().handleExecuteSceneGmAsk((ExecuteSceneGmAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SEARCHPATHASK:
                getSceneMapService().handleSearchPathAsk((SearchPathAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHCLANCITYPOINTLISTASK:
                getSceneMapService().handleFetchClanCityPointListAsk((FetchClanCityPointListAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHSINGLECLANMEMBERCITYPOINTASK:
                getSceneMapService().handleFetchSingleClanMemberCityPointAsk((FetchSingleClanMemberCityPointAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHTERRITORYMAPASK:
                getSceneMapService().handleFetchTerritoryMapAsk((FetchTerritoryMapAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDSCENEMARQUEECMD:
                getSceneMarqueeService().handleSendSceneMarqueeCmd((SendSceneMarqueeCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDSCENELOOPMARQUEEWITHIDIPASK:
                getSceneMarqueeService().handleSendSceneLoopMarqueeWithIdIpAsk((SendSceneLoopMarqueeWithIdIpAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERSEARCHMONSTERASK:
                getSceneObjectService().handlePlayerSearchMonsterAsk((PlayerSearchMonsterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDMONSTERASK:
                getSceneObjectService().handleAddMonsterAsk((AddMonsterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKCANBEATTACKASK:
                getSceneObjectService().handleCheckCanBeAttackAsk((CheckCanBeAttackAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYMAPBUILDINGIDASK:
                getSceneObjectService().handleQueryMapBuildingIdAsk((QueryMapBuildingIdAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETMONSTERNUMASK:
                getSceneObjectService().handleGetMonsterNumAsk((GetMonsterNumAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REFRESHACTMONSTERASK:
                getSceneObjectService().handleRefreshActMonsterAsk((RefreshActMonsterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SUMMONSKYNETMONSTERASK:
                getSceneObjectService().handleSummonSkynetMonsterAsk((SummonSkynetMonsterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATESPYPLANEASK:
                getScenePlaneService().handleCreateSpyPlaneAsk((CreateSpyPlaneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGEACTIONSPYPLANEASK:
                getScenePlaneService().handleChangeActionSpyPlaneAsk((ChangeActionSpyPlaneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKMAPCREATESPYPLANEASK:
                getScenePlaneService().handleCheckMapCreateSpyPlaneAsk((CheckMapCreateSpyPlaneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CREATELOGISTICSPLANEASK:
                getScenePlaneService().handleCreateLogisticsPlaneAsk((CreateLogisticsPlaneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKLOGISTICSACTIONASK:
                getScenePlaneService().handleCheckLogisticsActionAsk((CheckLogisticsActionAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGELOGISTICACTIONASK:
                getScenePlaneService().handleChangeLogisticActionAsk((ChangeLogisticActionAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FIRSTENTERBIGSCENEASK:
                getScenePlayerMgrService().handleFirstEnterBigSceneAsk((FirstEnterBigSceneAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEPLAYERVIEWASK:
                getScenePlayerMgrService().handleUpdatePlayerViewAsk((UpdatePlayerViewAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CLEARPLAYERVIEWASK:
                getScenePlayerMgrService().handleClearPlayerViewAsk((ClearPlayerViewAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERADDSOLDIERASK:
                getScenePlayerMgrService().handlePlayerAddSoldierAsk((PlayerAddSoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.DISMISSINCITYSOLDIERSASK:
                getScenePlayerMgrService().handleDismissInCitySoldiersAsk((DismissInCitySoldiersAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.RETURNTREATOVERSOLDIERSASK:
                getScenePlayerMgrService().handleReturnTreatOverSoldiersAsk((ReturnTreatOverSoldiersAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETALLSOLDIERASK:
                getScenePlayerMgrService().handleGetAllSoldierAsk((GetAllSoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.HOSPITALTREATCHECKASK:
                getScenePlayerMgrService().handleHospitalTreatCheckAsk((HospitalTreatCheckAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.HOSPITALTREATASK:
                getScenePlayerMgrService().handleHospitalTreatAsk((HospitalTreatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.HOSPITALFASTTREATASK:
                getScenePlayerMgrService().handleHospitalFastTreatAsk((HospitalFastTreatAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.HOSPITALTREATFINISHCMD:
                getScenePlayerMgrService().handleHospitalTreatFinishCmd((HospitalTreatFinishCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.ADDDEVBUFFFROMPLAYERASK:
                getScenePlayerMgrService().handleAddDevBuffFromPlayerAsk((AddDevBuffFromPlayerAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REMOVEDEVBUFFFROMPLAYERASK:
                getScenePlayerMgrService().handleRemoveDevBuffFromPlayerAsk((RemoveDevBuffFromPlayerAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATEADDITIONFROMPLAYERCMD:
                getScenePlayerMgrService().handleUpdateAdditionFromPlayerCmd((UpdateAdditionFromPlayerCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETSCENEADDITIONSYSASK:
                getScenePlayerMgrService().handleGetSceneAdditionSysAsk((GetSceneAdditionSysAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETSCENEDEVBUFFASK:
                getScenePlayerMgrService().handleGetSceneDevBuffAsk((GetSceneDevBuffAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.MARKPOSITIONASK:
                getScenePlayerMgrService().handleMarkPositionAsk((MarkPositionAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETMARKREADEDCMD:
                getScenePlayerMgrService().handleSetMarkReadedCmd((SetMarkReadedCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERCLANIDNAMECMD:
                getScenePlayerMgrService().handleSyncPlayerClanIdNameCmd((SyncPlayerClanIdNameCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERNAMECMD:
                getScenePlayerMgrService().handleSyncPlayerNameCmd((SyncPlayerNameCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERSOLDIERCMD:
                getScenePlayerMgrService().handleSyncPlayerSoldierCmd((SyncPlayerSoldierCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERCITYBUILDLEVELCMD:
                getScenePlayerMgrService().handleSyncPlayerCityBuildLevelCmd((SyncPlayerCityBuildLevelCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLANECMD:
                getScenePlayerMgrService().handleSyncPlaneCmd((SyncPlaneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERHEROCMD:
                getScenePlayerMgrService().handleSyncPlayerHeroCmd((SyncPlayerHeroCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCTECHDATACMD:
                getScenePlayerMgrService().handleSyncTechDataCmd((SyncTechDataCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERPICCMD:
                getScenePlayerMgrService().handleSyncPlayerPicCmd((SyncPlayerPicCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERPICFRAMECMD:
                getScenePlayerMgrService().handleSyncPlayerPicFrameCmd((SyncPlayerPicFrameCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERWALLHEROPLANECMD:
                getScenePlayerMgrService().handleSyncPlayerWallHeroPlaneCmd((SyncPlayerWallHeroPlaneCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERERACMD:
                getScenePlayerMgrService().handleSyncPlayerEraCmd((SyncPlayerEraCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.MARKNEWBIEOVERASK:
                getScenePlayerMgrService().handleMarkNewbieOverAsk((MarkNewbieOverAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTONLINEPLAYERCSCMD:
                getScenePlayerMgrService().handleBroadcastOnlinePlayerCsCmd((BroadcastOnlinePlayerCsCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADCASTONLINEPLAYERCSWITHMULTILANGUAGECMD:
                getScenePlayerMgrService().handleBroadcastOnlinePlayerCsWithMultiLanguageCmd((BroadcastOnlinePlayerCsWithMultiLanguageCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETMILESTONEHISTORYASK:
                getScenePlayerMgrService().handleGetMileStoneHistoryAsk((GetMileStoneHistoryAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETMILESTONERANKINFOASK:
                getScenePlayerMgrService().handleGetMileStoneRankInfoAsk((GetMileStoneRankInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHMAINCITYINFOASK:
                getScenePlayerMgrService().handleFetchMainCityInfoAsk((FetchMainCityInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETEXPRESSIONCMD:
                getScenePlayerMgrService().handleSetExpressionCmd((SetExpressionCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETPFLAGCMD:
                getScenePlayerMgrService().handleSetPFlagCmd((SetPFlagCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETBATTLELOSEASK:
                getScenePlayerMgrService().handleGetBattleLoseAsk((GetBattleLoseAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.USEDUNGEONSKILLASK:
                getScenePlayerMgrService().handleUseDungeonSkillAsk((UseDungeonSkillAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGECITYDRESSASK:
                getScenePlayerMgrService().handleChangeCityDressAsk((ChangeCityDressAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHECKCANADDDEVBUFFASK:
                getScenePlayerMgrService().handleCheckCanAddDevBuffAsk((CheckCanAddDevBuffAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPRETURNALLARMYASK:
                getScenePlayerMgrService().handleIdIpReturnAllArmyAsk((IdIpReturnAllArmyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPGLOBALPEACESHIELDASK:
                getScenePlayerMgrService().handleIdIpGlobalPeaceShieldAsk((IdIpGlobalPeaceShieldAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SYNCPLAYERPUSHNTFINFOASK:
                getScenePlayerMgrService().handleSyncPlayerPushNtfInfoAsk((SyncPlayerPushNtfInfoAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYPLAYERPUSHNTFASK:
                getScenePlayerMgrService().handleQueryPlayerPushNtfAsk((QueryPlayerPushNtfAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPMODIFYSOLDIERASK:
                getScenePlayerMgrService().handleIdIpModifySoldierAsk((IdIpModifySoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BROADONLINEPLAYERSSCMD:
                getScenePlayerMgrService().handleBroadOnlinePlayerSsCmd((BroadOnlinePlayerSsCmd) typedMsg.getMsg());
                break;
            case SsMsgTypes.SKYNETFINDMONSTERASK:
                getScenePlayerMgrService().handleSkynetFindMonsterAsk((SkynetFindMonsterAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.GETZONESEASONASK:
                getScenePlayerMgrService().handleGetZoneSeasonAsk((GetZoneSeasonAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHRALLYLISTASK:
                getSceneRallyAssistService().handleFetchRallyListAsk((FetchRallyListAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHONERALLYASK:
                getSceneRallyAssistService().handleFetchOneRallyAsk((FetchOneRallyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.PLAYERCANCELRALLYASK:
                getSceneRallyAssistService().handlePlayerCancelRallyAsk((PlayerCancelRallyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REPATRIATERALLYMEMBERASK:
                getSceneRallyAssistService().handleRepatriateRallyMemberAsk((RepatriateRallyMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETRALLYRECOMMENDSOLDIERASK:
                getSceneRallyAssistService().handleSetRallyRecommendSoldierAsk((SetRallyRecommendSoldierAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHWARNINGASK:
                getSceneRallyAssistService().handleFetchWarningAsk((FetchWarningAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SETWARNINGITEMTAGASK:
                getSceneRallyAssistService().handleSetWarningItemTagAsk((SetWarningItemTagAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IGNOREALLWARNINGASK:
                getSceneRallyAssistService().handleIgnoreAllWarningAsk((IgnoreAllWarningAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.FETCHINNERARMYASK:
                getSceneRallyAssistService().handleFetchInnerArmyAsk((FetchInnerArmyAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.REPATRIATEASSISTMEMBERASK:
                getSceneRallyAssistService().handleRepatriateAssistMemberAsk((RepatriateAssistMemberAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.CHANGEASSISTLEADERASK:
                getSceneRallyAssistService().handleChangeAssistLeaderAsk((ChangeAssistLeaderAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
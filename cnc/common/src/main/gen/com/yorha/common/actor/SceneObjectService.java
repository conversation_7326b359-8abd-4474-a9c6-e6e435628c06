package com.yorha.common.actor;

import com.yorha.proto.SsSceneObj.*;

public interface SceneObjectService {

    void handlePlayerSearchMonsterAsk(PlayerSearchMonsterAsk ask);

    void handleAddMonsterAsk(AddMonsterAsk ask);

    void handleCheckCanBeAttackAsk(CheckCanBeAttackAsk ask);

    void handleQueryMapBuildingIdAsk(QueryMapBuildingIdAsk ask);

    void handleGetMonsterNumAsk(GetMonsterNumAsk ask);

    void handleRefreshActMonsterAsk(RefreshActMonsterAsk ask);

    void handleSummonSkynetMonsterAsk(SummonSkynetMonsterAsk ask); // 召唤天网野怪

}
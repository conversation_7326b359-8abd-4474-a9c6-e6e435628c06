package com.yorha.common.actor;

import com.yorha.proto.SsZoneChat.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ZoneChatServices {
    Logger LOGGER = LogManager.getLogger(ZoneChatServices.class);

    ZoneChatService getZoneChatService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.FETCHCHATMSGASK:
                getZoneChatService().handleFetchChatMsgAsk((FetchChatMsgAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.SENDCHATMSGASK:
                getZoneChatService().handleSendChatMsgAsk((SendChatMsgAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPSENDCHATMSGASK:
                getZoneChatService().handleIdIpSendChatMsgAsk((IdIpSendChatMsgAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.IDIPEXECSCRIPTMSGASK:
                getZoneChatService().handleIdIpExecScriptMsgAsk((IdIpExecScriptMsgAsk) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
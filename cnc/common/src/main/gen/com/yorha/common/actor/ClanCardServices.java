package com.yorha.common.actor;

import com.yorha.proto.SsClanCard.*;
import com.yorha.common.io.SsMsgTypes;
import com.yorha.gemini.actor.msg.TypedMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public interface ClanCardServices {
    Logger LOGGER = LogManager.getLogger(ClanCardServices.class);

    ClanCardService getClanCardService();

    default void dispatchProtoMsg(TypedMsg typedMsg) {
        final int msgType = typedMsg.getMsgType();

        // switch case模式应该优于hashMap
        switch (msgType) {
            case SsMsgTypes.QUERYCLANNAMEASK:
                getClanCardService().handleQueryClanNameAsk((QueryClanNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYCLANSIMPLEASK:
                getClanCardService().handleQueryClanSimpleAsk((QueryClanSimpleAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.QUERYCLANCARDASK:
                getClanCardService().handleQueryClanCardAsk((QueryClanCardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYCLANNAMEASK:
                getClanCardService().handleBatchQueryClanNameAsk((BatchQueryClanNameAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYCLANSIMPLEASK:
                getClanCardService().handleBatchQueryClanSimpleAsk((BatchQueryClanSimpleAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.BATCHQUERYCLANCARDASK:
                getClanCardService().handleBatchQueryClanCardAsk((BatchQueryClanCardAsk) typedMsg.getMsg());
                break;
            case SsMsgTypes.UPDATECLANCARDCMD:
                getClanCardService().handleUpdateClanCardCmd((UpdateClanCardCmd) typedMsg.getMsg());
                break;
            default:
                LOGGER.error("msgType not recognized.{}", msgType);
        }
    }
}
package com.yorha.common.actor;

import com.yorha.proto.SsSceneInfoMgr.*;

public interface SceneInformationMgrService {

    void handleGetOnlinePlayerIdAsk(GetOnlinePlayerIdAsk ask); // 获取所有在线玩家id

    void handleGetOnlinePlayerIdFilterByCreateTimeAsk(GetOnlinePlayerIdFilterByCreateTimeAsk ask); // 获取所有符合创角时间条件的在线玩家id

    void handleCheckPlayerOnlineAsk(CheckPlayerOnlineAsk ask); // 检测玩家列表是否都在线 返回没有在线的

    void handleGetZoneIpPortAsk(GetZoneIpPortAsk ask); // 获取服ip port

}
package com.yorha.common.actor;

import com.yorha.proto.SsGateSession.*;

public interface GateSessionService {

    void handleSendMsgToSessionCmd(SendMsgToSessionCmd ask); // 单发

    void handlePlayerBoundCmd(PlayerBoundCmd ask); // 玩家绑定结果（注册或登录触发）

    void handleUpdateClientInfoCmd(UpdateClientInfoCmd ask); // 更新客户端信息

    void handleSendMsgToSessionWithLanguageCmd(SendMsgToSessionWithLanguageCmd ask); // 单发(包含多语言，需要在gateSession进行多语言筛选)

    void handleKickOffSessionCmd(KickOffSessionCmd ask); // 踢下去

}
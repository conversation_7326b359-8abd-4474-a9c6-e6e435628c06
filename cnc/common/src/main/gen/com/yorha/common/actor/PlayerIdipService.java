package com.yorha.common.actor;

import com.yorha.proto.SsPlayerIdip.*;

public interface PlayerIdipService {

    void handleConsumeDiamondAsk(ConsumeDiamondAsk ask); // idip删除金条

    void handleConsumeItemsAsk(ConsumeItemsAsk ask); // idip删除道具

    void handlePullMidasCmd(PullMidasCmd ask); // idip更新余额

    void handleResourceAsk(ResourceAsk ask); // idip资源增删

    void handleModifyVipExpAsk(ModifyVipExpAsk ask); // idip修改vip经验

    void handleModifySoldierAsk(ModifySoldierAsk ask); // idip修改士兵数量

}
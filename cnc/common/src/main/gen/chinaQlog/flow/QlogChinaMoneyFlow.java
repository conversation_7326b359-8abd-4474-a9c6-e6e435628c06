
package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaMoneyFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "MoneyFlow";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "iMoneyType", "BeforeMoney", "AfterMoney", "iMoney", "AddOrReduce", "Reason", "SubReason"};
    /**
     * (必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 充值货币
     */
    private int iMoneyType;
    /**
     * 货币发生变化前的数量
     */
    private long beforeMoney;
    /**
     * 货币发生变化后的数量
     */
    private long afterMoney;
    /**
     * 货币改变的数量
     */
    private long iMoney;
    /**
     * 货币变化的类型
     */
    private int addOrReduce;
    /**
     * 货币变化的一级原因
     */
    private String reason;
    /**
     * 货币变化的二级原因
     */
    private String subReason;


    public QlogChinaMoneyFlow() {
        dtEventTime = "";
        reason = "";
        subReason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaMoneyFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaMoneyFlow setIMoneyType(int iMoneyType) {
        bitFiled0_ |= 0x2;
        this.iMoneyType = iMoneyType;
        return this;
    }

    public QlogChinaMoneyFlow setBeforeMoney(long beforeMoney) {
        bitFiled0_ |= 0x4;
        this.beforeMoney = beforeMoney;
        return this;
    }

    public QlogChinaMoneyFlow setAfterMoney(long afterMoney) {
        bitFiled0_ |= 0x8;
        this.afterMoney = afterMoney;
        return this;
    }

    public QlogChinaMoneyFlow setIMoney(long iMoney) {
        bitFiled0_ |= 0x10;
        this.iMoney = iMoney;
        return this;
    }

    public QlogChinaMoneyFlow setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }

    public QlogChinaMoneyFlow setReason(String reason) {
        bitFiled0_ |= 0x40;
        this.reason = reason;
        return this;
    }

    public QlogChinaMoneyFlow setSubReason(String subReason) {
        bitFiled0_ |= 0x80;
        this.subReason = subReason;
        return this;
    }


    public static QlogChinaMoneyFlow init(QlogPlayerFlowInterface flow_name) {
        QlogChinaMoneyFlow flow = new QlogChinaMoneyFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iMoneyType);
        builder.append("|").append(beforeMoney);
        builder.append("|").append(afterMoney);
        builder.append("|").append(iMoney);
        builder.append("|").append(addOrReduce);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(subReason, 0, Math.min(subReason.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


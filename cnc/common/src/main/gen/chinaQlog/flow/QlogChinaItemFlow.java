
package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaItemFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "ItemFlow";
    static final int currentFieldCnt = 12;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "IGoodsId", "BeforeCount", "AfterCount", "Count", "AddOrReduce", "Reason", "SubReason", "iGoodsType", "iMoney", "iMoneyType"};
    /**
     * (必填)(必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 发生变化的道具id
     */
    private String iGoodsId;
    /**
     * 道具发生变化前的数量
     */
    private long beforeCount;
    /**
     * 发生变化后的数量
     */
    private long afterCount;
    /**
     * 道具改变的数量
     */
    private long count;
    /**
     * 道具变化的类型(0：道具增加,1：道具减少)
     */
    private int addOrReduce;
    /**
     * 道具变化的一级原因
     */
    private String reason;
    /**
     * 道具变化的二级原因
     */
    private String subReason;
    /**
     * 道具类型
     */
    private int iGoodsType;
    /**
     * 花费代币或金币购买道具情况下输出消耗的钱数量，否则填0
     */
    private int iMoney;
    /**
     * 钱的类型
     */
    private int iMoneyType;


    public QlogChinaItemFlow() {
        dtEventTime = "";
        iGoodsId = "";
        reason = "";
        subReason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaItemFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaItemFlow setIGoodsId(String iGoodsId) {
        bitFiled0_ |= 0x2;
        this.iGoodsId = iGoodsId;
        return this;
    }

    public QlogChinaItemFlow setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x4;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogChinaItemFlow setAfterCount(long afterCount) {
        bitFiled0_ |= 0x8;
        this.afterCount = afterCount;
        return this;
    }

    public QlogChinaItemFlow setCount(long count) {
        bitFiled0_ |= 0x10;
        this.count = count;
        return this;
    }

    public QlogChinaItemFlow setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }

    public QlogChinaItemFlow setReason(String reason) {
        bitFiled0_ |= 0x40;
        this.reason = reason;
        return this;
    }

    public QlogChinaItemFlow setSubReason(String subReason) {
        bitFiled0_ |= 0x80;
        this.subReason = subReason;
        return this;
    }

    public QlogChinaItemFlow setIGoodsType(int iGoodsType) {
        bitFiled0_ |= 0x100;
        this.iGoodsType = iGoodsType;
        return this;
    }

    public QlogChinaItemFlow setIMoney(int iMoney) {
        bitFiled0_ |= 0x200;
        this.iMoney = iMoney;
        return this;
    }

    public QlogChinaItemFlow setIMoneyType(int iMoneyType) {
        bitFiled0_ |= 0x400;
        this.iMoneyType = iMoneyType;
        return this;
    }


    public static QlogChinaItemFlow init(QlogPlayerFlowInterface flow_name) {
        QlogChinaItemFlow flow = new QlogChinaItemFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x400) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iGoodsId, 0, Math.min(iGoodsId.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(count);
        builder.append("|").append(addOrReduce);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(subReason, 0, Math.min(subReason.length(), 32));
        builder.append("|").append(iGoodsType);
        builder.append("|").append(iMoney);
        builder.append("|").append(iMoneyType);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaPlayerLogout extends AbstractPlayerQlogFlow {
    static final String META_NAME = "PlayerLogout";
    static final int currentFieldCnt = 15;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "SystemSoftware", "SystemHardware", "Memory", "GlVersion", "DeviceId", "OnlineTime", "Reason", "AccountName", "Country", "AdjustId", "LoginChannel", "xwid", "RegChannel"};
    /**
     * (必填)(必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 移动终端操作系统版本
     */
    private String systemSoftware;
    /**
     * 移动终端机型
     */
    private String systemHardware;
    /**
     * 内存信息单位M
     */
    private int memory;
    /**
     * opengl版本信息
     */
    private String glVersion;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 本次登录在线时间(秒)
     */
    private long onlineTime;
    /**
     * 登出原因
     */
    private String reason;
    /**
     * 账号名
     */
    private String accountName;
    /**
     * 玩家地区
     */
    private String country;
    /**
     * adjustId
     */
    private String adjustId;
    /**
     * 登录渠道
     */
    private int loginChannel;
    /**
     * 海外设备ID
     */
    private String xwid;
    /**
     * 注册渠道
     */
    private int regChannel;


    public QlogChinaPlayerLogout() {
        dtEventTime = "";
        systemSoftware = "";
        systemHardware = "";
        glVersion = "";
        deviceId = "";
        reason = "";
        accountName = "";
        country = "";
        adjustId = "";
        xwid = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaPlayerLogout setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaPlayerLogout setSystemSoftware(String systemSoftware) {
        bitFiled0_ |= 0x2;
        this.systemSoftware = systemSoftware;
        return this;
    }

    public QlogChinaPlayerLogout setSystemHardware(String systemHardware) {
        bitFiled0_ |= 0x4;
        this.systemHardware = systemHardware;
        return this;
    }

    public QlogChinaPlayerLogout setMemory(int memory) {
        bitFiled0_ |= 0x8;
        this.memory = memory;
        return this;
    }

    public QlogChinaPlayerLogout setGlVersion(String glVersion) {
        bitFiled0_ |= 0x10;
        this.glVersion = glVersion;
        return this;
    }

    public QlogChinaPlayerLogout setDeviceId(String deviceId) {
        bitFiled0_ |= 0x20;
        this.deviceId = deviceId;
        return this;
    }

    public QlogChinaPlayerLogout setOnlineTime(long onlineTime) {
        bitFiled0_ |= 0x40;
        this.onlineTime = onlineTime;
        return this;
    }

    public QlogChinaPlayerLogout setReason(String reason) {
        bitFiled0_ |= 0x80;
        this.reason = reason;
        return this;
    }

    public QlogChinaPlayerLogout setAccountName(String accountName) {
        bitFiled0_ |= 0x100;
        this.accountName = accountName;
        return this;
    }

    public QlogChinaPlayerLogout setCountry(String country) {
        bitFiled0_ |= 0x200;
        this.country = country;
        return this;
    }

    public QlogChinaPlayerLogout setAdjustId(String adjustId) {
        bitFiled0_ |= 0x400;
        this.adjustId = adjustId;
        return this;
    }

    public QlogChinaPlayerLogout setLoginChannel(int loginChannel) {
        bitFiled0_ |= 0x800;
        this.loginChannel = loginChannel;
        return this;
    }

    public QlogChinaPlayerLogout setXwid(String xwid) {
        bitFiled0_ |= 0x1000;
        this.xwid = xwid;
        return this;
    }

    public QlogChinaPlayerLogout setRegChannel(int regChannel) {
        bitFiled0_ |= 0x2000;
        this.regChannel = regChannel;
        return this;
    }


    public static QlogChinaPlayerLogout init(QlogPlayerFlowInterface flow_name) {
        QlogChinaPlayerLogout flow = new QlogChinaPlayerLogout();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3fff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x2000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(systemSoftware, 0, Math.min(systemSoftware.length(), 32));
        builder.append("|").append(systemHardware, 0, Math.min(systemHardware.length(), 32));
        builder.append("|").append(memory);
        builder.append("|").append(glVersion, 0, Math.min(glVersion.length(), 32));
        builder.append("|").append(deviceId, 0, Math.min(deviceId.length(), 64));
        builder.append("|").append(onlineTime);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(accountName, 0, Math.min(accountName.length(), 32));
        builder.append("|").append(country, 0, Math.min(country.length(), 64));
        builder.append("|").append(adjustId, 0, Math.min(adjustId.length(), 64));
        builder.append("|").append(loginChannel);
        builder.append("|").append(xwid, 0, Math.min(xwid.length(), 64));
        builder.append("|").append(regChannel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaPlayerLogin extends AbstractPlayerQlogFlow {
    static final String META_NAME = "PlayerLogin";
    static final int currentFieldCnt = 17;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "SystemSoftware", "SystemHardware", "Memory", "GlVersion", "DeviceId", "AccountName", "Country", "AdjustId", "LoginChannel", "xwid", "DeviceLevel", "OaId", "RegChannel", "MsdkRegChannel", "MsdkLoginChannel"};
    /**
     * (必填)(必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 移动终端操作系统版本
     */
    private String systemSoftware;
    /**
     * 移动终端机型
     */
    private String systemHardware;
    /**
     * 内存信息单位M
     */
    private int memory;
    /**
     * opengl版本信息
     */
    private String glVersion;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 账号名
     */
    private String accountName;
    /**
     * 玩家地区
     */
    private String country;
    /**
     * adjustId
     */
    private String adjustId;
    /**
     * 登录渠道
     */
    private int loginChannel;
    /**
     * 海外设备ID
     */
    private String xwid;
    /**
     * 设备机型档位
     */
    private int deviceLevel;
    /**
     * 安卓设备oaid
     */
    private String oaId;
    /**
     * 注册渠道
     */
    private int regChannel;
    /**
     * msdk注册渠道
     */
    private String msdkRegChannel;
    /**
     * msdk登录渠道
     */
    private String msdkLoginChannel;


    public QlogChinaPlayerLogin() {
        dtEventTime = "";
        systemSoftware = "";
        systemHardware = "";
        glVersion = "";
        deviceId = "";
        accountName = "";
        country = "";
        adjustId = "";
        xwid = "";
        oaId = "";
        msdkRegChannel = "";
        msdkLoginChannel = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaPlayerLogin setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaPlayerLogin setSystemSoftware(String systemSoftware) {
        bitFiled0_ |= 0x2;
        this.systemSoftware = systemSoftware;
        return this;
    }

    public QlogChinaPlayerLogin setSystemHardware(String systemHardware) {
        bitFiled0_ |= 0x4;
        this.systemHardware = systemHardware;
        return this;
    }

    public QlogChinaPlayerLogin setMemory(int memory) {
        bitFiled0_ |= 0x8;
        this.memory = memory;
        return this;
    }

    public QlogChinaPlayerLogin setGlVersion(String glVersion) {
        bitFiled0_ |= 0x10;
        this.glVersion = glVersion;
        return this;
    }

    public QlogChinaPlayerLogin setDeviceId(String deviceId) {
        bitFiled0_ |= 0x20;
        this.deviceId = deviceId;
        return this;
    }

    public QlogChinaPlayerLogin setAccountName(String accountName) {
        bitFiled0_ |= 0x40;
        this.accountName = accountName;
        return this;
    }

    public QlogChinaPlayerLogin setCountry(String country) {
        bitFiled0_ |= 0x80;
        this.country = country;
        return this;
    }

    public QlogChinaPlayerLogin setAdjustId(String adjustId) {
        bitFiled0_ |= 0x100;
        this.adjustId = adjustId;
        return this;
    }

    public QlogChinaPlayerLogin setLoginChannel(int loginChannel) {
        bitFiled0_ |= 0x200;
        this.loginChannel = loginChannel;
        return this;
    }

    public QlogChinaPlayerLogin setXwid(String xwid) {
        bitFiled0_ |= 0x400;
        this.xwid = xwid;
        return this;
    }

    public QlogChinaPlayerLogin setDeviceLevel(int deviceLevel) {
        bitFiled0_ |= 0x800;
        this.deviceLevel = deviceLevel;
        return this;
    }

    public QlogChinaPlayerLogin setOaId(String oaId) {
        bitFiled0_ |= 0x1000;
        this.oaId = oaId;
        return this;
    }

    public QlogChinaPlayerLogin setRegChannel(int regChannel) {
        bitFiled0_ |= 0x2000;
        this.regChannel = regChannel;
        return this;
    }

    public QlogChinaPlayerLogin setMsdkRegChannel(String msdkRegChannel) {
        bitFiled0_ |= 0x4000;
        this.msdkRegChannel = msdkRegChannel;
        return this;
    }

    public QlogChinaPlayerLogin setMsdkLoginChannel(String msdkLoginChannel) {
        bitFiled0_ |= 0x8000;
        this.msdkLoginChannel = msdkLoginChannel;
        return this;
    }


    public static QlogChinaPlayerLogin init(QlogPlayerFlowInterface flow_name) {
        QlogChinaPlayerLogin flow = new QlogChinaPlayerLogin();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xffff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(systemSoftware, 0, Math.min(systemSoftware.length(), 32));
        builder.append("|").append(systemHardware, 0, Math.min(systemHardware.length(), 32));
        builder.append("|").append(memory);
        builder.append("|").append(glVersion, 0, Math.min(glVersion.length(), 32));
        builder.append("|").append(deviceId, 0, Math.min(deviceId.length(), 64));
        builder.append("|").append(accountName, 0, Math.min(accountName.length(), 32));
        builder.append("|").append(country, 0, Math.min(country.length(), 64));
        builder.append("|").append(adjustId, 0, Math.min(adjustId.length(), 64));
        builder.append("|").append(loginChannel);
        builder.append("|").append(xwid, 0, Math.min(xwid.length(), 64));
        builder.append("|").append(deviceLevel);
        builder.append("|").append(oaId, 0, Math.min(oaId.length(), 64));
        builder.append("|").append(regChannel);
        builder.append("|").append(msdkRegChannel, 0, Math.min(msdkRegChannel.length(), 32));
        builder.append("|").append(msdkLoginChannel, 0, Math.min(msdkLoginChannel.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


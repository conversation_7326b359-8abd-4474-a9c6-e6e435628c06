
package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaCncVipExp extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncVipExp";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "Action", "ExpCount", "AfterVipLevel"};
    /**
     * (必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 获得的经验数量
     */
    private int expCount;
    /**
     * 行为后的VIP系统等级
     */
    private int afterVipLevel;


    public QlogChinaCncVipExp() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaCncVipExp setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaCncVipExp setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogChinaCncVipExp setExpCount(int expCount) {
        bitFiled0_ |= 0x4;
        this.expCount = expCount;
        return this;
    }

    public QlogChinaCncVipExp setAfterVipLevel(int afterVipLevel) {
        bitFiled0_ |= 0x8;
        this.afterVipLevel = afterVipLevel;
        return this;
    }


    public static QlogChinaCncVipExp init(QlogPlayerFlowInterface flow_name) {
        QlogChinaCncVipExp flow = new QlogChinaCncVipExp();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(expCount);
        builder.append("|").append(afterVipLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


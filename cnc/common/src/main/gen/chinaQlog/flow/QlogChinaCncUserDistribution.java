
package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaCncUserDistribution extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncUserDistribution";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"dtEventTime", "UserFeature", "DistributionStrategy", "ContinentlD", "Coordinates"};
    /**
     * (必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 用户参数(json格式)
     */
    private String userFeature;
    /**
     * 导量走的策略
     */
    private String distributionStrategy;
    /**
     * 落堡的州ID
     */
    private int continentlD;
    /**
     * 落堡的坐标(格式为[x, y])
     */
    private String coordinates;


    public QlogChinaCncUserDistribution() {
        dtEventTime = "";
        userFeature = "";
        distributionStrategy = "";
        coordinates = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaCncUserDistribution setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaCncUserDistribution setUserFeature(String userFeature) {
        bitFiled0_ |= 0x2;
        this.userFeature = userFeature;
        return this;
    }

    public QlogChinaCncUserDistribution setDistributionStrategy(String distributionStrategy) {
        bitFiled0_ |= 0x4;
        this.distributionStrategy = distributionStrategy;
        return this;
    }

    public QlogChinaCncUserDistribution setContinentlD(int continentlD) {
        bitFiled0_ |= 0x8;
        this.continentlD = continentlD;
        return this;
    }

    public QlogChinaCncUserDistribution setCoordinates(String coordinates) {
        bitFiled0_ |= 0x10;
        this.coordinates = coordinates;
        return this;
    }


    public static QlogChinaCncUserDistribution init(QlogPlayerFlowInterface flow_name) {
        QlogChinaCncUserDistribution flow = new QlogChinaCncUserDistribution();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(userFeature, 0, Math.min(userFeature.length(), 32));
        builder.append("|").append(distributionStrategy, 0, Math.min(distributionStrategy.length(), 32));
        builder.append("|").append(continentlD);
        builder.append("|").append(coordinates, 0, Math.min(coordinates.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


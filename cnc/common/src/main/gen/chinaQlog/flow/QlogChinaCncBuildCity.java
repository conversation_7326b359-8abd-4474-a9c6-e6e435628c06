
package chinaQlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogChinaCncBuildCity extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBuildCity";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Action", "dtEventTime", "StartTime", "UniqueBuildingID", "BuildingType", "AfterBuildingLevel", "BuildingFormation"};
    /**
     * 行为类型
     */
    private String action;
    /**
     * (必填)(必填)游戏事件的时间, 格式 YYYY-MM-DD HH:MM:SS, 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 建筑修复或升级时最初开始的时间
     */
    private String startTime;
    /**
     * 建筑唯一id
     */
    private String uniqueBuildingID;
    /**
     * 建筑类型
     */
    private String buildingType;
    /**
     * 建筑升级后的等级
     */
    private int afterBuildingLevel;
    /**
     * 建筑升级所用的队列
     */
    private int buildingFormation;


    public QlogChinaCncBuildCity() {
        action = "";
        dtEventTime = "";
        startTime = "";
        uniqueBuildingID = "";
        buildingType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogChinaCncBuildCity setAction(String action) {
        bitFiled0_ |= 0x1;
        this.action = action;
        return this;
    }

    public QlogChinaCncBuildCity setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x2;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogChinaCncBuildCity setStartTime(String startTime) {
        bitFiled0_ |= 0x4;
        this.startTime = startTime;
        return this;
    }

    public QlogChinaCncBuildCity setUniqueBuildingID(String uniqueBuildingID) {
        bitFiled0_ |= 0x8;
        this.uniqueBuildingID = uniqueBuildingID;
        return this;
    }

    public QlogChinaCncBuildCity setBuildingType(String buildingType) {
        bitFiled0_ |= 0x10;
        this.buildingType = buildingType;
        return this;
    }

    public QlogChinaCncBuildCity setAfterBuildingLevel(int afterBuildingLevel) {
        bitFiled0_ |= 0x20;
        this.afterBuildingLevel = afterBuildingLevel;
        return this;
    }

    public QlogChinaCncBuildCity setBuildingFormation(int buildingFormation) {
        bitFiled0_ |= 0x40;
        this.buildingFormation = buildingFormation;
        return this;
    }


    public static QlogChinaCncBuildCity init(QlogPlayerFlowInterface flow_name) {
        QlogChinaCncBuildCity flow = new QlogChinaCncBuildCity();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(startTime, 0, Math.min(startTime.length(), 32));
        builder.append("|").append(uniqueBuildingID, 0, Math.min(uniqueBuildingID.length(), 64));
        builder.append("|").append(buildingType, 0, Math.min(buildingType.length(), 32));
        builder.append("|").append(afterBuildingLevel);
        builder.append("|").append(buildingFormation);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBpExpFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBpExpFlow";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "GetExp", "BeforeLevel", "AfterLevel", "AfterExp", "ObjectId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 获得的经验
     */
    private int getExp;
    /**
     * 获得前BP等级
     */
    private int beforeLevel;
    /**
     * 获得后BP等级
     */
    private int afterLevel;
    /**
     * 获得后BP经验
     */
    private int afterExp;
    /**
     * 对象ID 
     */
    private int objectId;


    public QlogCncBpExpFlow() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBpExpFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBpExpFlow setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncBpExpFlow setGetExp(int getExp) {
        bitFiled0_ |= 0x4;
        this.getExp = getExp;
        return this;
    }

    public QlogCncBpExpFlow setBeforeLevel(int beforeLevel) {
        bitFiled0_ |= 0x8;
        this.beforeLevel = beforeLevel;
        return this;
    }

    public QlogCncBpExpFlow setAfterLevel(int afterLevel) {
        bitFiled0_ |= 0x10;
        this.afterLevel = afterLevel;
        return this;
    }

    public QlogCncBpExpFlow setAfterExp(int afterExp) {
        bitFiled0_ |= 0x20;
        this.afterExp = afterExp;
        return this;
    }

    public QlogCncBpExpFlow setObjectId(int objectId) {
        bitFiled0_ |= 0x40;
        this.objectId = objectId;
        return this;
    }


    public static QlogCncBpExpFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncBpExpFlow flow = new QlogCncBpExpFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(getExp);
        builder.append("|").append(beforeLevel);
        builder.append("|").append(afterLevel);
        builder.append("|").append(afterExp);
        builder.append("|").append(objectId);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


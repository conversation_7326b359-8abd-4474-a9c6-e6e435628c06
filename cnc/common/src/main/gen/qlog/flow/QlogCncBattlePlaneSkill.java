
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBattlePlaneSkill extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBattlePlaneSkill";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BattlePlaneType", "Action", "BattlePlaneSkillID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 战机类型
     */
    private int battlePlaneType;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 战机技能id
     */
    private int battlePlaneSkillID;


    public QlogCncBattlePlaneSkill() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBattlePlaneSkill setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBattlePlaneSkill setBattlePlaneType(int battlePlaneType) {
        bitFiled0_ |= 0x2;
        this.battlePlaneType = battlePlaneType;
        return this;
    }

    public QlogCncBattlePlaneSkill setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncBattlePlaneSkill setBattlePlaneSkillID(int battlePlaneSkillID) {
        bitFiled0_ |= 0x8;
        this.battlePlaneSkillID = battlePlaneSkillID;
        return this;
    }


    public static QlogCncBattlePlaneSkill init(QlogPlayerFlowInterface flow_name) {
        QlogCncBattlePlaneSkill flow = new QlogCncBattlePlaneSkill();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(battlePlaneType);
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(battlePlaneSkillID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


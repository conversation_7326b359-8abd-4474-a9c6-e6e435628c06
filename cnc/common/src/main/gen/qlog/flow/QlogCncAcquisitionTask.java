
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncAcquisitionTask extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncAcquisitionTask";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Identity", "Action", "TaskId", "InviteeId", "Score"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 身份
     */
    private String identity;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 任务ID
     */
    private int taskId;
    /**
     * 被邀请者ID
     */
    private long inviteeId;
    /**
     * 积分
     */
    private long score;


    public QlogCncAcquisitionTask() {
        dtEventTime = "";
        identity = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncAcquisitionTask setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncAcquisitionTask setIdentity(String identity) {
        bitFiled0_ |= 0x2;
        this.identity = identity;
        return this;
    }

    public QlogCncAcquisitionTask setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncAcquisitionTask setTaskId(int taskId) {
        bitFiled0_ |= 0x8;
        this.taskId = taskId;
        return this;
    }

    public QlogCncAcquisitionTask setInviteeId(long inviteeId) {
        bitFiled0_ |= 0x10;
        this.inviteeId = inviteeId;
        return this;
    }

    public QlogCncAcquisitionTask setScore(long score) {
        bitFiled0_ |= 0x20;
        this.score = score;
        return this;
    }


    public static QlogCncAcquisitionTask init(QlogPlayerFlowInterface flow_name) {
        QlogCncAcquisitionTask flow = new QlogCncAcquisitionTask();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(identity, 0, Math.min(identity.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(taskId);
        builder.append("|").append(inviteeId);
        builder.append("|").append(score);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


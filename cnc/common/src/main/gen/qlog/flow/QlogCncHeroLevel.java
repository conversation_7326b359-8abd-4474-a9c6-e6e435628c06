
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncHeroLevel extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncHeroLevel";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "HeroID", "Action", "SubAction", "AfterHeroLevel", "AfterHeroExp", "HeroExpCount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 英雄id
     */
    private String heroID;
    /**
     * 获得行为
     */
    private String action;
    /**
     * 次级行为类型
     */
    private String subAction;
    /**
     * 行为后等级
     */
    private int afterHeroLevel;
    /**
     * 行为后英雄经验余量
     */
    private long afterHeroExp;
    /**
     * 变化的英雄经验数值
     */
    private long heroExpCount;


    public QlogCncHeroLevel() {
        dtEventTime = "";
        heroID = "";
        action = "";
        subAction = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncHeroLevel setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncHeroLevel setHeroID(String heroID) {
        bitFiled0_ |= 0x2;
        this.heroID = heroID;
        return this;
    }

    public QlogCncHeroLevel setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncHeroLevel setSubAction(String subAction) {
        bitFiled0_ |= 0x8;
        this.subAction = subAction;
        return this;
    }

    public QlogCncHeroLevel setAfterHeroLevel(int afterHeroLevel) {
        bitFiled0_ |= 0x10;
        this.afterHeroLevel = afterHeroLevel;
        return this;
    }

    public QlogCncHeroLevel setAfterHeroExp(long afterHeroExp) {
        bitFiled0_ |= 0x20;
        this.afterHeroExp = afterHeroExp;
        return this;
    }

    public QlogCncHeroLevel setHeroExpCount(long heroExpCount) {
        bitFiled0_ |= 0x40;
        this.heroExpCount = heroExpCount;
        return this;
    }


    public static QlogCncHeroLevel init(QlogPlayerFlowInterface flow_name) {
        QlogCncHeroLevel flow = new QlogCncHeroLevel();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(heroID, 0, Math.min(heroID.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(subAction, 0, Math.min(subAction.length(), 32));
        builder.append("|").append(afterHeroLevel);
        builder.append("|").append(afterHeroExp);
        builder.append("|").append(heroExpCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


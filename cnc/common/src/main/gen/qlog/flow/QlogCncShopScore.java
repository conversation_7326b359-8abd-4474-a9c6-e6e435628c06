
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncShopScore extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncShopScore";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "score", "num", "reason", "after_popup"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 评分
     */
    private int score;
    /**
     * 第几次触发
     */
    private int num;
    /**
     * 评分弹出满足条件
     */
    private String reason;
    /**
     * 拉起后续弹框类型
     */
    private int after_popup;


    public QlogCncShopScore() {
        dtEventTime = "";
        action = "";
        reason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncShopScore setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncShopScore setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncShopScore setScore(int score) {
        bitFiled0_ |= 0x4;
        this.score = score;
        return this;
    }

    public QlogCncShopScore setNum(int num) {
        bitFiled0_ |= 0x8;
        this.num = num;
        return this;
    }

    public QlogCncShopScore setReason(String reason) {
        bitFiled0_ |= 0x10;
        this.reason = reason;
        return this;
    }

    public QlogCncShopScore setAfter_popup(int after_popup) {
        bitFiled0_ |= 0x20;
        this.after_popup = after_popup;
        return this;
    }


    public static QlogCncShopScore init(QlogPlayerFlowInterface flow_name) {
        QlogCncShopScore flow = new QlogCncShopScore();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(score);
        builder.append("|").append(num);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 64));
        builder.append("|").append(after_popup);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuidanceRecordFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuidanceRecordFlow";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "JobID", "Step", "State"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 引导id
     */
    private int jobID;
    /**
     * 当前步骤
     */
    private int step;
    /**
     * 状态，0表示触发该引导，1表示完成某一步
     */
    private int state;


    public QlogCncGuidanceRecordFlow() {
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuidanceRecordFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuidanceRecordFlow setJobID(int jobID) {
        bitFiled0_ |= 0x2;
        this.jobID = jobID;
        return this;
    }

    public QlogCncGuidanceRecordFlow setStep(int step) {
        bitFiled0_ |= 0x4;
        this.step = step;
        return this;
    }

    public QlogCncGuidanceRecordFlow setState(int state) {
        bitFiled0_ |= 0x8;
        this.state = state;
        return this;
    }


    public static QlogCncGuidanceRecordFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuidanceRecordFlow flow = new QlogCncGuidanceRecordFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(jobID);
        builder.append("|").append(step);
        builder.append("|").append(state);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


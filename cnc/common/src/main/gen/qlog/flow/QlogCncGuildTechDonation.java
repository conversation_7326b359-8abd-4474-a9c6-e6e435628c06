
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildTechDonation extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuildTechDonation";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "DonateQuantity", "GuildTechID", "GuildSubTechID", "GuildTechScore", "GuildScoreDiff"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 捐献消耗资源数量
     */
    private int donateQuantity;
    /**
     * 联盟科技id
     */
    private int guildTechID;
    /**
     * 联盟科技子id
     */
    private int guildSubTechID;
    /**
     * 捐献后的联盟科技点
     */
    private int guildTechScore;
    /**
     * 暴击后的积分增值
     */
    private int guildScoreDiff;


    public QlogCncGuildTechDonation() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildTechDonation setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildTechDonation setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildTechDonation setDonateQuantity(int donateQuantity) {
        bitFiled0_ |= 0x4;
        this.donateQuantity = donateQuantity;
        return this;
    }

    public QlogCncGuildTechDonation setGuildTechID(int guildTechID) {
        bitFiled0_ |= 0x8;
        this.guildTechID = guildTechID;
        return this;
    }

    public QlogCncGuildTechDonation setGuildSubTechID(int guildSubTechID) {
        bitFiled0_ |= 0x10;
        this.guildSubTechID = guildSubTechID;
        return this;
    }

    public QlogCncGuildTechDonation setGuildTechScore(int guildTechScore) {
        bitFiled0_ |= 0x20;
        this.guildTechScore = guildTechScore;
        return this;
    }

    public QlogCncGuildTechDonation setGuildScoreDiff(int guildScoreDiff) {
        bitFiled0_ |= 0x40;
        this.guildScoreDiff = guildScoreDiff;
        return this;
    }


    public static QlogCncGuildTechDonation init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuildTechDonation flow = new QlogCncGuildTechDonation();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(donateQuantity);
        builder.append("|").append(guildTechID);
        builder.append("|").append(guildSubTechID);
        builder.append("|").append(guildTechScore);
        builder.append("|").append(guildScoreDiff);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


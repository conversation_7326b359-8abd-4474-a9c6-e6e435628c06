
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncImmigrant extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncImmigrant";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "Type", "TargetZone"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 移民类型
     */
    private String type;
    /**
     * 目标大区
     */
    private int targetZone;


    public QlogCncImmigrant() {
        dtEventTime = "";
        action = "";
        type = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncImmigrant setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncImmigrant setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncImmigrant setType(String type) {
        bitFiled0_ |= 0x4;
        this.type = type;
        return this;
    }

    public QlogCncImmigrant setTargetZone(int targetZone) {
        bitFiled0_ |= 0x8;
        this.targetZone = targetZone;
        return this;
    }


    public static QlogCncImmigrant init(QlogPlayerFlowInterface flow_name) {
        QlogCncImmigrant flow = new QlogCncImmigrant();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(type, 0, Math.min(type.length(), 64));
        builder.append("|").append(targetZone);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncShop extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncShop";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ShopType", "Action", "IMoneyUse", "IGoodsGet"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 商店类型
     */
    private String shopType;
    /**
     * 商店行为
     */
    private String action;
    /**
     * 使用的货币类型和数量
     */
    private String iMoneyUse;
    /**
     * 获得的道具id和个数
     */
    private String iGoodsGet;


    public QlogCncShop() {
        dtEventTime = "";
        shopType = "";
        action = "";
        iMoneyUse = "";
        iGoodsGet = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncShop setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncShop setShopType(String shopType) {
        bitFiled0_ |= 0x2;
        this.shopType = shopType;
        return this;
    }

    public QlogCncShop setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncShop setIMoneyUse(String iMoneyUse) {
        bitFiled0_ |= 0x8;
        this.iMoneyUse = iMoneyUse;
        return this;
    }

    public QlogCncShop setIGoodsGet(String iGoodsGet) {
        bitFiled0_ |= 0x10;
        this.iGoodsGet = iGoodsGet;
        return this;
    }


    public static QlogCncShop init(QlogPlayerFlowInterface flow_name) {
        QlogCncShop flow = new QlogCncShop();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(shopType, 0, Math.min(shopType.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(iMoneyUse, 0, Math.min(iMoneyUse.length(), 2560));
        builder.append("|").append(iGoodsGet, 0, Math.min(iGoodsGet.length(), 2560));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


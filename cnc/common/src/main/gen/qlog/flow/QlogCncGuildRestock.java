
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildRestock extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildRestock";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "OptionRoleId", "OptionRoleGrade", "IGoodsType", "IGoodsId", "ICount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 进货者的角色id
     */
    private String optionRoleId;
    /**
     * 进货者的职级
     */
    private int optionRoleGrade;
    /**
     * 获取类型
     */
    private int iGoodsType;
    /**
     * 发生变化的道具id
     */
    private String iGoodsId;
    /**
     * 进货的道具数量
     */
    private int iCount;


    public QlogCncGuildRestock() {
        dtEventTime = "";
        action = "";
        optionRoleId = "";
        iGoodsId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildRestock setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildRestock setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildRestock setOptionRoleId(String optionRoleId) {
        bitFiled0_ |= 0x4;
        this.optionRoleId = optionRoleId;
        return this;
    }

    public QlogCncGuildRestock setOptionRoleGrade(int optionRoleGrade) {
        bitFiled0_ |= 0x8;
        this.optionRoleGrade = optionRoleGrade;
        return this;
    }

    public QlogCncGuildRestock setIGoodsType(int iGoodsType) {
        bitFiled0_ |= 0x10;
        this.iGoodsType = iGoodsType;
        return this;
    }

    public QlogCncGuildRestock setIGoodsId(String iGoodsId) {
        bitFiled0_ |= 0x20;
        this.iGoodsId = iGoodsId;
        return this;
    }

    public QlogCncGuildRestock setICount(int iCount) {
        bitFiled0_ |= 0x40;
        this.iCount = iCount;
        return this;
    }


    public static QlogCncGuildRestock init(QlogClanFlowInterface flow_name) {
        QlogCncGuildRestock flow = new QlogCncGuildRestock();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(optionRoleId, 0, Math.min(optionRoleId.length(), 32));
        builder.append("|").append(optionRoleGrade);
        builder.append("|").append(iGoodsType);
        builder.append("|").append(iGoodsId, 0, Math.min(iGoodsId.length(), 32));
        builder.append("|").append(iCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


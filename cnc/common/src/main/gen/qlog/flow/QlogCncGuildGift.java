
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildGift extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildGift";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "RoleId", "GiftType", "TargetId"};
    /**
     * 行为时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 玩家角色ID
     */
    private String roleId;
    /**
     * 礼物类型
     */
    private String giftType;
    /**
     * 技能ID
     */
    private String targetId;


    public QlogCncGuildGift() {
        dtEventTime = "";
        action = "";
        roleId = "";
        giftType = "";
        targetId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildGift setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildGift setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildGift setRoleId(String roleId) {
        bitFiled0_ |= 0x4;
        this.roleId = roleId;
        return this;
    }

    public QlogCncGuildGift setGiftType(String giftType) {
        bitFiled0_ |= 0x8;
        this.giftType = giftType;
        return this;
    }

    public QlogCncGuildGift setTargetId(String targetId) {
        bitFiled0_ |= 0x10;
        this.targetId = targetId;
        return this;
    }


    public static QlogCncGuildGift init(QlogClanFlowInterface flow_name) {
        QlogCncGuildGift flow = new QlogCncGuildGift();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(roleId, 0, Math.min(roleId.length(), 64));
        builder.append("|").append(giftType, 0, Math.min(giftType.length(), 64));
        builder.append("|").append(targetId, 0, Math.min(targetId.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


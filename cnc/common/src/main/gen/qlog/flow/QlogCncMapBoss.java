
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapBoss extends AbstractServerQlogFlow {
    static final String META_NAME = "CncMapBoss";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BossFieldId", "BossBattleId", "VRoleIdGroup", "VArmyIdGroup", "EventId"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * Boss战模板id
     */
    private long bossFieldId;
    /**
     * 本场boss战唯一id
     */
    private String bossBattleId;
    /**
     * 参战斗过的角色
     */
    private String vRoleIdGroup;
    /**
     * 在场部队
     */
    private String vArmyIdGroup;
    /**
     * 事件ID
     */
    private int eventId;


    public QlogCncMapBoss() {
        dtEventTime = "";
        bossBattleId = "";
        vRoleIdGroup = "";
        vArmyIdGroup = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapBoss setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapBoss setBossFieldId(long bossFieldId) {
        bitFiled0_ |= 0x2;
        this.bossFieldId = bossFieldId;
        return this;
    }

    public QlogCncMapBoss setBossBattleId(String bossBattleId) {
        bitFiled0_ |= 0x4;
        this.bossBattleId = bossBattleId;
        return this;
    }

    public QlogCncMapBoss setVRoleIdGroup(String vRoleIdGroup) {
        bitFiled0_ |= 0x8;
        this.vRoleIdGroup = vRoleIdGroup;
        return this;
    }

    public QlogCncMapBoss setVArmyIdGroup(String vArmyIdGroup) {
        bitFiled0_ |= 0x10;
        this.vArmyIdGroup = vArmyIdGroup;
        return this;
    }

    public QlogCncMapBoss setEventId(int eventId) {
        bitFiled0_ |= 0x20;
        this.eventId = eventId;
        return this;
    }


    public static QlogCncMapBoss init(QlogServerFlowInterface flow_name) {
        QlogCncMapBoss flow = new QlogCncMapBoss();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(bossFieldId);
        builder.append("|").append(bossBattleId, 0, Math.min(bossBattleId.length(), 32));
        builder.append("|").append(vRoleIdGroup, 0, Math.min(vRoleIdGroup.length(), 128));
        builder.append("|").append(vArmyIdGroup, 0, Math.min(vArmyIdGroup.length(), 128));
        builder.append("|").append(eventId);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


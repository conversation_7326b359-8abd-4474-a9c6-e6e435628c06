
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncArmyChange extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncArmyChange";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Action", "DtEventTime", "ArmyId", "BeforeArmyNum", "AfterArmyNum", "ICount", "AddOrReduce"};
    /**
     * 行为类型
     */
    private String action;
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 兵种id
     */
    private String armyId;
    /**
     * 变化前数量
     */
    private long beforeArmyNum;
    /**
     * 变化后数量
     */
    private long afterArmyNum;
    /**
     * 变化数量
     */
    private long iCount;
    /**
     * 变化方向
     */
    private int addOrReduce;


    public QlogCncArmyChange() {
        action = "";
        dtEventTime = "";
        armyId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncArmyChange setAction(String action) {
        bitFiled0_ |= 0x1;
        this.action = action;
        return this;
    }

    public QlogCncArmyChange setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x2;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncArmyChange setArmyId(String armyId) {
        bitFiled0_ |= 0x4;
        this.armyId = armyId;
        return this;
    }

    public QlogCncArmyChange setBeforeArmyNum(long beforeArmyNum) {
        bitFiled0_ |= 0x8;
        this.beforeArmyNum = beforeArmyNum;
        return this;
    }

    public QlogCncArmyChange setAfterArmyNum(long afterArmyNum) {
        bitFiled0_ |= 0x10;
        this.afterArmyNum = afterArmyNum;
        return this;
    }

    public QlogCncArmyChange setICount(long iCount) {
        bitFiled0_ |= 0x20;
        this.iCount = iCount;
        return this;
    }

    public QlogCncArmyChange setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x40;
        this.addOrReduce = addOrReduce;
        return this;
    }


    public static QlogCncArmyChange init(QlogPlayerFlowInterface flow_name) {
        QlogCncArmyChange flow = new QlogCncArmyChange();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(armyId, 0, Math.min(armyId.length(), 32));
        builder.append("|").append(beforeArmyNum);
        builder.append("|").append(afterArmyNum);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


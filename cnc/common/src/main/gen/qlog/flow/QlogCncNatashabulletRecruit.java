
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncNatashabulletRecruit extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncNatashabulletRecruit";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "activityid", "TotalCount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 活动ID
     */
    private int activityid;
    /**
     * 本期活动累计抽奖次数
     */
    private int totalCount;


    public QlogCncNatashabulletRecruit() {
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncNatashabulletRecruit setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncNatashabulletRecruit setActivityid(int activityid) {
        bitFiled0_ |= 0x2;
        this.activityid = activityid;
        return this;
    }

    public QlogCncNatashabulletRecruit setTotalCount(int totalCount) {
        bitFiled0_ |= 0x4;
        this.totalCount = totalCount;
        return this;
    }


    public static QlogCncNatashabulletRecruit init(QlogPlayerFlowInterface flow_name) {
        QlogCncNatashabulletRecruit flow = new QlogCncNatashabulletRecruit();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(activityid);
        builder.append("|").append(totalCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


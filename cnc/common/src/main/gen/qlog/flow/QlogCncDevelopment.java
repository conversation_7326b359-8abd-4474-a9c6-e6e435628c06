
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncDevelopment extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncDevelopment";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "DevelopmentTag", "DevelopPathID", "Action"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 发展度页签
     */
    private int developmentTag;
    /**
     * 任务ID
     */
    private int developPathID;
    /**
     * 行为类型
     */
    private String action;


    public QlogCncDevelopment() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncDevelopment setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncDevelopment setDevelopmentTag(int developmentTag) {
        bitFiled0_ |= 0x2;
        this.developmentTag = developmentTag;
        return this;
    }

    public QlogCncDevelopment setDevelopPathID(int developPathID) {
        bitFiled0_ |= 0x4;
        this.developPathID = developPathID;
        return this;
    }

    public QlogCncDevelopment setAction(String action) {
        bitFiled0_ |= 0x8;
        this.action = action;
        return this;
    }


    public static QlogCncDevelopment init(QlogPlayerFlowInterface flow_name) {
        QlogCncDevelopment flow = new QlogCncDevelopment();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(developmentTag);
        builder.append("|").append(developPathID);
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


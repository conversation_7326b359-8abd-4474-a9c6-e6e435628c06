
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMileStone extends AbstractServerQlogFlow {
    static final String META_NAME = "CncMileStone";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "MilestoneID", "Action", "RewardGroup", "MilestoneCompletedNum"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 里程碑ID
     */
    private int milestoneID;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 可领取奖励的范围
     */
    private String rewardGroup;
    /**
     * 完成里程碑人数
     */
    private int milestoneCompletedNum;


    public QlogCncMileStone() {
        dtEventTime = "";
        action = "";
        rewardGroup = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMileStone setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMileStone setMilestoneID(int milestoneID) {
        bitFiled0_ |= 0x2;
        this.milestoneID = milestoneID;
        return this;
    }

    public QlogCncMileStone setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncMileStone setRewardGroup(String rewardGroup) {
        bitFiled0_ |= 0x8;
        this.rewardGroup = rewardGroup;
        return this;
    }

    public QlogCncMileStone setMilestoneCompletedNum(int milestoneCompletedNum) {
        bitFiled0_ |= 0x10;
        this.milestoneCompletedNum = milestoneCompletedNum;
        return this;
    }


    public static QlogCncMileStone init(QlogServerFlowInterface flow_name) {
        QlogCncMileStone flow = new QlogCncMileStone();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(milestoneID);
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(rewardGroup, 0, Math.min(rewardGroup.length(), 32));
        builder.append("|").append(milestoneCompletedNum);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


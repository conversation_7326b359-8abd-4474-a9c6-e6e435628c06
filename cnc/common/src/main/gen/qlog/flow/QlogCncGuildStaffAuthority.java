
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildStaffAuthority extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuildStaffAuthority";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "AfterAuthority"};
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 变化后的权限明细
     */
    private String afterAuthority;


    public QlogCncGuildStaffAuthority() {
        dtEventTime = "";
        action = "";
        afterAuthority = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildStaffAuthority setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildStaffAuthority setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildStaffAuthority setAfterAuthority(String afterAuthority) {
        bitFiled0_ |= 0x4;
        this.afterAuthority = afterAuthority;
        return this;
    }


    public static QlogCncGuildStaffAuthority init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuildStaffAuthority flow = new QlogCncGuildStaffAuthority();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(afterAuthority, 0, Math.min(afterAuthority.length(), 2048));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


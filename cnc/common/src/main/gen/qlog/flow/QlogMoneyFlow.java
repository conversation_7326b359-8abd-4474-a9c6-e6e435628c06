
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogMoneyFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "MoneyFlow";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "iMoneyType", "BeforeMoney", "AfterMoney", "iMoney", "AddOrReduce", "Reason", "SubReason"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 充值货币
     */
    private int iMoneyType;
    /**
     * 货币发生变化前的数量
     */
    private long beforeMoney;
    /**
     * 货币发生变化后的数量
     */
    private long afterMoney;
    /**
     * 货币改变的数量
     */
    private long iMoney;
    /**
     * 货币变化的类型
     */
    private int addOrReduce;
    /**
     * 货币变化的一级原因
     */
    private String reason;
    /**
     * 货币变化的二级原因
     */
    private String subReason;


    public QlogMoneyFlow() {
        dtEventTime = "";
        reason = "";
        subReason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogMoneyFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogMoneyFlow setIMoneyType(int iMoneyType) {
        bitFiled0_ |= 0x2;
        this.iMoneyType = iMoneyType;
        return this;
    }

    public QlogMoneyFlow setBeforeMoney(long beforeMoney) {
        bitFiled0_ |= 0x4;
        this.beforeMoney = beforeMoney;
        return this;
    }

    public QlogMoneyFlow setAfterMoney(long afterMoney) {
        bitFiled0_ |= 0x8;
        this.afterMoney = afterMoney;
        return this;
    }

    public QlogMoneyFlow setIMoney(long iMoney) {
        bitFiled0_ |= 0x10;
        this.iMoney = iMoney;
        return this;
    }

    public QlogMoneyFlow setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }

    public QlogMoneyFlow setReason(String reason) {
        bitFiled0_ |= 0x40;
        this.reason = reason;
        return this;
    }

    public QlogMoneyFlow setSubReason(String subReason) {
        bitFiled0_ |= 0x80;
        this.subReason = subReason;
        return this;
    }


    public static QlogMoneyFlow init(QlogPlayerFlowInterface flow_name) {
        QlogMoneyFlow flow = new QlogMoneyFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iMoneyType);
        builder.append("|").append(beforeMoney);
        builder.append("|").append(afterMoney);
        builder.append("|").append(iMoney);
        builder.append("|").append(addOrReduce);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(subReason, 0, Math.min(subReason.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


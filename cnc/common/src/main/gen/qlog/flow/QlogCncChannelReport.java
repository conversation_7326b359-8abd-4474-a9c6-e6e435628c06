
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncChannelReport extends AbstractServerQlogFlow {
    static final String META_NAME = "CncChannelReport";
    static final int currentFieldCnt = 16;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Type", "deviceId", "openId", "playerId", "worlId", "zoneId", "channelName", "costMs", "clientIp", "clientCountry", "serverCountry", "systemHardware", "netType", "gear", "newOpenId"};
    /**
     * connect代表使用tcp, ping代表使用业务层心跳
     */
    private String type;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 字段废弃
     */
    private long openId;
    /**
     * 玩家id
     */
    private long playerId;
    /**
     * 大区id
     */
    private int worlId;
    /**
     * 小服id
     */
    private int zoneId;
    /**
     * 链路标识
     */
    private String channelName;
    /**
     * 耗时
     */
    private long costMs;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 客户端国家
     */
    private String clientCountry;
    /**
     * 服务器国家
     */
    private String serverCountry;
    /**
     * 移动终端机型
     */
    private String systemHardware;
    /**
     * 网络类型, 5g,4g,wifi
     */
    private String netType;
    /**
     * 机型档位
     */
    private int gear;
    /**
     * 账号id
     */
    private String newOpenId;


    public QlogCncChannelReport() {
        type = "";
        deviceId = "";
        channelName = "";
        clientIp = "";
        clientCountry = "";
        serverCountry = "";
        systemHardware = "";
        netType = "";
        newOpenId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncChannelReport setType(String type) {
        bitFiled0_ |= 0x1;
        this.type = type;
        return this;
    }

    public QlogCncChannelReport setDeviceId(String deviceId) {
        bitFiled0_ |= 0x2;
        this.deviceId = deviceId;
        return this;
    }

    public QlogCncChannelReport setOpenId(long openId) {
        bitFiled0_ |= 0x4;
        this.openId = openId;
        return this;
    }

    public QlogCncChannelReport setPlayerId(long playerId) {
        bitFiled0_ |= 0x8;
        this.playerId = playerId;
        return this;
    }

    public QlogCncChannelReport setWorlId(int worlId) {
        bitFiled0_ |= 0x10;
        this.worlId = worlId;
        return this;
    }

    public QlogCncChannelReport setZoneId(int zoneId) {
        bitFiled0_ |= 0x20;
        this.zoneId = zoneId;
        return this;
    }

    public QlogCncChannelReport setChannelName(String channelName) {
        bitFiled0_ |= 0x40;
        this.channelName = channelName;
        return this;
    }

    public QlogCncChannelReport setCostMs(long costMs) {
        bitFiled0_ |= 0x80;
        this.costMs = costMs;
        return this;
    }

    public QlogCncChannelReport setClientIp(String clientIp) {
        bitFiled0_ |= 0x100;
        this.clientIp = clientIp;
        return this;
    }

    public QlogCncChannelReport setClientCountry(String clientCountry) {
        bitFiled0_ |= 0x200;
        this.clientCountry = clientCountry;
        return this;
    }

    public QlogCncChannelReport setServerCountry(String serverCountry) {
        bitFiled0_ |= 0x400;
        this.serverCountry = serverCountry;
        return this;
    }

    public QlogCncChannelReport setSystemHardware(String systemHardware) {
        bitFiled0_ |= 0x800;
        this.systemHardware = systemHardware;
        return this;
    }

    public QlogCncChannelReport setNetType(String netType) {
        bitFiled0_ |= 0x1000;
        this.netType = netType;
        return this;
    }

    public QlogCncChannelReport setGear(int gear) {
        bitFiled0_ |= 0x2000;
        this.gear = gear;
        return this;
    }

    public QlogCncChannelReport setNewOpenId(String newOpenId) {
        bitFiled0_ |= 0x4000;
        this.newOpenId = newOpenId;
        return this;
    }


    public static QlogCncChannelReport init(QlogServerFlowInterface flow_name) {
        QlogCncChannelReport flow = new QlogCncChannelReport();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7fff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(type, 0, Math.min(type.length(), 32));
        builder.append("|").append(deviceId, 0, Math.min(deviceId.length(), 64));
        builder.append("|").append(openId);
        builder.append("|").append(playerId);
        builder.append("|").append(worlId);
        builder.append("|").append(zoneId);
        builder.append("|").append(channelName, 0, Math.min(channelName.length(), 64));
        builder.append("|").append(costMs);
        builder.append("|").append(clientIp, 0, Math.min(clientIp.length(), 32));
        builder.append("|").append(clientCountry, 0, Math.min(clientCountry.length(), 64));
        builder.append("|").append(serverCountry, 0, Math.min(serverCountry.length(), 64));
        builder.append("|").append(systemHardware, 0, Math.min(systemHardware.length(), 32));
        builder.append("|").append(netType, 0, Math.min(netType.length(), 32));
        builder.append("|").append(gear);
        builder.append("|").append(newOpenId, 0, Math.min(newOpenId.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


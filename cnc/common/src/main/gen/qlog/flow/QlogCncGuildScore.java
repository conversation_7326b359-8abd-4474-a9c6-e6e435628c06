
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildScore extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildScore";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "BeforeCount", "AfterCount", "ICount", "AddOrReduce", "TargetId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 行为前积分数量
     */
    private long beforeCount;
    /**
     * 行为后积分数量
     */
    private long afterCount;
    /**
     * 变化的积分数量
     */
    private long iCount;
    /**
     * 变化方向
     */
    private int addOrReduce;
    /**
     * 目标id
     */
    private long targetId;


    public QlogCncGuildScore() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildScore setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildScore setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildScore setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x4;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncGuildScore setAfterCount(long afterCount) {
        bitFiled0_ |= 0x8;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncGuildScore setICount(long iCount) {
        bitFiled0_ |= 0x10;
        this.iCount = iCount;
        return this;
    }

    public QlogCncGuildScore setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }

    public QlogCncGuildScore setTargetId(long targetId) {
        bitFiled0_ |= 0x40;
        this.targetId = targetId;
        return this;
    }


    public static QlogCncGuildScore init(QlogClanFlowInterface flow_name) {
        QlogCncGuildScore flow = new QlogCncGuildScore();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
        builder.append("|").append(targetId);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


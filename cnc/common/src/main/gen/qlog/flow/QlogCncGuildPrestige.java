
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildPrestige extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuildPrestige";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ActionCount", "AfterGuildPrestige", "PrestigeCount"};
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 行为的衡量值
     */
    private String actionCount;
    /**
     * 行为发生后联盟威望值
     */
    private long afterGuildPrestige;
    /**
     * 变化的威望值数量
     */
    private long prestigeCount;


    public QlogCncGuildPrestige() {
        dtEventTime = "";
        action = "";
        actionCount = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildPrestige setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildPrestige setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildPrestige setActionCount(String actionCount) {
        bitFiled0_ |= 0x4;
        this.actionCount = actionCount;
        return this;
    }

    public QlogCncGuildPrestige setAfterGuildPrestige(long afterGuildPrestige) {
        bitFiled0_ |= 0x8;
        this.afterGuildPrestige = afterGuildPrestige;
        return this;
    }

    public QlogCncGuildPrestige setPrestigeCount(long prestigeCount) {
        bitFiled0_ |= 0x10;
        this.prestigeCount = prestigeCount;
        return this;
    }


    public static QlogCncGuildPrestige init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuildPrestige flow = new QlogCncGuildPrestige();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(actionCount, 0, Math.min(actionCount.length(), 32));
        builder.append("|").append(afterGuildPrestige);
        builder.append("|").append(prestigeCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


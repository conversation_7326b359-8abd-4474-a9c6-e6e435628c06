
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncAccelerateFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncAccelerateFlow";
    static final int currentFieldCnt = 10;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ObjectID", "AccelerateFormation", "StartTime", "EndTimeActual", "Action", "AccelerateType", "GuildHelpOrNot", "AccelerateTime"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 加速对象id
     */
    private String objectID;
    /**
     * 加速队列ID
     */
    private String accelerateFormation;
    /**
     * 队列最初开始的时间
     */
    private String startTime;
    /**
     * 队列预期结束时间
     */
    private String endTimeActual;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 加速类型
     */
    private String accelerateType;
    /**
     * 是否开启联盟帮助
     */
    private int guildHelpOrNot;
    /**
     * 加速缩短的时间
     */
    private long accelerateTime;


    public QlogCncAccelerateFlow() {
        dtEventTime = "";
        objectID = "";
        accelerateFormation = "";
        startTime = "";
        endTimeActual = "";
        action = "";
        accelerateType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncAccelerateFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncAccelerateFlow setObjectID(String objectID) {
        bitFiled0_ |= 0x2;
        this.objectID = objectID;
        return this;
    }

    public QlogCncAccelerateFlow setAccelerateFormation(String accelerateFormation) {
        bitFiled0_ |= 0x4;
        this.accelerateFormation = accelerateFormation;
        return this;
    }

    public QlogCncAccelerateFlow setStartTime(String startTime) {
        bitFiled0_ |= 0x8;
        this.startTime = startTime;
        return this;
    }

    public QlogCncAccelerateFlow setEndTimeActual(String endTimeActual) {
        bitFiled0_ |= 0x10;
        this.endTimeActual = endTimeActual;
        return this;
    }

    public QlogCncAccelerateFlow setAction(String action) {
        bitFiled0_ |= 0x20;
        this.action = action;
        return this;
    }

    public QlogCncAccelerateFlow setAccelerateType(String accelerateType) {
        bitFiled0_ |= 0x40;
        this.accelerateType = accelerateType;
        return this;
    }

    public QlogCncAccelerateFlow setGuildHelpOrNot(int guildHelpOrNot) {
        bitFiled0_ |= 0x80;
        this.guildHelpOrNot = guildHelpOrNot;
        return this;
    }

    public QlogCncAccelerateFlow setAccelerateTime(long accelerateTime) {
        bitFiled0_ |= 0x100;
        this.accelerateTime = accelerateTime;
        return this;
    }


    public static QlogCncAccelerateFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncAccelerateFlow flow = new QlogCncAccelerateFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x100) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(objectID, 0, Math.min(objectID.length(), 64));
        builder.append("|").append(accelerateFormation, 0, Math.min(accelerateFormation.length(), 64));
        builder.append("|").append(startTime, 0, Math.min(startTime.length(), 32));
        builder.append("|").append(endTimeActual, 0, Math.min(endTimeActual.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(accelerateType, 0, Math.min(accelerateType.length(), 32));
        builder.append("|").append(guildHelpOrNot);
        builder.append("|").append(accelerateTime);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildManage extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildManage";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "IActType", "AfterContent", "AfterEntryStandard"};
    /**
     * 行为时间
     */
    private String dtEventTime;
    /**
     * 联盟行为
     */
    private String iActType;
    /**
     * 行为后内容
     */
    private String afterContent;
    /**
     * 行为后的入盟要求
     */
    private int afterEntryStandard;


    public QlogCncGuildManage() {
        dtEventTime = "";
        iActType = "";
        afterContent = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildManage setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildManage setIActType(String iActType) {
        bitFiled0_ |= 0x2;
        this.iActType = iActType;
        return this;
    }

    public QlogCncGuildManage setAfterContent(String afterContent) {
        bitFiled0_ |= 0x4;
        this.afterContent = afterContent;
        return this;
    }

    public QlogCncGuildManage setAfterEntryStandard(int afterEntryStandard) {
        bitFiled0_ |= 0x8;
        this.afterEntryStandard = afterEntryStandard;
        return this;
    }


    public static QlogCncGuildManage init(QlogClanFlowInterface flow_name) {
        QlogCncGuildManage flow = new QlogCncGuildManage();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iActType, 0, Math.min(iActType.length(), 32));
        builder.append("|").append(afterContent, 0, Math.min(afterContent.length(), 32));
        builder.append("|").append(afterEntryStandard);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBattlePlaneGet extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBattlePlaneGet";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BattlePlaneType", "Action"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 战机类型
     */
    private int battlePlaneType;
    /**
     * 行为类型
     */
    private String action;


    public QlogCncBattlePlaneGet() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBattlePlaneGet setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBattlePlaneGet setBattlePlaneType(int battlePlaneType) {
        bitFiled0_ |= 0x2;
        this.battlePlaneType = battlePlaneType;
        return this;
    }

    public QlogCncBattlePlaneGet setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }


    public static QlogCncBattlePlaneGet init(QlogPlayerFlowInterface flow_name) {
        QlogCncBattlePlaneGet flow = new QlogCncBattlePlaneGet();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(battlePlaneType);
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


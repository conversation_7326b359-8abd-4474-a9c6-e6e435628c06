
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMailFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncMailFlow";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "MailTabsType", "SendMailObject", "ReceiveMailObject", "MailId", "MailTemplateId", "RewardConfig"};
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 邮件类型
     */
    private String mailTabsType;
    /**
     * 邮件发送方
     */
    private String sendMailObject;
    /**
     * 邮件接收方
     */
    private String receiveMailObject;
    /**
     * 邮件唯一id
     */
    private String mailId;
    /**
     * 邮件模板id
     */
    private int mailTemplateId;
    /**
     * 道具奖励详情
     */
    private String rewardConfig;


    public QlogCncMailFlow() {
        dtEventTime = "";
        action = "";
        mailTabsType = "";
        sendMailObject = "";
        receiveMailObject = "";
        mailId = "";
        rewardConfig = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMailFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMailFlow setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncMailFlow setMailTabsType(String mailTabsType) {
        bitFiled0_ |= 0x4;
        this.mailTabsType = mailTabsType;
        return this;
    }

    public QlogCncMailFlow setSendMailObject(String sendMailObject) {
        bitFiled0_ |= 0x8;
        this.sendMailObject = sendMailObject;
        return this;
    }

    public QlogCncMailFlow setReceiveMailObject(String receiveMailObject) {
        bitFiled0_ |= 0x10;
        this.receiveMailObject = receiveMailObject;
        return this;
    }

    public QlogCncMailFlow setMailId(String mailId) {
        bitFiled0_ |= 0x20;
        this.mailId = mailId;
        return this;
    }

    public QlogCncMailFlow setMailTemplateId(int mailTemplateId) {
        bitFiled0_ |= 0x40;
        this.mailTemplateId = mailTemplateId;
        return this;
    }

    public QlogCncMailFlow setRewardConfig(String rewardConfig) {
        bitFiled0_ |= 0x80;
        this.rewardConfig = rewardConfig;
        return this;
    }


    public static QlogCncMailFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncMailFlow flow = new QlogCncMailFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(mailTabsType, 0, Math.min(mailTabsType.length(), 32));
        builder.append("|").append(sendMailObject, 0, Math.min(sendMailObject.length(), 32));
        builder.append("|").append(receiveMailObject, 0, Math.min(receiveMailObject.length(), 32));
        builder.append("|").append(mailId, 0, Math.min(mailId.length(), 32));
        builder.append("|").append(mailTemplateId);
        builder.append("|").append(rewardConfig, 0, Math.min(rewardConfig.length(), 2048));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


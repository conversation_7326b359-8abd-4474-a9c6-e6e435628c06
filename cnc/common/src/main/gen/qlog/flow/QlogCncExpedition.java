
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncExpedition extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncExpedition";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ChapterId", "LevelId", "BeforeStar", "TimeCost", "Result"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为
     */
    private String action;
    /**
     * 章节id
     */
    private int chapterId;
    /**
     * 关卡id
     */
    private int levelId;
    /**
     * 关卡星级
     */
    private int beforeStar;
    /**
     * 消耗时间
     */
    private int timeCost;
    /**
     * 结果
     */
    private int result;


    public QlogCncExpedition() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncExpedition setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncExpedition setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncExpedition setChapterId(int chapterId) {
        bitFiled0_ |= 0x4;
        this.chapterId = chapterId;
        return this;
    }

    public QlogCncExpedition setLevelId(int levelId) {
        bitFiled0_ |= 0x8;
        this.levelId = levelId;
        return this;
    }

    public QlogCncExpedition setBeforeStar(int beforeStar) {
        bitFiled0_ |= 0x10;
        this.beforeStar = beforeStar;
        return this;
    }

    public QlogCncExpedition setTimeCost(int timeCost) {
        bitFiled0_ |= 0x20;
        this.timeCost = timeCost;
        return this;
    }

    public QlogCncExpedition setResult(int result) {
        bitFiled0_ |= 0x40;
        this.result = result;
        return this;
    }


    public static QlogCncExpedition init(QlogPlayerFlowInterface flow_name) {
        QlogCncExpedition flow = new QlogCncExpedition();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(chapterId);
        builder.append("|").append(levelId);
        builder.append("|").append(beforeStar);
        builder.append("|").append(timeCost);
        builder.append("|").append(result);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPlayerRankings extends AbstractServerQlogFlow {
    static final String META_NAME = "CncPlayerRankings";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "RankingName", "Ranking", "RankingScore", "VRoleId", "IGuildId", "OptionalReward"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 排行榜名称
     */
    private String rankingName;
    /**
     * 结算名次
     */
    private int ranking;
    /**
     * 结算积分
     */
    private long rankingScore;
    /**
     * 角色ID
     */
    private String vRoleId;
    /**
     * 联盟ID
     */
    private long iGuildId;
    /**
     * 自选奖励id
     */
    private int optionalReward;


    public QlogCncPlayerRankings() {
        dtEventTime = "";
        rankingName = "";
        vRoleId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPlayerRankings setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPlayerRankings setRankingName(String rankingName) {
        bitFiled0_ |= 0x2;
        this.rankingName = rankingName;
        return this;
    }

    public QlogCncPlayerRankings setRanking(int ranking) {
        bitFiled0_ |= 0x4;
        this.ranking = ranking;
        return this;
    }

    public QlogCncPlayerRankings setRankingScore(long rankingScore) {
        bitFiled0_ |= 0x8;
        this.rankingScore = rankingScore;
        return this;
    }

    public QlogCncPlayerRankings setVRoleId(String vRoleId) {
        bitFiled0_ |= 0x10;
        this.vRoleId = vRoleId;
        return this;
    }

    public QlogCncPlayerRankings setIGuildId(long iGuildId) {
        bitFiled0_ |= 0x20;
        this.iGuildId = iGuildId;
        return this;
    }

    public QlogCncPlayerRankings setOptionalReward(int optionalReward) {
        bitFiled0_ |= 0x40;
        this.optionalReward = optionalReward;
        return this;
    }


    public static QlogCncPlayerRankings init(QlogServerFlowInterface flow_name) {
        QlogCncPlayerRankings flow = new QlogCncPlayerRankings();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(rankingName, 0, Math.min(rankingName.length(), 32));
        builder.append("|").append(ranking);
        builder.append("|").append(rankingScore);
        builder.append("|").append(vRoleId, 0, Math.min(vRoleId.length(), 64));
        builder.append("|").append(iGuildId);
        builder.append("|").append(optionalReward);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPlayerSnapshot extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncPlayerSnapshot";
    static final int currentFieldCnt = 24;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "HeroLevelStar", "HeroSkill", "BuildingConfig", "ArmyPower", "HeroPower", "BuildingPower", "GuidePosition", "RoleLastLoginTime", "RoleLastLogoutTime", "TechPower", "BattlePlaneSkill", "KillInfo", "BattlePlanePower", "BagInfo", "MechaInfo", "TechInfo", "EnergyInfo", "ResInfo", "MoneyInfo", "FlagInfo", "WeekMonthCardInfo", "BpInfo"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 英雄等级与星级
     */
    private String heroLevelStar;
    /**
     * 英雄技能等级
     */
    private String heroSkill;
    /**
     * 建筑等级详情
     */
    private String buildingConfig;
    /**
     * 部队战力
     */
    private long armyPower;
    /**
     * 英雄战力
     */
    private long heroPower;
    /**
     * 建筑战力
     */
    private long buildingPower;
    /**
     * 强新手引导停留位置
     */
    private int guidePosition;
    /**
     * 最后一次登录时间
     */
    private String roleLastLoginTime;
    /**
     * 最后一次登出时间
     */
    private String roleLastLogoutTime;
    /**
     * 科技战力
     */
    private long techPower;
    /**
     * 战斗机技能详情
     */
    private String battlePlaneSkill;
    /**
     * 击杀详情
     */
    private String killInfo;
    /**
     * 空军战力
     */
    private long battlePlanePower;
    /**
     * 背包详情
     */
    private String bagInfo;
    /**
     * 机甲详情
     */
    private String mechaInfo;
    /**
     * 科技详情
     */
    private String techInfo;
    /**
     * 体力值
     */
    private int energyInfo;
    /**
     * 资源详情
     */
    private String resInfo;
    /**
     * 玩家当前货币库存
     */
    private long moneyInfo;
    /**
     * 国旗配置
     */
    private int flagInfo;
    /**
     * 周卡月卡详情
     */
    private String weekMonthCardInfo;
    /**
     * BattlePass状态
     */
    private String bpInfo;


    public QlogCncPlayerSnapshot() {
        dtEventTime = "";
        heroLevelStar = "";
        heroSkill = "";
        buildingConfig = "";
        roleLastLoginTime = "";
        roleLastLogoutTime = "";
        battlePlaneSkill = "";
        killInfo = "";
        bagInfo = "";
        mechaInfo = "";
        techInfo = "";
        resInfo = "";
        weekMonthCardInfo = "";
        bpInfo = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPlayerSnapshot setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPlayerSnapshot setHeroLevelStar(String heroLevelStar) {
        bitFiled0_ |= 0x2;
        this.heroLevelStar = heroLevelStar;
        return this;
    }

    public QlogCncPlayerSnapshot setHeroSkill(String heroSkill) {
        bitFiled0_ |= 0x4;
        this.heroSkill = heroSkill;
        return this;
    }

    public QlogCncPlayerSnapshot setBuildingConfig(String buildingConfig) {
        bitFiled0_ |= 0x8;
        this.buildingConfig = buildingConfig;
        return this;
    }

    public QlogCncPlayerSnapshot setArmyPower(long armyPower) {
        bitFiled0_ |= 0x10;
        this.armyPower = armyPower;
        return this;
    }

    public QlogCncPlayerSnapshot setHeroPower(long heroPower) {
        bitFiled0_ |= 0x20;
        this.heroPower = heroPower;
        return this;
    }

    public QlogCncPlayerSnapshot setBuildingPower(long buildingPower) {
        bitFiled0_ |= 0x40;
        this.buildingPower = buildingPower;
        return this;
    }

    public QlogCncPlayerSnapshot setGuidePosition(int guidePosition) {
        bitFiled0_ |= 0x80;
        this.guidePosition = guidePosition;
        return this;
    }

    public QlogCncPlayerSnapshot setRoleLastLoginTime(String roleLastLoginTime) {
        bitFiled0_ |= 0x100;
        this.roleLastLoginTime = roleLastLoginTime;
        return this;
    }

    public QlogCncPlayerSnapshot setRoleLastLogoutTime(String roleLastLogoutTime) {
        bitFiled0_ |= 0x200;
        this.roleLastLogoutTime = roleLastLogoutTime;
        return this;
    }

    public QlogCncPlayerSnapshot setTechPower(long techPower) {
        bitFiled0_ |= 0x400;
        this.techPower = techPower;
        return this;
    }

    public QlogCncPlayerSnapshot setBattlePlaneSkill(String battlePlaneSkill) {
        bitFiled0_ |= 0x800;
        this.battlePlaneSkill = battlePlaneSkill;
        return this;
    }

    public QlogCncPlayerSnapshot setKillInfo(String killInfo) {
        bitFiled0_ |= 0x1000;
        this.killInfo = killInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setBattlePlanePower(long battlePlanePower) {
        bitFiled0_ |= 0x2000;
        this.battlePlanePower = battlePlanePower;
        return this;
    }

    public QlogCncPlayerSnapshot setBagInfo(String bagInfo) {
        bitFiled0_ |= 0x4000;
        this.bagInfo = bagInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setMechaInfo(String mechaInfo) {
        bitFiled0_ |= 0x8000;
        this.mechaInfo = mechaInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setTechInfo(String techInfo) {
        bitFiled0_ |= 0x10000;
        this.techInfo = techInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setEnergyInfo(int energyInfo) {
        bitFiled0_ |= 0x20000;
        this.energyInfo = energyInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setResInfo(String resInfo) {
        bitFiled0_ |= 0x40000;
        this.resInfo = resInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setMoneyInfo(long moneyInfo) {
        bitFiled0_ |= 0x80000;
        this.moneyInfo = moneyInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setFlagInfo(int flagInfo) {
        bitFiled0_ |= 0x100000;
        this.flagInfo = flagInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setWeekMonthCardInfo(String weekMonthCardInfo) {
        bitFiled0_ |= 0x200000;
        this.weekMonthCardInfo = weekMonthCardInfo;
        return this;
    }

    public QlogCncPlayerSnapshot setBpInfo(String bpInfo) {
        bitFiled0_ |= 0x400000;
        this.bpInfo = bpInfo;
        return this;
    }


    public static QlogCncPlayerSnapshot init(QlogPlayerFlowInterface flow_name) {
        QlogCncPlayerSnapshot flow = new QlogCncPlayerSnapshot();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7fffff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x400000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(heroLevelStar, 0, Math.min(heroLevelStar.length(), 2048));
        builder.append("|").append(heroSkill, 0, Math.min(heroSkill.length(), 2048));
        builder.append("|").append(buildingConfig, 0, Math.min(buildingConfig.length(), 2048));
        builder.append("|").append(armyPower);
        builder.append("|").append(heroPower);
        builder.append("|").append(buildingPower);
        builder.append("|").append(guidePosition);
        builder.append("|").append(roleLastLoginTime, 0, Math.min(roleLastLoginTime.length(), 32));
        builder.append("|").append(roleLastLogoutTime, 0, Math.min(roleLastLogoutTime.length(), 32));
        builder.append("|").append(techPower);
        builder.append("|").append(battlePlaneSkill, 0, Math.min(battlePlaneSkill.length(), 2048));
        builder.append("|").append(killInfo, 0, Math.min(killInfo.length(), 2048));
        builder.append("|").append(battlePlanePower);
        builder.append("|").append(bagInfo, 0, Math.min(bagInfo.length(), 2048));
        builder.append("|").append(mechaInfo, 0, Math.min(mechaInfo.length(), 2048));
        builder.append("|").append(techInfo, 0, Math.min(techInfo.length(), 2048));
        builder.append("|").append(energyInfo);
        builder.append("|").append(resInfo, 0, Math.min(resInfo.length(), 2048));
        builder.append("|").append(moneyInfo);
        builder.append("|").append(flagInfo);
        builder.append("|").append(weekMonthCardInfo, 0, Math.min(weekMonthCardInfo.length(), 2048));
        builder.append("|").append(bpInfo, 0, Math.min(bpInfo.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


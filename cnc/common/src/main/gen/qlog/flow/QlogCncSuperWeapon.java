
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncSuperWeapon extends AbstractClanQlogFlow {
    static final String META_NAME = "CncSuperWeapon";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "WeaponType", "SkillId", "BeforeEnergy", "AfterEnergy", "iCount", "TargetCoordinates"};
    /**
     * 行为时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 超武类型
     */
    private int weaponType;
    /**
     * 技能ID
     */
    private int skillId;
    /**
     * 超武释放前的能量
     */
    private int beforeEnergy;
    /**
     * 超武释放后的能量
     */
    private int afterEnergy;
    /**
     * 消耗的能量
     */
    private int iCount;
    /**
     * 释放的中心坐标
     */
    private String targetCoordinates;


    public QlogCncSuperWeapon() {
        dtEventTime = "";
        action = "";
        targetCoordinates = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncSuperWeapon setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncSuperWeapon setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncSuperWeapon setWeaponType(int weaponType) {
        bitFiled0_ |= 0x4;
        this.weaponType = weaponType;
        return this;
    }

    public QlogCncSuperWeapon setSkillId(int skillId) {
        bitFiled0_ |= 0x8;
        this.skillId = skillId;
        return this;
    }

    public QlogCncSuperWeapon setBeforeEnergy(int beforeEnergy) {
        bitFiled0_ |= 0x10;
        this.beforeEnergy = beforeEnergy;
        return this;
    }

    public QlogCncSuperWeapon setAfterEnergy(int afterEnergy) {
        bitFiled0_ |= 0x20;
        this.afterEnergy = afterEnergy;
        return this;
    }

    public QlogCncSuperWeapon setICount(int iCount) {
        bitFiled0_ |= 0x40;
        this.iCount = iCount;
        return this;
    }

    public QlogCncSuperWeapon setTargetCoordinates(String targetCoordinates) {
        bitFiled0_ |= 0x80;
        this.targetCoordinates = targetCoordinates;
        return this;
    }


    public static QlogCncSuperWeapon init(QlogClanFlowInterface flow_name) {
        QlogCncSuperWeapon flow = new QlogCncSuperWeapon();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(weaponType);
        builder.append("|").append(skillId);
        builder.append("|").append(beforeEnergy);
        builder.append("|").append(afterEnergy);
        builder.append("|").append(iCount);
        builder.append("|").append(targetCoordinates, 0, Math.min(targetCoordinates.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


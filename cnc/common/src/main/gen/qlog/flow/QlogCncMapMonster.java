
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapMonster extends AbstractServerQlogFlow {
    static final String META_NAME = "CncMapMonster";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "MonsterConfig", "Action"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 野怪详情
     */
    private String monsterConfig;
    /**
     * 行为类型
     */
    private String action;


    public QlogCncMapMonster() {
        dtEventTime = "";
        monsterConfig = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapMonster setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapMonster setMonsterConfig(String monsterConfig) {
        bitFiled0_ |= 0x2;
        this.monsterConfig = monsterConfig;
        return this;
    }

    public QlogCncMapMonster setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }


    public static QlogCncMapMonster init(QlogServerFlowInterface flow_name) {
        QlogCncMapMonster flow = new QlogCncMapMonster();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(monsterConfig, 0, Math.min(monsterConfig.length(), 2048));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


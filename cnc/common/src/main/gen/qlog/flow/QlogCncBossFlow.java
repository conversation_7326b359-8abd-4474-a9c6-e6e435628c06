
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBossFlow extends AbstractServerQlogFlow {
    static final String META_NAME = "CncBossFlow";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "AreaID", "MonsterId", "MonsterLevel"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 片ID
     */
    private int areaID;
    /**
     * Boss的ID
     */
    private int monsterId;
    /**
     * Boss的等级
     */
    private int monsterLevel;


    public QlogCncBossFlow() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBossFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBossFlow setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncBossFlow setAreaID(int areaID) {
        bitFiled0_ |= 0x4;
        this.areaID = areaID;
        return this;
    }

    public QlogCncBossFlow setMonsterId(int monsterId) {
        bitFiled0_ |= 0x8;
        this.monsterId = monsterId;
        return this;
    }

    public QlogCncBossFlow setMonsterLevel(int monsterLevel) {
        bitFiled0_ |= 0x10;
        this.monsterLevel = monsterLevel;
        return this;
    }


    public static QlogCncBossFlow init(QlogServerFlowInterface flow_name) {
        QlogCncBossFlow flow = new QlogCncBossFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(areaID);
        builder.append("|").append(monsterId);
        builder.append("|").append(monsterLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


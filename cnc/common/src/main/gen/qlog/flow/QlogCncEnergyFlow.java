
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncEnergyFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncEnergyFlow";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ParamEnergy", "BeforeCount", "AfterCount", "iCount", "AddOrReduce"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 体力变化行为的附加参数值
     */
    private String paramEnergy;
    /**
     * 行为前体力值
     */
    private int beforeCount;
    /**
     * 行为后体力值
     */
    private int afterCount;
    /**
     * 配置变化值
     */
    private int iCount;
    /**
     * 变化方向
     */
    private int addOrReduce;


    public QlogCncEnergyFlow() {
        dtEventTime = "";
        action = "";
        paramEnergy = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncEnergyFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncEnergyFlow setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncEnergyFlow setParamEnergy(String paramEnergy) {
        bitFiled0_ |= 0x4;
        this.paramEnergy = paramEnergy;
        return this;
    }

    public QlogCncEnergyFlow setBeforeCount(int beforeCount) {
        bitFiled0_ |= 0x8;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncEnergyFlow setAfterCount(int afterCount) {
        bitFiled0_ |= 0x10;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncEnergyFlow setICount(int iCount) {
        bitFiled0_ |= 0x20;
        this.iCount = iCount;
        return this;
    }

    public QlogCncEnergyFlow setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x40;
        this.addOrReduce = addOrReduce;
        return this;
    }


    public static QlogCncEnergyFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncEnergyFlow flow = new QlogCncEnergyFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(paramEnergy, 0, Math.min(paramEnergy.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


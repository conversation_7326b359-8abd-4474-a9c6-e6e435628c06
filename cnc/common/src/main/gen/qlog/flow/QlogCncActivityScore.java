
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncActivityScore extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncActivityScore";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "SubjectID", "icount", "ActivityScore", "ActivityScoreID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 目标ID
     */
    private String subjectID;
    /**
     * 改变的数量
     */
    private int icount;
    /**
     * 活动积分
     */
    private int activityScore;
    /**
     * 活动积分ID
     */
    private String activityScoreID;


    public QlogCncActivityScore() {
        dtEventTime = "";
        action = "";
        subjectID = "";
        activityScoreID = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncActivityScore setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncActivityScore setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncActivityScore setSubjectID(String subjectID) {
        bitFiled0_ |= 0x4;
        this.subjectID = subjectID;
        return this;
    }

    public QlogCncActivityScore setIcount(int icount) {
        bitFiled0_ |= 0x8;
        this.icount = icount;
        return this;
    }

    public QlogCncActivityScore setActivityScore(int activityScore) {
        bitFiled0_ |= 0x10;
        this.activityScore = activityScore;
        return this;
    }

    public QlogCncActivityScore setActivityScoreID(String activityScoreID) {
        bitFiled0_ |= 0x20;
        this.activityScoreID = activityScoreID;
        return this;
    }


    public static QlogCncActivityScore init(QlogPlayerFlowInterface flow_name) {
        QlogCncActivityScore flow = new QlogCncActivityScore();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(subjectID, 0, Math.min(subjectID.length(), 32));
        builder.append("|").append(icount);
        builder.append("|").append(activityScore);
        builder.append("|").append(activityScoreID, 0, Math.min(activityScoreID.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


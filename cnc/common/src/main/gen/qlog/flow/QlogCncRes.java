
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncRes extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncRes";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ResId", "BeforeCount", "AfterCount", "iCount", "AddOrReduce", "Reason", "SubReason"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 资源Id
     */
    private int resId;
    /**
     * 行为前该种资源数量
     */
    private long beforeCount;
    /**
     * 行为后该种资源数量
     */
    private long afterCount;
    /**
     * 改变的数量
     */
    private long iCount;
    /**
     * 资源变化类型
     */
    private int addOrReduce;
    /**
     * 资源变化的一级原因
     */
    private String reason;
    /**
     * 资源变化的二级原因
     */
    private String subReason;


    public QlogCncRes() {
        dtEventTime = "";
        reason = "";
        subReason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncRes setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncRes setResId(int resId) {
        bitFiled0_ |= 0x2;
        this.resId = resId;
        return this;
    }

    public QlogCncRes setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x4;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncRes setAfterCount(long afterCount) {
        bitFiled0_ |= 0x8;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncRes setICount(long iCount) {
        bitFiled0_ |= 0x10;
        this.iCount = iCount;
        return this;
    }

    public QlogCncRes setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }

    public QlogCncRes setReason(String reason) {
        bitFiled0_ |= 0x40;
        this.reason = reason;
        return this;
    }

    public QlogCncRes setSubReason(String subReason) {
        bitFiled0_ |= 0x80;
        this.subReason = subReason;
        return this;
    }


    public static QlogCncRes init(QlogPlayerFlowInterface flow_name) {
        QlogCncRes flow = new QlogCncRes();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(resId);
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(subReason, 0, Math.min(subReason.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncCanyon extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncCanyon";
    static final int currentFieldCnt = 15;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ScoreBefore", "ScoreCount", "ScoreAfter", "RankBefore", "RankAfter", "DefenderType", "DefenderID", "DefenderScoreBefore", "DefenderScoreCount", "DefenderScoreAfter", "DefenderRankBefore", "DefenderRankAfter", "BattleResult"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 进攻方原有积分
     */
    private int scoreBefore;
    /**
     * 进攻方积分变化值
     */
    private int scoreCount;
    /**
     * 进攻方战斗后积分
     */
    private int scoreAfter;
    /**
     * 进攻方原排名
     */
    private int rankBefore;
    /**
     * 进攻方战斗后排名
     */
    private int rankAfter;
    /**
     * 被挑战的类型
     */
    private int defenderType;
    /**
     * 防守方roleid
     */
    private String defenderID;
    /**
     * 防守方原积分
     */
    private int defenderScoreBefore;
    /**
     * 防守方积分变化值
     */
    private int defenderScoreCount;
    /**
     * 防守方战斗后积分
     */
    private int defenderScoreAfter;
    /**
     * 防守方原排名
     */
    private int defenderRankBefore;
    /**
     * 防守方战斗后排名
     */
    private int defenderRankAfter;
    /**
     * 战斗结果
     */
    private int battleResult;


    public QlogCncCanyon() {
        dtEventTime = "";
        defenderID = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncCanyon setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncCanyon setScoreBefore(int scoreBefore) {
        bitFiled0_ |= 0x2;
        this.scoreBefore = scoreBefore;
        return this;
    }

    public QlogCncCanyon setScoreCount(int scoreCount) {
        bitFiled0_ |= 0x4;
        this.scoreCount = scoreCount;
        return this;
    }

    public QlogCncCanyon setScoreAfter(int scoreAfter) {
        bitFiled0_ |= 0x8;
        this.scoreAfter = scoreAfter;
        return this;
    }

    public QlogCncCanyon setRankBefore(int rankBefore) {
        bitFiled0_ |= 0x10;
        this.rankBefore = rankBefore;
        return this;
    }

    public QlogCncCanyon setRankAfter(int rankAfter) {
        bitFiled0_ |= 0x20;
        this.rankAfter = rankAfter;
        return this;
    }

    public QlogCncCanyon setDefenderType(int defenderType) {
        bitFiled0_ |= 0x40;
        this.defenderType = defenderType;
        return this;
    }

    public QlogCncCanyon setDefenderID(String defenderID) {
        bitFiled0_ |= 0x80;
        this.defenderID = defenderID;
        return this;
    }

    public QlogCncCanyon setDefenderScoreBefore(int defenderScoreBefore) {
        bitFiled0_ |= 0x100;
        this.defenderScoreBefore = defenderScoreBefore;
        return this;
    }

    public QlogCncCanyon setDefenderScoreCount(int defenderScoreCount) {
        bitFiled0_ |= 0x200;
        this.defenderScoreCount = defenderScoreCount;
        return this;
    }

    public QlogCncCanyon setDefenderScoreAfter(int defenderScoreAfter) {
        bitFiled0_ |= 0x400;
        this.defenderScoreAfter = defenderScoreAfter;
        return this;
    }

    public QlogCncCanyon setDefenderRankBefore(int defenderRankBefore) {
        bitFiled0_ |= 0x800;
        this.defenderRankBefore = defenderRankBefore;
        return this;
    }

    public QlogCncCanyon setDefenderRankAfter(int defenderRankAfter) {
        bitFiled0_ |= 0x1000;
        this.defenderRankAfter = defenderRankAfter;
        return this;
    }

    public QlogCncCanyon setBattleResult(int battleResult) {
        bitFiled0_ |= 0x2000;
        this.battleResult = battleResult;
        return this;
    }


    public static QlogCncCanyon init(QlogPlayerFlowInterface flow_name) {
        QlogCncCanyon flow = new QlogCncCanyon();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3fff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x2000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(scoreBefore);
        builder.append("|").append(scoreCount);
        builder.append("|").append(scoreAfter);
        builder.append("|").append(rankBefore);
        builder.append("|").append(rankAfter);
        builder.append("|").append(defenderType);
        builder.append("|").append(defenderID, 0, Math.min(defenderID.length(), 64));
        builder.append("|").append(defenderScoreBefore);
        builder.append("|").append(defenderScoreCount);
        builder.append("|").append(defenderScoreAfter);
        builder.append("|").append(defenderRankBefore);
        builder.append("|").append(defenderRankAfter);
        builder.append("|").append(battleResult);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


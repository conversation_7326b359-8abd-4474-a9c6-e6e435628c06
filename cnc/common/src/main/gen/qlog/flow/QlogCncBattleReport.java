
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBattleReport extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBattleReport";
    static final int currentFieldCnt = 15;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BattleReportId", "BattleId", "MainObjectType", "DefenceOrNot", "MainObjectId", "MainObjectBuildingId", "BattleReportTimeStart", "BattleReportTimeEnd", "BattleReportResult", "EnemyId", "PlayerPower", "PlayerLevel", "ClanId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 战报Id
     */
    private String battleReportId;
    /**
     * 包含的战斗Id
     */
    private String battleId;
    /**
     * 战报主体类型
     */
    private int mainObjectType;
    /**
     * 是否为驻防部队
     */
    private int defenceOrNot;
    /**
     * 战报主体角色id
     */
    private String mainObjectId;
    /**
     * 战报主体的建筑id
     */
    private String mainObjectBuildingId;
    /**
     * 战报中第一场战斗开始的时间
     */
    private String battleReportTimeStart;
    /**
     * 战报中最后一场战斗结束的时间
     */
    private String battleReportTimeEnd;
    /**
     * 战报主体的战斗结果
     */
    private int battleReportResult;
    /**
     * 敌方id
     */
    private String enemyId;
    /**
     * 当前战力
     */
    private long playerPower;
    /**
     * 玩家主堡等级
     */
    private int playerLevel;
    /**
     * 玩家所属联盟id
     */
    private long clanId;


    public QlogCncBattleReport() {
        dtEventTime = "";
        battleReportId = "";
        battleId = "";
        mainObjectId = "";
        mainObjectBuildingId = "";
        battleReportTimeStart = "";
        battleReportTimeEnd = "";
        enemyId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBattleReport setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBattleReport setBattleReportId(String battleReportId) {
        bitFiled0_ |= 0x2;
        this.battleReportId = battleReportId;
        return this;
    }

    public QlogCncBattleReport setBattleId(String battleId) {
        bitFiled0_ |= 0x4;
        this.battleId = battleId;
        return this;
    }

    public QlogCncBattleReport setMainObjectType(int mainObjectType) {
        bitFiled0_ |= 0x8;
        this.mainObjectType = mainObjectType;
        return this;
    }

    public QlogCncBattleReport setDefenceOrNot(int defenceOrNot) {
        bitFiled0_ |= 0x10;
        this.defenceOrNot = defenceOrNot;
        return this;
    }

    public QlogCncBattleReport setMainObjectId(String mainObjectId) {
        bitFiled0_ |= 0x20;
        this.mainObjectId = mainObjectId;
        return this;
    }

    public QlogCncBattleReport setMainObjectBuildingId(String mainObjectBuildingId) {
        bitFiled0_ |= 0x40;
        this.mainObjectBuildingId = mainObjectBuildingId;
        return this;
    }

    public QlogCncBattleReport setBattleReportTimeStart(String battleReportTimeStart) {
        bitFiled0_ |= 0x80;
        this.battleReportTimeStart = battleReportTimeStart;
        return this;
    }

    public QlogCncBattleReport setBattleReportTimeEnd(String battleReportTimeEnd) {
        bitFiled0_ |= 0x100;
        this.battleReportTimeEnd = battleReportTimeEnd;
        return this;
    }

    public QlogCncBattleReport setBattleReportResult(int battleReportResult) {
        bitFiled0_ |= 0x200;
        this.battleReportResult = battleReportResult;
        return this;
    }

    public QlogCncBattleReport setEnemyId(String enemyId) {
        bitFiled0_ |= 0x400;
        this.enemyId = enemyId;
        return this;
    }

    public QlogCncBattleReport setPlayerPower(long playerPower) {
        bitFiled0_ |= 0x800;
        this.playerPower = playerPower;
        return this;
    }

    public QlogCncBattleReport setPlayerLevel(int playerLevel) {
        bitFiled0_ |= 0x1000;
        this.playerLevel = playerLevel;
        return this;
    }

    public QlogCncBattleReport setClanId(long clanId) {
        bitFiled0_ |= 0x2000;
        this.clanId = clanId;
        return this;
    }


    public static QlogCncBattleReport init(QlogPlayerFlowInterface flow_name) {
        QlogCncBattleReport flow = new QlogCncBattleReport();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3fff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x2000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(battleReportId, 0, Math.min(battleReportId.length(), 32));
        builder.append("|").append(battleId, 0, Math.min(battleId.length(), 128));
        builder.append("|").append(mainObjectType);
        builder.append("|").append(defenceOrNot);
        builder.append("|").append(mainObjectId, 0, Math.min(mainObjectId.length(), 32));
        builder.append("|").append(mainObjectBuildingId, 0, Math.min(mainObjectBuildingId.length(), 32));
        builder.append("|").append(battleReportTimeStart, 0, Math.min(battleReportTimeStart.length(), 32));
        builder.append("|").append(battleReportTimeEnd, 0, Math.min(battleReportTimeEnd.length(), 32));
        builder.append("|").append(battleReportResult);
        builder.append("|").append(enemyId, 0, Math.min(enemyId.length(), 128));
        builder.append("|").append(playerPower);
        builder.append("|").append(playerLevel);
        builder.append("|").append(clanId);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


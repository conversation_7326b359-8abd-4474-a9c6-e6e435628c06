
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncCollectRes extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncCollectRes";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "Reason", "ResourceID", "ResourcePartID", "ResStock", "ResGet", "ResBuildingID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 采集开始或结束的原因
     */
    private String reason;
    /**
     * 资源田的模板id
     */
    private int resourceID;
    /**
     * 资源田所属的地图片id
     */
    private int resourcePartID;
    /**
     * 资源田资源存储数量
     */
    private long resStock;
    /**
     * 采集获得的资源数量
     */
    private long resGet;
    /**
     * 军团资源田的唯一id，普通采集填0
     */
    private long resBuildingID;


    public QlogCncCollectRes() {
        dtEventTime = "";
        action = "";
        reason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncCollectRes setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncCollectRes setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncCollectRes setReason(String reason) {
        bitFiled0_ |= 0x4;
        this.reason = reason;
        return this;
    }

    public QlogCncCollectRes setResourceID(int resourceID) {
        bitFiled0_ |= 0x8;
        this.resourceID = resourceID;
        return this;
    }

    public QlogCncCollectRes setResourcePartID(int resourcePartID) {
        bitFiled0_ |= 0x10;
        this.resourcePartID = resourcePartID;
        return this;
    }

    public QlogCncCollectRes setResStock(long resStock) {
        bitFiled0_ |= 0x20;
        this.resStock = resStock;
        return this;
    }

    public QlogCncCollectRes setResGet(long resGet) {
        bitFiled0_ |= 0x40;
        this.resGet = resGet;
        return this;
    }

    public QlogCncCollectRes setResBuildingID(long resBuildingID) {
        bitFiled0_ |= 0x80;
        this.resBuildingID = resBuildingID;
        return this;
    }


    public static QlogCncCollectRes init(QlogPlayerFlowInterface flow_name) {
        QlogCncCollectRes flow = new QlogCncCollectRes();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(resourceID);
        builder.append("|").append(resourcePartID);
        builder.append("|").append(resStock);
        builder.append("|").append(resGet);
        builder.append("|").append(resBuildingID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


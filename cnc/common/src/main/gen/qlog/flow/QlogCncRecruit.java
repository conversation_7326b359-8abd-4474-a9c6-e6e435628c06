
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncRecruit extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncRecruit";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "RecruitPoolType", "FreeOrNot", "Param", "guaranteeCnt", "totalCnt"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 招募卡池类型
     */
    private String recruitPoolType;
    /**
     * 是否为免费招募 0:免费, 1:非免费
     */
    private int freeOrNot;
    /**
     * 本次招募日志中，玩家实际招募了多少次
     */
    private int param;
    /**
     * 本次触发保底招募的次数
     */
    private int guaranteeCnt;
    /**
     * 本宝箱总招募次数
     */
    private int totalCnt;


    public QlogCncRecruit() {
        dtEventTime = "";
        action = "";
        recruitPoolType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncRecruit setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncRecruit setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncRecruit setRecruitPoolType(String recruitPoolType) {
        bitFiled0_ |= 0x4;
        this.recruitPoolType = recruitPoolType;
        return this;
    }

    public QlogCncRecruit setFreeOrNot(int freeOrNot) {
        bitFiled0_ |= 0x8;
        this.freeOrNot = freeOrNot;
        return this;
    }

    public QlogCncRecruit setParam(int param) {
        bitFiled0_ |= 0x10;
        this.param = param;
        return this;
    }

    public QlogCncRecruit setGuaranteeCnt(int guaranteeCnt) {
        bitFiled0_ |= 0x20;
        this.guaranteeCnt = guaranteeCnt;
        return this;
    }

    public QlogCncRecruit setTotalCnt(int totalCnt) {
        bitFiled0_ |= 0x40;
        this.totalCnt = totalCnt;
        return this;
    }


    public static QlogCncRecruit init(QlogPlayerFlowInterface flow_name) {
        QlogCncRecruit flow = new QlogCncRecruit();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(recruitPoolType, 0, Math.min(recruitPoolType.length(), 128));
        builder.append("|").append(freeOrNot);
        builder.append("|").append(param);
        builder.append("|").append(guaranteeCnt);
        builder.append("|").append(totalCnt);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


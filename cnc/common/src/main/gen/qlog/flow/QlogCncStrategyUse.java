
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncStrategyUse extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncStrategyUse";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ChipID", "StrategicSkillID", "BeforeStrategicPoint", "AfterStrategicPoint", "StrategicPointCount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 芯片ID
     */
    private long chipID;
    /**
     * 战略技能子id
     */
    private int strategicSkillID;
    /**
     * 行为发生前战略点数量
     */
    private int beforeStrategicPoint;
    /**
     * 行为发生后战略点数量
     */
    private int afterStrategicPoint;
    /**
     * 变化的战略点数量
     */
    private int strategicPointCount;


    public QlogCncStrategyUse() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncStrategyUse setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncStrategyUse setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncStrategyUse setChipID(long chipID) {
        bitFiled0_ |= 0x4;
        this.chipID = chipID;
        return this;
    }

    public QlogCncStrategyUse setStrategicSkillID(int strategicSkillID) {
        bitFiled0_ |= 0x8;
        this.strategicSkillID = strategicSkillID;
        return this;
    }

    public QlogCncStrategyUse setBeforeStrategicPoint(int beforeStrategicPoint) {
        bitFiled0_ |= 0x10;
        this.beforeStrategicPoint = beforeStrategicPoint;
        return this;
    }

    public QlogCncStrategyUse setAfterStrategicPoint(int afterStrategicPoint) {
        bitFiled0_ |= 0x20;
        this.afterStrategicPoint = afterStrategicPoint;
        return this;
    }

    public QlogCncStrategyUse setStrategicPointCount(int strategicPointCount) {
        bitFiled0_ |= 0x40;
        this.strategicPointCount = strategicPointCount;
        return this;
    }


    public static QlogCncStrategyUse init(QlogPlayerFlowInterface flow_name) {
        QlogCncStrategyUse flow = new QlogCncStrategyUse();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(chipID);
        builder.append("|").append(strategicSkillID);
        builder.append("|").append(beforeStrategicPoint);
        builder.append("|").append(afterStrategicPoint);
        builder.append("|").append(strategicPointCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


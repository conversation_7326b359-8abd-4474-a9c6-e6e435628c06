
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncHospitalFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncHospitalFlow";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ArmyConfig", "ArmyInHosConfig"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 部队的详情
     */
    private String armyConfig;
    /**
     * 行为发生后医院剩余部队详情
     */
    private String armyInHosConfig;


    public QlogCncHospitalFlow() {
        dtEventTime = "";
        action = "";
        armyConfig = "";
        armyInHosConfig = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncHospitalFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncHospitalFlow setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncHospitalFlow setArmyConfig(String armyConfig) {
        bitFiled0_ |= 0x4;
        this.armyConfig = armyConfig;
        return this;
    }

    public QlogCncHospitalFlow setArmyInHosConfig(String armyInHosConfig) {
        bitFiled0_ |= 0x8;
        this.armyInHosConfig = armyInHosConfig;
        return this;
    }


    public static QlogCncHospitalFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncHospitalFlow flow = new QlogCncHospitalFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(armyConfig, 0, Math.min(armyConfig.length(), 2048));
        builder.append("|").append(armyInHosConfig, 0, Math.min(armyInHosConfig.length(), 2048));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


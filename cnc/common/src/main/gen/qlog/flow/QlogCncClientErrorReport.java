
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncClientErrorReport extends AbstractServerQlogFlow {
    static final String META_NAME = "CncClientErrorReport";
    static final int currentFieldCnt = 14;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Type", "sdkName", "openId", "playerId", "deviceId", "worlId", "zoneId", "clientIp", "clientCountry", "serverCountry", "systemHardware", "gear", "error"};
    /**
     * 监控类型（ puffer  dolphin / login / sdk）(必须有)
     */
    private String type;
    /**
     * sdk名
     */
    private String sdkName;
    /**
     * 账号id
     */
    private String openId;
    /**
     * 玩家id
     */
    private long playerId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 大区id
     */
    private int worlId;
    /**
     * 小服id
     */
    private int zoneId;
    /**
     * 客户端ip
     */
    private String clientIp;
    /**
     * 客户端国家
     */
    private String clientCountry;
    /**
     * 服务器国家
     */
    private String serverCountry;
    /**
     * 移动终端机型
     */
    private String systemHardware;
    /**
     * 机型档位
     */
    private int gear;
    /**
     * 错误信息
     */
    private String error;


    public QlogCncClientErrorReport() {
        type = "";
        sdkName = "";
        openId = "";
        deviceId = "";
        clientIp = "";
        clientCountry = "";
        serverCountry = "";
        systemHardware = "";
        error = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncClientErrorReport setType(String type) {
        bitFiled0_ |= 0x1;
        this.type = type;
        return this;
    }

    public QlogCncClientErrorReport setSdkName(String sdkName) {
        bitFiled0_ |= 0x2;
        this.sdkName = sdkName;
        return this;
    }

    public QlogCncClientErrorReport setOpenId(String openId) {
        bitFiled0_ |= 0x4;
        this.openId = openId;
        return this;
    }

    public QlogCncClientErrorReport setPlayerId(long playerId) {
        bitFiled0_ |= 0x8;
        this.playerId = playerId;
        return this;
    }

    public QlogCncClientErrorReport setDeviceId(String deviceId) {
        bitFiled0_ |= 0x10;
        this.deviceId = deviceId;
        return this;
    }

    public QlogCncClientErrorReport setWorlId(int worlId) {
        bitFiled0_ |= 0x20;
        this.worlId = worlId;
        return this;
    }

    public QlogCncClientErrorReport setZoneId(int zoneId) {
        bitFiled0_ |= 0x40;
        this.zoneId = zoneId;
        return this;
    }

    public QlogCncClientErrorReport setClientIp(String clientIp) {
        bitFiled0_ |= 0x80;
        this.clientIp = clientIp;
        return this;
    }

    public QlogCncClientErrorReport setClientCountry(String clientCountry) {
        bitFiled0_ |= 0x100;
        this.clientCountry = clientCountry;
        return this;
    }

    public QlogCncClientErrorReport setServerCountry(String serverCountry) {
        bitFiled0_ |= 0x200;
        this.serverCountry = serverCountry;
        return this;
    }

    public QlogCncClientErrorReport setSystemHardware(String systemHardware) {
        bitFiled0_ |= 0x400;
        this.systemHardware = systemHardware;
        return this;
    }

    public QlogCncClientErrorReport setGear(int gear) {
        bitFiled0_ |= 0x800;
        this.gear = gear;
        return this;
    }

    public QlogCncClientErrorReport setError(String error) {
        bitFiled0_ |= 0x1000;
        this.error = error;
        return this;
    }


    public static QlogCncClientErrorReport init(QlogServerFlowInterface flow_name) {
        QlogCncClientErrorReport flow = new QlogCncClientErrorReport();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1fff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x1000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(type, 0, Math.min(type.length(), 32));
        builder.append("|").append(sdkName, 0, Math.min(sdkName.length(), 64));
        builder.append("|").append(openId, 0, Math.min(openId.length(), 64));
        builder.append("|").append(playerId);
        builder.append("|").append(deviceId, 0, Math.min(deviceId.length(), 64));
        builder.append("|").append(worlId);
        builder.append("|").append(zoneId);
        builder.append("|").append(clientIp, 0, Math.min(clientIp.length(), 32));
        builder.append("|").append(clientCountry, 0, Math.min(clientCountry.length(), 64));
        builder.append("|").append(serverCountry, 0, Math.min(serverCountry.length(), 64));
        builder.append("|").append(systemHardware, 0, Math.min(systemHardware.length(), 32));
        builder.append("|").append(gear);
        builder.append("|").append(error, 0, Math.min(error.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


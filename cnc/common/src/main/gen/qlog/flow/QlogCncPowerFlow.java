
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPowerFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncPowerFlow";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BeforeCount", "AfterCount", "Reason"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 发生变化前的数量
     */
    private long beforeCount;
    /**
     * 发生变化后的数量
     */
    private long afterCount;
    /**
     * 战力变化的一级原因
     */
    private String reason;


    public QlogCncPowerFlow() {
        dtEventTime = "";
        reason = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPowerFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPowerFlow setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x2;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncPowerFlow setAfterCount(long afterCount) {
        bitFiled0_ |= 0x4;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncPowerFlow setReason(String reason) {
        bitFiled0_ |= 0x8;
        this.reason = reason;
        return this;
    }


    public static QlogCncPowerFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncPowerFlow flow = new QlogCncPowerFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


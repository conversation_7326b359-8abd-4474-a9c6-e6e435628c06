
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncAccountBound extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncAccountBound";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "AccountType", "AfterBoundConfig"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 账号类型
     */
    private String accountType;
    /**
     * 行为后账号绑定情况
     */
    private String afterBoundConfig;


    public QlogCncAccountBound() {
        dtEventTime = "";
        action = "";
        accountType = "";
        afterBoundConfig = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncAccountBound setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncAccountBound setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncAccountBound setAccountType(String accountType) {
        bitFiled0_ |= 0x4;
        this.accountType = accountType;
        return this;
    }

    public QlogCncAccountBound setAfterBoundConfig(String afterBoundConfig) {
        bitFiled0_ |= 0x8;
        this.afterBoundConfig = afterBoundConfig;
        return this;
    }


    public static QlogCncAccountBound init(QlogPlayerFlowInterface flow_name) {
        QlogCncAccountBound flow = new QlogCncAccountBound();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(accountType, 0, Math.min(accountType.length(), 32));
        builder.append("|").append(afterBoundConfig, 0, Math.min(afterBoundConfig.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBundlePurchase extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBundlePurchase";
    static final int currentFieldCnt = 11;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "BundleId", "Price", "LimitedCount", "RemainLimitedCount", "IfDouble", "IfUseToken", "ItemGet", "ActionNote"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 礼包ID
     */
    private int bundleId;
    /**
     * 礼包价格
     */
    private float price;
    /**
     * 礼包的限购次数
     */
    private int limitedCount;
    /**
     * 剩余的限购次数
     */
    private int remainLimitedCount;
    /**
     * 是否双倍黄金
     */
    private int ifDouble;
    /**
     * 是否使用内部福利代币购买
     */
    private int ifUseToken;
    /**
     * 获得的非固定道具
     */
    private String itemGet;
    /**
     * 行为类型扩充
     */
    private String actionNote;


    public QlogCncBundlePurchase() {
        dtEventTime = "";
        action = "";
        itemGet = "";
        actionNote = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBundlePurchase setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBundlePurchase setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncBundlePurchase setBundleId(int bundleId) {
        bitFiled0_ |= 0x4;
        this.bundleId = bundleId;
        return this;
    }

    public QlogCncBundlePurchase setPrice(float price) {
        bitFiled0_ |= 0x8;
        this.price = price;
        return this;
    }

    public QlogCncBundlePurchase setLimitedCount(int limitedCount) {
        bitFiled0_ |= 0x10;
        this.limitedCount = limitedCount;
        return this;
    }

    public QlogCncBundlePurchase setRemainLimitedCount(int remainLimitedCount) {
        bitFiled0_ |= 0x20;
        this.remainLimitedCount = remainLimitedCount;
        return this;
    }

    public QlogCncBundlePurchase setIfDouble(int ifDouble) {
        bitFiled0_ |= 0x40;
        this.ifDouble = ifDouble;
        return this;
    }

    public QlogCncBundlePurchase setIfUseToken(int ifUseToken) {
        bitFiled0_ |= 0x80;
        this.ifUseToken = ifUseToken;
        return this;
    }

    public QlogCncBundlePurchase setItemGet(String itemGet) {
        bitFiled0_ |= 0x100;
        this.itemGet = itemGet;
        return this;
    }

    public QlogCncBundlePurchase setActionNote(String actionNote) {
        bitFiled0_ |= 0x200;
        this.actionNote = actionNote;
        return this;
    }


    public static QlogCncBundlePurchase init(QlogPlayerFlowInterface flow_name) {
        QlogCncBundlePurchase flow = new QlogCncBundlePurchase();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x200) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(bundleId);
        builder.append("|").append(price);
        builder.append("|").append(limitedCount);
        builder.append("|").append(remainLimitedCount);
        builder.append("|").append(ifDouble);
        builder.append("|").append(ifUseToken);
        builder.append("|").append(itemGet, 0, Math.min(itemGet.length(), 512));
        builder.append("|").append(actionNote, 0, Math.min(actionNote.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


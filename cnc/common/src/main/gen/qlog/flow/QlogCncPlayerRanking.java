
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPlayerRanking extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncPlayerRanking";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "RankingID", "Ranking", "RankingScore"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 排行榜id
     */
    private int rankingID;
    /**
     * 名次
     */
    private int ranking;
    /**
     * 分数
     */
    private long rankingScore;


    public QlogCncPlayerRanking() {
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPlayerRanking setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPlayerRanking setRankingID(int rankingID) {
        bitFiled0_ |= 0x2;
        this.rankingID = rankingID;
        return this;
    }

    public QlogCncPlayerRanking setRanking(int ranking) {
        bitFiled0_ |= 0x4;
        this.ranking = ranking;
        return this;
    }

    public QlogCncPlayerRanking setRankingScore(long rankingScore) {
        bitFiled0_ |= 0x8;
        this.rankingScore = rankingScore;
        return this;
    }


    public static QlogCncPlayerRanking init(QlogPlayerFlowInterface flow_name) {
        QlogCncPlayerRanking flow = new QlogCncPlayerRanking();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(rankingID);
        builder.append("|").append(ranking);
        builder.append("|").append(rankingScore);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


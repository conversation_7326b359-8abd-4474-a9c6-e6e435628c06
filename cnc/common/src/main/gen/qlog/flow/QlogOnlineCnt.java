
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogOnlineCnt extends AbstractServerQlogFlow {
    static final String META_NAME = "OnlineCnt";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "GameAppid", "TimeKey", "OnlinecntIos", "OnlinecntAndroid", "GsId", "ZoneAreaId", "Country"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 游戏APPID，用来区分不同项目
     */
    private String gameAppid;
    /**
     * 当前时间的时间戳，即当前的unixtime
     */
    private long timeKey;
    /**
     * ios在线人数
     */
    private int onlinecntIos;
    /**
     * android在线人数
     */
    private int onlinecntAndroid;
    /**
     * worldId
     */
    private String gsId;
    /**
     * 服务器id
     */
    private int zoneAreaId;
    /**
     * 国家名称，使用中文
     */
    private String country;


    public QlogOnlineCnt() {
        dtEventTime = "";
        gameAppid = "";
        gsId = "";
        country = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogOnlineCnt setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogOnlineCnt setGameAppid(String gameAppid) {
        bitFiled0_ |= 0x2;
        this.gameAppid = gameAppid;
        return this;
    }

    public QlogOnlineCnt setTimeKey(long timeKey) {
        bitFiled0_ |= 0x4;
        this.timeKey = timeKey;
        return this;
    }

    public QlogOnlineCnt setOnlinecntIos(int onlinecntIos) {
        bitFiled0_ |= 0x8;
        this.onlinecntIos = onlinecntIos;
        return this;
    }

    public QlogOnlineCnt setOnlinecntAndroid(int onlinecntAndroid) {
        bitFiled0_ |= 0x10;
        this.onlinecntAndroid = onlinecntAndroid;
        return this;
    }

    public QlogOnlineCnt setGsId(String gsId) {
        bitFiled0_ |= 0x20;
        this.gsId = gsId;
        return this;
    }

    public QlogOnlineCnt setZoneAreaId(int zoneAreaId) {
        bitFiled0_ |= 0x40;
        this.zoneAreaId = zoneAreaId;
        return this;
    }

    public QlogOnlineCnt setCountry(String country) {
        bitFiled0_ |= 0x80;
        this.country = country;
        return this;
    }


    public static QlogOnlineCnt init(QlogServerFlowInterface flow_name) {
        QlogOnlineCnt flow = new QlogOnlineCnt();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(gameAppid, 0, Math.min(gameAppid.length(), 32));
        builder.append("|").append(timeKey);
        builder.append("|").append(onlinecntIos);
        builder.append("|").append(onlinecntAndroid);
        builder.append("|").append(gsId, 0, Math.min(gsId.length(), 32));
        builder.append("|").append(zoneAreaId);
        builder.append("|").append(country, 0, Math.min(country.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


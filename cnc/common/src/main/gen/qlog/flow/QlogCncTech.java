
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncTech extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncTech";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "StartTime", "TechID", "SubTechID", "TechType", "TechLevel"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 科技开始进行研究的时间
     */
    private String startTime;
    /**
     * 科技id
     */
    private int techID;
    /**
     * 科技子id
     */
    private int subTechID;
    /**
     * 科技类型
     */
    private String techType;
    /**
     * 科技等级
     */
    private int techLevel;


    public QlogCncTech() {
        dtEventTime = "";
        action = "";
        startTime = "";
        techType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncTech setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncTech setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncTech setStartTime(String startTime) {
        bitFiled0_ |= 0x4;
        this.startTime = startTime;
        return this;
    }

    public QlogCncTech setTechID(int techID) {
        bitFiled0_ |= 0x8;
        this.techID = techID;
        return this;
    }

    public QlogCncTech setSubTechID(int subTechID) {
        bitFiled0_ |= 0x10;
        this.subTechID = subTechID;
        return this;
    }

    public QlogCncTech setTechType(String techType) {
        bitFiled0_ |= 0x20;
        this.techType = techType;
        return this;
    }

    public QlogCncTech setTechLevel(int techLevel) {
        bitFiled0_ |= 0x40;
        this.techLevel = techLevel;
        return this;
    }


    public static QlogCncTech init(QlogPlayerFlowInterface flow_name) {
        QlogCncTech flow = new QlogCncTech();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(startTime, 0, Math.min(startTime.length(), 32));
        builder.append("|").append(techID);
        builder.append("|").append(subTechID);
        builder.append("|").append(techType, 0, Math.min(techType.length(), 32));
        builder.append("|").append(techLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


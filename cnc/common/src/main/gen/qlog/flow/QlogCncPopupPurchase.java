
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPopupPurchase extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncPopupPurchase";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "Position", "BundleId", "Price", "Count"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 礼包位置
     */
    private String position;
    /**
     * 礼包id
     */
    private int bundleId;
    /**
     * 礼包价格
     */
    private String price;
    /**
     * 第几次出现给玩家
     */
    private int count;


    public QlogCncPopupPurchase() {
        dtEventTime = "";
        action = "";
        position = "";
        price = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPopupPurchase setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPopupPurchase setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncPopupPurchase setPosition(String position) {
        bitFiled0_ |= 0x4;
        this.position = position;
        return this;
    }

    public QlogCncPopupPurchase setBundleId(int bundleId) {
        bitFiled0_ |= 0x8;
        this.bundleId = bundleId;
        return this;
    }

    public QlogCncPopupPurchase setPrice(String price) {
        bitFiled0_ |= 0x10;
        this.price = price;
        return this;
    }

    public QlogCncPopupPurchase setCount(int count) {
        bitFiled0_ |= 0x20;
        this.count = count;
        return this;
    }


    public static QlogCncPopupPurchase init(QlogPlayerFlowInterface flow_name) {
        QlogCncPopupPurchase flow = new QlogCncPopupPurchase();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(position, 0, Math.min(position.length(), 64));
        builder.append("|").append(bundleId);
        builder.append("|").append(price, 0, Math.min(price.length(), 32));
        builder.append("|").append(count);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


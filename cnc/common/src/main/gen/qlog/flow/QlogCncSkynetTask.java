
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncSkynetTask extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncSkynetTask";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "TaskId", "TaskQuality", "task_module", "task_type"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 任务ID
     */
    private int taskId;
    /**
     * 任务品质
     */
    private int taskQuality;
    /**
     * 任务模块
     */
    private int task_module;
    /**
     * 任务类型
     */
    private int task_type;


    public QlogCncSkynetTask() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncSkynetTask setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncSkynetTask setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncSkynetTask setTaskId(int taskId) {
        bitFiled0_ |= 0x4;
        this.taskId = taskId;
        return this;
    }

    public QlogCncSkynetTask setTaskQuality(int taskQuality) {
        bitFiled0_ |= 0x8;
        this.taskQuality = taskQuality;
        return this;
    }

    public QlogCncSkynetTask setTask_module(int task_module) {
        bitFiled0_ |= 0x10;
        this.task_module = task_module;
        return this;
    }

    public QlogCncSkynetTask setTask_type(int task_type) {
        bitFiled0_ |= 0x20;
        this.task_type = task_type;
        return this;
    }


    public static QlogCncSkynetTask init(QlogPlayerFlowInterface flow_name) {
        QlogCncSkynetTask flow = new QlogCncSkynetTask();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(taskId);
        builder.append("|").append(taskQuality);
        builder.append("|").append(task_module);
        builder.append("|").append(task_type);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildSnapshot extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildSnapshot";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ExpireOrNot", "GuildResConfig", "GuildScore", "GuildCityList", "GuildFlag"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 状态字段，0:淘汰, 1:正常, 2:解散
     */
    private int expireOrNot;
    /**
     * 联盟资源详情
     */
    private String guildResConfig;
    /**
     * 联盟积分数量
     */
    private long guildScore;
    /**
     * 联盟占领的城市列表
     */
    private String guildCityList;
    /**
     * 联盟旗帜
     */
    private int guildFlag;


    public QlogCncGuildSnapshot() {
        dtEventTime = "";
        guildResConfig = "";
        guildCityList = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildSnapshot setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildSnapshot setExpireOrNot(int expireOrNot) {
        bitFiled0_ |= 0x2;
        this.expireOrNot = expireOrNot;
        return this;
    }

    public QlogCncGuildSnapshot setGuildResConfig(String guildResConfig) {
        bitFiled0_ |= 0x4;
        this.guildResConfig = guildResConfig;
        return this;
    }

    public QlogCncGuildSnapshot setGuildScore(long guildScore) {
        bitFiled0_ |= 0x8;
        this.guildScore = guildScore;
        return this;
    }

    public QlogCncGuildSnapshot setGuildCityList(String guildCityList) {
        bitFiled0_ |= 0x10;
        this.guildCityList = guildCityList;
        return this;
    }

    public QlogCncGuildSnapshot setGuildFlag(int guildFlag) {
        bitFiled0_ |= 0x20;
        this.guildFlag = guildFlag;
        return this;
    }


    public static QlogCncGuildSnapshot init(QlogClanFlowInterface flow_name) {
        QlogCncGuildSnapshot flow = new QlogCncGuildSnapshot();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(expireOrNot);
        builder.append("|").append(guildResConfig, 0, Math.min(guildResConfig.length(), 2048));
        builder.append("|").append(guildScore);
        builder.append("|").append(guildCityList, 0, Math.min(guildCityList.length(), 2048));
        builder.append("|").append(guildFlag);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


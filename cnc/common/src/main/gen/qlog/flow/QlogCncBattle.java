
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBattle extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBattle";
    static final int currentFieldCnt = 25;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BattleID", "AttackOrNot", "DefenceOrNot", "MainObjectType", "MainObjectID", "MainObjectBuildingID", "ArmedMajorHeroConfig", "ArmedSubHeroConfig", "ArmedMechaID", "ArmedMechaConfig", "ArmedMechaSuit", "StartArmyConfig", "BattleStartCoordinate", "EnemyType", "EnemyID", "EndArmyConfig", "SeriousInjuryArmyConfig", "SlightlyInjuryArmyConfig", "DeadArmyConfig", "BattleTimeStart", "BattleTimeEnd", "BattleResult", "ArmyId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 战斗id
     */
    private String battleID;
    /**
     * 是否为攻击方, 0 进攻方，1 防守方
     */
    private int attackOrNot;
    /**
     * 是否为驻防部队, 0 非驻防部队，1 玩家基地驻防，2 联盟建筑驻防
     */
    private int defenceOrNot;
    /**
     * 战斗主体类型, 0代表单个玩家部队，1代表集结部队
     */
    private int mainObjectType;
    /**
     * 战斗主体id
     */
    private String mainObjectID;
    /**
     * 战斗主体的建筑id
     */
    private String mainObjectBuildingID;
    /**
     * 上阵的主英雄详情
     */
    private String armedMajorHeroConfig;
    /**
     * 上阵的副英雄详情
     */
    private String armedSubHeroConfig;
    /**
     * 上阵机甲ID
     */
    private int armedMechaID;
    /**
     * 上阵机甲配件详情
     */
    private String armedMechaConfig;
    /**
     * 上阵机甲配件套装
     */
    private String armedMechaSuit;
    /**
     * 开始时部队详情
     */
    private String startArmyConfig;
    /**
     * 战斗开始时战斗主体坐标
     */
    private String battleStartCoordinate;
    /**
     * 敌方类型
     */
    private String enemyType;
    /**
     * 敌方id
     */
    private String enemyID;
    /**
     * 结束时部队详情
     */
    private String endArmyConfig;
    /**
     * 战斗中重伤部队数量详情
     */
    private String seriousInjuryArmyConfig;
    /**
     * 战斗中轻伤部队数量详情
     */
    private String slightlyInjuryArmyConfig;
    /**
     * 战斗中死亡部队数量详情
     */
    private String deadArmyConfig;
    /**
     * 战斗开始的时间
     */
    private String battleTimeStart;
    /**
     * 战斗结束的时间
     */
    private String battleTimeEnd;
    /**
     * 该场战斗的战斗主体结果
     */
    private int battleResult;
    /**
     * 行军唯一id
     */
    private String armyId;


    public QlogCncBattle() {
        dtEventTime = "";
        battleID = "";
        mainObjectID = "";
        mainObjectBuildingID = "";
        armedMajorHeroConfig = "";
        armedSubHeroConfig = "";
        armedMechaConfig = "";
        armedMechaSuit = "";
        startArmyConfig = "";
        battleStartCoordinate = "";
        enemyType = "";
        enemyID = "";
        endArmyConfig = "";
        seriousInjuryArmyConfig = "";
        slightlyInjuryArmyConfig = "";
        deadArmyConfig = "";
        battleTimeStart = "";
        battleTimeEnd = "";
        armyId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBattle setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBattle setBattleID(String battleID) {
        bitFiled0_ |= 0x2;
        this.battleID = battleID;
        return this;
    }

    public QlogCncBattle setAttackOrNot(int attackOrNot) {
        bitFiled0_ |= 0x4;
        this.attackOrNot = attackOrNot;
        return this;
    }

    public QlogCncBattle setDefenceOrNot(int defenceOrNot) {
        bitFiled0_ |= 0x8;
        this.defenceOrNot = defenceOrNot;
        return this;
    }

    public QlogCncBattle setMainObjectType(int mainObjectType) {
        bitFiled0_ |= 0x10;
        this.mainObjectType = mainObjectType;
        return this;
    }

    public QlogCncBattle setMainObjectID(String mainObjectID) {
        bitFiled0_ |= 0x20;
        this.mainObjectID = mainObjectID;
        return this;
    }

    public QlogCncBattle setMainObjectBuildingID(String mainObjectBuildingID) {
        bitFiled0_ |= 0x40;
        this.mainObjectBuildingID = mainObjectBuildingID;
        return this;
    }

    public QlogCncBattle setArmedMajorHeroConfig(String armedMajorHeroConfig) {
        bitFiled0_ |= 0x80;
        this.armedMajorHeroConfig = armedMajorHeroConfig;
        return this;
    }

    public QlogCncBattle setArmedSubHeroConfig(String armedSubHeroConfig) {
        bitFiled0_ |= 0x100;
        this.armedSubHeroConfig = armedSubHeroConfig;
        return this;
    }

    public QlogCncBattle setArmedMechaID(int armedMechaID) {
        bitFiled0_ |= 0x200;
        this.armedMechaID = armedMechaID;
        return this;
    }

    public QlogCncBattle setArmedMechaConfig(String armedMechaConfig) {
        bitFiled0_ |= 0x400;
        this.armedMechaConfig = armedMechaConfig;
        return this;
    }

    public QlogCncBattle setArmedMechaSuit(String armedMechaSuit) {
        bitFiled0_ |= 0x800;
        this.armedMechaSuit = armedMechaSuit;
        return this;
    }

    public QlogCncBattle setStartArmyConfig(String startArmyConfig) {
        bitFiled0_ |= 0x1000;
        this.startArmyConfig = startArmyConfig;
        return this;
    }

    public QlogCncBattle setBattleStartCoordinate(String battleStartCoordinate) {
        bitFiled0_ |= 0x2000;
        this.battleStartCoordinate = battleStartCoordinate;
        return this;
    }

    public QlogCncBattle setEnemyType(String enemyType) {
        bitFiled0_ |= 0x4000;
        this.enemyType = enemyType;
        return this;
    }

    public QlogCncBattle setEnemyID(String enemyID) {
        bitFiled0_ |= 0x8000;
        this.enemyID = enemyID;
        return this;
    }

    public QlogCncBattle setEndArmyConfig(String endArmyConfig) {
        bitFiled0_ |= 0x10000;
        this.endArmyConfig = endArmyConfig;
        return this;
    }

    public QlogCncBattle setSeriousInjuryArmyConfig(String seriousInjuryArmyConfig) {
        bitFiled0_ |= 0x20000;
        this.seriousInjuryArmyConfig = seriousInjuryArmyConfig;
        return this;
    }

    public QlogCncBattle setSlightlyInjuryArmyConfig(String slightlyInjuryArmyConfig) {
        bitFiled0_ |= 0x40000;
        this.slightlyInjuryArmyConfig = slightlyInjuryArmyConfig;
        return this;
    }

    public QlogCncBattle setDeadArmyConfig(String deadArmyConfig) {
        bitFiled0_ |= 0x80000;
        this.deadArmyConfig = deadArmyConfig;
        return this;
    }

    public QlogCncBattle setBattleTimeStart(String battleTimeStart) {
        bitFiled0_ |= 0x100000;
        this.battleTimeStart = battleTimeStart;
        return this;
    }

    public QlogCncBattle setBattleTimeEnd(String battleTimeEnd) {
        bitFiled0_ |= 0x200000;
        this.battleTimeEnd = battleTimeEnd;
        return this;
    }

    public QlogCncBattle setBattleResult(int battleResult) {
        bitFiled0_ |= 0x400000;
        this.battleResult = battleResult;
        return this;
    }

    public QlogCncBattle setArmyId(String armyId) {
        bitFiled0_ |= 0x800000;
        this.armyId = armyId;
        return this;
    }


    public static QlogCncBattle init(QlogPlayerFlowInterface flow_name) {
        QlogCncBattle flow = new QlogCncBattle();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xffffff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x800000) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(battleID, 0, Math.min(battleID.length(), 32));
        builder.append("|").append(attackOrNot);
        builder.append("|").append(defenceOrNot);
        builder.append("|").append(mainObjectType);
        builder.append("|").append(mainObjectID, 0, Math.min(mainObjectID.length(), 32));
        builder.append("|").append(mainObjectBuildingID, 0, Math.min(mainObjectBuildingID.length(), 32));
        builder.append("|").append(armedMajorHeroConfig, 0, Math.min(armedMajorHeroConfig.length(), 128));
        builder.append("|").append(armedSubHeroConfig, 0, Math.min(armedSubHeroConfig.length(), 128));
        builder.append("|").append(armedMechaID);
        builder.append("|").append(armedMechaConfig, 0, Math.min(armedMechaConfig.length(), 2560));
        builder.append("|").append(armedMechaSuit, 0, Math.min(armedMechaSuit.length(), 2560));
        builder.append("|").append(startArmyConfig, 0, Math.min(startArmyConfig.length(), 2560));
        builder.append("|").append(battleStartCoordinate, 0, Math.min(battleStartCoordinate.length(), 32));
        builder.append("|").append(enemyType, 0, Math.min(enemyType.length(), 128));
        builder.append("|").append(enemyID, 0, Math.min(enemyID.length(), 128));
        builder.append("|").append(endArmyConfig, 0, Math.min(endArmyConfig.length(), 2560));
        builder.append("|").append(seriousInjuryArmyConfig, 0, Math.min(seriousInjuryArmyConfig.length(), 2560));
        builder.append("|").append(slightlyInjuryArmyConfig, 0, Math.min(slightlyInjuryArmyConfig.length(), 2560));
        builder.append("|").append(deadArmyConfig, 0, Math.min(deadArmyConfig.length(), 2560));
        builder.append("|").append(battleTimeStart, 0, Math.min(battleTimeStart.length(), 32));
        builder.append("|").append(battleTimeEnd, 0, Math.min(battleTimeEnd.length(), 32));
        builder.append("|").append(battleResult);
        builder.append("|").append(armyId, 0, Math.min(armyId.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


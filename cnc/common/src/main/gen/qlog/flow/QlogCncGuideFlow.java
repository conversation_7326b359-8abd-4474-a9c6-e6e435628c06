
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuideFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuideFlow";
    static final int currentFieldCnt = 3;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "iGuideID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 新手引导的步骤id
     */
    private String iGuideID;


    public QlogCncGuideFlow() {
        dtEventTime = "";
        iGuideID = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuideFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuideFlow setIGuideID(String iGuideID) {
        bitFiled0_ |= 0x2;
        this.iGuideID = iGuideID;
        return this;
    }


    public static QlogCncGuideFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuideFlow flow = new QlogCncGuideFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x2) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iGuideID, 0, Math.min(iGuideID.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


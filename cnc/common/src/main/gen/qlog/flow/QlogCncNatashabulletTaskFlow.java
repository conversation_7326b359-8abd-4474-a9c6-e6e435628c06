
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncNatashabulletTaskFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncNatashabulletTaskFlow";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "activityid", "Action", "iTaskID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 活动ID
     */
    private int activityid;
    /**
     * 任务模块类型
     */
    private String action;
    /**
     * 任务ID
     */
    private int iTaskID;


    public QlogCncNatashabulletTaskFlow() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncNatashabulletTaskFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncNatashabulletTaskFlow setActivityid(int activityid) {
        bitFiled0_ |= 0x2;
        this.activityid = activityid;
        return this;
    }

    public QlogCncNatashabulletTaskFlow setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncNatashabulletTaskFlow setITaskID(int iTaskID) {
        bitFiled0_ |= 0x8;
        this.iTaskID = iTaskID;
        return this;
    }


    public static QlogCncNatashabulletTaskFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncNatashabulletTaskFlow flow = new QlogCncNatashabulletTaskFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(activityid);
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(iTaskID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


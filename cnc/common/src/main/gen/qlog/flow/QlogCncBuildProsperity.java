
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBuildProsperity extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBuildProsperity";
    static final int currentFieldCnt = 9;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Action", "dtEventTime", "BuildingId", "ProsperityType", "BeforeCount", "AfterCount", "ICount", "AddOrReduce"};
    /**
     * 行为类型
     */
    private String action;
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 建筑物id
     */
    private String buildingId;
    /**
     * 繁荣度类型
     */
    private String prosperityType;
    /**
     * 行为前数值
     */
    private long beforeCount;
    /**
     * 行为后数值
     */
    private long afterCount;
    /**
     * 变化的数值
     */
    private long iCount;
    /**
     * 繁荣度或繁荣度上限的变化方向
     */
    private int addOrReduce;


    public QlogCncBuildProsperity() {
        action = "";
        dtEventTime = "";
        buildingId = "";
        prosperityType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBuildProsperity setAction(String action) {
        bitFiled0_ |= 0x1;
        this.action = action;
        return this;
    }

    public QlogCncBuildProsperity setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x2;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBuildProsperity setBuildingId(String buildingId) {
        bitFiled0_ |= 0x4;
        this.buildingId = buildingId;
        return this;
    }

    public QlogCncBuildProsperity setProsperityType(String prosperityType) {
        bitFiled0_ |= 0x8;
        this.prosperityType = prosperityType;
        return this;
    }

    public QlogCncBuildProsperity setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x10;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncBuildProsperity setAfterCount(long afterCount) {
        bitFiled0_ |= 0x20;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncBuildProsperity setICount(long iCount) {
        bitFiled0_ |= 0x40;
        this.iCount = iCount;
        return this;
    }

    public QlogCncBuildProsperity setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x80;
        this.addOrReduce = addOrReduce;
        return this;
    }


    public static QlogCncBuildProsperity init(QlogPlayerFlowInterface flow_name) {
        QlogCncBuildProsperity flow = new QlogCncBuildProsperity();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x80) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(buildingId, 0, Math.min(buildingId.length(), 32));
        builder.append("|").append(prosperityType, 0, Math.min(prosperityType.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncStrategySkill extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncStrategySkill";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ChipID", "StrategicSkillCategoryID", "AfterSkillLevel"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 芯片ID
     */
    private long chipID;
    /**
     * 技能所属的技能组id
     */
    private int strategicSkillCategoryID;
    /**
     * 行为后该技能等级
     */
    private int afterSkillLevel;


    public QlogCncStrategySkill() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncStrategySkill setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncStrategySkill setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncStrategySkill setChipID(long chipID) {
        bitFiled0_ |= 0x4;
        this.chipID = chipID;
        return this;
    }

    public QlogCncStrategySkill setStrategicSkillCategoryID(int strategicSkillCategoryID) {
        bitFiled0_ |= 0x8;
        this.strategicSkillCategoryID = strategicSkillCategoryID;
        return this;
    }

    public QlogCncStrategySkill setAfterSkillLevel(int afterSkillLevel) {
        bitFiled0_ |= 0x10;
        this.afterSkillLevel = afterSkillLevel;
        return this;
    }


    public static QlogCncStrategySkill init(QlogPlayerFlowInterface flow_name) {
        QlogCncStrategySkill flow = new QlogCncStrategySkill();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(chipID);
        builder.append("|").append(strategicSkillCategoryID);
        builder.append("|").append(afterSkillLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


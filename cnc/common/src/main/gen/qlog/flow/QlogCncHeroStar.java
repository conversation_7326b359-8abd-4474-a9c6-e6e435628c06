
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncHeroStar extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncHeroStar";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "HeroID", "AfterHeroStar", "AfterHeroStarExp", "HeroStarExpCount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 英雄id
     */
    private String heroID;
    /**
     * 行为后英雄星级阶数
     */
    private int afterHeroStar;
    /**
     * 行为后英雄星级经验余量
     */
    private long afterHeroStarExp;
    /**
     * 一次升级行为增加的经验
     */
    private long heroStarExpCount;


    public QlogCncHeroStar() {
        dtEventTime = "";
        action = "";
        heroID = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncHeroStar setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncHeroStar setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncHeroStar setHeroID(String heroID) {
        bitFiled0_ |= 0x4;
        this.heroID = heroID;
        return this;
    }

    public QlogCncHeroStar setAfterHeroStar(int afterHeroStar) {
        bitFiled0_ |= 0x8;
        this.afterHeroStar = afterHeroStar;
        return this;
    }

    public QlogCncHeroStar setAfterHeroStarExp(long afterHeroStarExp) {
        bitFiled0_ |= 0x10;
        this.afterHeroStarExp = afterHeroStarExp;
        return this;
    }

    public QlogCncHeroStar setHeroStarExpCount(long heroStarExpCount) {
        bitFiled0_ |= 0x20;
        this.heroStarExpCount = heroStarExpCount;
        return this;
    }


    public static QlogCncHeroStar init(QlogPlayerFlowInterface flow_name) {
        QlogCncHeroStar flow = new QlogCncHeroStar();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(heroID, 0, Math.min(heroID.length(), 32));
        builder.append("|").append(afterHeroStar);
        builder.append("|").append(afterHeroStarExp);
        builder.append("|").append(heroStarExpCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


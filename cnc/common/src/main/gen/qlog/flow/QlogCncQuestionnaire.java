
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncQuestionnaire extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncQuestionnaire";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Action", "DtEventTime", "QuestionnaireId", "Answer"};
    /**
     * 行为类型
     */
    private String action;
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 问卷ID
     */
    private int questionnaireId;
    /**
     * 回答的答案ID
     */
    private int answer;


    public QlogCncQuestionnaire() {
        action = "";
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncQuestionnaire setAction(String action) {
        bitFiled0_ |= 0x1;
        this.action = action;
        return this;
    }

    public QlogCncQuestionnaire setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x2;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncQuestionnaire setQuestionnaireId(int questionnaireId) {
        bitFiled0_ |= 0x4;
        this.questionnaireId = questionnaireId;
        return this;
    }

    public QlogCncQuestionnaire setAnswer(int answer) {
        bitFiled0_ |= 0x8;
        this.answer = answer;
        return this;
    }


    public static QlogCncQuestionnaire init(QlogPlayerFlowInterface flow_name) {
        QlogCncQuestionnaire flow = new QlogCncQuestionnaire();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(questionnaireId);
        builder.append("|").append(answer);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


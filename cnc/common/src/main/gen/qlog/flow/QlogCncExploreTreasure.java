
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncExploreTreasure extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncExploreTreasure";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Floor", "FloorCount", "BigReward"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 当前的层数
     */
    private int floor;
    /**
     * 该层第几次探秘
     */
    private int floorCount;
    /**
     * 大奖
     */
    private String bigReward;


    public QlogCncExploreTreasure() {
        dtEventTime = "";
        bigReward = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncExploreTreasure setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncExploreTreasure setFloor(int floor) {
        bitFiled0_ |= 0x2;
        this.floor = floor;
        return this;
    }

    public QlogCncExploreTreasure setFloorCount(int floorCount) {
        bitFiled0_ |= 0x4;
        this.floorCount = floorCount;
        return this;
    }

    public QlogCncExploreTreasure setBigReward(String bigReward) {
        bitFiled0_ |= 0x8;
        this.bigReward = bigReward;
        return this;
    }


    public static QlogCncExploreTreasure init(QlogPlayerFlowInterface flow_name) {
        QlogCncExploreTreasure flow = new QlogCncExploreTreasure();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(floor);
        builder.append("|").append(floorCount);
        builder.append("|").append(bigReward, 0, Math.min(bigReward.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


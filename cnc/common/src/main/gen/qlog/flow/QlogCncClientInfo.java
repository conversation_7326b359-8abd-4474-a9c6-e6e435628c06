
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncClientInfo extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncClientInfo";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Type", "ClientInfo"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 类型
     */
    private int type;
    /**
     * 客户端回传字段
     */
    private String clientInfo;


    public QlogCncClientInfo() {
        dtEventTime = "";
        clientInfo = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncClientInfo setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncClientInfo setType(int type) {
        bitFiled0_ |= 0x2;
        this.type = type;
        return this;
    }

    public QlogCncClientInfo setClientInfo(String clientInfo) {
        bitFiled0_ |= 0x4;
        this.clientInfo = clientInfo;
        return this;
    }


    public static QlogCncClientInfo init(QlogPlayerFlowInterface flow_name) {
        QlogCncClientInfo flow = new QlogCncClientInfo();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(type);
        builder.append("|").append(clientInfo, 0, Math.min(clientInfo.length(), 256));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


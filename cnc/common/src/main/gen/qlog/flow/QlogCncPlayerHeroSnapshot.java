
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncPlayerHeroSnapshot extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncPlayerHeroSnapshot";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "HeroLevelStar", "HeroSkill", "HeroTalent"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 英雄等级与星级
     */
    private String heroLevelStar;
    /**
     * 英雄技能等级
     */
    private String heroSkill;
    /**
     * 英雄天赋
     */
    private String heroTalent;


    public QlogCncPlayerHeroSnapshot() {
        dtEventTime = "";
        heroLevelStar = "";
        heroSkill = "";
        heroTalent = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncPlayerHeroSnapshot setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncPlayerHeroSnapshot setHeroLevelStar(String heroLevelStar) {
        bitFiled0_ |= 0x2;
        this.heroLevelStar = heroLevelStar;
        return this;
    }

    public QlogCncPlayerHeroSnapshot setHeroSkill(String heroSkill) {
        bitFiled0_ |= 0x4;
        this.heroSkill = heroSkill;
        return this;
    }

    public QlogCncPlayerHeroSnapshot setHeroTalent(String heroTalent) {
        bitFiled0_ |= 0x8;
        this.heroTalent = heroTalent;
        return this;
    }


    public static QlogCncPlayerHeroSnapshot init(QlogPlayerFlowInterface flow_name) {
        QlogCncPlayerHeroSnapshot flow = new QlogCncPlayerHeroSnapshot();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(heroLevelStar, 0, Math.min(heroLevelStar.length(), 2048));
        builder.append("|").append(heroSkill, 0, Math.min(heroSkill.length(), 2048));
        builder.append("|").append(heroTalent, 0, Math.min(heroTalent.length(), 2048));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


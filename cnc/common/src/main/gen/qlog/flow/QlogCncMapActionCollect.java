
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapActionCollect extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncMapActionCollect";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "MapChestType", "MapChestID", "UniqueMapChestID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 拾取物类型
     */
    private String mapChestType;
    /**
     * 拾取物id
     */
    private long mapChestID;
    /**
     * 拾取物唯一id
     */
    private long uniqueMapChestID;


    public QlogCncMapActionCollect() {
        dtEventTime = "";
        action = "";
        mapChestType = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapActionCollect setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapActionCollect setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncMapActionCollect setMapChestType(String mapChestType) {
        bitFiled0_ |= 0x4;
        this.mapChestType = mapChestType;
        return this;
    }

    public QlogCncMapActionCollect setMapChestID(long mapChestID) {
        bitFiled0_ |= 0x8;
        this.mapChestID = mapChestID;
        return this;
    }

    public QlogCncMapActionCollect setUniqueMapChestID(long uniqueMapChestID) {
        bitFiled0_ |= 0x10;
        this.uniqueMapChestID = uniqueMapChestID;
        return this;
    }


    public static QlogCncMapActionCollect init(QlogPlayerFlowInterface flow_name) {
        QlogCncMapActionCollect flow = new QlogCncMapActionCollect();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(mapChestType, 0, Math.min(mapChestType.length(), 32));
        builder.append("|").append(mapChestID);
        builder.append("|").append(uniqueMapChestID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


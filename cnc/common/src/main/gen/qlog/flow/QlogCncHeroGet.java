
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncHeroGet extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncHeroGet";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "HeroID", "Action"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 英雄id
     */
    private String heroID;
    /**
     * 获得行为
     */
    private String action;


    public QlogCncHeroGet() {
        dtEventTime = "";
        heroID = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncHeroGet setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncHeroGet setHeroID(String heroID) {
        bitFiled0_ |= 0x2;
        this.heroID = heroID;
        return this;
    }

    public QlogCncHeroGet setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }


    public static QlogCncHeroGet init(QlogPlayerFlowInterface flow_name) {
        QlogCncHeroGet flow = new QlogCncHeroGet();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(heroID, 0, Math.min(heroID.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildPersonalScore extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuildPersonalScore";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "BeforeCount", "AfterCount", "ICount", "AddOrReduce"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 行为前积分数量
     */
    private long beforeCount;
    /**
     * 行为后积分数量
     */
    private long afterCount;
    /**
     * 变化的积分数量
     */
    private long iCount;
    /**
     * 变化方向
     */
    private int addOrReduce;


    public QlogCncGuildPersonalScore() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildPersonalScore setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildPersonalScore setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncGuildPersonalScore setBeforeCount(long beforeCount) {
        bitFiled0_ |= 0x4;
        this.beforeCount = beforeCount;
        return this;
    }

    public QlogCncGuildPersonalScore setAfterCount(long afterCount) {
        bitFiled0_ |= 0x8;
        this.afterCount = afterCount;
        return this;
    }

    public QlogCncGuildPersonalScore setICount(long iCount) {
        bitFiled0_ |= 0x10;
        this.iCount = iCount;
        return this;
    }

    public QlogCncGuildPersonalScore setAddOrReduce(int addOrReduce) {
        bitFiled0_ |= 0x20;
        this.addOrReduce = addOrReduce;
        return this;
    }


    public static QlogCncGuildPersonalScore init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuildPersonalScore flow = new QlogCncGuildPersonalScore();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(beforeCount);
        builder.append("|").append(afterCount);
        builder.append("|").append(iCount);
        builder.append("|").append(addOrReduce);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


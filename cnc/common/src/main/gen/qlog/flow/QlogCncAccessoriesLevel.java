
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncAccessoriesLevel extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncAccessoriesLevel";
    static final int currentFieldCnt = 11;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "AccessoriesID", "PartID", "SetID", "MainEntryID", "Action", "StrengthOrNot", "AfterLevel", "BeforeSubEntryConfig", "AfterSubEntryConfig"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 配件ID
     */
    private long accessoriesID;
    /**
     * 所属部位ID
     */
    private int partID;
    /**
     * 所属套装ID
     */
    private int setID;
    /**
     * 主词条ID
     */
    private int mainEntryID;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 本次强化是否为指定强化，0：不是，1：是
     */
    private int strengthOrNot;
    /**
     * 行为后配件等级
     */
    private int afterLevel;
    /**
     * 行为前副词条详情
     */
    private String beforeSubEntryConfig;
    /**
     * 行为后副词条详情
     */
    private String afterSubEntryConfig;


    public QlogCncAccessoriesLevel() {
        dtEventTime = "";
        action = "";
        beforeSubEntryConfig = "";
        afterSubEntryConfig = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncAccessoriesLevel setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncAccessoriesLevel setAccessoriesID(long accessoriesID) {
        bitFiled0_ |= 0x2;
        this.accessoriesID = accessoriesID;
        return this;
    }

    public QlogCncAccessoriesLevel setPartID(int partID) {
        bitFiled0_ |= 0x4;
        this.partID = partID;
        return this;
    }

    public QlogCncAccessoriesLevel setSetID(int setID) {
        bitFiled0_ |= 0x8;
        this.setID = setID;
        return this;
    }

    public QlogCncAccessoriesLevel setMainEntryID(int mainEntryID) {
        bitFiled0_ |= 0x10;
        this.mainEntryID = mainEntryID;
        return this;
    }

    public QlogCncAccessoriesLevel setAction(String action) {
        bitFiled0_ |= 0x20;
        this.action = action;
        return this;
    }

    public QlogCncAccessoriesLevel setStrengthOrNot(int strengthOrNot) {
        bitFiled0_ |= 0x40;
        this.strengthOrNot = strengthOrNot;
        return this;
    }

    public QlogCncAccessoriesLevel setAfterLevel(int afterLevel) {
        bitFiled0_ |= 0x80;
        this.afterLevel = afterLevel;
        return this;
    }

    public QlogCncAccessoriesLevel setBeforeSubEntryConfig(String beforeSubEntryConfig) {
        bitFiled0_ |= 0x100;
        this.beforeSubEntryConfig = beforeSubEntryConfig;
        return this;
    }

    public QlogCncAccessoriesLevel setAfterSubEntryConfig(String afterSubEntryConfig) {
        bitFiled0_ |= 0x200;
        this.afterSubEntryConfig = afterSubEntryConfig;
        return this;
    }


    public static QlogCncAccessoriesLevel init(QlogPlayerFlowInterface flow_name) {
        QlogCncAccessoriesLevel flow = new QlogCncAccessoriesLevel();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x200) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(accessoriesID);
        builder.append("|").append(partID);
        builder.append("|").append(setID);
        builder.append("|").append(mainEntryID);
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(strengthOrNot);
        builder.append("|").append(afterLevel);
        builder.append("|").append(beforeSubEntryConfig, 0, Math.min(beforeSubEntryConfig.length(), 4096));
        builder.append("|").append(afterSubEntryConfig, 0, Math.min(afterSubEntryConfig.length(), 4096));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


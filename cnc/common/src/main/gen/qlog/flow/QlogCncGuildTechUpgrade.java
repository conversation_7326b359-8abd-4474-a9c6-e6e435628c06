
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildTechUpgrade extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildTechUpgrade";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "OptionRoleId", "Action", "GuildTechID", "GuildSubTechID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 操作者id
     */
    private String optionRoleId;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 联盟科技id
     */
    private int guildTechID;
    /**
     * 联盟科技子id
     */
    private int guildSubTechID;


    public QlogCncGuildTechUpgrade() {
        dtEventTime = "";
        optionRoleId = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildTechUpgrade setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildTechUpgrade setOptionRoleId(String optionRoleId) {
        bitFiled0_ |= 0x2;
        this.optionRoleId = optionRoleId;
        return this;
    }

    public QlogCncGuildTechUpgrade setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncGuildTechUpgrade setGuildTechID(int guildTechID) {
        bitFiled0_ |= 0x8;
        this.guildTechID = guildTechID;
        return this;
    }

    public QlogCncGuildTechUpgrade setGuildSubTechID(int guildSubTechID) {
        bitFiled0_ |= 0x10;
        this.guildSubTechID = guildSubTechID;
        return this;
    }


    public static QlogCncGuildTechUpgrade init(QlogClanFlowInterface flow_name) {
        QlogCncGuildTechUpgrade flow = new QlogCncGuildTechUpgrade();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(optionRoleId, 0, Math.min(optionRoleId.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(guildTechID);
        builder.append("|").append(guildSubTechID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


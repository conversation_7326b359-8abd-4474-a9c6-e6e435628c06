
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapActionFog extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncMapActionFog";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "AfterExplorePer", "ExploreBuilding", "ValleryCount", "CaveCount"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 探索进度
     */
    private String afterExplorePer;
    /**
     * 探索过的建筑
     */
    private String exploreBuilding;
    /**
     * 探索到村庄的数量
     */
    private int valleryCount;
    /**
     * 探索到山洞的数量
     */
    private int caveCount;


    public QlogCncMapActionFog() {
        dtEventTime = "";
        afterExplorePer = "";
        exploreBuilding = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapActionFog setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapActionFog setAfterExplorePer(String afterExplorePer) {
        bitFiled0_ |= 0x2;
        this.afterExplorePer = afterExplorePer;
        return this;
    }

    public QlogCncMapActionFog setExploreBuilding(String exploreBuilding) {
        bitFiled0_ |= 0x4;
        this.exploreBuilding = exploreBuilding;
        return this;
    }

    public QlogCncMapActionFog setValleryCount(int valleryCount) {
        bitFiled0_ |= 0x8;
        this.valleryCount = valleryCount;
        return this;
    }

    public QlogCncMapActionFog setCaveCount(int caveCount) {
        bitFiled0_ |= 0x10;
        this.caveCount = caveCount;
        return this;
    }


    public static QlogCncMapActionFog init(QlogPlayerFlowInterface flow_name) {
        QlogCncMapActionFog flow = new QlogCncMapActionFog();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(afterExplorePer, 0, Math.min(afterExplorePer.length(), 32));
        builder.append("|").append(exploreBuilding, 0, Math.min(exploreBuilding.length(), 2000));
        builder.append("|").append(valleryCount);
        builder.append("|").append(caveCount);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncStrategyLevel extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncStrategyLevel";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ExpCount", "AfterStrategicLevel"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 获得的经验数量
     */
    private long expCount;
    /**
     * 行为后战略系统等级
     */
    private int afterStrategicLevel;


    public QlogCncStrategyLevel() {
        dtEventTime = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncStrategyLevel setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncStrategyLevel setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncStrategyLevel setExpCount(long expCount) {
        bitFiled0_ |= 0x4;
        this.expCount = expCount;
        return this;
    }

    public QlogCncStrategyLevel setAfterStrategicLevel(int afterStrategicLevel) {
        bitFiled0_ |= 0x8;
        this.afterStrategicLevel = afterStrategicLevel;
        return this;
    }


    public static QlogCncStrategyLevel init(QlogPlayerFlowInterface flow_name) {
        QlogCncStrategyLevel flow = new QlogCncStrategyLevel();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(expCount);
        builder.append("|").append(afterStrategicLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


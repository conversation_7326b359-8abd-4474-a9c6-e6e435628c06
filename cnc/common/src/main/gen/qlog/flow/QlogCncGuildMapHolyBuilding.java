
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildMapHolyBuilding extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildMapHolyBuilding";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BuildingId", "State", "HolyOwnerId", "Action", "OptionRoleID"};
    /**
     * 行为时间
     */
    private String dtEventTime;
    /**
     * 建筑片id
     */
    private int buildingId;
    /**
     * 行为发生前城市所处状态
     */
    private int state;
    /**
     * 行为发生前城市占据或占领者id
     */
    private String holyOwnerId;
    /**
     * 行为
     */
    private String action;
    /**
     * 行为主体id
     */
    private String optionRoleID;


    public QlogCncGuildMapHolyBuilding() {
        dtEventTime = "";
        holyOwnerId = "";
        action = "";
        optionRoleID = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildMapHolyBuilding setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildMapHolyBuilding setBuildingId(int buildingId) {
        bitFiled0_ |= 0x2;
        this.buildingId = buildingId;
        return this;
    }

    public QlogCncGuildMapHolyBuilding setState(int state) {
        bitFiled0_ |= 0x4;
        this.state = state;
        return this;
    }

    public QlogCncGuildMapHolyBuilding setHolyOwnerId(String holyOwnerId) {
        bitFiled0_ |= 0x8;
        this.holyOwnerId = holyOwnerId;
        return this;
    }

    public QlogCncGuildMapHolyBuilding setAction(String action) {
        bitFiled0_ |= 0x10;
        this.action = action;
        return this;
    }

    public QlogCncGuildMapHolyBuilding setOptionRoleID(String optionRoleID) {
        bitFiled0_ |= 0x20;
        this.optionRoleID = optionRoleID;
        return this;
    }


    public static QlogCncGuildMapHolyBuilding init(QlogClanFlowInterface flow_name) {
        QlogCncGuildMapHolyBuilding flow = new QlogCncGuildMapHolyBuilding();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(buildingId);
        builder.append("|").append(state);
        builder.append("|").append(holyOwnerId, 0, Math.min(holyOwnerId.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(optionRoleID, 0, Math.min(optionRoleID.length(), 512));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


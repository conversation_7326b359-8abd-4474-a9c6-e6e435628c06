
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapActionCity extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncMapActionCity";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "SubAction", "BeforeCoordinate", "AfterCoordinate", "BeforeContinent", "AfterContinent"};
    /**
     * 事件时间
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 次级行为类型
     */
    private String subAction;
    /**
     * 之前坐标
     */
    private String beforeCoordinate;
    /**
     * 之后坐标
     */
    private String afterCoordinate;
    /**
     * 之前所在州id
     */
    private String beforeContinent;
    /**
     * 之后所在州id
     */
    private String afterContinent;


    public QlogCncMapActionCity() {
        dtEventTime = "";
        action = "";
        subAction = "";
        beforeCoordinate = "";
        afterCoordinate = "";
        beforeContinent = "";
        afterContinent = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapActionCity setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapActionCity setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncMapActionCity setSubAction(String subAction) {
        bitFiled0_ |= 0x4;
        this.subAction = subAction;
        return this;
    }

    public QlogCncMapActionCity setBeforeCoordinate(String beforeCoordinate) {
        bitFiled0_ |= 0x8;
        this.beforeCoordinate = beforeCoordinate;
        return this;
    }

    public QlogCncMapActionCity setAfterCoordinate(String afterCoordinate) {
        bitFiled0_ |= 0x10;
        this.afterCoordinate = afterCoordinate;
        return this;
    }

    public QlogCncMapActionCity setBeforeContinent(String beforeContinent) {
        bitFiled0_ |= 0x20;
        this.beforeContinent = beforeContinent;
        return this;
    }

    public QlogCncMapActionCity setAfterContinent(String afterContinent) {
        bitFiled0_ |= 0x40;
        this.afterContinent = afterContinent;
        return this;
    }


    public static QlogCncMapActionCity init(QlogPlayerFlowInterface flow_name) {
        QlogCncMapActionCity flow = new QlogCncMapActionCity();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(subAction, 0, Math.min(subAction.length(), 32));
        builder.append("|").append(beforeCoordinate, 0, Math.min(beforeCoordinate.length(), 32));
        builder.append("|").append(afterCoordinate, 0, Math.min(afterCoordinate.length(), 32));
        builder.append("|").append(beforeContinent, 0, Math.min(beforeContinent.length(), 32));
        builder.append("|").append(afterContinent, 0, Math.min(afterContinent.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncArmyFormation extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncArmyFormation";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "ArmyId", "Action", "ArmyConfig", "MajorHeroConfig", "SubHeroConfig", "MechaConfig"};
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 行军唯一id
     */
    private String armyId;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 部队详情
     */
    private String armyConfig;
    /**
     * 主英雄详情
     */
    private String majorHeroConfig;
    /**
     * 副英雄详情
     */
    private String subHeroConfig;
    /**
     * 出征机甲详情
     */
    private String mechaConfig;


    public QlogCncArmyFormation() {
        dtEventTime = "";
        armyId = "";
        action = "";
        armyConfig = "";
        majorHeroConfig = "";
        subHeroConfig = "";
        mechaConfig = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncArmyFormation setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncArmyFormation setArmyId(String armyId) {
        bitFiled0_ |= 0x2;
        this.armyId = armyId;
        return this;
    }

    public QlogCncArmyFormation setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }

    public QlogCncArmyFormation setArmyConfig(String armyConfig) {
        bitFiled0_ |= 0x8;
        this.armyConfig = armyConfig;
        return this;
    }

    public QlogCncArmyFormation setMajorHeroConfig(String majorHeroConfig) {
        bitFiled0_ |= 0x10;
        this.majorHeroConfig = majorHeroConfig;
        return this;
    }

    public QlogCncArmyFormation setSubHeroConfig(String subHeroConfig) {
        bitFiled0_ |= 0x20;
        this.subHeroConfig = subHeroConfig;
        return this;
    }

    public QlogCncArmyFormation setMechaConfig(String mechaConfig) {
        bitFiled0_ |= 0x40;
        this.mechaConfig = mechaConfig;
        return this;
    }


    public static QlogCncArmyFormation init(QlogPlayerFlowInterface flow_name) {
        QlogCncArmyFormation flow = new QlogCncArmyFormation();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(armyId, 0, Math.min(armyId.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(armyConfig, 0, Math.min(armyConfig.length(), 2560));
        builder.append("|").append(majorHeroConfig, 0, Math.min(majorHeroConfig.length(), 128));
        builder.append("|").append(subHeroConfig, 0, Math.min(subHeroConfig.length(), 128));
        builder.append("|").append(mechaConfig, 0, Math.min(mechaConfig.length(), 128));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


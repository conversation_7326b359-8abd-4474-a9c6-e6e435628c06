
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncNotification extends AbstractServerQlogFlow {
    static final String META_NAME = "CncNotification";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Title", "Content", "NotificationTargetList", "NotificationTime", "NotificationResult"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 推送类型
     */
    private String title;
    /**
     * 推送条目
     */
    private String content;
    /**
     * 推送玩家list
     */
    private String notificationTargetList;
    /**
     * 推送时间
     */
    private String notificationTime;
    /**
     * 推送结果
     */
    private String notificationResult;


    public QlogCncNotification() {
        dtEventTime = "";
        title = "";
        content = "";
        notificationTargetList = "";
        notificationTime = "";
        notificationResult = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncNotification setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncNotification setTitle(String title) {
        bitFiled0_ |= 0x2;
        this.title = title;
        return this;
    }

    public QlogCncNotification setContent(String content) {
        bitFiled0_ |= 0x4;
        this.content = content;
        return this;
    }

    public QlogCncNotification setNotificationTargetList(String notificationTargetList) {
        bitFiled0_ |= 0x8;
        this.notificationTargetList = notificationTargetList;
        return this;
    }

    public QlogCncNotification setNotificationTime(String notificationTime) {
        bitFiled0_ |= 0x10;
        this.notificationTime = notificationTime;
        return this;
    }

    public QlogCncNotification setNotificationResult(String notificationResult) {
        bitFiled0_ |= 0x20;
        this.notificationResult = notificationResult;
        return this;
    }


    public static QlogCncNotification init(QlogServerFlowInterface flow_name) {
        QlogCncNotification flow = new QlogCncNotification();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(title, 0, Math.min(title.length(), 64));
        builder.append("|").append(content, 0, Math.min(content.length(), 64));
        builder.append("|").append(notificationTargetList, 0, Math.min(notificationTargetList.length(), 2048));
        builder.append("|").append(notificationTime, 0, Math.min(notificationTime.length(), 64));
        builder.append("|").append(notificationResult, 0, Math.min(notificationResult.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


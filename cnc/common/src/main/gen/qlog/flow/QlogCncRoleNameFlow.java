
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncRoleNameFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncRoleNameFlow";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "NameBefore", "NameAfter"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 更名前名字
     */
    private String nameBefore;
    /**
     * 更名后名字
     */
    private String nameAfter;


    public QlogCncRoleNameFlow() {
        dtEventTime = "";
        nameBefore = "";
        nameAfter = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncRoleNameFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncRoleNameFlow setNameBefore(String nameBefore) {
        bitFiled0_ |= 0x2;
        this.nameBefore = nameBefore;
        return this;
    }

    public QlogCncRoleNameFlow setNameAfter(String nameAfter) {
        bitFiled0_ |= 0x4;
        this.nameAfter = nameAfter;
        return this;
    }


    public static QlogCncRoleNameFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncRoleNameFlow flow = new QlogCncRoleNameFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(nameBefore, 0, Math.min(nameBefore.length(), 32));
        builder.append("|").append(nameAfter, 0, Math.min(nameAfter.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


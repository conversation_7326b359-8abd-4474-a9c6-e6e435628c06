
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogPlayerRegister extends AbstractPlayerQlogFlow {
    static final String META_NAME = "PlayerRegister";
    static final int currentFieldCnt = 12;
    final boolean needFullHead = true;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "SystemSoftware", "SystemHardware", "Memory", "GlVersion", "DeviceId", "Country", "BornPartId", "AdjustId", "LoginChannel", "xwid"};
    /**
     * 游戏事件的时间
     */
    private String dtEventTime;
    /**
     * 移动终端操作系统版本
     */
    private String systemSoftware;
    /**
     * 移动终端机型
     */
    private String systemHardware;
    /**
     * 内存信息单位M
     */
    private int memory;
    /**
     * opengl版本信息
     */
    private String glVersion;
    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 注册时所在国家
     */
    private String country;
    /**
     * 创角时落堡所处的地图片id
     */
    private int bornPartId;
    /**
     * adjustId
     */
    private String adjustId;
    /**
     * 登录渠道
     */
    private int loginChannel;
    /**
     * 海外设备ID
     */
    private String xwid;


    public QlogPlayerRegister() {
        dtEventTime = "";
        systemSoftware = "";
        systemHardware = "";
        glVersion = "";
        deviceId = "";
        country = "";
        adjustId = "";
        xwid = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogPlayerRegister setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogPlayerRegister setSystemSoftware(String systemSoftware) {
        bitFiled0_ |= 0x2;
        this.systemSoftware = systemSoftware;
        return this;
    }

    public QlogPlayerRegister setSystemHardware(String systemHardware) {
        bitFiled0_ |= 0x4;
        this.systemHardware = systemHardware;
        return this;
    }

    public QlogPlayerRegister setMemory(int memory) {
        bitFiled0_ |= 0x8;
        this.memory = memory;
        return this;
    }

    public QlogPlayerRegister setGlVersion(String glVersion) {
        bitFiled0_ |= 0x10;
        this.glVersion = glVersion;
        return this;
    }

    public QlogPlayerRegister setDeviceId(String deviceId) {
        bitFiled0_ |= 0x20;
        this.deviceId = deviceId;
        return this;
    }

    public QlogPlayerRegister setCountry(String country) {
        bitFiled0_ |= 0x40;
        this.country = country;
        return this;
    }

    public QlogPlayerRegister setBornPartId(int bornPartId) {
        bitFiled0_ |= 0x80;
        this.bornPartId = bornPartId;
        return this;
    }

    public QlogPlayerRegister setAdjustId(String adjustId) {
        bitFiled0_ |= 0x100;
        this.adjustId = adjustId;
        return this;
    }

    public QlogPlayerRegister setLoginChannel(int loginChannel) {
        bitFiled0_ |= 0x200;
        this.loginChannel = loginChannel;
        return this;
    }

    public QlogPlayerRegister setXwid(String xwid) {
        bitFiled0_ |= 0x400;
        this.xwid = xwid;
        return this;
    }


    public static QlogPlayerRegister init(QlogPlayerFlowInterface flow_name) {
        QlogPlayerRegister flow = new QlogPlayerRegister();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x400) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(systemSoftware, 0, Math.min(systemSoftware.length(), 32));
        builder.append("|").append(systemHardware, 0, Math.min(systemHardware.length(), 32));
        builder.append("|").append(memory);
        builder.append("|").append(glVersion, 0, Math.min(glVersion.length(), 32));
        builder.append("|").append(deviceId, 0, Math.min(deviceId.length(), 32));
        builder.append("|").append(country, 0, Math.min(country.length(), 64));
        builder.append("|").append(bornPartId);
        builder.append("|").append(adjustId, 0, Math.min(adjustId.length(), 64));
        builder.append("|").append(loginChannel);
        builder.append("|").append(xwid, 0, Math.min(xwid.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


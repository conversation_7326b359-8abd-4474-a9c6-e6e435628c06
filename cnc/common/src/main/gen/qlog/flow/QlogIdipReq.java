
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogIdipReq extends AbstractPlayerQlogFlow {
    static final String META_NAME = "IdipReq";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "iGoodsId", "iCount", "IdipCommand", "SeqId", "ChannId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 道具ID
     */
    private String iGoodsId;
    /**
     * 改变的数量
     */
    private int iCount;
    /**
     * IDIP命令字
     */
    private String idipCommand;
    /**
     * 流水号
     */
    private String seqId;
    /**
     * 渠道号
     */
    private String channId;


    public QlogIdipReq() {
        dtEventTime = "";
        iGoodsId = "";
        idipCommand = "";
        seqId = "";
        channId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogIdipReq setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogIdipReq setIGoodsId(String iGoodsId) {
        bitFiled0_ |= 0x2;
        this.iGoodsId = iGoodsId;
        return this;
    }

    public QlogIdipReq setICount(int iCount) {
        bitFiled0_ |= 0x4;
        this.iCount = iCount;
        return this;
    }

    public QlogIdipReq setIdipCommand(String idipCommand) {
        bitFiled0_ |= 0x8;
        this.idipCommand = idipCommand;
        return this;
    }

    public QlogIdipReq setSeqId(String seqId) {
        bitFiled0_ |= 0x10;
        this.seqId = seqId;
        return this;
    }

    public QlogIdipReq setChannId(String channId) {
        bitFiled0_ |= 0x20;
        this.channId = channId;
        return this;
    }


    public static QlogIdipReq init(QlogPlayerFlowInterface flow_name) {
        QlogIdipReq flow = new QlogIdipReq();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iGoodsId, 0, Math.min(iGoodsId.length(), 32));
        builder.append("|").append(iCount);
        builder.append("|").append(idipCommand, 0, Math.min(idipCommand.length(), 64));
        builder.append("|").append(seqId, 0, Math.min(seqId.length(), 64));
        builder.append("|").append(channId, 0, Math.min(channId.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


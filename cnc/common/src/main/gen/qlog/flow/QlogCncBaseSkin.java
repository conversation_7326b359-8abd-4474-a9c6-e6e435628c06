
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncBaseSkin extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncBaseSkin";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"Action", "DtEventTime", "SkinType", "SkinId"};
    /**
     * 行为类型
     */
    private String action;
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 皮肤类型
     */
    private int skinType;
    /**
     * 皮肤ID
     */
    private int skinId;


    public QlogCncBaseSkin() {
        action = "";
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncBaseSkin setAction(String action) {
        bitFiled0_ |= 0x1;
        this.action = action;
        return this;
    }

    public QlogCncBaseSkin setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x2;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncBaseSkin setSkinType(int skinType) {
        bitFiled0_ |= 0x4;
        this.skinType = skinType;
        return this;
    }

    public QlogCncBaseSkin setSkinId(int skinId) {
        bitFiled0_ |= 0x8;
        this.skinId = skinId;
        return this;
    }


    public static QlogCncBaseSkin init(QlogPlayerFlowInterface flow_name) {
        QlogCncBaseSkin flow = new QlogCncBaseSkin();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(skinType);
        builder.append("|").append(skinId);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


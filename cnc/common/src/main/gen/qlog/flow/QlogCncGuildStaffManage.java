
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildStaffManage extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncGuildStaffManage";
    static final int currentFieldCnt = 10;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "IActType", "Reason", "OptionRoleId", "OptionRoleGrade", "GuildPowerPersonCount", "AfterGuildGrade", "ObjectGuildId", "GuildPowerPersonCountOnline"};
    /**
     * 行为发生时间
     */
    private String dtEventTime;
    /**
     * 联盟行为
     */
    private String iActType;
    /**
     * 原因
     */
    private String reason;
    /**
     * 操作者id
     */
    private String optionRoleId;
    /**
     * 操作者职位
     */
    private int optionRoleGrade;
    /**
     * 行为发生时联盟有审批权限的人数，仅IActType为apply_guild与join_guild值有意义
     */
    private int guildPowerPersonCount;
    /**
     * 行为后联盟职位
     */
    private int afterGuildGrade;
    /**
     * 行为对应的联盟id
     */
    private String objectGuildId;
    /**
     * 行为发生时在线的联盟有审批权限的人数，仅IActType为apply_guild与join_guild值有意义
     */
    private int guildPowerPersonCountOnline;


    public QlogCncGuildStaffManage() {
        dtEventTime = "";
        iActType = "";
        reason = "";
        optionRoleId = "";
        objectGuildId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildStaffManage setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildStaffManage setIActType(String iActType) {
        bitFiled0_ |= 0x2;
        this.iActType = iActType;
        return this;
    }

    public QlogCncGuildStaffManage setReason(String reason) {
        bitFiled0_ |= 0x4;
        this.reason = reason;
        return this;
    }

    public QlogCncGuildStaffManage setOptionRoleId(String optionRoleId) {
        bitFiled0_ |= 0x8;
        this.optionRoleId = optionRoleId;
        return this;
    }

    public QlogCncGuildStaffManage setOptionRoleGrade(int optionRoleGrade) {
        bitFiled0_ |= 0x10;
        this.optionRoleGrade = optionRoleGrade;
        return this;
    }

    public QlogCncGuildStaffManage setGuildPowerPersonCount(int guildPowerPersonCount) {
        bitFiled0_ |= 0x20;
        this.guildPowerPersonCount = guildPowerPersonCount;
        return this;
    }

    public QlogCncGuildStaffManage setAfterGuildGrade(int afterGuildGrade) {
        bitFiled0_ |= 0x40;
        this.afterGuildGrade = afterGuildGrade;
        return this;
    }

    public QlogCncGuildStaffManage setObjectGuildId(String objectGuildId) {
        bitFiled0_ |= 0x80;
        this.objectGuildId = objectGuildId;
        return this;
    }

    public QlogCncGuildStaffManage setGuildPowerPersonCountOnline(int guildPowerPersonCountOnline) {
        bitFiled0_ |= 0x100;
        this.guildPowerPersonCountOnline = guildPowerPersonCountOnline;
        return this;
    }


    public static QlogCncGuildStaffManage init(QlogPlayerFlowInterface flow_name) {
        QlogCncGuildStaffManage flow = new QlogCncGuildStaffManage();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1ff);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x100) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iActType, 0, Math.min(iActType.length(), 32));
        builder.append("|").append(reason, 0, Math.min(reason.length(), 32));
        builder.append("|").append(optionRoleId, 0, Math.min(optionRoleId.length(), 32));
        builder.append("|").append(optionRoleGrade);
        builder.append("|").append(guildPowerPersonCount);
        builder.append("|").append(afterGuildGrade);
        builder.append("|").append(objectGuildId, 0, Math.min(objectGuildId.length(), 32));
        builder.append("|").append(guildPowerPersonCountOnline);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


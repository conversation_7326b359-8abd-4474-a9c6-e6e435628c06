
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncKingdomActivate extends AbstractServerQlogFlow {
    static final String META_NAME = "CncKingdomActivate";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "ConfigId", "TargetRoleId", "OperationRoleId"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 配置的增益或技能id
     */
    private int configId;
    /**
     * 目标角色id，如果没有目标（或全服目标）则填0
     */
    private String targetRoleId;
    /**
     * 操作者id
     */
    private String operationRoleId;


    public QlogCncKingdomActivate() {
        dtEventTime = "";
        action = "";
        targetRoleId = "";
        operationRoleId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncKingdomActivate setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncKingdomActivate setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncKingdomActivate setConfigId(int configId) {
        bitFiled0_ |= 0x4;
        this.configId = configId;
        return this;
    }

    public QlogCncKingdomActivate setTargetRoleId(String targetRoleId) {
        bitFiled0_ |= 0x8;
        this.targetRoleId = targetRoleId;
        return this;
    }

    public QlogCncKingdomActivate setOperationRoleId(String operationRoleId) {
        bitFiled0_ |= 0x10;
        this.operationRoleId = operationRoleId;
        return this;
    }


    public static QlogCncKingdomActivate init(QlogServerFlowInterface flow_name) {
        QlogCncKingdomActivate flow = new QlogCncKingdomActivate();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(configId);
        builder.append("|").append(targetRoleId, 0, Math.min(targetRoleId.length(), 64));
        builder.append("|").append(operationRoleId, 0, Math.min(operationRoleId.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncSkynetLevel extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncSkynetLevel";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "iCount", "LevelAfter", "ExpAfter"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 获取的经验点数
     */
    private int iCount;
    /**
     * 获得经验后的等级
     */
    private int levelAfter;
    /**
     * 获得经验后的经验
     */
    private int expAfter;


    public QlogCncSkynetLevel() {
        dtEventTime = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncSkynetLevel setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncSkynetLevel setICount(int iCount) {
        bitFiled0_ |= 0x2;
        this.iCount = iCount;
        return this;
    }

    public QlogCncSkynetLevel setLevelAfter(int levelAfter) {
        bitFiled0_ |= 0x4;
        this.levelAfter = levelAfter;
        return this;
    }

    public QlogCncSkynetLevel setExpAfter(int expAfter) {
        bitFiled0_ |= 0x8;
        this.expAfter = expAfter;
        return this;
    }


    public static QlogCncSkynetLevel init(QlogPlayerFlowInterface flow_name) {
        QlogCncSkynetLevel flow = new QlogCncSkynetLevel();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(iCount);
        builder.append("|").append(levelAfter);
        builder.append("|").append(expAfter);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


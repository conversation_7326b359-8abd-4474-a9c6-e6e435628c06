
package qlog.flow;

import com.yorha.common.qlog.QlogClanFlowInterface;
import com.yorha.common.qlog.AbstractClanQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncGuildExpansion extends AbstractClanQlogFlow {
    static final String META_NAME = "CncGuildExpansion";
    static final int currentFieldCnt = 7;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "BuildingId", "BuildingCoordinate", "Action", "OptionRoleId", "GuildBuildingID"};
    /**
     * 行为时间
     */
    private String dtEventTime;
    /**
     * 建筑片id
     */
    private int buildingId;
    /**
     * 建筑坐标
     */
    private String buildingCoordinate;
    /**
     * 行为
     */
    private String action;
    /**
     * 操作者id
     */
    private String optionRoleId;
    /**
     * 联盟建筑ID
     */
    private long guildBuildingID;


    public QlogCncGuildExpansion() {
        dtEventTime = "";
        buildingCoordinate = "";
        action = "";
        optionRoleId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncGuildExpansion setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncGuildExpansion setBuildingId(int buildingId) {
        bitFiled0_ |= 0x2;
        this.buildingId = buildingId;
        return this;
    }

    public QlogCncGuildExpansion setBuildingCoordinate(String buildingCoordinate) {
        bitFiled0_ |= 0x4;
        this.buildingCoordinate = buildingCoordinate;
        return this;
    }

    public QlogCncGuildExpansion setAction(String action) {
        bitFiled0_ |= 0x8;
        this.action = action;
        return this;
    }

    public QlogCncGuildExpansion setOptionRoleId(String optionRoleId) {
        bitFiled0_ |= 0x10;
        this.optionRoleId = optionRoleId;
        return this;
    }

    public QlogCncGuildExpansion setGuildBuildingID(long guildBuildingID) {
        bitFiled0_ |= 0x20;
        this.guildBuildingID = guildBuildingID;
        return this;
    }


    public static QlogCncGuildExpansion init(QlogClanFlowInterface flow_name) {
        QlogCncGuildExpansion flow = new QlogCncGuildExpansion();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x3f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x20) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(buildingId);
        builder.append("|").append(buildingCoordinate, 0, Math.min(buildingCoordinate.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
        builder.append("|").append(optionRoleId, 0, Math.min(optionRoleId.length(), 32));
        builder.append("|").append(guildBuildingID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


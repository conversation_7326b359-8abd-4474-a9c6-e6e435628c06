
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncMapPublicChest extends AbstractServerQlogFlow {
    static final String META_NAME = "CncMapPublicChest";
    static final int currentFieldCnt = 8;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "RoleId", "BeforePickTimes", "AfterPickTimes", "MapChestID", "UniqueMapChestID"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 角色id
     */
    private String roleId;
    /**
     * 行为发生前剩余可拾取次数
     */
    private int beforePickTimes;
    /**
     * 行为发生后剩余可拾取次数
     */
    private int afterPickTimes;
    /**
     * 拾取物id
     */
    private long mapChestID;
    /**
     * 拾取物唯一id
     */
    private long uniqueMapChestID;


    public QlogCncMapPublicChest() {
        dtEventTime = "";
        action = "";
        roleId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncMapPublicChest setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncMapPublicChest setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncMapPublicChest setRoleId(String roleId) {
        bitFiled0_ |= 0x4;
        this.roleId = roleId;
        return this;
    }

    public QlogCncMapPublicChest setBeforePickTimes(int beforePickTimes) {
        bitFiled0_ |= 0x8;
        this.beforePickTimes = beforePickTimes;
        return this;
    }

    public QlogCncMapPublicChest setAfterPickTimes(int afterPickTimes) {
        bitFiled0_ |= 0x10;
        this.afterPickTimes = afterPickTimes;
        return this;
    }

    public QlogCncMapPublicChest setMapChestID(long mapChestID) {
        bitFiled0_ |= 0x20;
        this.mapChestID = mapChestID;
        return this;
    }

    public QlogCncMapPublicChest setUniqueMapChestID(long uniqueMapChestID) {
        bitFiled0_ |= 0x40;
        this.uniqueMapChestID = uniqueMapChestID;
        return this;
    }


    public static QlogCncMapPublicChest init(QlogServerFlowInterface flow_name) {
        QlogCncMapPublicChest flow = new QlogCncMapPublicChest();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x40) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(roleId, 0, Math.min(roleId.length(), 32));
        builder.append("|").append(beforePickTimes);
        builder.append("|").append(afterPickTimes);
        builder.append("|").append(mapChestID);
        builder.append("|").append(uniqueMapChestID);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


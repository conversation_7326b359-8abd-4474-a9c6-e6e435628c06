
package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncHeroSkill extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncHeroSkill";
    static final int currentFieldCnt = 6;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "Action", "HeroID", "HeroSkillGroupId", "AfterSkillLevel"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 行为类型
     */
    private String action;
    /**
     * 英雄id
     */
    private String heroID;
    /**
     * 技能组id
     */
    private String heroSkillGroupId;
    /**
     * 行为后的技能等级
     */
    private int afterSkillLevel;


    public QlogCncHeroSkill() {
        dtEventTime = "";
        action = "";
        heroID = "";
        heroSkillGroupId = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncHeroSkill setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncHeroSkill setAction(String action) {
        bitFiled0_ |= 0x2;
        this.action = action;
        return this;
    }

    public QlogCncHeroSkill setHeroID(String heroID) {
        bitFiled0_ |= 0x4;
        this.heroID = heroID;
        return this;
    }

    public QlogCncHeroSkill setHeroSkillGroupId(String heroSkillGroupId) {
        bitFiled0_ |= 0x8;
        this.heroSkillGroupId = heroSkillGroupId;
        return this;
    }

    public QlogCncHeroSkill setAfterSkillLevel(int afterSkillLevel) {
        bitFiled0_ |= 0x10;
        this.afterSkillLevel = afterSkillLevel;
        return this;
    }


    public static QlogCncHeroSkill init(QlogPlayerFlowInterface flow_name) {
        QlogCncHeroSkill flow = new QlogCncHeroSkill();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x1f);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x10) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
        builder.append("|").append(heroID, 0, Math.min(heroID.length(), 32));
        builder.append("|").append(heroSkillGroupId, 0, Math.min(heroSkillGroupId.length(), 32));
        builder.append("|").append(afterSkillLevel);
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


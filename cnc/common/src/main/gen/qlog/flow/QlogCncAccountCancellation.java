
package qlog.flow;

import com.yorha.common.qlog.QlogServerFlowInterface;
import com.yorha.common.qlog.AbstractServerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncAccountCancellation extends AbstractServerQlogFlow {
    static final String META_NAME = "CncAccountCancellation";
    static final int currentFieldCnt = 4;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "vOpenId", "Action"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 账号id
     */
    private String vOpenId;
    /**
     * 行为类型
     */
    private String action;


    public QlogCncAccountCancellation() {
        dtEventTime = "";
        vOpenId = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncAccountCancellation setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncAccountCancellation setVOpenId(String vOpenId) {
        bitFiled0_ |= 0x2;
        this.vOpenId = vOpenId;
        return this;
    }

    public QlogCncAccountCancellation setAction(String action) {
        bitFiled0_ |= 0x4;
        this.action = action;
        return this;
    }


    public static QlogCncAccountCancellation init(QlogServerFlowInterface flow_name) {
        QlogCncAccountCancellation flow = new QlogCncAccountCancellation();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0x7);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x4) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(vOpenId, 0, Math.min(vOpenId.length(), 64));
        builder.append("|").append(action, 0, Math.min(action.length(), 64));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}



package qlog.flow;

import com.yorha.common.qlog.QlogPlayerFlowInterface;
import com.yorha.common.qlog.AbstractPlayerQlogFlow;
import java.util.ArrayList;
import java.util.List;

/**
 * generated by parse_templ.py
 * <AUTHOR>
 */
public class QlogCncTaskFlow extends AbstractPlayerQlogFlow {
    static final String META_NAME = "CncTaskFlow";
    static final int currentFieldCnt = 5;
    final boolean needFullHead = false;
    private long bitFiled0_ = 0;
    private static final String[] FIELD_NAMES = {"DtEventTime", "TaskClass", "iTaskID", "Action"};
    /**
     * (必填) 格式 YYYY-MM-DD HH:MM:SS
     */
    private String dtEventTime;
    /**
     * 任务模块类型
     */
    private String taskClass;
    /**
     * 模块任务id
     */
    private int iTaskID;
    /**
     * 任务操作类型
     */
    private String action;


    public QlogCncTaskFlow() {
        dtEventTime = "";
        taskClass = "";
        action = "";
    }

    @Override
    protected boolean needFullHead() {
        return needFullHead;
    }


    public QlogCncTaskFlow setDtEventTime(String dtEventTime) {
        bitFiled0_ |= 0x1;
        this.dtEventTime = dtEventTime;
        return this;
    }

    public QlogCncTaskFlow setTaskClass(String taskClass) {
        bitFiled0_ |= 0x2;
        this.taskClass = taskClass;
        return this;
    }

    public QlogCncTaskFlow setITaskID(int iTaskID) {
        bitFiled0_ |= 0x4;
        this.iTaskID = iTaskID;
        return this;
    }

    public QlogCncTaskFlow setAction(String action) {
        bitFiled0_ |= 0x8;
        this.action = action;
        return this;
    }


    public static QlogCncTaskFlow init(QlogPlayerFlowInterface flow_name) {
        QlogCncTaskFlow flow = new QlogCncTaskFlow();
        flow.fillHead(flow_name);
        return flow;
    }

    @Override
    public boolean checkCompletion() {
        return (bitFiled0_ == 0xf);
    }

    @Override
    public List<String> getIncompleteFields() {
        List<String> result = new ArrayList<>();
        long mask;
        int i = 0;
        while ((mask = 1L << (i % 64)) <= 0x8) {
            if ((mask & bitFiled0_) == 0) {
                result.add(FIELD_NAMES[i]);
            }
            ++i;
        }
        return result;
    }


    @Override
    protected int getCurrentFieldCnt() {
        return currentFieldCnt;
    }


    @Override
    protected void addUsrDefContent(StringBuilder builder) {
        builder.append("|").append(dtEventTime, 0, Math.min(dtEventTime.length(), 32));
        builder.append("|").append(taskClass, 0, Math.min(taskClass.length(), 32));
        builder.append("|").append(iTaskID);
        builder.append("|").append(action, 0, Math.min(action.length(), 32));
    }


    @Override
    protected String getMetaName() {
        return META_NAME;
    }
}


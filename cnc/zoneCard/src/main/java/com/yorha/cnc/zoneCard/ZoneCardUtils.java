package com.yorha.cnc.zoneCard;

import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.server.ServerOpenStatus;
import com.yorha.common.server.config.ClusterConfigUtils;
import com.yorha.common.server.config.ConfigObj;
import com.yorha.common.server.discovery.ZoneItem;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public class ZoneCardUtils {
    private static final Logger LOGGER = LogManager.getLogger(ZoneCardUtils.class);

    private ZoneCardUtils() {
    }

    public static long getServerOpenTsMs(final int zoneId) {
        final ZoneItem zoneItem = DefaultZoneGateItemHandler.getInstance().getZone(zoneId);
        if (zoneItem != null) {
            return zoneItem.getOpenTsMs();
        }
        return ClusterConfigUtils.getZoneConfig(zoneId).getLongItem("open_zone_time");
    }

    /**
     * 普通玩家是否可见该服
     *
     * @param zoneId
     * @param nowTsMs
     * @return
     */
    public static boolean zoneAvailableForPlayer(final int zoneId, final long nowTsMs) {
        // 校验是否对外
        ConfigObj zoneConfig = ClusterConfigUtils.getZoneConfigOrNull(zoneId);
        if (zoneConfig == null) {
            LOGGER.warn("zoneAvailableForPlayer zoneId={}, no config", zoneId);
            return false;
        }
        String status = zoneConfig.getStringItem("cur_server_status");
        try {
            final ServerOpenStatus curStatus = ServerOpenStatus.valueOf(status);
            if (curStatus != ServerOpenStatus.OPEN) {
                LOGGER.info("zoneAvailableForPlayer zoneId={}, ServerOpenStatus not open", zoneId);
                return false;
            }
        } catch (Exception e) {
            LOGGER.error("zoneAvailableForPlayer zoneId{}, unknown status={}", zoneId, status);
            return false;
        }

        // 校验开服时间
        final boolean zoneIsOpen = getServerOpenTsMs(zoneId) <= nowTsMs;
        LOGGER.info("zoneAvailableForPlayer zoneId={}, zoneIsOpen={}", zoneId, zoneIsOpen);
        return zoneIsOpen;
    }


}

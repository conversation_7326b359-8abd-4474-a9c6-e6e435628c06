package com.yorha.cnc.zoneCard;

import com.google.common.collect.Maps;
import com.yorha.common.actor.node.DefaultZoneGateItemHandler;
import com.yorha.common.resource.ResHolder;
import com.yorha.common.resource.resservice.multiServer.MultiServerService;
import com.yorha.common.server.discovery.ZoneItem;
import com.yorha.common.utils.Pair;
import com.yorha.common.utils.time.SystemClock;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import res.template.ConstServerTemplate;
import res.template.ServerAttributeTemplate;

import java.util.*;

/**
 * 获取zoneCard
 *
 * <AUTHOR>
 */
public class ZoneCardGetter {
    private static final Logger LOGGER = LogManager.getLogger(ZoneCardGetter.class);
    /**
     * 普通玩家可见的zoneServerInfo缓存(未开服不可见)
     */
    private List<CommonMsg.ZoneServerInfo> zoneServerInfoListForNormalPlayer = new ArrayList<>();

    /**
     * 超级白名单可见的zoneServerInfo缓存(进程存活就可见)
     */
    private List<CommonMsg.ZoneServerInfo> zoneServerInfoListForSuperWhitePlayer = new ArrayList<>();

    /**
     * 指定赛季下服务器信息(未开服不可见)
     */
    private Map<CommonEnum.ZoneSeason, List<Integer>> zoneSeasons = new EnumMap<>(CommonEnum.ZoneSeason.class);
    /**
     * zoneCard(未开服不可见)
     */
    private Map<Integer, CommonMsg.ZoneCard> zoneCards = new HashMap<>();
    /**
     * 高战斗力服务器id
     */
    private Set<Integer> highPowerZoneIds = new HashSet<>();

    public ZoneCardGetter() {

    }

    /**
     * 主刷新入口
     */
    public void update() {
        final Map<CommonEnum.ZoneSeason, List<Integer>> newZoneSeasons = new EnumMap<>(CommonEnum.ZoneSeason.class);
        final List<CommonMsg.ZoneServerInfo> newZoneServerInfoList = new ArrayList<>();
        final List<CommonMsg.ZoneServerInfo> newZoneServerInfoWhiteList = new ArrayList<>();
        final Map<Integer, CommonMsg.ZoneCard> zoneCards = new HashMap<>();

        final CommonMsg.ZoneServerInfo.Builder zoneServerInfoBuilder = CommonMsg.ZoneServerInfo.newBuilder();

        int maxZoneId = 0;
        int minZoneId = Integer.MAX_VALUE;
        final long nowTsMs = SystemClock.now();

        for (ZoneItem zone : DefaultZoneGateItemHandler.getInstance().getZoneList()) {
            zoneServerInfoBuilder.clear();
            final int zoneId = zone.getZoneId();
            final long serverOpenTsMs = zone.getOpenTsMs();
            final CommonMsg.ZoneCard zoneCard = zone.getZoneCard();
            // 普通玩家不可见
            if (!ZoneCardUtils.zoneAvailableForPlayer(zoneId, nowTsMs)) {
                zoneServerInfoBuilder
                        .setZoneId(zoneId)
                        .setOpenTsMs(serverOpenTsMs)
                        .setServerJamStatus(zoneCard.getServerJamStatus().getNumber());
                newZoneServerInfoWhiteList.add(zoneServerInfoBuilder.build());
                continue;
            }
            zoneCards.put(zoneId, zoneCard);

            CommonEnum.ZoneSeason zoneSeason = formZoneSeason(zoneCard.getZoneSeason());
            if (zoneSeason == CommonEnum.ZoneSeason.ZS_BUILDING) {
                zoneSeason = CommonEnum.ZoneSeason.ZS_PREPARE;
            }
            newZoneSeasons.computeIfAbsent(zoneSeason, (k) -> new LinkedList<>()).add(zoneId);
            maxZoneId = Math.max(maxZoneId, zoneId);
            minZoneId = Math.min(minZoneId, zoneId);


            zoneServerInfoBuilder
                    .setZoneId(zoneId)
                    .setOpenTsMs(serverOpenTsMs)
                    .setServerJamStatus(zoneCard.getServerJamStatus().getNumber());
            final CommonMsg.ZoneServerInfo zoneServerInfo = zoneServerInfoBuilder.build();
            newZoneServerInfoList.add(zoneServerInfo);
            newZoneServerInfoWhiteList.add(zoneServerInfo);
        }

        zoneSeasons = newZoneSeasons;
        zoneServerInfoListForNormalPlayer = newZoneServerInfoList;
        zoneServerInfoListForSuperWhitePlayer = newZoneServerInfoWhiteList;
        highPowerZoneIds = this.calculateHighPowerZoneIds(minZoneId, maxZoneId, zoneCards);
        this.zoneCards = zoneCards;
    }

    /**
     * 计算高战力服务器
     *
     * @param minZoneId 最小服务器id
     * @param maxZoneId 最大服务器id
     * @param zoneCards 服务器数据
     * @return Set<Integer>
     */
    private Set<Integer> calculateHighPowerZoneIds(final int minZoneId, final int maxZoneId, final Map<Integer, CommonMsg.ZoneCard> zoneCards) {
        final Set<Integer> newHighPowerZoneIds = new HashSet<>();
        if (zoneCards.isEmpty()) {
            return newHighPowerZoneIds;
        }
        final int minGroupId = ResHolder.getInstance().findValueFromMap(ServerAttributeTemplate.class, minZoneId).getGroupid();
        final int maxGroupId = ResHolder.getInstance().findValueFromMap(ServerAttributeTemplate.class, maxZoneId).getGroupid();

        for (int groupId = minGroupId; groupId <= maxGroupId; groupId++) {
            final Set<Integer> zoneIds = ResHolder.getResService(MultiServerService.class).getZonesUnderGroup(groupId);
            if (zoneIds == null) {
                LOGGER.warn("noZoneIds under groupId={}", groupId);
                continue;
            }

            final List<Pair<Integer, Long>> zoneIdWithPower = new ArrayList<>(zoneIds.size());
            boolean needContinue = false;
            for (final Integer zoneId : zoneIds) {
                final CommonMsg.ZoneCard zoneCard = zoneCards.get(zoneId);
                // 组下没有足够的已开服服务器，不生成新秀战场
                if (zoneCard == null) {
                    LOGGER.warn("no zoneCard for zoneId={}", zoneId);
                    needContinue = true;
                    break;
                }
                // 建设中or未开服 不计算高战力
                if (zoneCard.getZoneSeason() == CommonEnum.ZoneSeason.ZS_BUILDING || zoneCard.getZoneSeason() == CommonEnum.ZoneSeason.ZS_NONE) {
                    needContinue = true;
                    LOGGER.info("skip, before prepare zoneId={} groupId={}", zoneId, groupId);
                    break;
                }
                zoneIdWithPower.add(Pair.of(zoneId, zoneCard.getServerPower()));
            }
            if (needContinue) {
                continue;
            }
            zoneIdWithPower.sort(Comparator.<Pair<Integer, Long>>comparingLong(Pair::getSecond).reversed());
            // 取前N%
            final int requiredNum = zoneIdWithPower.size() * ResHolder.getInstance().getConstTemplate(ConstServerTemplate.class).getMulserverHighPrenum() / 100;
            for (int i = 0; i < requiredNum; i++) {
                newHighPowerZoneIds.add(zoneIdWithPower.get(i).getFirst());
                LOGGER.info("zoneId={}, power={}, is highPowerZone", zoneIdWithPower.get(i).getFirst(), zoneIdWithPower.get(i).getSecond());
            }
        }
        LOGGER.info("newHighPowerZoneIds={}", newHighPowerZoneIds);
        return newHighPowerZoneIds;
    }

    /**
     * 建设中归为预备赛季
     *
     * @param dbValue db内数据
     * @return
     */
    private CommonEnum.ZoneSeason formZoneSeason(final CommonEnum.ZoneSeason dbValue) {
        if (dbValue == CommonEnum.ZoneSeason.ZS_BUILDING) {
            return CommonEnum.ZoneSeason.ZS_PREPARE;
        }
        return dbValue;
    }

    /**
     * 获取db中所有zoneCard数据
     *
     * @return
     */
    public Map<Integer, CommonMsg.ZoneCard> getZoneCards() {
        return Collections.unmodifiableMap(zoneCards);
    }

    /**
     * 获得给普通玩家的serverInfo
     *
     * @return
     */
    public List<CommonMsg.ZoneServerInfo> getZoneServerInfoListForNormalPlayer() {
        return Collections.unmodifiableList(zoneServerInfoListForNormalPlayer);
    }

    /**
     * 获得给超级白名单玩家的serverInfo
     *
     * @return
     */
    public List<CommonMsg.ZoneServerInfo> getZoneServerInfoListForSuperWhitePlayer() {
        return Collections.unmodifiableList(zoneServerInfoListForSuperWhitePlayer);
    }

    /**
     * 获得高战服务器id
     *
     * @return
     */
    public Set<Integer> getHighPowerZoneIds() {
        return Collections.unmodifiableSet(highPowerZoneIds);
    }

    public Map<Integer, Integer> getZonesUnderSeason(final CommonEnum.ZoneSeason zoneSeason) {
        final Map<Integer, Integer> zonesUnderSeason = Maps.newHashMap();

        final List<Integer> zoneIds = this.zoneSeasons.get(zoneSeason);
        if (zoneIds == null) {
            return zonesUnderSeason;
        }
        for (final Integer zoneId : zoneIds) {
            final CommonMsg.ZoneCard zoneCard = this.zoneCards.get(zoneId);
            if (zoneCard == null) {
                continue;
            }
            zonesUnderSeason.put(zoneId, 0);
        }
        return zonesUnderSeason;

    }

}


package com.yorha.cnc.zoneCard;

import com.yorha.common.actor.ZoneCardService;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.common.enums.error.ErrorCode;
import com.yorha.common.exception.GeminiException;
import com.yorha.proto.CommonEnum;
import com.yorha.proto.CommonMsg;
import com.yorha.proto.SsZoneCard;
import com.yorha.proto.StructMsg;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

public class ZoneCardServiceImpl implements ZoneCardService {
    private static final Logger LOGGER = LogManager.getLogger(ZoneCardServiceImpl.class);
    private final ZoneCardActor actor;
    private final ZoneCardGetter zoneCardGetter;

    public ZoneCardServiceImpl(ZoneCardActor actor) {
        this.actor = actor;
        this.zoneCardGetter = new ZoneCardGetter();
        this.actor.addRepeatTimer(
                TimerReasonType.REFRESH_HIGH_POWER_ZONE,
                this::updateZoneCard,
                0,
                30,
                TimeUnit.SECONDS);
    }

    @Override
    public void handleGetAllZoneInfoAsk(SsZoneCard.GetAllZoneInfoAsk ask) {
        if (ask.getIsSuperWhite()) {
            actor.answer(SsZoneCard.GetAllZoneInfoAns.newBuilder().addAllZoneServerList(
                    this.zoneCardGetter.getZoneServerInfoListForSuperWhitePlayer()).build());
        } else {
            actor.answer(SsZoneCard.GetAllZoneInfoAns.newBuilder().addAllZoneServerList(
                    this.zoneCardGetter.getZoneServerInfoListForNormalPlayer()).build());
        }
    }

    @Override
    public void handleGetMultiZoneStatusAsk(SsZoneCard.GetMultiZoneStatusAsk ask) {
        LOGGER.info("handleGetMultiZoneStatusAsk ask={}", ask);
        final SsZoneCard.GetMultiZoneStatusAns.Builder ans = SsZoneCard.GetMultiZoneStatusAns.newBuilder();
        final Map<Integer, StructMsg.ZoneStatus> statusMap = getZoneStatus(ask.getZoneIdsList());
        ans.putAllServersStatus(statusMap);
        actor.answer(ans.build());
    }

    @Override
    public void handleGetZonesUnderSeasonAsk(SsZoneCard.GetZonesUnderSeasonAsk ask) {
        LOGGER.info("handleGetZonesUnderSeasonAsk ask={}", ask);
        final CommonEnum.ZoneSeason zoneSeason = ask.getZoneSeason();
        final SsZoneCard.GetZonesUnderSeasonAns ans = SsZoneCard.GetZonesUnderSeasonAns.newBuilder()
                .putAllZoneIds(this.zoneCardGetter.getZonesUnderSeason(zoneSeason))
                .build();
        actor.answer(ans);
    }

    @Override
    public void handleGetZoneMileStoneAsk(SsZoneCard.GetZoneMileStoneAsk ask) {
        LOGGER.info("handleGetZoneMileStoneAsk ask={}", ask);
        CommonMsg.ZoneCard zoneCard = this.zoneCardGetter.getZoneCards().getOrDefault(ask.getZoneId(), null);
        if (zoneCard == null) {
            LOGGER.warn("handleGetZoneMileStoneAsk zoneId={} not in zoneCards", ask.getZoneId());
            throw new GeminiException(ErrorCode.MULTI_SERVER_GET_TARGET_SERVER_INFO_FAIL);
        }
        actor.answer(SsZoneCard.GetZoneMileStoneAns.newBuilder()
                .setMileStoneId(zoneCard.getMileStoneId())
                .build());
    }

    private Map<Integer, StructMsg.ZoneStatus> getZoneStatus(final List<Integer> zoneIds) {
        final Map<Integer, StructMsg.ZoneStatus> result = new HashMap<>(zoneIds.size());
        final Map<Integer, CommonMsg.ZoneCard> zoneCards = this.zoneCardGetter.getZoneCards();
        final StructMsg.ZoneStatus.Builder builder = StructMsg.ZoneStatus.newBuilder();
        final Set<Integer> highPowerZoneIds = this.zoneCardGetter.getHighPowerZoneIds();
        for (final Integer zoneId : zoneIds) {
            final CommonMsg.ZoneCard zoneCard = zoneCards.get(zoneId);
            if (zoneCard == null) {
                continue;
            }
            final long zoneStartTsMs = ZoneCardUtils.getServerOpenTsMs(zoneId);
            builder.setZoneSeason(zoneCard.getZoneSeason())
                    .setStartTsMs(zoneStartTsMs)
                    .setIsHighPower(highPowerZoneIds.contains(zoneId));
            result.put(zoneId, builder.build());
        }

        return result;
    }

    /**
     * 更新zoneCard相关数据
     */
    private void updateZoneCard() {
        this.zoneCardGetter.update();
    }

}

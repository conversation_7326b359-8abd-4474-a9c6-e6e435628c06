package com.yorha.cnc.zoneCard;


import com.yorha.common.actor.IActorRef;
import com.yorha.common.actor.ZoneCardService;
import com.yorha.common.actor.ZoneCardServices;
import com.yorha.common.actor.node.IZoneCardActor;
import com.yorha.common.actorservice.ActorSystem;
import com.yorha.common.actorservice.ActorTimer;
import com.yorha.common.actorservice.BaseGameActor;
import com.yorha.common.enums.TimerReasonType;
import com.yorha.gemini.actor.msg.TypedMsg;

import javax.annotation.Nullable;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
public class ZoneCardActor extends BaseGameActor implements ZoneCardServices, IZoneCardActor {

    private final ZoneCardService service;

    public ZoneCardActor(ActorSystem system, IActorRef self) {
        super(system, self);
        service = new ZoneCardServiceImpl(this);
    }

    @Nullable
    public ActorTimer addRepeatTimer(TimerReasonType timerReasonType, Runnable runnable, long initialDelay, long period, TimeUnit unit) {
        return dangerousAddRepeatTimer(getId(), timerReasonType, runnable, initialDelay, period, unit, false);
    }

    @Override
    protected void handleTypedMsg(TypedMsg typedMsg) {
        dispatchProtoMsg(typedMsg);
    }

    /**
     * 用于tellAndCreate
     */
    @Override
    public void start() {

    }

    @Override
    public ZoneCardService getZoneCardService() {
        return service;
    }

}
